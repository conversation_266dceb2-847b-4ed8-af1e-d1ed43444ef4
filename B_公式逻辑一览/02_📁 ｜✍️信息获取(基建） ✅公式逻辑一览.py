```text
📁编号（最终）
TEXT([📁 ｜✍️信息获取(基建）]
.COUNTIF(CurrentValue.[📁编号（参考勿删！）]<[📁编号（参考勿删！）])+1

，"👀000000000"）
=====
📁编号（参考勿删！）
自增数字
=====
🍬商品信息（原始版）
导入信息
=====
🍬商品信息ID
导入信息
=====
🍬信息收集账号/人
导入信息
=====
🍬信息来源 / 发车人
导入信息
=====
🍬源信息发布时间（自动）
导入信息
=====
🍬源信息采集时间（自动）
导入信息
=====
🧠基础单位
IF([🍬商品信息（原始版）].ISBLANK(),"",
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠基础单位]!="").[🧠基础单位].LISTCOMBINE().UNIQUE())
=====
🧠初级单位
IF([🍬商品信息（原始版）].ISBLANK(),"",
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠初级单位]!="").[🧠初级单位].LISTCOMBINE().UNIQUE())
=====
🧠进阶单位
IF([🍬商品信息（原始版）].ISBLANK(),"",
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠进阶单位]!="").[🧠进阶单位].LISTCOMBINE().UNIQUE())
=====
🧠终极单位
IF([🍬商品信息（原始版）].ISBLANK(),"",
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠终极单位]!="").[🧠终极单位].LISTCOMBINE().UNIQUE())
=====
🧠高阶单位
IF([🍬商品信息（原始版）].ISBLANK(),""，
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠高阶单位]!="").[🧠高阶单位].LISTCOMBINE().UNIQUE())
=====
🧠单位合集
IF([🍬商品信息（原始版）].ISBLANK(),"",
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠单位合集]!="").[🧠单位合集].LISTCOMBINE().UNIQUE())
=====
🧠加号单位
IF([🍬商品信息（原始版）].ISBLANK(),"",
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠加法符号]!="").[🧠加法符号].LISTCOMBINE().UNIQUE())
=====
🧠赠送信息参考-逻辑
IF([🍬商品信息（原始版）].ISBLANK(),"",
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠赠送信息-逻辑]!="").[🧠赠送信息-逻辑].LISTCOMBINE().UNIQUE())
=====
🧠信息类型参考-逻辑-1
IF([🍬商品信息（原始版）].ISBLANK(),"",
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠信息类型参考-逻辑-1]!="").[🧠信息类型参考-逻辑-1].LISTCOMBINE().UNIQUE())
=====
🧠信息类型参考-逻辑-2
IF([🍬商品信息（原始版）].ISBLANK(),"",
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠信息类型参考-逻辑-2]!="").[🧠信息类型参考-逻辑-2].LISTCOMBINE().UNIQUE())
=====
🧠领券文案干扰信息-逻辑
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠领券文案干扰信息-逻辑]!="").[🧠领券文案干扰信息-逻辑].LISTCOMBINE().UNIQUE()
=====
🧠品牌-逻辑
IF([🍬商品信息（原始版）].ISBLANK(),"",
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠品牌-逻辑]!="").[🧠品牌-逻辑].LISTCOMBINE().UNIQUE())
=====
Ⓜ️🤍‼️品牌（标准格式）
[Ⓜ️ ｜ ✍️ 品牌识别（基建）].FILTER(CurrentValue.[Ⓜ️编号（最终）]=[📁编号（最终）]).[Ⓜ️🤍‼️品牌（标准格式）].LISTCOMBINE()
=====
Ⓜ️✍️品牌填写判断（手动）
[Ⓜ️ ｜ ✍️ 品牌识别（基建）].FILTER(CurrentValue.[Ⓜ️编号（最终）]=[📁编号（最终）]).[Ⓜ️✍️品牌识别进度（手动）].LISTCOMBINE()
=====
💰到手价格（终版）
[🧪 规格解构（基建）].FILTER(CurrentValue.[🧪编号（最终）]=[📁编号（最终）]).[💰到手价格（终版）].LISTCOMBINE()
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.VALUE()
.ROUND(0)
=====
💰💚单价-【基础规格】颗粒度
[💻 单价计算（基建）].FILTER(CurrentValue.[💻编号（最终）]=[📁编号（最终）]).[💻💚基础单价].LISTCOMBINE()

=====
🛍️🤍一级品类（唯一值）
[🛍️ ｜✍️ 品类｜口味｜制作工艺 ｜ 使用对象 ｜ 成长期（基建）].FILTER(CurrentValue.[🛍️编号（最终）]=[📁编号（最终）]).[🛍️🤍😀一级品类（唯一值）].LISTCOMBINE()
=====
🛍️🤍二级品类（唯一值）
[🛍️ ｜✍️ 品类｜口味｜制作工艺 ｜ 使用对象 ｜ 成长期（基建）].FILTER(CurrentValue.[🛍️编号（最终）]=[📁编号（最终）]).[🛍️🤍😀二级品类（唯一值）].LISTCOMBINE()
=====
🛍️💜使用对象(唯一值)
[🛍️ ｜✍️ 品类｜口味｜制作工艺 ｜ 使用对象 ｜ 成长期（基建）].FILTER(CurrentValue.[🛍️编号（最终）]=[📁编号（最终）]).[🛍️💜使用对象(唯一值)].LISTCOMBINE()
=====
🛍️💜适用宠物成长期（唯一值）
[🛍️ ｜✍️ 品类｜口味｜制作工艺 ｜ 使用对象 ｜ 成长期（基建）].FILTER(CurrentValue.[🛍️编号（最终）]=[📁编号（最终）]).[🛍️💜适用宠物成长期（唯一值）].LISTCOMBINE()
=====
🛍️💛口味（唯一值）
[🛍️ ｜✍️ 品类｜口味｜制作工艺 ｜ 使用对象 ｜ 成长期（基建）].FILTER(CurrentValue.[🛍️编号（最终）]=[📁编号（最终）]).[🛍️💜口味（唯一值）].LISTCOMBINE()
=====
🛍️💜制作工艺（唯一值）
[🛍️ ｜✍️ 品类｜口味｜制作工艺 ｜ 使用对象 ｜ 成长期（基建）].FILTER(CurrentValue.[🛍️编号（最终）]=[📁编号（最终）]).[🛍️💜制作工艺（唯一值）].LISTCOMBINE()
=====
🧪规格类型（唯一值）
[🧪 规格解构（基建）].FILTER(CurrentValue.[🧪编号（最终）]=[📁编号（最终）]).[🧪规格类型（唯一值）].LISTCOMBINE()
=====
🧪🤍规格✖️数量✖️起拍数量（终版）
[🧪 规格解构（基建）].FILTER(CurrentValue.[🧪编号（最终）]=[📁编号（最终）]).[🧪🔥🤍规格✖️数量✖️起拍数量].LISTCOMBINE()

.REGEXREPLACE("[❶-❻🔞]","")
=====
🔪💚商品名称（信息重组专用版）
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[📁编号（最终）]).[🔪💚商品名称（信息重组专用版）].LISTCOMBINE()
=====
🔪🤍😀领券文案
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[📁编号（最终）]).[🔪🧡领券文案].LISTCOMBINE()
=====
🔪🖤SKU数量
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[📁编号（最终）]).[🔪🖤SKU数量].LISTCOMBINE()
=====
🐽💚商品信息（重组版）
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[📁编号（最终）]).[🐽💚商品信息（重组版）].LISTCOMBINE()
=====
💻💚计算单价合并（高级）
[💻 单价计算（基建）].FILTER(CurrentValue.[💻编号（最终）]=[📁编号（最终）]).[💻💚计算单价合并（高级）].LISTCOMBINE()
=====
📁所属平台
IF(
ISBLANK([🍬商品信息（原始版）].REGEXREPLACE("(?m)^\s*$\r?\n?", ""))
,"",
IF(
[🍬商品信息（原始版）]
.CONTAINTEXT("u.jd.com")
,"🔴京东",

IF(
[🍬商品信息（原始版）]
.CONTAINTEXT("pinduoduo.com")
,"🟡拼多多",

IF(
[🍬商品信息（原始版）]
.REGEXMATCH("(.*?[0-9a-zA-z]@[.0-9a-zA-z]{4,99}.*)|◤◥")
,"🟣抖音",

"🍑淘宝")))）
=====
📁社群定位
[♠︎ 数据来源管理（底库）]
.FILTER(CurrentValue.[🤖【yg】信息来源 / 发车人]=[🍬信息来源 / 发车人])
.[社群定位]
=====
📁预售情况
IF(
[🍬商品信息（原始版）]
.ISBLANK()
,"",

IF(
[🍬商品信息（原始版）]
.REGEXMATCH("(预售|[付结]?尾款)"),

"⏰预售"
,"🛍️现货")
)
=====
📁💜信息类型
CONCATENATE(

IF(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(1)
.TRIM()
.ISBLANK()

,"",

IF(
OR(
AND(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(1)
.REGEXMATCH("💰(\d+(?:\.\d+)?)")
.NOT()，

[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(1)
.REGEXMATCH([🧠信息类型参考-逻辑-1])
)
,
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(1)
.REGEXMATCH([🧠信息类型参考-逻辑-2])

)

,"❶🎫领券/凑单信息❶🔞"
，
IF(
OR(
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[📁编号（最终）]).[🔪🧡下单口令/链接提取]
.REGEXEXTRACTALL("❶.*?❶🔞")
.ISBLANK()

,
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(1)
.TRIM()
=
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[📁编号（最终）]).[🔪🧡下单口令/链接提取]
.REGEXEXTRACTALL("❶.*?❶🔞")
.TRIM()

，
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(1)
.REGEXMATCH("快来玩")

)
,"❶📦其他信息❶🔞"

,
IF(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(1)
.REGEXMATCH("cyg888")
，"❶🍎不可转链商品信息❶"


，"❶🛒商品信息❶🔞"
)

)))

,CHAR(10),

IF(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(2)
.TRIM()
.ISBLANK()

,"",


IF(
OR(
AND(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(2)
.REGEXMATCH("💰(\d+(?:\.\d+)?)")
.NOT(),
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(2)
.REGEXMATCH([🧠信息类型参考-逻辑-1])
),
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(2)
.REGEXMATCH([🧠信息类型参考-逻辑-2])
),
"❷🎫领券/凑单信息❷🔞",
IF(
OR(
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[📁编号（最终）]).[🔪🧡下单口令/链接提取]
.REGEXEXTRACTALL("❷.*?❷🔞")
.ISBLANK(),
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(2)
.TRIM()
=
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[📁编号（最终）]).[🔪🧡下单口令/链接提取]
.REGEXEXTRACTALL("❷.*?❷🔞")
.TRIM(),
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(2)
.REGEXMATCH("快来玩"),

),
"❷📦其他信息❷🔞"
,
IF(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(2)
.REGEXMATCH("cyg888")
，"❷🍎不可转链商品信息❷"

,"❷🛒商品信息❷🔞"
)
)))

,CHAR(10),

IF(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(3)
.TRIM()
.ISBLANK()

,"",


IF(
OR(
AND(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(3)
.REGEXMATCH("💰(\d+(?:\.\d+)?)")
.NOT(),
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(3)
.REGEXMATCH([🧠信息类型参考-逻辑-1])
),
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(3)
.REGEXMATCH([🧠信息类型参考-逻辑-2])
),
"❸🎫领券/凑单信息❸🔞",
IF(
OR(
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[📁编号（最终）]).[🔪🧡下单口令/链接提取]
.REGEXEXTRACTALL("❸.*?❸🔞")
.ISBLANK(),
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(3)
.TRIM()
=
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[📁编号（最终）]).[🔪🧡下单口令/链接提取]
.REGEXEXTRACTALL("❸.*?❸🔞")
.TRIM(),
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(3)
.REGEXMATCH("快来玩"),

),
"❸📦其他信息❸🔞"

,
IF(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(3)
.REGEXMATCH("cyg888")
，"❸🍎不可转链商品信息❸"
,
"❸🛒商品信息❸🔞"
)
)))

,CHAR(10),

IF(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(4)
.TRIM()
.ISBLANK()

,"",


IF(
OR(
AND(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(4)
.REGEXMATCH("💰(\d+(?:\.\d+)?)")
.NOT(),
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(4)
.REGEXMATCH([🧠信息类型参考-逻辑-1])
),
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(4)
.REGEXMATCH("328.*?(权益)?[券包]|开.*?会员")
),
"❹🎫领券/凑单信息❹🔞",
IF(
OR(
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[📁编号（最终）]).[🔪🧡下单口令/链接提取]
.REGEXEXTRACTALL("❹.*?❹🔞")
.ISBLANK(),
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(4)
.TRIM()
=
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[📁编号（最终）]).[🔪🧡下单口令/链接提取]
.REGEXEXTRACTALL("❹.*?❹🔞")
.TRIM(),
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(4)
.REGEXMATCH("快来玩"),

),
"❹📦其他信息❹🔞"
,
IF(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(4)
.REGEXMATCH("cyg888")
，"❹🍎不可转链商品信息❹"
,
"❹🛒商品信息❹🔞"
)
)))


,CHAR(10),

IF(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(5)
.TRIM()
.ISBLANK()

,"",


IF(
OR(
AND(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(5)
.REGEXMATCH("💰(\d+(?:\.\d+)?)")
.NOT(),
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(5)
.REGEXMATCH([🧠信息类型参考-逻辑-1])
),
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(5)
.REGEXMATCH([🧠信息类型参考-逻辑-2])
),
"❺🎫领券/凑单信息❺🔞",
IF(
OR(
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[📁编号（最终）]).[🔪🧡下单口令/链接提取]
.REGEXEXTRACTALL("❺.*?❺🔞")
.ISBLANK(),
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(5)
.TRIM()
=
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[📁编号（最终）]).[🔪🧡下单口令/链接提取]
.REGEXEXTRACTALL("❺.*?❺🔞")
.TRIM(),
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(5)
.REGEXMATCH("快来玩"),

),
"❺📦其他信息❺🔞"

,
IF(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(5)
.REGEXMATCH("cyg888")
,"❺🍎不可转链商品信息❺"
,"❺🛒商品信息❺🔞"
)
)))


,CHAR(10),

IF(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(6)
.TRIM()
.ISBLANK()

,"",


IF(
OR(
AND(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(6)
.REGEXMATCH("💰(\d+(?:\.\d+)?)")
.NOT(),
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(6)
.REGEXMATCH([🧠信息类型参考-逻辑-1])
),
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(6)
.REGEXMATCH([🧠信息类型参考-逻辑-2])
),
"❻🎫领券/凑单信息❻🔞",
IF(
OR(
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[📁编号（最终）]).[🔪🧡下单口令/链接提取]
.REGEXEXTRACTALL("❻.*?❻🔞")
.ISBLANK(),
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(6)
.TRIM()
=
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[📁编号（最终）]).[🔪🧡下单口令/链接提取]
.REGEXEXTRACTALL("❻.*?❻🔞")
.TRIM(),
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(6)
.REGEXMATCH("快来玩"),

),
"❻📦其他信息❻🔞"

,
IF(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(6)
.REGEXMATCH("cyg888")
,"❻🍎不可转链商品信息❻"
,"❻🛒商品信息❻🔞"
)
)))


,CHAR(10),

IF(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(7)
.TRIM()
.ISBLANK()

,"",


IF(
OR(
AND(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(7)
.REGEXMATCH("💰(\d+(?:\.\d+)?)")
.NOT(),
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(7)
.REGEXMATCH([🧠信息类型参考-逻辑-1])
),
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(7)
.REGEXMATCH([🧠信息类型参考-逻辑-2])
),
"❼🎫领券/凑单信息❼🔞",
IF(
OR(
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[📁编号（最终）]).[🔪🧡下单口令/链接提取]
.REGEXEXTRACTALL("❼.*?❼🔞")
.ISBLANK(),
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(7)
.TRIM()
=
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[📁编号（最终）]).[🔪🧡下单口令/链接提取]
.REGEXEXTRACTALL("❼.*?❼🔞")
.TRIM(),
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(7)
.REGEXMATCH("快来玩"),

),
"❼📦其他信息❼🔞"

,
IF(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(7)
.REGEXMATCH("cyg888")
,"❼🍎不可转链商品信息❼"
,"❼🛒商品信息❼🔞"
)
)))


,CHAR(10),

IF(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(8)
.TRIM()
.ISBLANK()

,"",


IF(
OR(
AND(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(8)
.REGEXMATCH("💰(\d+(?:\.\d+)?)")
.NOT(),
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(8)
.REGEXMATCH([🧠信息类型参考-逻辑-1])
),
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(8)
.REGEXMATCH([🧠信息类型参考-逻辑-2])
),
"❽🎫领券/凑单信息❽🔞",
IF(
OR(
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[📁编号（最终）]).[🔪🧡下单口令/链接提取]
.REGEXEXTRACTALL("❽.*?❽🔞")
.ISBLANK(),
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(8)
.TRIM()
=
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[📁编号（最终）]).[🔪🧡下单口令/链接提取]
.REGEXEXTRACTALL("❽.*?❽🔞")
.TRIM(),
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(8)
.REGEXMATCH("快来玩"),

),
"❽📦其他信息❽🔞"
,
IF(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(8)
.REGEXMATCH("cyg888")
,"❽🍎不可转链商品信息❽"
,
"❽🛒商品信息❽🔞"
)
)))

,CHAR(10),

IF(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(9)
.TRIM()
.ISBLANK()

,"",


IF(
OR(
AND(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(9)
.REGEXMATCH("💰(\d+(?:\.\d+)?)")
.NOT(),
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(9)
.REGEXMATCH([🧠信息类型参考-逻辑-1])
),
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(9)
.REGEXMATCH([🧠信息类型参考-逻辑-2])
),
"❾🎫领券/凑单信息❾🔞",
IF(
OR(
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[📁编号（最终）]).[🔪🧡下单口令/链接提取]
.REGEXEXTRACTALL("❾.*?❾🔞")
.ISBLANK(),
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(9)
.TRIM()
=
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[📁编号（最终）]).[🔪🧡下单口令/链接提取]
.REGEXEXTRACTALL("❾.*?❾🔞")
.TRIM(),
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(9)
.REGEXMATCH("快来玩"),

),
"❾📦其他信息❾🔞"
,
IF(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(9)
.REGEXMATCH("cyg888")
,"❾🍎不可转链商品信息❾"
,
"❾🛒商品信息❾🔞"
)
)))


)

.REGEXREPLACE("(?m)^\s*$\r?\n?", "")

=====
📁🩵信息类型（唯一值）
IF(
[🍬商品信息（原始版）]
.ISBLANK()
,""，

IF(
[📁🩵商品信息（清洗版）]
.REGEXMATCH("cyg888|dwz.cn")
，"🍎不可转链商品信息"


,

IF(
AND(
[📁🩵商品信息（清洗版）]
.REGEXMATCH("💰(\d+(?:\.\d+)?)")
.NOT()，
[📁🩵商品信息（清洗版）].REGEXMATCH([🧠信息类型参考-逻辑-1]&"|"&[🧠信息类型参考-逻辑-2]))

,"🎫领券/凑单信息"，

IF(
AND(
OR(
[Ⓜ️ ｜ ✍️ 品牌识别（基建）].FILTER(CurrentValue.[Ⓜ️编号（最终）]=[📁编号（最终）]).[Ⓜ️🤍品牌（唯一值）].ISBLANK().NOT()
,
[📁🩵商品信息（清洗版）]
.REGEXREPLACE("天猫|猫超|猫人|热狗","")
.REGEXMATCH("猫|狗|犬|喵|宠物|动物|兽|人宠")
)
，
[📁🩵商品信息（清洗版）]
.REGEXMATCH("💰(\d+(?:\.\d+)?)")
,
[📁🩵下单口令/链接提取（原始）]
.ISBLANK()
.NOT()
)

，"🛒商品信息",

IF(
OR(
[📁🩵下单口令/链接提取（原始）]
.ISBLANK()
,

[🍬商品信息（原始版）].TRIM()
=[📁🩵下单口令/链接提取（原始）].TRIM()

，
[📁🩵商品信息（清洗版）]
.REGEXMATCH("快来玩")

，AND(
[📁🩵商品信息（清洗版）]
.REGEXREPLACE("天猫|猫超|猫人","")
.REGEXMATCH("猫|狗|犬|喵|宠物|动物|兽|人宠")
.NOT()
，
[📁🩵商品信息（清洗版）]
.REGEXMATCH("💰")
.NOT()
)
)
,"📦其他信息"

,
IF(
[📁🩵商品信息（清洗版）].REGEXMATCH([🧠信息类型参考-逻辑-2])

,"🎫领券/凑单信息"

，"🛒商品信息"
)

)))))
=====
📁🩵商品信息（清洗版）
IF(
[📁所属平台]
    .REGEXMATCH("淘宝")，
 [🍬商品信息（原始版）]
    .REGEXREPLACE("(https?://[A-Za-z].tb.cn/(?:[A-Za-z].)?[A-Za-z0-9]*)", "$1Ⓜ️")
    .REGEXREPLACE("(https?://[A-Za-z].click.taobao.com/(?:[A-Za-z].)?[A-Za-z0-9]*)","$1Ⓜ️")
    .REGEXREPLACE("(https?://985\.so/[a-zA-Z0-9]*)", "$1Ⓜ️")
    .REGEXREPLACE("(https?://[A-Za-z].jd.com/[A-Za-z0-9]*)", "$1Ⓜ️")
    .REGEXREPLACE("(https?://[a-zA-Z0-9]*.[a-zA-Z0-9]*-[a-zA-Z0-9]*.cn/[a-zA-Z0-9]*)", "$1Ⓜ️")
    .REGEXREPLACE("(#小程序://快团团/点击查看/[a-zA-Z0-9]*)", "$1Ⓜ️")
    .SPLIT("Ⓜ️")
    .NTH(1)
    , [🍬商品信息（原始版）]）

 
    .REGEXREPLACE("㊙️㊙️㊙️下方为常见不可见字符的清除逻辑","")

    .REGEXREPLACE("[\u200B-\u200D\uFE00-\uFE0F\u202A-\u202E\u2060-\u206F]|CHAR(8203)|CHAR(8288)","")
    .REGEXREPLACE("[\u2028]", CHAR(10))

 
    .REGEXREPLACE("㊙️㊙️㊙️下方为常见 干扰字符/信息模块 的处理逻辑","")

    .REGEXREPLACE("(拍)(①|⓵|❶|1⃣)","${1}1")
    .REGEXREPLACE("(拍)(②|⓶|❷|2⃣)","${1}2")
    .REGEXREPLACE("(拍)(③|⓷|❸|3⃣)","${1}3")
    .REGEXREPLACE("(拍)(④|⓸|❹|4⃣)","${1}4")
    .REGEXREPLACE("(拍)(⑤|⓹|❺|5⃣)","${1}5")
    .REGEXREPLACE("(拍)(⑥|⓺|❻|6⃣)","${1}6")
    .REGEXREPLACE("(拍)(⑦|⓻|❼|7⃣)","${1}7")
    .REGEXREPLACE("(拍)(⑧|⓼|❽|8⃣)","${1}8")
    .REGEXREPLACE("(拍)(⑨|⓽|❾|9⃣)","${1}9")


    .REGEXREPLACE("[①-⑨⓵-⓽❶-❾]|1⃣|2⃣|3⃣|4⃣|(冲(?:‼|‼️))","")
    .REGEXREPLACE("(‼️|❗❗|❗️\s?❗️\s?|❗|✨|‼)","$0"&CHAR(10))
    .REGEXREPLACE("❗"&CHAR(10)&"❗","❗️❗️"&CHAR(10))
    .REGEXREPLACE("车回"，"")
    .REGEXREPLACE("\[[Pp]\d+\]","")
    .REGEXREPLACE("^\d+","")


    .REGEXREPLACE("㊙️㊙️㊙️下方为常见残缺信息补全","")
    .REGEXREPLACE(" (试吃)","+$1")


    .REGEXREPLACE("㊙️㊙️㊙️下方为一些文本结构优化的处理逻辑","")
  
   .REGEXREPLACE("([拍凑])\s*((?:\d+|[一-十二两])[:：]?)?("&[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[📁编号（最终）]).[🧠下单口令/链接-逻辑]&")"，"$1$2"&CHAR(10)&"$3")

    .REGEXREPLACE("(猫)"&CHAR(10)&"([抓爪][板窝])","$1$2")
    .REGEXREPLACE("(猫[抓爪])"&CHAR(10)&"([板窝])","$1$2")
    .REGEXREPLACE("(猫)"&CHAR(10)&"(草片)","$1$2")
    .REGEXREPLACE("(防潮)"&CHAR(10)&"(储粮桶)","$1$2")
    .REGEXREPLACE("(超?级?[大中小])"&CHAR(10)&"(.*?(?:储粮[桶罐]|猫砂[铲盆]))","$1$2")
    .REGEXREPLACE("(宠)"&CHAR(10)&"(物)","$1$2")
    .REGEXREPLACE("(主食)\s?"&CHAR(10)&"(罐)","$1$2")
    .REGEXREPLACE("(软)"&CHAR(10)&"(骨)","$1$2")
    .REGEXREPLACE("(牧野奇迹山谷)"&CHAR(10)&"(盛宴)","$1$2")
    .REGEXREPLACE("([猫狗犬宠物]{1,2})"&CHAR(10)&"([粮罐])","$1$2")
    .REGEXREPLACE("(弗列加特.*)"&CHAR(10)&"(\d+%.*)","$1$2")
    .REGEXREPLACE("(双)"&CHAR(10)&"(拼)","$1$2")

    .REGEXREPLACE("㊙️㊙️㊙️下方为常见错误文字矫正的处理逻辑","")


    .REGEXREPLACE("＝","=")
    .REGEXREPLACE("αρρ","app")
    .REGEXREPLACE("(λ)", "入")
    .REGEXREPLACE("(亓|π)", "元") 
    .REGEXREPLACE("U先","优先")
    .REGEXREPLACE("τao","淘")
    .REGEXREPLACE("𝑱𝑫","京东")
    .REGEXREPLACE("(𝒓)", "")
    .REGEXREPLACE("𝟖𝟖𝐯𝐢𝐩","88vip")
    .REGEXREPLACE("([最更])D","$1低")
    .REGEXREPLACE("([Pp][Ll][Uu][Aa])","Plus")
    .REGEXREPLACE("(jiǎng)","奖")
    .REGEXREPLACE("(加\s*🛒)","加车")

    .REGEXREPLACE("(天喵)","天猫")
    .REGEXREPLACE("[桃🍑掏](宝|金币)","淘$1")
    .REGEXREPLACE("(蕞)","最")
    .REGEXREPLACE("(單|単|箪)", "单")
    .REGEXREPLACE("(琻)", "金")
    .REGEXREPLACE("(現)", "现")
    .REGEXREPLACE("(帀)", "币")
    .REGEXREPLACE("(湊)", "凑")
    .REGEXREPLACE("(歀)", "款")
    .REGEXREPLACE("(箅)", "算")
    .REGEXREPLACE("(刬)", "划")
    .REGEXREPLACE("(斗音)", "抖音")
    .REGEXREPLACE("(彩荭)", "彩虹")
    .REGEXREPLACE("([虹荭紘][包苞])", "红包")
    .REGEXREPLACE("(貨)", "货")
    .REGEXREPLACE("(仟)", "千")
    .REGEXREPLACE("(雋)", "售")
    .REGEXREPLACE("(圖)", "图")
    .REGEXREPLACE("(贈)", "赠")
    .REGEXREPLACE("(扴|忦|'忦)", "价")
    .REGEXREPLACE("(糧)", "量")
    .REGEXREPLACE("(卷|劵|卷|锩)", "券")
    .REGEXREPLACE("(券纸)", "卷纸")
    .REGEXREPLACE("(肉券)", "肉卷")
    .REGEXREPLACE("(拚)", "拼")
    .REGEXREPLACE("(秒[沙])", "秒杀")
    .REGEXREPLACE("(棉牵)", "棉签")
    .REGEXREPLACE("(□|囗)", "口")  
    .REGEXREPLACE("(歩)", "步") 
    .REGEXREPLACE("鳥","鸟")
    .REGEXREPLACE("寳","宝")
    .REGEXREPLACE("語","语")
    .REGEXREPLACE("億","亿")
    .REGEXREPLACE("旗见","旗舰")
    .REGEXREPLACE("挌","格")
    .REGEXREPLACE("苞","包")
    .REGEXREPLACE("痁","店")
    .REGEXREPLACE("篮氏","蓝氏")
    .REGEXREPLACE("(拎|領)", "领") 
    .REGEXREPLACE("翎(.*?券)", "领$1") 
    .REGEXREPLACE("(好苹|好評|好萍)", "好评") 
    .REGEXREPLACE("(內)", "内")
    .REGEXREPLACE("帼","国")
    .REGEXREPLACE("粅","物")
    .REGEXREPLACE("蕞.*?抵","最低")
    .REGEXREPLACE("蕞","最")
    .REGEXREPLACE("費","费")



    .REGEXREPLACE("(在领)", "再领") 
    .REGEXREPLACE("佰亿","百亿")
    .REGEXREPLACE("\'?补['\.]贴","补贴")
    .REGEXREPLACE("百[/\.]补","百补")
    .REGEXREPLACE("百补","百亿补贴")
    .REGEXREPLACE("超[/\.]级","超级")
    .REGEXREPLACE("会[/\.]员","会员")
    .REGEXREPLACE("尾[\\]款","尾款")
    .REGEXREPLACE("大礼'包", "大礼包")

    .REGEXREPLACE("收.藏", "收藏")
    .REGEXREPLACE("复-制", "复制")
    .REGEXREPLACE("任选排下", "任选拍下")


    .REGEXREPLACE("官期","官旗")
    .REGEXREPLACE("回钩","回购")
    .REGEXREPLACE("京[Dd]ong","京东")
    .REGEXREPLACE("(仅划)", "仅")
    .REGEXREPLACE("(到[✋])", "到手")
    .REGEXREPLACE("付尾", "付尾款")
    .REGEXREPLACE("款+", "款")
    .REGEXREPLACE("简沫A\+","简沫A＋")


    .REGEXREPLACE("拆算", "折算") 
    .REGEXREPLACE("史[Dd]", "史低") 
    .REGEXREPLACE("猫卡", "猫超卡")
    .REGEXREPLACE("勉单", "免单")


    .REGEXREPLACE("(好评?|晒单|晒图|入会|会员)[反饭]", "$1返") 
    .REGEXREPLACE("([鸡鸭鱼牛羊].*?)券","$1卷")
    .REGEXREPLACE("猫爪([板窝])","猫抓$1")
    .REGEXREPLACE("([双多])磅", "$1榜")
    .REGEXREPLACE("(拍\d+)券", "$1卷")


    .REGEXREPLACE("排\s*((?:\d+|[一二两三四五六七八九十])(?:"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&"|"&[🧠终极单位]&")?|[:：])","拍$1")
     .REGEXREPLACE("[牛肋兔鸡羊]{1,2}拍","$1排")


    .REGEXREPLACE("(猫券叮当)", "猫卷叮当")
    .REGEXREPLACE("阿飞与巴弟","阿飞和巴弟")
    .REGEXREPLACE("瓜州牧","瓜洲牧")
    .REGEXREPLACE("八公粮品","八公良品")
    .REGEXREPLACE("🍑豆[王玩]国","淘豆玩国")
    .REGEXREPLACE("(翎先)", "领先") 
    .REGEXREPLACE("食物连","食物链")
    .REGEXREPLACE("美仕","美士")
    .REGEXREPLACE("美士唐纳滋","美仕唐纳滋")
    .REGEXREPLACE("城市理亨","城市理享")
    .REGEXREPLACE("lū宠","撸宠")
    .REGEXREPLACE("拜师","拜狮")
    .REGEXREPLACE("特喵得","特喵德")
    .REGEXREPLACE("铁锤","铁锤艾禾美")
    .REGEXREPLACE("绿10字","绿十字")
    .REGEXREPLACE("邵珂珂","邵可可")
    .REGEXREPLACE("3[Mm]5","3M5Pet")
    .REGEXREPLACE("(诚实一口[\s\S]*?[Pp](?:\d+))[Pp][Ll][Uu][Ss]","$1𝑷𝑳𝑼𝑺")


    .REGEXREPLACE("㊙️㊙️㊙️下方为干扰空格的处理逻辑，要小心单价+mp的组合，会被误判","")

    .REGEXREPLACE("(\d+(?:\.\d+)?)\s*("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&"|"&[🧠终极单位]&")?!p","$1$2")

    .REGEXREPLACE("㊙️㊙️㊙️下方为数字大小写的转换处理逻辑","")

    .REGEXREPLACE("(买|发|拍|共|合|赠|送|到手|加购|凑单?)(?:一)("&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&"|"&[🧠终极单位]&")?", "${1}1${2}")
    .REGEXREPLACE("(买|发|拍|共|合|赠|送|到手|加购|凑单?)(?:二|两)("&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&"|"&[🧠终极单位]&")?", "${1}2${2}")
    .REGEXREPLACE("(买|发|拍|共|合|赠|送|到手|加购|凑单?)(?:三)("&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&"|"&[🧠终极单位]&")?", "${1}3${2}")
    .REGEXREPLACE("(买|发|拍|共|合|赠|送|到手|加购|凑单?)(?:四)("&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&"|"&[🧠终极单位]&")?", "${1}4${2}")
    .REGEXREPLACE("(买|发|拍|共|合|赠|送|到手|加购|凑单?)(?:五)("&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&"|"&[🧠终极单位]&")?", "${1}5${2}")
    .REGEXREPLACE("(买|发|拍|共|合|赠|送|到手|加购|凑单?)(?:六)("&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&"|"&[🧠终极单位]&")?", "${1}6${2}")
    .REGEXREPLACE("(买|发|拍|共|合|赠|送|到手|加购|凑单?)(?:七)("&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&"|"&[🧠终极单位]&")?", "${1}7${2}")
    .REGEXREPLACE("(买|发|拍|共|合|赠|送|到手|加购|凑单?)(?:八)("&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&"|"&[🧠终极单位]&")?", "${1}8${2}")
    .REGEXREPLACE("(买|发|拍|共|合|赠|送|到手|加购|凑单?)(?:九)("&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&"|"&[🧠终极单位]&")?", "${1}9${2}")
    .REGEXREPLACE("(买|发|拍|共|合|赠|送|到手|加购|凑单?)(?:十)("&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&"|"&[🧠终极单位]&")?", "${1}10${2}")



    .REGEXREPLACE("(?:一|单)("&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&"|"&[🧠终极单位]&")", " 1${1}")
    .REGEXREPLACE("(?:二|两)("&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&"|"&[🧠终极单位]&")", " 2${1}")
    .REGEXREPLACE("(?:三)("&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&"|"&[🧠终极单位]&")", " 3${1}")
    .REGEXREPLACE("(?:四)("&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&"|"&[🧠终极单位]&")", " 4${1}")
    .REGEXREPLACE("(?:五)("&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&"|"&[🧠终极单位]&")", " 5${1}")
    .REGEXREPLACE("(?:六)("&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&"|"&[🧠终极单位]&")", " 6${1}")
    .REGEXREPLACE("(?:七)("&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&"|"&[🧠终极单位]&")", " 7${1}")
    .REGEXREPLACE("(?:八)("&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&"|"&[🧠终极单位]&")", " 8${1}")
    .REGEXREPLACE("(?:九)("&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&"|"&[🧠终极单位]&")", " 9${1}")
    .REGEXREPLACE("(?:十)("&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&"|"&[🧠终极单位]&")", " 10${1}")
  
  

    .REGEXREPLACE("1((?:"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&"|"&[🧠终极单位]&")拍\d+)","单$1")
     .REGEXREPLACE("1起","一起")
     .REGEXREPLACE("3文鱼","三文鱼")
     .REGEXREPLACE("3只松鼠","三只松鼠")
     .REGEXREPLACE("简简\s*1单","简简单单")


    .REGEXREPLACE("㊙️㊙️㊙️下方为价格相关信息的矫正逻辑","")
    
    .REGEXREPLACE("(?:[【(（])?(🎁|专享|下单链接|下单口令|点击|口令|秒杀价|秒杀)\s*(?:[:：】）)])?", "")
    .REGEXREPLACE("(?:[【(（])?(推荐理由)(?:[:：】）)])(.+)\s+","")

    .REGEXREPLACE("([,，。；;]?)(币\s*💰\d+(?:\.\d+)?)","$1金$2")
    .REGEXREPLACE("金+","金")

    .REGEXREPLACE("(?:[【(（])?(券面|日常)(价|额)?仅?(?:[:：】）)])?💰?(\d+(?:\.\d+)?)元?[,，；;。]?","")
    .REGEXREPLACE("[【\(（]?原价[:：】）\)]?💰?\d+(?:\.\d+)?[,，]?"，"")
    .REGEXREPLACE("\=\s*(💰\d+(?:\.\d+)?)[,，]?"，"。实际到手$1")
    .REGEXREPLACE("(可拍)(\d+次)"，"可下单$2")
    .REGEXREPLACE("拆(💰|\d+)","折$1")
    .REGEXREPLACE("(折)+","$1")
   
    .REGEXREPLACE("[【(（]?(券后|折扣)价?仅?[:：】）)]?", "💰")
    .REGEXREPLACE("([【(（])?(首单|凑单)价?仅?(\d+(?:\.\d+)?)([:：】）)])?", "$2💰$3")

    .REGEXREPLACE("(叠金币|88[Vv][Ii][Pp]|[Pp][Ll][Uu][Ss])(\d+(?:\.\d+)?)","$1💰$2")
    .REGEXREPLACE("(等于|折|换算成|凑单|到手)(\d+(?:\.\d+)?\/(?:\d+(?:\.\d+)?)?(?:"&[🧠单位合集]&"))","$1💰$2")

    .REGEXREPLACE("([拍]\d+(?:\.\d+)?(?:"&[🧠单位合集]&"))(\d+)","$1💰$2")



    .REGEXREPLACE("💰+", "💰")
   
    .REGEXREPLACE("领💰","领券后")
    .REGEXREPLACE("首单💰(凑单|plus|PLUS|Plus|88vip|88Vip|88VIP)","首单$1")
    .REGEXREPLACE("(等于|折|换算成|凑单|到手)💰(\d+(?:\.\d+)?(?:"&[🧠单位合集]&")(?:💰\d+))","$1$2")

    .REGEXREPLACE("((?:等于|折|换算成|凑单|到手)💰\d+(?:\.\d+)?)("&[🧠单位合集]&")","$1/$2")

    .REGEXREPLACE("(/(?:"&[🧠单位合集]&"))([,，；。;])", "$1$2"&CHAR(10))

    .REGEXREPLACE("(折)(?:\/|每|1)("&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&"|"&[🧠终极单位]&")(💰\d+(?:\.\d+)?)", "$1$3/$2")



    .REGEXREPLACE("㊙️㊙️㊙️下方为其他相关信息的矫正逻辑（商品名称、规格等）","")

    .REGEXREPLACE("(\d+(?:\.\d+)?)(cm|CM)?(\*)(\d+(?:\.\d+)?)(cm|CM)","$1$2＊$4$5")
    .REGEXREPLACE("(辅酶)(Q|q)10(片)","$1$2🔟$3")
    .REGEXREPLACE("(\d+(?:\.\d+)?)包邮","$1💗包邮")
    .REGEXREPLACE("麦当劳配色","麦当💗劳配色")
    .REGEXREPLACE("〰","◦")

    .REGEXREPLACE("🈷️🈷️🈷️🈷️🈷️🈷️🈷️🈷️🈷️🈷️🈷️","")


   .REGEXREPLACE(".*?(復zhi打开|整段|文案|𝙏𝙖𝙤𝘽𝙖𝙤 𝘼𝙋𝙋|自助查车|车群.*?cyg888|车群\r?\n\s*cyg888|dwz.cn|chongzhekou|猫车团|群聊).*", "")
   .REGEXREPLACE("[✅]","")
   .REGEXREPLACE("-+"&CHAR(10), "")

   .REGEXREPLACE("[,，+➕～\s]+$"&CHAR(10), "")
   .REGEXREPLACE("[,，+➕～\s]+$", "")
   .REGEXREPLACE("(?m)^\s*$\r?\n?", "")



=====
📁🩵待补领券模块
IF(
[📁🩵🖤商品信息组合类型（原始版）]
.REGEXMATCH("☀️"),

[📁🩵商品信息（清洗版）]
.REGEXREPLACE("((?:标题|主图)[旁上下]|详情页|直播间|入会).*?[领券]{1,2}.*","")
.REGEXEXTRACTALL("(.*?"&[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠优惠券-逻辑]!="").[🧠优惠券-逻辑]&".*)").ARRAYJOIN(CHAR(10)),"")

.REGEXREPLACE(",.*","")
.REGEXREPLACE("\[","")

=====
📁🩵是否需要补全领券信息
IF(
[📁🩵待补领券模块]
.ISBLANK()
.NOT()
,"🪣需补全领券地址"
,""
)
=====
📁🩵需补全优惠券类型
[📁🩵待补领券模块]
.REGEXEXTRACTALL(".*?("&[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠优惠券类型-逻辑]!="").[🧠优惠券类型-逻辑]&").*")
.REGEXREPLACE("^券","消费券")

.TRIM()
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")

=====
📁🩵需补全优惠券面值
IF(
[📁🩵是否需要补全领券信息]
.ISBLANK()
,"",
[📁🩵商品信息（清洗版）]
.REGEXREPLACE("((?:标题|主图)[旁上下]|详情页|直播间|入会).*?[领券]{1,2}.*","")
.REGEXEXTRACTALL([🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠优惠券-逻辑]!="").[🧠优惠券-逻辑].LISTCOMBINE().UNIQUE()).ARRAYJOIN(CHAR(10)))

.TRIM()
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
📁🩵领券信息补全
IF(
[📁🩵是否需要补全领券信息]
.ISBLANK()
,"",

IF(
[🎫  优惠券管理（底库）]
.FILTER(CurrentValue.[📁源信息发布时间(最终) -精确到天]=[📁源信息发布时间(最终) -精确到天]
&&CurrentValue.[📁所属平台]=[📁所属平台]
&&CurrentValue.[🎫🩵优惠券类型]=[📁🩵需补全优惠券类型]
&&CurrentValue.[🎫🩵优惠券面值]=([📁🩵需补全优惠券面值]))
.[📁🩵商品信息（清洗版）]
.ISBLANK()
,

[🎫  优惠券管理（底库）]
.FILTER(CurrentValue.[📁源信息发布时间(最终) -精确到天]=[📁源信息发布时间(最终) -精确到天]
&&CurrentValue.[📁所属平台]=[📁所属平台]
&&CurrentValue.[🎫🩵优惠券类型]=[📁🩵需补全优惠券类型]
&&CurrentValue.[🎫🩵优惠券面值].CONTAINTEXT([📁🩵需补全优惠券面值]))
.[📁🩵商品信息（清洗版）]
.LISTCOMBINE().RANDOMITEM()

,
[🎫  优惠券管理（底库）]
.FILTER(CurrentValue.[📁源信息发布时间(最终) -精确到天]=[📁源信息发布时间(最终) -精确到天]
&&CurrentValue.[📁所属平台]=[📁所属平台]
&&CurrentValue.[🎫🩵优惠券类型]=[📁🩵需补全优惠券类型]
&&CurrentValue.[🎫🩵优惠券面值]=([📁🩵需补全优惠券面值]))
.[📁🩵商品信息（清洗版）]
.LISTCOMBINE().RANDOMITEM()

))


=====
📁🩵领券信息补全-下单口令/链接
IF(
[📁🩵是否需要补全领券信息]
.ISBLANK()
,"",

IF(
[🎫  优惠券管理（底库）]
.FILTER(CurrentValue.[📁源信息发布时间(最终) -精确到天]=[📁源信息发布时间(最终) -精确到天]
&&CurrentValue.[📁所属平台]=[📁所属平台]
&&CurrentValue.[🎫🩵优惠券类型]=[📁🩵需补全优惠券类型]
&&CurrentValue.[🎫🩵优惠券面值]=([📁🩵需补全优惠券面值]))
.[📁🩵下单口令/链接提取]
.ISBLANK()
,

[🎫  优惠券管理（底库）]
.FILTER(CurrentValue.[📁源信息发布时间(最终) -精确到天]=[📁源信息发布时间(最终) -精确到天]
&&CurrentValue.[📁所属平台]=[📁所属平台]
&&CurrentValue.[🎫🩵优惠券类型]=[📁🩵需补全优惠券类型]
&&CurrentValue.[🎫🩵优惠券面值].CONTAINTEXT([📁🩵需补全优惠券面值]))
.[📁🩵下单口令/链接提取]
.LISTCOMBINE().RANDOMITEM()

,
[🎫  优惠券管理（底库）]
.FILTER(CurrentValue.[📁源信息发布时间(最终) -精确到天]=[📁源信息发布时间(最终) -精确到天]
&&CurrentValue.[📁所属平台]=[📁所属平台]
&&CurrentValue.[🎫🩵优惠券类型]=[📁🩵需补全优惠券类型]
&&CurrentValue.[🎫🩵优惠券面值]=([📁🩵需补全优惠券面值]))
.[📁🩵下单口令/链接提取]
.LISTCOMBINE().RANDOMITEM()

))


=====
📁💜商品信息（终版）
IF(
[📁🩵商品信息（清洗版）]
.REGEXMATCH("(\d+(?:\.\d+)?(?:"&[🧠基础单位]&"))装?\/(1(?:"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&"))")
,
[📁🩵商品信息（清洗版）]
.REGEXREPLACE("(\d+(?:\.\d+)?(?:"&[🧠基础单位]&"))装?\/(1(?:"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&"))","$1*$2")

,


IF(
[📁🩵商品信息（清洗版）]
.REGEXMATCH("(?:\d+(?:\.\d+)?(?:"&[🧠基础单位]&")\*)(\d+)("&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")装?\/(\d+(?:\.\d+)?)("&[🧠基础单位]&")")
,


[📁🩵商品信息（清洗版）]
.REGEXREPLACE("(?:\d+(?:\.\d+)?(?:"&[🧠基础单位]&")\*)(\d+)("&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")装?\/(\d+(?:\.\d+)?)("&[🧠基础单位]&")","$1$2$3")

，

IF(
[📁🩵商品信息（清洗版）]
.REGEXMATCH("(\d+)("&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")装?\/(\d+(?:\.\d+)?)("&[🧠基础单位]&")")
,
[📁🩵商品信息（清洗版）]
.REGEXREPLACE("(\d+)("&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")装?\/(\d+(?:\.\d+)?)("&[🧠基础单位]&")",

(
[📁🩵商品信息（清洗版）]
.REGEXEXTRACT("((?:\d+)(?:"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")装?\/(?:\d+(?:\.\d+)?)(?:"&[🧠基础单位]&"))")
.REGEXREPLACE("(\d+)("&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")装?\/(\d+(?:\.\d+)?)("&[🧠基础单位]&")","$3")
.VALUE()

/

[📁🩵商品信息（清洗版）]
.REGEXEXTRACT("((?:\d+)(?:"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")装?\/(?:\d+(?:\.\d+)?)(?:"&[🧠基础单位]&"))")
.REGEXREPLACE("(\d+)("&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")装?\/(\d+(?:\.\d+)?)("&[🧠基础单位]&")","$1")
.VALUE())
.ROUNDDOWN(2)

&"$4*$1$2")

,

[📁🩵商品信息（清洗版）])))

.REGEXREPLACE("(?m)^\s*$\r?\n?", "")

=====
📁🧡商品信息（补全版）

=====
📁🩵商品信息（清洗版分割版）
IF(
[🍬商品信息（原始版）]
.TRIM()
.ISBLANK()
，""，
[📁🩵商品信息（清洗版）])


.REGEXREPLACE("([Ll]ovely)\s?([Dd]oggy)","$1💗$2")
.REGEXREPLACE("([Nn]utri)\s?([Ss]ource)","$1💗$2")
.REGEXREPLACE("([Vv][Ee]tri)\s?([Ss][Cc]ience)","$1💗$2")
.REGEXREPLACE("[Mm]ecredy","$0💗")

.REGEXREPLACE("(.*?[a-zA-Z0-9\/ 《()]{11,99}.*)|(.*?[0-9a-zA-z]@[.0-9a-zA-z]{4,99}.*)|(https?://\S+)"，"$0🙂‍↕️🅾️")
.CONCATENATE("🙂‍↕️🅾️")

.REGEXREPLACE("\s*(🙂‍↕️🅾️)","$1")
.REGEXEXTRACTALL("[\s\S]*?🙂‍↕️🅾️")
.ARRAYJOIN(CHAR(10)&"〰〰〰")

.REGEXREPLACE("〰〰〰🙂‍↕️🅾️","")
.REGEXREPLACE("(〰〰〰\r?\n)\s*[–-]","$1")

.REGEXREPLACE("^🙂‍↕️🅾️","")

.REGEXREPLACE("🔄.*|💗","")


.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
📁💜商品信息（终版分割版）
IF(
[🍬商品信息（原始版）]
.TRIM()
.ISBLANK()
，""，
[📁💜商品信息（终版）])

.REGEXREPLACE("([Ll]ovely)\s?([Dd]oggy)","$1💗$2")
.REGEXREPLACE("([Nn]utri)\s?([Ss]ource)","$1💗$2")
.REGEXREPLACE("([Vv][Ee]tri)\s?([Ss][Cc]ience)","$1💗$2")
.REGEXREPLACE("[Mm]ecredy","$0💗")

.REGEXREPLACE("(.*?[a-zA-Z0-9\/ 《()]{11,99}.*)|(.*?[0-9a-zA-z]@[.0-9a-zA-z]{4,99}.*)|(https?://\S+)"，"$0🙂‍↕️🅾️")
.CONCATENATE("🙂‍↕️🅾️")

.REGEXREPLACE("\s*(🙂‍↕️🅾️)","$1")
.REGEXEXTRACTALL("[\s\S]*?🙂‍↕️🅾️")
.ARRAYJOIN(CHAR(10)&"〰〰〰")

.REGEXREPLACE("〰〰〰🙂‍↕️🅾️","")
.REGEXREPLACE("(〰〰〰\r?\n)\s*[–-]","$1")

.REGEXREPLACE("^🙂‍↕️🅾️","")

.REGEXREPLACE("🔄.*|💗","")

.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
📁🧡商品信息（补全版分割版）
IF(
[🍬商品信息（原始版）]
.TRIM()
.ISBLANK()
，""，
[📁🧡商品信息（补全版）])

.REGEXREPLACE("([Ll]ovely)\s?([Dd]oggy)","$1💗$2")
.REGEXREPLACE("([Nn]utri)\s?([Ss]ource)","$1💗$2")
.REGEXREPLACE("([Vv][Ee]tri)\s?([Ss][Cc]ience)","$1💗$2")
.REGEXREPLACE("[Mm]ecredy","$0💗")

.REGEXREPLACE("(.*?[a-zA-Z0-9\/ 《()]{11,99}.*)|(.*?[0-9a-zA-z]@[.0-9a-zA-z]{4,99}.*)|(https?://\S+)"，"$0🙂‍↕️🅾️")
.CONCATENATE("🙂‍↕️🅾️")

.REGEXREPLACE("\s*(🙂‍↕️🅾️)","$1")
.REGEXEXTRACTALL("[\s\S]*?🙂‍↕️🅾️")
.ARRAYJOIN(CHAR(10)&"〰〰〰")

.REGEXREPLACE("〰〰〰🙂‍↕️🅾️","")
.REGEXREPLACE("(〰〰〰\r?\n)\s*[–-]","$1")

.REGEXREPLACE("^🙂‍↕️🅾️","")

.REGEXREPLACE("🔄.*|💗","")

.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
📁🤍😀商品信息（纯净分割版） -商品名称&价格专享版
[📁💜商品信息（终版分割版）]
    .REGEXREPLACE("㊙️㊙️㊙️下方为字符过长以至于干扰 下单口令/链接 识别的 品牌信息干扰处理逻辑","")
    .REGEXREPLACE("([Ll]ovely)\s?([Dd]oggy)","$1💗$2")
    .REGEXREPLACE("([Nn]utri)\s?([Ss]ource)","$1💗$2")
    .REGEXREPLACE("([Vv][Ee]tri)\s?([Ss][Cc]ience)","$1💗$2")
    .REGEXREPLACE("[Mm]ecredy","$0💗")
    
    .REGEXREPLACE("㊙️㊙️㊙️下方为下单口令/链接 清除的处理逻辑","")
    .REGEXREPLACE("([拍凑加]\s*(?:\d+|[一-十二两])[:：]?)?"&[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠下单口令/链接-逻辑]!="").[🧠下单口令/链接-逻辑]&"\s*"，"$1"&CHAR(10)&"🔗")
    
    .REGEXREPLACE("㊙️㊙️㊙️下方为常见不可见字符的处理逻辑","")
    .REGEXREPLACE("[\u202A-\u202E]", "")
    .REGEXREPLACE("[\u2060-\u206F\uFE00-\uFE0E]", "")
    .REGEXREPLACE("[\u2028]", CHAR(10))
    .REGEXREPLACE("[0-9]+[Kk](高清|电视|纳米)", "📺")
    
    .REGEXREPLACE("㊙️㊙️㊙️下方为品牌文案标准化/信息补全 的处理逻辑","")
    .REGEXREPLACE("伯纳","伯纳天纯")
    .REGEXREPLACE("自然光","自然光环")
    .REGEXREPLACE("农场盛宴","爱肯拿$0")
    .REGEXREPLACE("白月光","爱肯拿$0")
    .REGEXREPLACE("海陆空","卫仕$0")
    .REGEXREPLACE("卡玛尼","英皇$0")
    .REGEXREPLACE("好命家","好命天生")
    .REGEXREPLACE("合宠","$0怡爪")
    .REGEXREPLACE("白一万","$0是只猫")
    .REGEXREPLACE("网易严选|网易天成","网易")
    .REGEXREPLACE("Nowfoods|nowfoods|诺奥","Now")
    .REGEXREPLACE("喔喔","WoWo")
    .REGEXREPLACE("lorde|LORDE|Lorde","里兜")
    .REGEXREPLACE("简沫A\+","简沫A＋")
    .REGEXREPLACE("1只喜欢","一只喜欢")
    .REGEXREPLACE("[Kk]9[Nn]atural","K9")
    .REGEXREPLACE("HG","Hound&Gatos")
    .REGEXREPLACE("名创","$0优品")
    .REGEXREPLACE("高爷","$0家")
    .REGEXREPLACE("阿飞","$0和巴弟")
    .REGEXREPLACE("[Ss][Cc]","星益生趣")
    .REGEXREPLACE("(海乐妙|海乐旺)","海正动保$0")
    .REGEXREPLACE("果饭儿","顽皮$0")
    .REGEXREPLACE("拜宠清","礼蓝动保$0")
    .REGEXREPLACE("绿十字","维倍思$0")
    .REGEXREPLACE("百灵金方","$0百灵金方")
    .REGEXREPLACE("小银枕","猫殿下$0")
    .REGEXREPLACE("月落桂影","奥丁$0")
    .REGEXREPLACE("小金袋","诚实一口$0") 
    .REGEXREPLACE("(巴氏)?小仙包M90","食物链$0")
    .REGEXREPLACE("红晶矿石","$0猫砂")
    .REGEXREPLACE("金素鸡?","solidgold$0")
    .REGEXREPLACE("游猎民族","伊萨$0")
    .REGEXREPLACE("伊萨","$0游猎民族")
    .REGEXREPLACE("OP(.*?罐)","奥鲑冠$0")
    
    .REGEXREPLACE("㊙️㊙️㊙️下方为比较短的英文品牌变体的处理逻辑","")
    .REGEXREPLACE("(GO|Go|go)","𝑮𝑶")
    .REGEXREPLACE("(VE|Ve|ve)","𝑽𝑬")
    .REGEXREPLACE("([Mm][Aa][Gg])","𝑴𝑨𝑮")
    .REGEXREPLACE("([Nn][Aa][Ss])","𝑵𝑨𝑺")
    .REGEXREPLACE("([Sa][Aa][Ee])","𝑺𝑨𝑬")
    .REGEXREPLACE("([Oo][Dd][Ee])","𝑶𝑫𝑬")
    .REGEXREPLACE("([Ii][Ss][Bb])","𝑰𝑺𝑩")
    .REGEXREPLACE("(N1|n1)","𝑵1")
    .REGEXREPLACE("([Kk]9)","𝑲9")
    .REGEXREPLACE("(\d+)[K]","$1𝑲")
    .REGEXREPLACE("(\d+)[k]","$1𝒌")
    .REGEXREPLACE("(𝑲|𝐤|𝒌)[Gg]","kg")
    .REGEXREPLACE("(百亿补贴)(𝑮𝑶|𝑮𝒐|𝒈𝒐)(进入)","$1GO$3")
    .REGEXREPLACE("(𝑴𝑨𝑮[Ii][Cc][Oo][Rr])","MAGICOR")
    .REGEXREPLACE("E𝑽𝑬rClean","EverClean")
    .REGEXREPLACE("ZE𝑽𝑬LO","ZEVELO")
    .REGEXREPLACE("[Ll]o𝑽𝑬ly[Dd]ogy","LovelyDoggy")
    .REGEXREPLACE("[Ff][Ii]𝑽𝑬","Five") 
    .REGEXREPLACE("[Ss]olid(𝑮𝑶)ld","solidgold")
    .REGEXREPLACE("(𝑮𝑶)lden","Golden")
    .REGEXREPLACE("𝑽𝑬triSc星益生趣ience","VetriScience")
    .REGEXREPLACE("(英皇|爱肯拿|妮可露|好命天生|卫仕|是只猫|怡爪|海正动保|顽皮|拜耳|礼蓝动保|里兜|维倍思|WoWo|solidgold|奥鲑冠|星益生趣|奥丁|Now|！|猫砂|优品|家|伊萨|游猎民族|诚实一口|食物链|和巴弟|贝里奥|猫殿下|天纯|环)+","$1")
    
    .REGEXREPLACE("㊙️㊙️㊙️下方为清除保质期的处理逻辑","")
    .REGEXREPLACE("有效期|保质期(?:[至|到])?(?:[^\d])?(?:\d{2,4}|今|明)?(?:年)?(?:\.)?\d{1,2}(?:-)?(?:\d{2,4}|今|明)?(?:年)?(?:\.)?(?:\d{1,2})?(?:月)?"，"📅")
    .REGEXREPLACE("(?:[^\d])?(?:\d{2,4}|今|明)?(?:年)?(?:\.)?\d{1,2}(?:月)?到期"，"📅")
    
    .REGEXREPLACE("㊙️㊙️㊙️下方均为干扰价格提取信息/格式的处理逻辑","")
    .REGEXREPLACE("(💰)\s*(\d+)","$1$2")
    .REGEXREPLACE("(返[现卡])\s?💰(\d+)","$1$2")
    .REGEXREPLACE("(.*?💰\d+(?:\.\d+)?[,。；;。]?)\s*([赠送])","$1"&CHAR(10)&"$2")
    .REGEXREPLACE("(.*?(?:定金|尾款)💰?\d+(?:\.\d+)?)\s*\r?\n(.*?💰.*)","$1，$2")
    .REGEXREPLACE("部分人💰?\d+(?:\.\d+)?","")
    .REGEXREPLACE("(?:[【(（])?(?:日常)[价💰]?\d+(?:\.\d+)?(?:[，,:：】）)])?", "")
    .REGEXREPLACE("([,，+。])+","$1")
    
    .REGEXREPLACE("㊙️㊙️㊙️将拍凑、充值和下方的相关价格整合到同一行","")
    .REGEXREPLACE("(.*?(?:拍).*?\d+.*)\r?\n\s*(💰.*)","$1$2")
    .REGEXREPLACE("(.*?(?:\d+折))\s*\r?\n\s*(💰.*)","$1$2")
    .REGEXREPLACE("(.*?拍.*?\d+.*)\r?\n\s*(.*?💰.*)","$1，$2")
    
    .REGEXREPLACE("㊙️㊙️㊙️最好不要出现“送”相关的判断，容易把赠送相关的信息和其他关键行合并到一行，后续不好分开","")
    .REGEXREPLACE("((?:🔗)?.*?💰.*?)\r?\n\s*(.*?(?:凑单|首单|[淘叠]金币|会员|88[Vv][ii][Pp]|[Pp][Ll][Uu][Ss]|晒图|晒单|入会|会员|[没有]{0,2}学生号|价值|部分(?:人|金币|礼金)?|[新老]客(?:回购)?|尾款|[定订]金|消费券|福袋|.*?[好评返现卡]{1,4}|=|贵|少|多|便宜|立减).*?💰.*)","$1，$2")
    .REGEXREPLACE("(〰〰〰)\s*，","$1"&CHAR(10))
    
    .REGEXREPLACE("㊙️㊙️㊙️下方为折算价格的信息清理(循环3次)","")
    .REGEXREPLACE("(.*?💰.*?)[,，；：。]?"&[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠下单口令/链接-逻辑]!="").[🧠折算价格-逻辑], "$1"&CHAR(10))
    .REGEXREPLACE("(.*?💰.*?)[,，；：。]?"&[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠下单口令/链接-逻辑]!="").[🧠折算价格-逻辑], "$1"&CHAR(10))
    .REGEXREPLACE("(.*?💰.*?)[,，；：。]?"&[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠下单口令/链接-逻辑]!="").[🧠折算价格-逻辑], "$1"&CHAR(10))
    .REGEXREPLACE("(算上赠品|排除赠品)","")
    
    .REGEXREPLACE("㊙️㊙️㊙️因为折算的逻辑中有多个并行的捕获逻辑，在有多个折算文本的商品中，是很容易在第一次循环的时候就尽可能捕获信息了，导致折算信息被拆开从而后续的循环，捕获逻辑无法生效，因此会在这里定向处理一些文本的捕获。但是一定要注意不要把共、累计、到手XX规格这种信息清除，这是拍凑维度的有效信息","")
    .REGEXREPLACE("[,，；：。]?(?:[折换]算成?|折|相当于|约等于|约)\s*\d+(?:\.\d+)?(?:"&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠终极单位]&")",CHAR(10))
    
    .REGEXREPLACE("㊙️㊙️㊙️下方均为干扰赠送信息提取的的 格式/文本 处理","")
    .REGEXREPLACE("("&[🧠加号单位]&"|[赠送])\s*\r?\n(.*)","$1，$2")
    .REGEXREPLACE("((?:"&[🧠加号单位]&"|[赠送]|赠品价值).*)\r?\n(.*?(?:"&[🧠加号单位]&"|试[吃用]|[赠送]|赠品价值).*)","$1，$2")
    .REGEXREPLACE("([赠送].*)\r?\n\s*((?:"&[🧠加号单位]&"|[赠送]).*)+","$1$2")
    
    .REGEXREPLACE("㊙️㊙️㊙️下方的逻辑是特意准备两次，因此存在赠送的信息放在3行的情况","")
    .REGEXREPLACE("([赠送].*)(\r?\n\s*(.*?\d+"&[🧠单位合集]&".*))","$1，$3")
    .REGEXREPLACE("([赠送].*)(\r?\n\s*(.*?\d+"&[🧠单位合集]&".*))","$1，$3")
    
    .REGEXREPLACE("㊙️㊙️㊙️下方为干扰商品标题提取的  信息/格式的 清除逻辑,主要是上一个逻辑很容易让赠送信息和商品标题合并到一行来，对后续的商品标题提取造成干扰。这一定要注意开头就得是品牌信息，否则也会存在将赠送的品牌赠品当作标题捕获了","")
    .REGEXREPLACE("[,；，;。❗]+((?:"&[🧠品牌-逻辑]&").*?(?:\d+"&[🧠单位合集]&".*))",CHAR(10)&"$1")
    
    .REGEXREPLACE("㊙️㊙️㊙️下方均为干扰品牌相关信息清除的逻辑","")
    .REGEXREPLACE("[,，；;]?(对标)"&[🧠品牌-逻辑]&"[,，；;。～！!]?","")
    .REGEXREPLACE("[,，；;]?(配方比"&[🧠品牌-逻辑]&"还好)[,，；;。～！!]?","")
    .REGEXREPLACE("[,，；;]?"&[🧠品牌-逻辑]&"(耳肤灵)?(同厂|平替|同款|同品质|同配方)[,，；;。～！!]?","")
    .REGEXREPLACE("[,，；;]?(美国|英国|法国|德国|日本|意大利|加拿大|芬兰|新西兰|瑞典|新加坡|台湾|西班牙|荷兰|韩国|菲律宾|挪威)"&[🧠品牌-逻辑]&"[,，；;。～！!]?","$2")
    
    .REGEXREPLACE("㊙️㊙️㊙️下方为烦扰付款类型信息的清理逻辑","")
    .REGEXREPLACE("口味任选","")
    
    .REGEXREPLACE("㊙️㊙️㊙️下方均为高频出现且场景模糊词汇的处理逻辑","")
    .REGEXREPLACE("营养膏","💗$0")
    .REGEXREPLACE("(京)(东)(支付|购物)?","$1💗$2$3")
    .REGEXREPLACE("(淘)(宝)(支付|购物)?","$1💗$2$3")
    .REGEXREPLACE("(抖)(音)(支付|购物)?","$1💗$2$3")
    .REGEXREPLACE("(美)(团)(支付|购物)?","$1💗$2$3")
    .REGEXREPLACE("(天)(猫)(支付|购物)?","$1💗$2$3")
    .REGEXREPLACE("(猫)(超)","$1💗$2$3")
    .REGEXREPLACE("(羊)(毛毡)","$1💗$2")
    .REGEXREPLACE("(自有|直营|自营|海外)|(淘?工厂|官方旗舰店|天猫国际(?:超市)?|猫超|京东|美团|顺丰|天猫超市|官旗|旗舰店|品牌)|(发货|送货上门|配送到家|直发|当日达|次日达)"，"")
    .REGEXREPLACE([🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠下单口令/链接-逻辑]!="").[🧠价格优势-逻辑],"")
    
    .REGEXREPLACE("㊙️㊙️㊙️㊙️","很多空格其实都是+号。但不绝对，所以这里给一个定位符")
    .REGEXREPLACE(" ","º")
    
    .REGEXREPLACE("㊙️㊙️㊙️下方均为多余 定位符/干扰符 的处理逻辑","")
    .REGEXREPLACE("[🎫🎁📺🚫❗✨❌📦💗📅]","")
    .REGEXREPLACE("🔗",CHAR(10)&"$0🙂‍↕️🅾️")
    .REGEXREPLACE("🙂‍↕️🅾️\s*🙂‍↕️🅾️","🙂‍↕️🅾️")
    .REGEXREPLACE("🔗🙂‍↕️🅾️\s*"&CHAR(10)&"🔗🙂‍↕️🅾️\s*","🔗🙂‍↕️🅾️")
    .REGEXREPLACE("(?m)^\s*$\r?\n?", "")
    .REGEXREPLACE("([Dd]ogy)","Doggy")
    
    .REGEXREPLACE("㊙️㊙️㊙️下方均为常见不可见字符的处理逻辑","")
    .REGEXREPLACE("[\u202A-\u202E]", "")
    .REGEXREPLACE("[\u2060-\u206F\uFE00-\uFE0E]", "")
    .REGEXREPLACE("[\u2028]", CHAR(10))
    
    .REGEXREPLACE("㊙️㊙️㊙️下方均为emoji处理逻辑","")
    .REGEXREPLACE("\[[Pp]\d+\]","")
    
    .REGEXREPLACE("㊙️㊙️㊙️定位符分行逻辑加强","")
    .REGEXREPLACE("[,，]?\s*(🙂‍↕️🅾️)",CHAR(10)&"$1")
    .REGEXREPLACE("[,，]?\s*(🔗)\r?\n\s*(🙂‍↕️🅾️)",CHAR(10)&"$1$2")
    .REGEXREPLACE("(〰)+",CHAR(10)&"$0")
    .REGEXREPLACE("(([,，。+➕～])\s?)+", "$2")
    .REGEXREPLACE("[,，。+➕～]\s+$", "")
    .REGEXREPLACE("(?m)^\s*$\r?\n?", "")
    =====
📁🤍商品信息（纯净分割版）
[📁🤍😀商品信息（纯净分割版） -商品名称&价格专享版]
.REGEXREPLACE("(?:[【(（])?(?:日常)[价💰]?\d+(?:\.\d+)?(?:[，,:：】）)])?", "")
.REGEXREPLACE("(\d+(?:\.\d+)?)[Mm][Mm]","$1𝑚𝑚")

.REGEXREPLACE("㊙️㊙️处理规格作为衡量体重的情况","")
.REGEXREPLACE("\d+(?:\.\d+)?("&[🧠基础单位]&")?-\d+(?:\.\d+)?"&[🧠基础单位],"")
.REGEXREPLACE("\d+(?:\.\d+)?"&[🧠基础单位]&"内","")
.REGEXREPLACE("(驱虫)\d+"&[🧠基础单位],"$1")

.REGEXREPLACE("㊙️㊙️赠送相关信息清除，不能直接使用赠送的逻辑，因为那是贪婪捕获","")
.REGEXREPLACE("("&[🧠加号单位]&"试[吃用]|[赠送]|赠品价值).*\r?\n.*?("&[🧠加号单位]&"|试[吃用]|[赠送]|赠品价值|\d+(?:\.\d+)?(?:"&[🧠单位合集]&")).*","$1$2")
.REGEXREPLACE("("&[🧠品牌-逻辑]&".*?(?:\.\d+)?(?:"&[🧠单位合集]&").*)(\r?\n.*?(?:试[吃用]|[赠送]|赠品价值).*)(?:\r?\n.*?(?:"&[🧠加号单位]&"|试[吃用]|[赠送]|赠品价值).*)?","$1")
.REGEXREPLACE("("&[🧠加号单位]&"试[吃用]|[赠送])\s*\r?\n(.*)","")
.REGEXREPLACE("("&[🧠加号单位]&"试[吃用]|[赠送]|赠品价值).*","")

.REGEXREPLACE("㊙️㊙️下方为处理干扰【规格】提取信息的清理和格式调整","")
.REGEXREPLACE("单月装","1粒装")
.REGEXREPLACE("季度装","3粒装")
.REGEXREPLACE("(贵|减|少|优惠|便宜)(\d+(?:\.\d+)?)块钱?","$1$2")

.REGEXREPLACE("㊙️㊙️情况1：单位成一行的价格,优先合并到下一行中的拍凑规格信息中去","")
.REGEXREPLACE(CHAR(10)&"(💰\d+(?:\.\d+)?\s*)"&CHAR(10)&"(.*?(?:\d+(?:"&[🧠单位合集]&")选项|选项拍\d+(?:"&[🧠单位合集]&")?).*)",CHAR(10)&"$1，$2")

.REGEXREPLACE("㊙️㊙️情况2：单位成一行的价格，不满足上面的情况的时候，合并到上一行中的规格信息的后面","")
.REGEXREPLACE("(\d+(?:\.\d+)?"&[🧠单位合集]&")"&CHAR(10)&"(💰\d+(?:\.\d+)?\s*)"&CHAR(10),"$1$2"&CHAR(10))

.REGEXREPLACE("㊙️㊙️情况3：同一颗粒度、但是数量不同的规格分布在不同行时，合并到一行","")
.REGEXREPLACE("(\d+(?:\.\d+)?"&[🧠基础单位]&")"&CHAR(10)&"(💰\d+(?:\.\d+)?.*?(?:拍?\d+"&[🧠基础单位]&".*?💰.*?))",CHAR(10)&"$1$2")
.REGEXREPLACE("(\d+(?:\.\d+)?"&[🧠初级单位]&")"&CHAR(10)&"(💰\d+(?:\.\d+)?.*?(?:拍?\d+"&[🧠初级单位]&".*?💰.*?))",CHAR(10)&"$1$2")
.REGEXREPLACE("(\d+(?:\.\d+)?"&[🧠进阶单位]&")"&CHAR(10)&"(💰\d+(?:\.\d+)?.*?(?:拍?\d+"&[🧠进阶单位]&".*?💰.*?))",CHAR(10)&"$1$2")
.REGEXREPLACE("(\d+(?:\.\d+)?"&[🧠高阶单位]&")"&CHAR(10)&"(💰\d+(?:\.\d+)?.*?(?:拍?\d+"&[🧠高阶单位]&".*?💰.*?))",CHAR(10)&"$1$2")
.REGEXREPLACE("(\d+(?:\.\d+)?"&[🧠终极单位]&")"&CHAR(10)&"(💰\d+(?:\.\d+)?.*?(?:拍?\d+"&[🧠终极单位]&".*?💰.*?))",CHAR(10)&"$1$2")

.REGEXREPLACE("㊙️㊙️情况4：一行文本中带了“规格+💰”，下一行是拍凑信息，且颗粒度大于当前。那么当前的💰相关的信息和下方的文本合并","")
.REGEXREPLACE("(\d+(?:\.\d+)?(?:"&[🧠基础单位]&"))(💰.*"&CHAR(10)&"拍\d+(?:"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&"|"&[🧠终极单位]&")?.*?💰.*)","$1"&CHAR(10)&"$2")
.REGEXREPLACE("(\d+(?:\.\d+)?(?:"&[🧠初级单位]&"))(💰.*"&CHAR(10)&"拍\d+(?:"&[🧠进阶单位]&"|"&[🧠高阶单位]&"|"&[🧠终极单位]&")?.*?💰.*)","$1"&CHAR(10)&"$2")
.REGEXREPLACE("(\d+(?:\.\d+)?(?:"&[🧠进阶单位]&"))(💰.*"&CHAR(10)&"拍\d+(?:"&[🧠高阶单位]&"|"&[🧠终极单位]&")?.*?💰.*)","$1"&CHAR(10)&"$2")
.REGEXREPLACE("(\d+(?:\.\d+)?(?:"&[🧠高阶单位]&"))(💰.*"&CHAR(10)&"拍\d+(?:"&[🧠终极单位]&")?.*?💰.*)","$1"&CHAR(10)&"$2")

.REGEXREPLACE("㊙️㊙️情况5：单独成行的拍**规格 和上一行只带规格且规格维度更低的文本合并在一行","")
.REGEXREPLACE("(\d+(?:\.\d+)?(?:"&[🧠基础单位]&"))"&CHAR(10)&"(拍\d+(?:"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&"|"&[🧠终极单位]&").*?💰.*)","$1$2")
.REGEXREPLACE("(\d+(?:\.\d+)?(?:"&[🧠基础单位]&"|"&[🧠初级单位]&"))"&CHAR(10)&"(拍\d+(?:"&[🧠进阶单位]&"|"&[🧠高阶单位]&"|"&[🧠终极单位]&").*?💰.*)","$1$2")
.REGEXREPLACE("(\d+(?:\.\d+)?(?:"&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"))"&CHAR(10)&"(拍\d+(?:"&[🧠高阶单位]&"|"&[🧠终极单位]&").*?💰.*)","$1$2")
.REGEXREPLACE("(\d+(?:\.\d+)?(?:"&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&"))"&CHAR(10)&"(拍\d+(?:"&[🧠终极单位]&").*?💰.*)","$1$2")

.REGEXREPLACE("㊙️㊙️情况5：单独成行的拍**规格（初级及以上单位） 和上一行带*规格 的信息合并在一起","")
.REGEXREPLACE("(\*\d+(?:\.\d+)?)"&CHAR(10)&"(拍\d+(?:"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&"|"&[🧠终极单位]&")?.*?💰.*)","$1$2")

.REGEXREPLACE("㊙️㊙️情况6：被省略掉的数量为1 规格 进行补全","")
.REGEXREPLACE(CHAR(10)&"(💰\d+(?:\.\d+)?).*?(拍?\d+)("&[🧠单位合集]&")(选项)?(💰.*?)",CHAR(10)&"1$3$1，$2$3$4$5")
.REGEXREPLACE(CHAR(10)&"(💰\d+(?:\.\d+)?).*"&CHAR(10)&"(拍?\d+)("&[🧠单位合集]&")(?:选项)?(💰.*?)",CHAR(10)&"1$3$1，$2$3$4")

.REGEXREPLACE("㊙️㊙️规格干扰信息处理","")
.REGEXREPLACE("\d+(?:\.\d+)?("&[🧠单位合集]&")[抵顶]\d+(?:\.\d+)?("&[🧠单位合集]&")"，"")
.REGEXREPLACE("(\d+袋\s*\d+只半乳鸽)","")
.REGEXREPLACE("(\d+块肉饼\s*\d+种鲜肉)","")
.REGEXREPLACE("拍?(第|最后)\s*\d+个?选项","")
.REGEXREPLACE("\d+(?:\.\d+)?个凑单","")
.REGEXREPLACE("\d+个?选项","")
.REGEXREPLACE("((?:买|拍|加购|加车|加购物车|凑|凑单|任选)\d+("&[🧠单位合集]&")?)(到手\d+("&[🧠单位合集]&"))?","$1")
.REGEXREPLACE("(相当于)\d+(?:\.\d+)?("&[🧠单位合集]&")(仅需|到手|仅仅)💰?\d+(?:\.\d+)?","")
.REGEXREPLACE("[,，。]?(猫粮|罐头|狗粮|餐包|犬粮|零食|罐罐|猫砂|冻干|猫条)?(到手💰?)?(共|累积|累计)\d+(?:\.\d+)?("&[🧠单位合集]&")?", "")
.REGEXREPLACE("(到手💰?)(共|累积|累计)?\d+(?:\.\d+)?("&[🧠单位合集]&")","")
.REGEXREPLACE("(💰\d+(?:\.\d+)?[,，；;～]?)"&CHAR(10)&"(凑单|88[Vv][Ii][Pp]|[Pp][Ll][Uu][Ss]|凑单|(?:拍|买)\d+(?:\.\d+)?(?:"&[🧠单位合集]&")|部分人|淘?金币|.*?[好评返现卡送]{1,4})","$1，$2")
.REGEXREPLACE("[,，。]?(猫粮|罐头|狗粮|餐包|犬粮|零食|罐罐|猫砂)?(价值💰?\d+).*", "")
.REGEXREPLACE("[,，。]?(回购|拍下|买|下单).*?\d+("&[🧠单位合集]&").*?免单","")

.REGEXREPLACE("㊙️㊙️干扰信息处理-推荐理由清除","")
.REGEXREPLACE(
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[📁编号（最终）]).[🔪💛推荐理由（合并版）]
.REGEXREPLACE("✨.*✨","")
.REGEXREPLACE("[+*]","\$0")
.REGEXEXTRACTALL("(.*?‼️)|(.*?✔️)").NTH(1)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")
.REGEXREPLACE(
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[📁编号（最终）]).[🔪💛推荐理由（合并版）]
.REGEXREPLACE("✨.*✨","")
.REGEXREPLACE("[+*]","\$0")
.REGEXEXTRACTALL("(.*?‼️)|(.*?✔️)").NTH(2)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")
.REGEXREPLACE(
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[📁编号（最终）]).[🔪💛推荐理由（合并版）]
.REGEXREPLACE("✨.*✨","")
.REGEXREPLACE("[+*]","\$0")
.REGEXEXTRACTALL("(.*?‼️)|(.*?✔️)").NTH(3)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")
.REGEXREPLACE(
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[📁编号（最终）]).[🔪💛推荐理由（合并版）]
.REGEXREPLACE("✨.*✨","")
.REGEXREPLACE("[+*]","\$0")
.REGEXEXTRACTALL("(.*?‼️)|(.*?✔️)").NTH(4)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")
.REGEXREPLACE(
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[📁编号（最终）]).[🔪💛推荐理由（合并版）]
.REGEXREPLACE("✨.*✨","")
.REGEXREPLACE("[+*]","\$0")
.REGEXEXTRACTALL("(.*?‼️)|(.*?✔️)").NTH(5)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")
.REGEXREPLACE(
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[📁编号（最终）]).[🔪💛推荐理由（合并版）]
.REGEXREPLACE("✨.*✨","")
.REGEXREPLACE("[+*]","\$0")
.REGEXEXTRACTALL("(.*?‼️)|(.*?✔️)").NTH(6)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")

.REGEXREPLACE("㊙️㊙️㊙️㊙️㊙️","无实际意义，为了看信息更方便")
.REGEXREPLACE("[🪶🎁📦]","")
.REGEXREPLACE("🔗","$0🙂‍↕️🅾️")
.REGEXREPLACE("🙂‍↕️🅾️🙂‍↕️🅾️","🙂‍↕️🅾️")
.REGEXREPLACE("^🙂‍↕️🅾️","")
.REGEXREPLACE("[,，+➕～]\s?+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
📁💜sku分割1️⃣
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(1)
.REGEXREPLACE("〰〰〰","")

.REGEXREPLACE("[,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
📁💜sku分割2️⃣
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(2)
.REGEXREPLACE("〰〰〰","")

.REGEXREPLACE("[,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
📁💜sku分割3️⃣
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(3)
.REGEXREPLACE("〰〰〰","")

.REGEXREPLACE("[,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
📁💜sku分割4️⃣
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(4)
.REGEXREPLACE("〰〰〰","")

.REGEXREPLACE("[,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
📁💜sku分割5️⃣
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(5)
.REGEXREPLACE("〰〰〰","")

.REGEXREPLACE("[,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
📁💜sku分割6️⃣
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(6)
.REGEXREPLACE("〰〰〰","")

.REGEXREPLACE("[,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
📁💜sku分割7️⃣
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(7)
.REGEXREPLACE("〰〰〰","")

.REGEXREPLACE("[,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
📁💜sku分割8️⃣
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(8)
.REGEXREPLACE("〰〰〰","")

.REGEXREPLACE("[,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
📁💜sku分割9️⃣
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(9)
.REGEXREPLACE("〰〰〰","")

.REGEXREPLACE("[,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
📁⚫️SKU｜所属平台-品牌-2级品类-规格-口味-制作工艺-使用对象-成长期-到手价格
IF(ISBLANK([🍬商品信息（原始版）]),"",
CONCATENATE([📁所属平台],"-",
[Ⓜ️🤍‼️品牌（标准格式）],"-",
[🛍️🤍二级品类（唯一值）],"-",
[🧪🤍规格✖️数量✖️起拍数量（终版）],"-",
[🛍️💛口味（唯一值）],"-",
[🛍️💜制作工艺（唯一值）],"-",
[🛍️💜使用对象(唯一值)],"-",
[🛍️💜适用宠物成长期（唯一值）],"-💰",
[💰到手价格（终版）]))

.REGEXREPLACE("\-+","-")
.REGEXREPLACE("^-","")
.LOWER()

.REGEXREPLACE("[,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
📁🟠SKU｜所属平台-品牌-2级品类-规格-口味-使用对象-成长期
IF(ISBLANK([🍬商品信息（原始版）]),"",CONCATENATE([📁所属平台]，"-"，[Ⓜ️🤍‼️品牌（标准格式）],"-",[🛍️🤍二级品类（唯一值）],"-",[🧪🤍规格✖️数量✖️起拍数量（终版）],"-"，[🛍️💛口味（唯一值）],"-",[🛍️💜使用对象(唯一值)]，"-"，[🛍️💜适用宠物成长期（唯一值）]))
.REGEXREPLACE("\-+","-")
.REGEXREPLACE("^-","")

.LOWER()

.REGEXREPLACE("[,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
📁⚫️SKU是否重复
IF(
ISBLANK([🍬商品信息（原始版）]),
"",
IF(
[📁 ｜✍️信息获取(基建）].COUNTIF(
((CurrentValue.[📁源信息发布时间(最终) -精确到天] < [📁源信息发布时间(最终) -精确到天])  
+
(CurrentValue.[📁编号（最终）].REGEXREPLACE("👀","").VALUE()<[📁编号（最终）].REGEXREPLACE("👀","").VALUE()))

*
(CurrentValue.[📁⚫️SKU｜所属平台-品牌-2级品类-规格-口味-制作工艺-使用对象-成长期-到手价格] = [📁⚫️SKU｜所属平台-品牌-2级品类-规格-口味-制作工艺-使用对象-成长期-到手价格])
*
([📁源信息发布时间(最终) -精确到天]-CurrentValue.[📁源信息发布时间(最终) -精确到天] <= 3) 

) > 0,
"🚫重复信息",
"🆕新信息"
    )
)

=====
📁选品定位符
CONCATENATE([📁⚫️SKU是否重复]，"-"，[📁🩵信息类型（唯一值）]，"【"，[🛍️💜使用对象(唯一值)]，"】")
.REGEXREPLACE("【】","")
=====
📁看新信息数量（专用字段）
IF(
[📁⚫️SKU是否重复]
.REGEXMATCH("🆕"),"1"，"")
=====
📁看新信息比例（专用字段）
IF(
[📁⚫️SKU是否重复]
.REGEXMATCH("🆕"),"1"，"")
=====
📁🩵下单口令/链接提取（原始）
[📁🩵商品信息（清洗版）]
.REGEXEXTRACTALL([🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠下单口令/链接-逻辑]!="").[🧠下单口令/链接-逻辑])
.ARRAYJOIN(CHAR(10))
.REGEXREPLACE([🧠品牌-逻辑],"")

.REGEXREPLACE("[,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")

=====
📁🩵链接/口令数量（原始版）
IF(ISBLANK([📁🩵商品信息（清洗版）])
, "",
[📁🩵下单口令/链接提取（原始）]
.SPLIT(CHAR(10))
.COUNTA()
)
=====
📁🩵🖤商品信息组合类型（原始版）
IF(
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[📁编号（最终）]).[📁🩵信息类型（唯一值）]
.REGEXMATCH("🛒").NOT()

，""，

IF(
AND(
[📁🩵链接/口令数量（原始版）]=1
,
[🔪🖤SKU数量]=1)

，"☀️单个商品（无下单步骤）"，

IF(
AND(
[📁🩵链接/口令数量（原始版）]>1
,
[🔪🖤SKU数量]=1)

，"🌪️单个商品（有下单步骤）",

IF(
AND(
[📁🩵链接/口令数量（原始版）]>1
,
[🔪🖤SKU数量]>1
,
[📁🩵链接/口令数量（原始版）]=[🔪🖤SKU数量]
)

，"🌤️多个商品（无下单步骤）"

，"💧多个商品（有下单步骤）"
))))
=====
📁🧡下单口令/链接提取
[📁🧡商品信息（补全版）]
.REGEXEXTRACTALL([🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠下单口令/链接-逻辑]!="").[🧠下单口令/链接-逻辑])
.ARRAYJOIN(CHAR(10))
.REGEXREPLACE([🧠品牌-逻辑],"")

.REGEXREPLACE("[,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
📁🧡链接/口令数量（终版）
IF(ISBLANK([📁🧡商品信息（补全版）])
,"",
[📁🧡下单口令/链接提取]
.SPLIT(CHAR(10))
.COUNTA()
)
=====
📁🧡🖤商品信息组合类型（终版）
IF(
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[📁编号（最终）]).[📁🩵信息类型（唯一值）]
.REGEXMATCH("🛒").NOT()

,"",

IF(
AND(
[📁🧡链接/口令数量（终版）]=1
,
[🔪🖤SKU数量]=1)

,"☀️单个商品（无下单步骤）",

IF(
AND(
[📁🧡链接/口令数量（终版）]>1
,
[🔪🖤SKU数量]=1)

,"🌪️单个商品（有下单步骤）",

IF(
AND(
[📁🧡链接/口令数量（终版）]>1
,
[🔪🖤SKU数量]>1
,
[📁🧡链接/口令数量（终版）]=[🔪🖤SKU数量]
)

,"🌤️多个商品（无下单步骤）"

,"💧多个商品（有下单步骤）"
))))
=====
📁💜SKU组合
CONCATENATE(
"❶",[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(1),"❶🔞"

,CHAR(10),

"❷",[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(2),"❷🔞"

,CHAR(10),

"❸",[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(3),"❸🔞"

,CHAR(10),

"❹",[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(4),"❹🔞"

,CHAR(10),

"❺",[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(5),"❺🔞"

,CHAR(10),

"❻",[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(6),"❻🔞"

,CHAR(10),

"❼",[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(7),"❼🔞"

,CHAR(10),

"❽",[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(8),"❽🔞"

,CHAR(10),

"❾",[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(9),"❾🔞"
)

.REGEXREPLACE("[❶-❾]\s*[❶-❾]🔞","")
.REGEXREPLACE("([❶-❾])"&CHAR(10)&"(〰+"&CHAR(10)&")","$2$1")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
📁🧡SKU组合
CONCATENATE(
"❶",[📁🧡商品信息（补全版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(1),"❶🔞"

,CHAR(10),

"❷",[📁🧡商品信息（补全版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(2),"❷🔞"

,CHAR(10),

"❸",[📁🧡商品信息（补全版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(3),"❸🔞"

,CHAR(10),

"❹",[📁🧡商品信息（补全版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(4),"❹🔞"

,CHAR(10),

"❺",[📁🧡商品信息（补全版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(5),"❺🔞"

,CHAR(10),

"❻",[📁🧡商品信息（补全版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(6),"❻🔞"

,CHAR(10),

"❼",[📁🧡商品信息（补全版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(7),"❼🔞"

,CHAR(10),

"❽",[📁🧡商品信息（补全版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(8),"❽🔞"

,CHAR(10),

"❾",[📁🧡商品信息（补全版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(9),"❾🔞"
)

.REGEXREPLACE("[❶-❾]\s*[❶-❾]🔞","")
.REGEXREPLACE("([❶-❾])"&CHAR(10)&"(〰+"&CHAR(10)&")","$2$1")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
📁🤍😀SKU组合
CONCATENATE(
"❶",[📁🤍😀商品信息（纯净分割版） -商品名称&价格专享版]
.SPLIT("🙂‍↕️🅾️")
.NTH(1),"❶🔞"

,CHAR(10),

"❷",[📁🤍😀商品信息（纯净分割版） -商品名称&价格专享版]
.SPLIT("🙂‍↕️🅾️")
.NTH(2),"❷🔞"

,CHAR(10),

"❸",[📁🤍😀商品信息（纯净分割版） -商品名称&价格专享版]
.SPLIT("🙂‍↕️🅾️")
.NTH(3),"❸🔞"

,CHAR(10),

"❹",[📁🤍😀商品信息（纯净分割版） -商品名称&价格专享版]
.SPLIT("🙂‍↕️🅾️")
.NTH(4),"❹🔞"

,CHAR(10),

"❺",[📁🤍😀商品信息（纯净分割版） -商品名称&价格专享版]
.SPLIT("🙂‍↕️🅾️")
.NTH(5),"❺🔞"

,CHAR(10),

"❻",[📁🤍😀商品信息（纯净分割版） -商品名称&价格专享版]
.SPLIT("🙂‍↕️🅾️")
.NTH(6),"❻🔞"

,CHAR(10),

"❼",[📁🤍😀商品信息（纯净分割版） -商品名称&价格专享版]
.SPLIT("🙂‍↕️🅾️")
.NTH(7),"❼🔞"

,CHAR(10),

"❽",[📁🤍😀商品信息（纯净分割版） -商品名称&价格专享版]
.SPLIT("🙂‍↕️🅾️")
.NTH(8),"❽🔞"

,CHAR(10),

"❾",[📁🤍😀商品信息（纯净分割版） -商品名称&价格专享版]
.SPLIT("🙂‍↕️🅾️")
.NTH(9),"❾🔞"
)


.REGEXREPLACE("[❶-❾]\s*[❶-❾]🔞","")
.REGEXREPLACE("([❶-❾])"&CHAR(10)&"(〰+"&CHAR(10)&")","$2$1")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
📁🤍SKU组合
CONCATENATE(
"❶",[📁🤍商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(1),"❶🔞"

,CHAR(10),

"❷",[📁🤍商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(2),"❷🔞"

,CHAR(10),

"❸",[📁🤍商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(3),"❸🔞"

,CHAR(10),

"❹",[📁🤍商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(4),"❹🔞"

,CHAR(10),

"❺",[📁🤍商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(5),"❺🔞"

,CHAR(10),

"❻",[📁🤍商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(6),"❻🔞"

,CHAR(10),

"❼",[📁🤍商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(7),"❼🔞"

,CHAR(10),

"❽",[📁🤍商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(8),"❽🔞"

,CHAR(10),

"❾",[📁🤍商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(9),"❾🔞"
)
=====
📁源信息入本表时间（自动）
自动输入信息
=====
📁✍️源信息发布时间（手动）
手动输入信息
=====
📁源信息发布时间(最终)
IF(NOT(ISBLANK([🍬源信息发布时间（自动）])),[🍬源信息发布时间（自动）],[📁✍️源信息发布时间（手动）])
.TODATE()
.TEXT("YYYY/MM/DD HH:mm")
=====
📁源信息发布时间(最终) -精确到天
IF(NOT(ISBLANK([🍬源信息发布时间（自动）])),[🍬源信息发布时间（自动）]，[📁✍️源信息发布时间（手动）])
.TEXT("yyyy-mm-dd")
=====
📁源信息新鲜度(天)
IF(ISBLANK([📁源信息发布时间(最终)]),"",DAYS(TODAY(),TODATE([📁源信息发布时间(最终)])))
=====
📁✍️信息留存时间（天）
手动输入信息
=====
🌀记录清除/删除判断
IF(
OR(
[📁源信息新鲜度(天)]
.ISBLANK(),
[📁✍️信息留存时间（天）]
.ISBLANK()
)
,"",
IF(
[📁源信息新鲜度(天)]>=[📁✍️信息留存时间（天）],"🗑️记录待删除",""))
=====


```
💰编号（最终）
TEXT([💰 价格解构（基建）]
.COUNTIF(CurrentValue.[💰编号（参考勿删！）]<[💰编号（参考勿删！）])+1

，"👀000000000"）
=====
💰编号（参考勿删！）
自增数字
=====
🍬商品信息（原始版）
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[💰编号（最终）]).[🍬商品信息（原始版）].LISTCOMBINE()
=====
🍬商品信息ID
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[💰编号（最终）]).[🍬商品信息ID].LISTCOMBINE()
=====
🧠基础单位
IF([🍬商品信息（原始版）].ISBLANK(),""，
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠基础单位]!="").[🧠基础单位].LISTCOMBINE().UNIQUE())
=====
🧠初级单位
IF([🍬商品信息（原始版）].ISBLANK(),""，
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠初级单位]!="").[🧠初级单位].LISTCOMBINE().UNIQUE())
=====
🧠进阶单位
IF([🍬商品信息（原始版）].ISBLANK(),""，
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠进阶单位]!="").[🧠进阶单位].LISTCOMBINE().UNIQUE())
=====
🧠高阶单位
IF([🍬商品信息（原始版）].ISBLANK(),""，
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠高阶单位]!="").[🧠高阶单位].LISTCOMBINE().UNIQUE())
=====
🧠终极单位
IF([🍬商品信息（原始版）].ISBLANK(),""，
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠终极单位]!="").[🧠终极单位].LISTCOMBINE().UNIQUE())
=====
🧠单位合集
IF([🍬商品信息（原始版）].ISBLANK(),""，
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠单位合集]!="").[🧠单位合集].LISTCOMBINE().UNIQUE())
=====
🧠商品价格-逻辑
IF([🍬商品信息（原始版）].ISBLANK(),""，
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠商品价格-逻辑]!="").[🧠商品价格-逻辑].LISTCOMBINE().UNIQUE())
=====
📁💜商品信息（终版分割版）
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[💰编号（最终）]).[📁💜商品信息（终版分割版）].LISTCOMBINE()
=====
📁🩵商品信息（清洗版分割版）
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[💰编号（最终）]).[📁🩵商品信息（清洗版分割版）].LISTCOMBINE()
=====
📁🤍😀商品标题（纯净分割版）
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[💰编号（最终）]).[📁🤍😀商品信息（纯净分割版） -商品名称&价格专享版].LISTCOMBINE()
=====
📁🤍商品信息（纯净分割版）
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[💰编号（最终）]).[📁🤍商品信息（纯净分割版）].LISTCOMBINE()
=====
📁💜sku数量（勿删）
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[💰编号（最终）]).[📁🩵链接/口令数量（原始版）]
=====
📁选品定位符
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[💰编号（最终）]).[📁选品定位符].LISTCOMBINE()
=====
📁源信息发布时间(最终)
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[💰编号（最终）]).[📁源信息发布时间(最终)]
=====
📁所属平台
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[💰编号（最终）]).[📁所属平台].LISTCOMBINE()
=====
🔪💚商品名称（信息重组专用版）
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[💰编号（最终）]).[🔪💚商品名称（信息重组专用版）].LISTCOMBINE()
=====
🔪🖤商品名称（字段维护专用）
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[💰编号（最终）]).[🔪🖤商品名称（字段维护专用）].LISTCOMBINE()
=====
🔪💚赠送信息（信息重组专用版）
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[💰编号（最终）]).[🔪💚赠送信息（信息重组专用版）].LISTCOMBINE()
=====
🔪💜🖤商品信息组合类型
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[💰编号（最终）]).[📁🧡🖤商品信息组合类型（终版）].LISTCOMBINE()
=====
🔪🤍😀下单文案
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[💰编号（最终）]).[🔪🧡下单文案]
=====
🔪🖤sku数量（勿删）
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[💰编号（最终）]).[🔪🖤SKU数量]
=====
🧪🤍规格类型（原始值）
[🧪 规格解构（基建）].FILTER(CurrentValue.[🧪编号（最终）]=[💰编号（最终）]).[🧪🤍规格类型（原始值）].LISTCOMBINE()
=====
🧪🤍规格✖️数量✖️起拍数量
[🧪 规格解构（基建）].FILTER(CurrentValue.[🧪编号（最终）]=[💰编号（最终）]).[🧪🔥🤍规格✖️数量✖️起拍数量].LISTCOMBINE()
=====
🧪🔥🤍规格✖️数量提取（终版）
[🧪 规格解构（基建）].FILTER(CurrentValue.[🧪编号（最终）]=[💰编号（最终）]).[🧪🔥🤍规格✖️数量提取（终版）].LISTCOMBINE()
=====
🐽💚商品信息（重组版）
[🐽 sku重组-社群专用 (基建)].FILTER(CurrentValue.[🐽编号（最终）]=[💰编号（最终）]).[🐽💚商品信息（重组版）].LISTCOMBINE()
=====
💻🖤单价（字段维护专用）
[💻 单价计算（基建）].FILTER(CurrentValue.[💻编号（最终）]=[💰编号（最终）]).[💻🖤单价（字段维护专用）]
=====
💰🤍😀付款类型
IF(
OR(
[🍬商品信息（原始版）]
.ISBLANK()
,
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[💰编号（最终）]).[📁🩵链接/口令数量（原始版）]=0
)
，""
，

IF(
OR(
AND(
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[💰编号（最终）]).[📁🤍😀商品信息（纯净分割版）]
.REGEXMATCH("[任][选意拍]")
,

[📁🩵商品信息（清洗版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH([📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[💰编号（最终）]).[📁🩵链接/口令数量（原始版）])
>1
,

[📁🩵商品信息（清洗版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH([📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[💰编号（最终）]).[📁🩵链接/口令数量（原始版）])
.REGEXMATCH("💰")
)

,
AND(
[🧪 规格解构（基建）].FILTER(CurrentValue.[🧪编号（最终）]=[💰编号（最终）]).[🧪🤍规格✖️数量提取（原始版）]
.REGEXEXTRACTALL("[❶-❾]")
.UNIQUE()
.COUNTA()
>
[💰 价格解构（基建）].FILTER(CurrentValue.[💰编号（最终）]=[💰编号（最终）]).[💰🩶原始高价]
.REGEXEXTRACTALL("[❶-❾]")
.UNIQUE()
.COUNTA()
，
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[💰编号（最终）]).[📁🤍😀商品信息（纯净分割版）]
.REGEXMATCH("[任][选意拍]")

)

)
，"🟧任选下单"
，

IF(
OR(
AND(
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[💰编号（最终）]).[📁🤍😀商品信息（纯净分割版）]
.REGEXMATCH("一起|合并|[各][选意拍]")
,

[📁🩵商品信息（清洗版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH([📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[💰编号（最终）]).[📁🩵链接/口令数量（原始版）])
.REGEXMATCH("💰")
)

，

[🧪 规格解构（基建）].FILTER(CurrentValue.[🧪编号（最终）]=[💰编号（最终）]).[🧪🤍规格✖️数量提取（原始版）]
.REGEXEXTRACTALL("[❶-❾]")
.UNIQUE()
.COUNTA()
>
[💰 价格解构（基建）].FILTER(CurrentValue.[💰编号（最终）]=[💰编号（最终）]).[💰🩶原始高价]
.REGEXEXTRACTALL("[❶-❾]")
.UNIQUE()
.COUNTA()
)

，"🟩合并下单"

，

"⬛️常规付款"
)))
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
💰🩶规格&价格数量对应关系
IF(
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[💰编号（最终）]).[📁🩵信息类型（唯一值）]
.REGEXMATCH("🛒")
.NOT()

，""

，

CONCATENATE(

IF(
AND(
[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❶.*?❶🔞")
.COUNTA()
=
[💰🩶原始高价]
.REGEXEXTRACTALL("❶.*?❶🔞")
.COUNTA()
，
[💰🩶原始高价]
.REGEXEXTRACTALL("❶.*?❶🔞")
.COUNTA()=0
）

，""
，

IF(
AND(
[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❶.*?❶🔞")
.COUNTA()
=
[💰🩶原始高价]
.REGEXEXTRACTALL("❶.*?❶🔞")
.COUNTA()
,
[💰🩶原始高价]
.REGEXEXTRACTALL("❶.*?❶🔞")
.COUNTA()=1
)

，"❶♥️规格 1 Vs 价格 1❶🔞"

，
IF(
AND(

[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❶.*?❶🔞")
.COUNTA()
>
[💰🩶原始高价]
.REGEXEXTRACTALL("❶.*?❶🔞")
.COUNTA()
,

[💰🩶原始高价]
.REGEXEXTRACTALL("❶.*?❶🔞")
.COUNTA()=1
)

，"❶♠️规格 N  Vs 价格 1❶🔞"

，
IF(
AND(

[💰🩶原始高价]
.REGEXEXTRACTALL("❶.*?❶🔞")
.COUNTA()
>
[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❶.*?❶🔞")
.COUNTA()
,

[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❶.*?❶🔞")
.COUNTA()=1
)

，"❶♦️规格 1  Vs 价格 N❶🔞"

，
IF(
AND(

[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❶.*?❶🔞")
.COUNTA()
=
[💰🩶原始高价]
.REGEXEXTRACTALL("❶.*?❶🔞")
.COUNTA()
,

[💰🩶原始高价]
.REGEXEXTRACTALL("❶.*?❶🔞")
.COUNTA()>1

)

，"❶♣️规格 N  Vs 价格 N(数量对等)❶🔞"


，
IF(
OR(
[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❶.*?❶🔞")
.COUNTA()
=0
，

[💰🩶原始高价]
.REGEXEXTRACTALL("❶.*?❶🔞")
.COUNTA()
=0
)
，"❶🃏无规格/无价格❶🔞"

，"❶🎴规格 N  Vs 价格 N(数量不对等)❶🔞"

)))))）



,CHAR(10),

IF(
AND(
[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❷.*?❷🔞")
.COUNTA()
=
[💰🩶原始高价]
.REGEXEXTRACTALL("❷.*?❷🔞")
.COUNTA()
,
[💰🩶原始高价]
.REGEXEXTRACTALL("❷.*?❷🔞")
.COUNTA()=0
)
,""
,
IF(
AND(
[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❷.*?❷🔞")
.COUNTA()
=
[💰🩶原始高价]
.REGEXEXTRACTALL("❷.*?❷🔞")
.COUNTA()
,
[💰🩶原始高价]
.REGEXEXTRACTALL("❷.*?❷🔞")
.COUNTA()=1
)
,"❷♥️规格 1 Vs 价格 1❷🔞"
,
IF(
AND(
[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❷.*?❷🔞")
.COUNTA()
>
[💰🩶原始高价]
.REGEXEXTRACTALL("❷.*?❷🔞")
.COUNTA()
,
[💰🩶原始高价]
.REGEXEXTRACTALL("❷.*?❷🔞")
.COUNTA()=1
)
,"❷♠️规格 N Vs 价格 1❷🔞"
,
IF(
AND(
[💰🩶原始高价]
.REGEXEXTRACTALL("❷.*?❷🔞")
.COUNTA()
>
[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❷.*?❷🔞")
.COUNTA()
,
[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❷.*?❷🔞")
.COUNTA()=1
)
,"❷♦️规格 1 Vs 价格 N❷🔞"
,
IF(
AND(
[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❷.*?❷🔞")
.COUNTA()
=
[💰🩶原始高价]
.REGEXEXTRACTALL("❷.*?❷🔞")
.COUNTA()
,
[💰🩶原始高价]
.REGEXEXTRACTALL("❷.*?❷🔞")
.COUNTA()>1
)
,"❷♣️规格 N Vs 价格 N(数量对等)❷🔞"


，
IF(

OR(
[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❷.*?❷🔞")
.COUNTA()
=0
,

[💰🩶原始高价]
.REGEXEXTRACTALL("❷.*?❷🔞")
.COUNTA()
=0
)
，"❷🃏无规格/无价格❷🔞"


,"❷🎴规格 N Vs 价格 N(数量不对等)❷🔞"
))))))



,CHAR(10),

IF(
AND(
[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❸.*?❸🔞")
.COUNTA()
=
[💰🩶原始高价]
.REGEXEXTRACTALL("❸.*?❸🔞")
.COUNTA()
,
[💰🩶原始高价]
.REGEXEXTRACTALL("❸.*?❸🔞")
.COUNTA()=0
)
,""
,
IF(
AND(
[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❸.*?❸🔞")
.COUNTA()
=
[💰🩶原始高价]
.REGEXEXTRACTALL("❸.*?❸🔞")
.COUNTA()
,
[💰🩶原始高价]
.REGEXEXTRACTALL("❸.*?❸🔞")
.COUNTA()=1
)
,"❸♥️规格 1 Vs 价格 1❸🔞"
,
IF(
AND(
[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❸.*?❸🔞")
.COUNTA()
>
[💰🩶原始高价]
.REGEXEXTRACTALL("❸.*?❸🔞")
.COUNTA()
,
[💰🩶原始高价]
.REGEXEXTRACTALL("❸.*?❸🔞")
.COUNTA()=1
)
,"❸♠️规格 N Vs 价格 1❸🔞"
,
IF(
AND(
[💰🩶原始高价]
.REGEXEXTRACTALL("❸.*?❸🔞")
.COUNTA()
>
[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❸.*?❸🔞")
.COUNTA()
,
[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❸.*?❸🔞")
.COUNTA()=1
)
,"❸♦️规格 1 Vs 价格 N❸🔞"
,
IF(
AND(
[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❸.*?❸🔞")
.COUNTA()
=
[💰🩶原始高价]
.REGEXEXTRACTALL("❸.*?❸🔞")
.COUNTA()
,
[💰🩶原始高价]
.REGEXEXTRACTALL("❸.*?❸🔞")
.COUNTA()>1
)
,"❸♣️规格 N Vs 价格 N(数量对等)❸🔞"

，
IF(
OR(
[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❸.*?❸🔞")
.COUNTA()
=0

，
[💰🩶原始高价]
.REGEXEXTRACTALL("❸.*?❸🔞")
.COUNTA()
=0

）
，"❸🃏无规格/无价格❸🔞"



,"❸🎴规格 N Vs 价格 N(数量不对等)❸🔞"
))))))

,CHAR(10),

IF(
AND(
[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❹.*?❹🔞")
.COUNTA()
=
[💰🩶原始高价]
.REGEXEXTRACTALL("❹.*?❹🔞")
.COUNTA()
,
[💰🩶原始高价]
.REGEXEXTRACTALL("❹.*?❹🔞")
.COUNTA()=0
)
,""
,
IF(
AND(
[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❹.*?❹🔞")
.COUNTA()
=
[💰🩶原始高价]
.REGEXEXTRACTALL("❹.*?❹🔞")
.COUNTA()
,
[💰🩶原始高价]
.REGEXEXTRACTALL("❹.*?❹🔞")
.COUNTA()=1
)
,"❹♥️规格 1 Vs 价格 1❹🔞"
,
IF(
AND(
[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❹.*?❹🔞")
.COUNTA()
>
[💰🩶原始高价]
.REGEXEXTRACTALL("❹.*?❹🔞")
.COUNTA()
,
[💰🩶原始高价]
.REGEXEXTRACTALL("❹.*?❹🔞")
.COUNTA()=1
)
,"❹♠️规格 N Vs 价格 1❹🔞"
,
IF(
AND(
[💰🩶原始高价]
.REGEXEXTRACTALL("❹.*?❹🔞")
.COUNTA()
>
[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❹.*?❹🔞")
.COUNTA()
,
[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❹.*?❹🔞")
.COUNTA()=1
)
,"❹♦️规格 1 Vs 价格 N❹🔞"
,
IF(
AND(
[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❹.*?❹🔞")
.COUNTA()
=
[💰🩶原始高价]
.REGEXEXTRACTALL("❹.*?❹🔞")
.COUNTA()
,
[💰🩶原始高价]
.REGEXEXTRACTALL("❹.*?❹🔞")
.COUNTA()>1
)
,"❹♣️规格 N Vs 价格 N(数量对等)❹🔞"


，
IF(
OR(
[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❹.*?❹🔞")
.COUNTA()
=0

，

[💰🩶原始高价]
.REGEXEXTRACTALL("❹.*?❹🔞")
.COUNTA()
=0

)
，"❹🃏无规格/无价格❹🔞"




,"❹🎴规格 N Vs 价格 N(数量不对等)❹🔞"
))))))

,CHAR(10),

IF(
AND(
[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❺.*?❺🔞")
.COUNTA()
=
[💰🩶原始高价]
.REGEXEXTRACTALL("❺.*?❺🔞")
.COUNTA()
,
[💰🩶原始高价]
.REGEXEXTRACTALL("❺.*?❺🔞")
.COUNTA()=0
)
,""
,
IF(
AND(
[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❺.*?❺🔞")
.COUNTA()
=
[💰🩶原始高价]
.REGEXEXTRACTALL("❺.*?❺🔞")
.COUNTA()
,
[💰🩶原始高价]
.REGEXEXTRACTALL("❺.*?❺🔞")
.COUNTA()=1
)
,"❺♥️规格 1 Vs 价格 1❺🔞"
,
IF(
AND(
[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❺.*?❺🔞")
.COUNTA()
>
[💰🩶原始高价]
.REGEXEXTRACTALL("❺.*?❺🔞")
.COUNTA()
,
[💰🩶原始高价]
.REGEXEXTRACTALL("❺.*?❺🔞")
.COUNTA()=1
)
,"❺♠️规格 N Vs 价格 1❺🔞"
,
IF(
AND(
[💰🩶原始高价]
.REGEXEXTRACTALL("❺.*?❺🔞")
.COUNTA()
>
[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❺.*?❺🔞")
.COUNTA()
,
[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❺.*?❺🔞")
.COUNTA()=1
)
,"♦️规格 1 Vs 价格 N"
,
IF(
AND(
[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❺.*?❺🔞")
.COUNTA()
=
[💰🩶原始高价]
.REGEXEXTRACTALL("❺.*?❺🔞")
.COUNTA()
,
[💰🩶原始高价]
.REGEXEXTRACTALL("❺.*?❺🔞")
.COUNTA()>1
)
,"❺♣️规格 N Vs 价格 N(数量对等)❺🔞"


，
IF(

OR(
[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❺.*?❺🔞")
.COUNTA()
=0
，

[💰🩶原始高价]
.REGEXEXTRACTALL("❺.*?❺🔞")
.COUNTA()
=0

）
，"❺🃏无规格/无价格❺🔞"
,"❺🎴规格 N Vs 价格 N(数量不对等)❺🔞"
))))))



,CHAR(10),

IF(
AND(
[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❻.*?❻🔞")
.COUNTA()
=
[💰🩶原始高价]
.REGEXEXTRACTALL("❻.*?❻🔞")
.COUNTA()
,
[💰🩶原始高价]
.REGEXEXTRACTALL("❻.*?❻🔞")
.COUNTA()=0
)
,""
,
IF(
AND(
[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❻.*?❻🔞")
.COUNTA()
=
[💰🩶原始高价]
.REGEXEXTRACTALL("❻.*?❻🔞")
.COUNTA()
,
[💰🩶原始高价]
.REGEXEXTRACTALL("❻.*?❻🔞")
.COUNTA()=1
)
,"❻♥️规格 1 Vs 价格 1❻🔞"
,
IF(
AND(
[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❻.*?❻🔞")
.COUNTA()
>
[💰🩶原始高价]
.REGEXEXTRACTALL("❻.*?❻🔞")
.COUNTA()
,
[💰🩶原始高价]
.REGEXEXTRACTALL("❻.*?❻🔞")
.COUNTA()=1
)
,"❻♠️规格 N Vs 价格 1❻🔞"
,
IF(
AND(
[💰🩶原始高价]
.REGEXEXTRACTALL("❻.*?❻🔞")
.COUNTA()
>
[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❻.*?❻🔞")
.COUNTA()
,
[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❻.*?❻🔞")
.COUNTA()=1
)
,"❻♦️规格 1 Vs 价格 N❻🔞"
,
IF(
AND(
[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❻.*?❻🔞")
.COUNTA()
=
[💰🩶原始高价]
.REGEXEXTRACTALL("❻.*?❻🔞")
.COUNTA()
,
[💰🩶原始高价]
.REGEXEXTRACTALL("❻.*?❻🔞")
.COUNTA()>1
)
,"❻♣️规格 N Vs 价格 N(数量对等)❻🔞"

，
IF(

OR(
[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❻.*?❻🔞")
.COUNTA()
=0

，

[💰🩶原始高价]
.REGEXEXTRACTALL("❻.*?❻🔞")
.COUNTA()
=0

）

，"❻🃏无规格/无价格❻🔞"
,"❻🎴规格 N Vs 价格 N(数量不对等)❻🔞"
))))))



,CHAR(10),

IF(
AND(
[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❼.*?❼🔞")
.COUNTA()
=
[💰🩶原始高价]
.REGEXEXTRACTALL("❼.*?❼🔞")
.COUNTA()
,
[💰🩶原始高价]
.REGEXEXTRACTALL("❼.*?❼🔞")
.COUNTA()=0
)
,""
,
IF(
AND(
[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❼.*?❼🔞")
.COUNTA()
=
[💰🩶原始高价]
.REGEXEXTRACTALL("❼.*?❼🔞")
.COUNTA()
,
[💰🩶原始高价]
.REGEXEXTRACTALL("❼.*?❼🔞")
.COUNTA()=1
)
,"❼♥️规格 1 Vs 价格 1❼🔞"
,
IF(
AND(
[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❼.*?❼🔞")
.COUNTA()
>
[💰🩶原始高价]
.REGEXEXTRACTALL("❼.*?❼🔞")
.COUNTA()
,
[💰🩶原始高价]
.REGEXEXTRACTALL("❼.*?❼🔞")
.COUNTA()=1
)
,"❼♠️规格 N Vs 价格 1❼🔞"
,
IF(
AND(
[💰🩶原始高价]
.REGEXEXTRACTALL("❼.*?❼🔞")
.COUNTA()
>
[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❼.*?❼🔞")
.COUNTA()
,
[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❼.*?❼🔞")
.COUNTA()=1
)
,"❼♦️规格 1 Vs 价格 N❼🔞"
,
IF(
AND(
[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❼.*?❼🔞")
.COUNTA()
=
[💰🩶原始高价]
.REGEXEXTRACTALL("❼.*?❼🔞")
.COUNTA()
,
[💰🩶原始高价]
.REGEXEXTRACTALL("❼.*?❼🔞")
.COUNTA()>1
)
,"❼♣️规格 N Vs 价格 N(数量对等)❼🔞"

，
IF(

OR(
[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❼.*?❼🔞")
.COUNTA()
=0
，

[💰🩶原始高价]
.REGEXEXTRACTALL("❼.*?❼🔞")
.COUNTA()
=0

）

，"❼🃏无规格/无价格❼🔞"
,"❼🎴规格 N Vs 价格 N(数量不对等)❼🔞"
))))))

,CHAR(10),

IF(
AND(
[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❽.*?❽🔞")
.COUNTA()
=
[💰🩶原始高价]
.REGEXEXTRACTALL("❽.*?❽🔞")
.COUNTA()
,
[💰🩶原始高价]
.REGEXEXTRACTALL("❽.*?❽🔞")
.COUNTA()=0
)
,""
,
IF(
AND(
[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❽.*?❽🔞")
.COUNTA()
=
[💰🩶原始高价]
.REGEXEXTRACTALL("❽.*?❽🔞")
.COUNTA()
,
[💰🩶原始高价]
.REGEXEXTRACTALL("❽.*?❽🔞")
.COUNTA()=1
)
,"❽♥️规格 1 Vs 价格 1❽🔞"
,
IF(
AND(
[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❽.*?❽🔞")
.COUNTA()
>
[💰🩶原始高价]
.REGEXEXTRACTALL("❽.*?❽🔞")
.COUNTA()
,
[💰🩶原始高价]
.REGEXEXTRACTALL("❽.*?❽🔞")
.COUNTA()=1
)
,"❽♠️规格 N Vs 价格 1❽🔞"
,
IF(
AND(
[💰🩶原始高价]
.REGEXEXTRACTALL("❽.*?❽🔞")
.COUNTA()
>
[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❽.*?❽🔞")
.COUNTA()
,
[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❽.*?❽🔞")
.COUNTA()=1
)
,"❽♦️规格 1 Vs 价格 N❽🔞"
,
IF(
AND(
[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❽.*?❽🔞")
.COUNTA()
=
[💰🩶原始高价]
.REGEXEXTRACTALL("❽.*?❽🔞")
.COUNTA()
,
[💰🩶原始高价]
.REGEXEXTRACTALL("❽.*?❽🔞")
.COUNTA()>1
)
,"❽♣️规格 N Vs 价格 N(数量对等)❽🔞"


，
IF(

OR(
[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❽.*?❽🔞")
.COUNTA()
=0
，

[💰🩶原始高价]
.REGEXEXTRACTALL("❽.*?❽🔞")
.COUNTA()
=0
）

，"❽🃏无规格/无价格❽🔞"
,"❽🎴规格 N Vs 价格 N(数量不对等)❽🔞"
))))))


,CHAR(10),

IF(
AND(
[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❾.*?❾🔞")
.COUNTA()
=
[💰🩶原始高价]
.REGEXEXTRACTALL("❾.*?❾🔞")
.COUNTA()
,
[💰🩶原始高价]
.REGEXEXTRACTALL("❾.*?❾🔞")
.COUNTA()=0
)
,""
,
IF(
AND(
[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❾.*?❾🔞")
.COUNTA()
=
[💰🩶原始高价]
.REGEXEXTRACTALL("❾.*?❾🔞")
.COUNTA()
,
[💰🩶原始高价]
.REGEXEXTRACTALL("❾.*?❾🔞")
.COUNTA()=1
)
,"❾♥️规格 1 Vs 价格 1❾🔞"
,
IF(
AND(
[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❾.*?❾🔞")
.COUNTA()
>
[💰🩶原始高价]
.REGEXEXTRACTALL("❾.*?❾🔞")
.COUNTA()
,
[💰🩶原始高价]
.REGEXEXTRACTALL("❾.*?❾🔞")
.COUNTA()=1
)
,"❾♠️规格 N Vs 价格 1❾🔞"
,
IF(
AND(
[💰🩶原始高价]
.REGEXEXTRACTALL("❾.*?❾🔞")
.COUNTA()
>
[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❾.*?❾🔞")
.COUNTA()
,
[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❾.*?❾🔞")
.COUNTA()=1
)
,"❾♦️规格 1 Vs 价格 N❾🔞"
,
IF(
AND(
[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❾.*?❾🔞")
.COUNTA()
=
[💰🩶原始高价]
.REGEXEXTRACTALL("❾.*?❾🔞")
.COUNTA()
,
[💰🩶原始高价]
.REGEXEXTRACTALL("❾.*?❾🔞")
.COUNTA()>1
)
,"❾♣️规格 N Vs 价格 N(数量对等)❾🔞"


，
IF(
OR(
[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❾.*?❾🔞")
.COUNTA()
=0
，

[💰🩶原始高价]
.REGEXEXTRACTALL("❾.*?❾🔞")
.COUNTA()
=0
）
，"❾🃏无规格/无价格❾🔞"
,"❾🎴规格 N Vs 价格 N(数量不对等)❾🔞"
))))))


)）
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
💰🩶商品价格（纯净版）
[💰🤎商品价格（清洗版）]
.REGEXREPLACE("([❶-❾].*?💰\d+(?:\.\d+)?).*?(?:[,，；。;]?[叠淘]?(?:[Pp][Ll][Uu][Ss]|88[Vv][Ii][Pp]|首单|凑单|好?返现?|金币|福袋|实际到手|到手|[没有]{0,2}学生号|部分(?:人|金币|礼金)?|[新老]客(?:回购)?|尾款|＝)共?💰\d+(?:\.\d+)?[，。;,；]?)+(.*?[❶-❾]🔞)","$1$2")

.REGEXREPLACE("[,，。；;]?(好?[评返]{1,2}|晒图)(\d+(?:\.\d+)?)?.*?[,，。；;]?(实际)?(到手)?仅?＝?💰\d+(?:\.\d+)?","")

.REGEXREPLACE("[❶-❾]\s*[❶-❾]🔞","")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
💰🩶原始高价
CONCATENATE(

[💰🩶商品价格（纯净版）]
.REGEXEXTRACTALL("❶[\s\S]*❶🔞")
.REGEXEXTRACTALL("💰\d+(?:\.\d+)?")
.ARRAYJOIN("；")
.REGEXREPLACE("💰\d+(?:\.\d+)?","❶$0❶🔞")

,CHAR(10),

[💰🩶商品价格（纯净版）]
.REGEXEXTRACTALL("❷[\s\S]*❷🔞")
.REGEXEXTRACTALL("💰\d+(?:\.\d+)?")
.ARRAYJOIN("；")
.REGEXREPLACE("💰\d+(?:\.\d+)?","❷$0❷🔞")

,CHAR(10),

[💰🩶商品价格（纯净版）]
.REGEXEXTRACTALL("❸[\s\S]*❸🔞")
.REGEXEXTRACTALL("💰\d+(?:\.\d+)?")
.ARRAYJOIN("；")
.REGEXREPLACE("💰\d+(?:\.\d+)?","❸$0❸🔞")


,CHAR(10),

[💰🩶商品价格（纯净版）]
.REGEXEXTRACTALL("❹[\s\S]*❹🔞")
.REGEXEXTRACTALL("💰\d+(?:\.\d+)?")
.ARRAYJOIN("；")
.REGEXREPLACE("💰\d+(?:\.\d+)?","❹$0❹🔞")


,CHAR(10),

[💰🩶商品价格（纯净版）]
.REGEXEXTRACTALL("❺[\s\S]*❺🔞")
.REGEXEXTRACTALL("💰\d+(?:\.\d+)?")
.ARRAYJOIN("；")
.REGEXREPLACE("💰\d+(?:\.\d+)?","❺$0❺🔞")


,CHAR(10),

[💰🩶商品价格（纯净版）]
.REGEXEXTRACTALL("❻[\s\S]*❻🔞")
.REGEXEXTRACTALL("💰\d+(?:\.\d+)?")
.ARRAYJOIN("；")
.REGEXREPLACE("💰\d+(?:\.\d+)?","❻$0❻🔞")


,CHAR(10),

[💰🩶商品价格（纯净版）]
.REGEXEXTRACTALL("❼[\s\S]*❼🔞")
.REGEXEXTRACTALL("💰\d+(?:\.\d+)?")
.ARRAYJOIN("；")
.REGEXREPLACE("💰\d+(?:\.\d+)?","❼$0❼🔞")


,CHAR(10),

[💰🩶商品价格（纯净版）]
.REGEXEXTRACTALL("❽[\s\S]*❽🔞")
.REGEXEXTRACTALL("💰\d+(?:\.\d+)?")
.ARRAYJOIN("；")
.REGEXREPLACE("💰\d+(?:\.\d+)?","❽$0❽🔞")


,CHAR(10),

[💰🩶商品价格（纯净版）]
.REGEXEXTRACTALL("❾[\s\S]*❾🔞")
.REGEXEXTRACTALL("💰\d+(?:\.\d+)?")
.ARRAYJOIN("；")
.REGEXREPLACE("💰\d+(?:\.\d+)?","❾$0❾🔞")


)

.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
💰🤎商品价格（清洗版）
[🔪 ｜ ✍️ sku解构(基建)]
.FILTER(CurrentValue.[🔪编号（最终）]=[💰编号（最终）]).[🔪💚商品价格（信息重组专用版）]
.REGEXREPLACE("(送.*?)💰(\d+(?:\.\d+)?[，,。])","$1价值$2")
.REGEXREPLACE("(返[卡现]?)💰(\d+(?:\.\d+)?)","$1$2")
.REGEXREPLACE("(买([一-十]|\d+)送([一-十]|\d+).*?)价值","$1💰")
.REGEXREPLACE("(下单)(\d+(?:\.\d+)?)","$1💰$2")
.REGEXREPLACE("(.*?💰.*?)((?:没有|无).*?💰\d+(?:\.\d+)?)","$1")
.REGEXREPLACE("\d+("&[🧠单位合集]&")(到手|折|低至)💰\d+(?:\.\d+)?","")
.REGEXREPLACE("(?:到手|折|低至)💰\d+(?:\.\d+)?/.*?([❶-❾]🔞)","$1")

.REGEXREPLACE("㊙️㊙️一定要先分行之后才可以执行下方这个逻辑，否则就容易出现第二个捕获组和第三个捕获组之间的定位符号被吞掉","")

.REGEXREPLACE("([❶-❾]🔞)([❶-❾])","$1"&CHAR(10)&"$2")

.REGEXREPLACE("([❶-❾]).*?(💰\d+(?:\.\d+)?).*?(\d+(?:\.\d+)?(?:"&[🧠单位合集]&")💰.*?)([❶-❾]🔞)","$1$2$4"&CHAR(10)&"$1$3$4")


.REGEXREPLACE("㊙️㊙️基于定位符进行分行，一定要操作，否则💰🩶商品价格（纯净版）的逻辑和上面的一样，会进行中间定位符的吞噬","")
.REGEXREPLACE("([❶-❾]🔞)([❶-❾])","$1"&CHAR(10)&"$2")

.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
💰🤎原始低价
[💰🤎商品价格（清洗版）]
.REGEXEXTRACTALL("[❶-❾].*?💰.*?[❶-❾]🔞")
.ARRAYJOIN(CHAR(10))
.REGEXREPLACE("([❶-❾]).*?(?:💰\d+(?:\.\d+)?.*?)*(💰\d+(?:\.\d+)?).*?([❶-❾]🔞)","$1$2$3")


.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
💰🤎到手价格（终版）
CONCATENATE(

([💰🤎到手价格（好返价）]
.REGEXEXTRACTALL("❶.*?(\-?\d+(?:\.\d+)?).*?❶🔞").NTH(1)
.VALUE()
-
[💰🤎赠送价到手详情]
.REGEXEXTRACTALL("❶.*?(?:价值|💰)(\-?\d+(?:\.\d+)?).*?❶🔞").NTH(1)
.VALUE())
.REGEXREPLACE("\-?\d+(?:\.\d+)?","❶$0❶🔞")


,CHAR(10),

([💰🤎到手价格（好返价）]
.REGEXEXTRACTALL("❶.*?(\-?\d+(?:\.\d+)?).*?❶🔞").NTH(2)
.VALUE()
-
[💰🤎赠送价到手详情]
.REGEXEXTRACTALL("❶.*?(?:价值|💰)(\-?\d+(?:\.\d+)?).*?❶🔞").NTH(2)
.VALUE())
.REGEXREPLACE("\-?\d+(?:\.\d+)?","❶$0❶🔞")


,CHAR(10),

([💰🤎到手价格（好返价）]
.REGEXEXTRACTALL("❶.*?(\-?\d+(?:\.\d+)?).*?❶🔞").NTH(3)
.VALUE()
-
[💰🤎赠送价到手详情]
.REGEXEXTRACTALL("❶.*?(?:价值|💰)(\-?\d+(?:\.\d+)?).*?❶🔞").NTH(3)
.VALUE())
.REGEXREPLACE("\-?\d+(?:\.\d+)?","❶$0❶🔞")


,CHAR(10),



([💰🤎到手价格（好返价）]
.REGEXEXTRACTALL("❶.*?(\-?\d+(?:\.\d+)?).*?❶🔞").NTH(4)
.VALUE()
-
[💰🤎赠送价到手详情]
.REGEXEXTRACTALL("❶.*?(?:价值|💰)(\-?\d+(?:\.\d+)?).*?❶🔞").NTH(4)
.VALUE())
.REGEXREPLACE("\-?\d+(?:\.\d+)?","❶$0❶🔞")


,CHAR(10),

([💰🤎到手价格（好返价）]
.REGEXEXTRACTALL("❶.*?(\-?\d+(?:\.\d+)?).*?❶🔞").NTH(5)
.VALUE()
-
[💰🤎赠送价到手详情]
.REGEXEXTRACTALL("❶.*?(?:价值|💰)(\-?\d+(?:\.\d+)?).*?❶🔞").NTH(5)
.VALUE())
.REGEXREPLACE("\-?\d+(?:\.\d+)?","❶$0❶🔞")


,CHAR(10),


([💰🤎到手价格（好返价）]
.REGEXEXTRACTALL("❶.*?(\-?\d+(?:\.\d+)?).*?❶🔞").NTH(6)
.VALUE()
-
[💰🤎赠送价到手详情]
.REGEXEXTRACTALL("❶.*?(?:价值|💰)(\-?\d+(?:\.\d+)?).*?❶🔞").NTH(6)
.VALUE())
.REGEXREPLACE("\-?\d+(?:\.\d+)?","❶$0❶🔞")


,CHAR(10),



([💰🤎到手价格（好返价）]
.REGEXEXTRACTALL("❷.*?(\-?\d+(?:\.\d+)?).*?❷🔞").NTH(1)
.VALUE()
-
[💰🤎赠送价到手详情]
.REGEXEXTRACTALL("❷.*?(?:价值|💰)(\-?\d+(?:\.\d+)?).*?❷🔞").NTH(1)
.VALUE())
.REGEXREPLACE("\-?\d+(?:\.\d+)?","❷$0❷🔞")

,CHAR(10),

([💰🤎到手价格（好返价）]
.REGEXEXTRACTALL("❷.*?(\-?\d+(?:\.\d+)?).*?❷🔞").NTH(2)
.VALUE()
-
[💰🤎赠送价到手详情]
.REGEXEXTRACTALL("❷.*?(?:价值|💰)(\-?\d+(?:\.\d+)?).*?❷🔞").NTH(2)
.VALUE())
.REGEXREPLACE("\-?\d+(?:\.\d+)?","❷$0❷🔞")

,CHAR(10),


([💰🤎到手价格（好返价）]
.REGEXEXTRACTALL("❷.*?(\-?\d+(?:\.\d+)?).*?❷🔞").NTH(3)
.VALUE()
-
[💰🤎赠送价到手详情]
.REGEXEXTRACTALL("❷.*?(?:价值|💰)(\-?\d+(?:\.\d+)?).*?❷🔞").NTH(3)
.VALUE())
.REGEXREPLACE("\-?\d+(?:\.\d+)?","❷$0❷🔞")

,CHAR(10),


([💰🤎到手价格（好返价）]
.REGEXEXTRACTALL("❷.*?(\-?\d+(?:\.\d+)?).*?❷🔞").NTH(4)
.VALUE()
-
[💰🤎赠送价到手详情]
.REGEXEXTRACTALL("❷.*?(?:价值|💰)(\-?\d+(?:\.\d+)?).*?❷🔞").NTH(4)
.VALUE())
.REGEXREPLACE("\-?\d+(?:\.\d+)?","❷$0❷🔞")

,CHAR(10),


([💰🤎到手价格（好返价）]
.REGEXEXTRACTALL("❷.*?(\-?\d+(?:\.\d+)?).*?❷🔞").NTH(5)
.VALUE()
-
[💰🤎赠送价到手详情]
.REGEXEXTRACTALL("❷.*?(?:价值|💰)(\-?\d+(?:\.\d+)?).*?❷🔞").NTH(5)
.VALUE())
.REGEXREPLACE("\-?\d+(?:\.\d+)?","❷$0❷🔞")

,CHAR(10),

([💰🤎到手价格（好返价）]
.REGEXEXTRACTALL("❷.*?(\-?\d+(?:\.\d+)?).*?❷🔞").NTH(6)
.VALUE()
-
[💰🤎赠送价到手详情]
.REGEXEXTRACTALL("❷.*?(?:价值|💰)(\-?\d+(?:\.\d+)?).*?❷🔞").NTH(6)
.VALUE())
.REGEXREPLACE("\-?\d+(?:\.\d+)?","❷$0❷🔞")

,CHAR(10),

([💰🤎到手价格（好返价）]
.REGEXEXTRACTALL("❸.*?(\-?\d+(?:\.\d+)?).*?❸🔞").NTH(1)
.VALUE()
-
[💰🤎赠送价到手详情]
.REGEXEXTRACTALL("❸.*?(?:价值|💰)(\-?\d+(?:\.\d+)?).*?❸🔞").NTH(1)
.VALUE())
.REGEXREPLACE("\-?\d+(?:\.\d+)?","❸$0❸🔞")

,CHAR(10),


([💰🤎到手价格（好返价）]
.REGEXEXTRACTALL("❸.*?(\-?\d+(?:\.\d+)?).*?❸🔞").NTH(2)
.VALUE()
-
[💰🤎赠送价到手详情]
.REGEXEXTRACTALL("❸.*?(?:价值|💰)(\-?\d+(?:\.\d+)?).*?❸🔞").NTH(2)
.VALUE())
.REGEXREPLACE("\-?\d+(?:\.\d+)?","❸$0❸🔞")

,CHAR(10),




([💰🤎到手价格（好返价）]
.REGEXEXTRACTALL("❸.*?(\-?\d+(?:\.\d+)?).*?❸🔞").NTH(3)
.VALUE()
-
[💰🤎赠送价到手详情]
.REGEXEXTRACTALL("❸.*?(?:价值|💰)(\-?\d+(?:\.\d+)?).*?❸🔞").NTH(3)
.VALUE())
.REGEXREPLACE("\-?\d+(?:\.\d+)?","❸$0❸🔞")

,CHAR(10),




([💰🤎到手价格（好返价）]
.REGEXEXTRACTALL("❸.*?(\-?\d+(?:\.\d+)?).*?❸🔞").NTH(4)
.VALUE()
-
[💰🤎赠送价到手详情]
.REGEXEXTRACTALL("❸.*?(?:价值|💰)(\-?\d+(?:\.\d+)?).*?❸🔞").NTH(4)
.VALUE())
.REGEXREPLACE("\-?\d+(?:\.\d+)?","❸$0❸🔞")

,CHAR(10),




([💰🤎到手价格（好返价）]
.REGEXEXTRACTALL("❸.*?(\-?\d+(?:\.\d+)?).*?❸🔞").NTH(5)
.VALUE()
-
[💰🤎赠送价到手详情]
.REGEXEXTRACTALL("❸.*?(?:价值|💰)(\-?\d+(?:\.\d+)?).*?❸🔞").NTH(5)
.VALUE())
.REGEXREPLACE("\-?\d+(?:\.\d+)?","❸$0❸🔞")

,CHAR(10),


([💰🤎到手价格（好返价）]
.REGEXEXTRACTALL("❸.*?(\-?\d+(?:\.\d+)?).*?❸🔞").NTH(6)
.VALUE()
-
[💰🤎赠送价到手详情]
.REGEXEXTRACTALL("❸.*?(?:价值|💰)(\-?\d+(?:\.\d+)?).*?❸🔞").NTH(6)
.VALUE())
.REGEXREPLACE("\-?\d+(?:\.\d+)?","❸$0❸🔞")

,CHAR(10),




([💰🤎到手价格（好返价）]
.REGEXEXTRACTALL("❹.*?(\-?\d+(?:\.\d+)?).*?❹🔞").NTH(1)
.VALUE()
-
[💰🤎赠送价到手详情]
.REGEXEXTRACTALL("❹.*?(?:价值|💰)(\-?\d+(?:\.\d+)?).*?❹🔞").NTH(1)
.VALUE())
.REGEXREPLACE("\-?\d+(?:\.\d+)?","❹$0❹🔞")

,CHAR(10),

([💰🤎到手价格（好返价）]
.REGEXEXTRACTALL("❹.*?(\-?\d+(?:\.\d+)?).*?❹🔞").NTH(2)
.VALUE()
-
[💰🤎赠送价到手详情]
.REGEXEXTRACTALL("❹.*?(?:价值|💰)(\-?\d+(?:\.\d+)?).*?❹🔞").NTH(2)
.VALUE())
.REGEXREPLACE("\-?\d+(?:\.\d+)?","❹$0❹🔞")

,CHAR(10),



([💰🤎到手价格（好返价）]
.REGEXEXTRACTALL("❹.*?(\-?\d+(?:\.\d+)?).*?❹🔞").NTH(3)
.VALUE()
-
[💰🤎赠送价到手详情]
.REGEXEXTRACTALL("❹.*?(?:价值|💰)(\-?\d+(?:\.\d+)?).*?❹🔞").NTH(3)
.VALUE())
.REGEXREPLACE("\-?\d+(?:\.\d+)?","❹$0❹🔞")

,CHAR(10),



([💰🤎到手价格（好返价）]
.REGEXEXTRACTALL("❹.*?(\-?\d+(?:\.\d+)?).*?❹🔞").NTH(4)
.VALUE()
-
[💰🤎赠送价到手详情]
.REGEXEXTRACTALL("❹.*?(?:价值|💰)(\-?\d+(?:\.\d+)?).*?❹🔞").NTH(4)
.VALUE())
.REGEXREPLACE("\-?\d+(?:\.\d+)?","❹$0❹🔞")

,CHAR(10),




([💰🤎到手价格（好返价）]
.REGEXEXTRACTALL("❹.*?(\-?\d+(?:\.\d+)?).*?❹🔞").NTH(5)
.VALUE()
-
[💰🤎赠送价到手详情]
.REGEXEXTRACTALL("❹.*?(?:价值|💰)(\-?\d+(?:\.\d+)?).*?❹🔞").NTH(5)
.VALUE())
.REGEXREPLACE("\-?\d+(?:\.\d+)?","❹$0❹🔞")

,CHAR(10),



([💰🤎到手价格（好返价）]
.REGEXEXTRACTALL("❹.*?(\-?\d+(?:\.\d+)?).*?❹🔞").NTH(6)
.VALUE()
-
[💰🤎赠送价到手详情]
.REGEXEXTRACTALL("❹.*?(?:价值|💰)(\-?\d+(?:\.\d+)?).*?❹🔞").NTH(6)
.VALUE())
.REGEXREPLACE("\-?\d+(?:\.\d+)?","❹$0❹🔞")

,CHAR(10),





([💰🤎到手价格（好返价）]
.REGEXEXTRACTALL("❺.*?(\-?\d+(?:\.\d+)?).*?❺🔞").NTH(1)
.VALUE()
-
[💰🤎赠送价到手详情]
.REGEXEXTRACTALL("❺.*?(?:价值|💰)(\-?\d+(?:\.\d+)?).*?❺🔞").NTH(1)
.VALUE())
.REGEXREPLACE("\-?\d+(?:\.\d+)?","❺$0❺🔞")

,CHAR(10),

([💰🤎到手价格（好返价）]
.REGEXEXTRACTALL("❺.*?(\-?\d+(?:\.\d+)?).*?❺🔞").NTH(2)
.VALUE()
-
[💰🤎赠送价到手详情]
.REGEXEXTRACTALL("❺.*?(?:价值|💰)(\-?\d+(?:\.\d+)?).*?❺🔞").NTH(2)
.VALUE())
.REGEXREPLACE("\-?\d+(?:\.\d+)?","❺$0❺🔞")

,CHAR(10),



([💰🤎到手价格（好返价）]
.REGEXEXTRACTALL("❺.*?(\-?\d+(?:\.\d+)?).*?❺🔞").NTH(3)
.VALUE()
-
[💰🤎赠送价到手详情]
.REGEXEXTRACTALL("❺.*?(?:价值|💰)(\-?\d+(?:\.\d+)?).*?❺🔞").NTH(3)
.VALUE())
.REGEXREPLACE("\-?\d+(?:\.\d+)?","❺$0❺🔞")

,CHAR(10),



([💰🤎到手价格（好返价）]
.REGEXEXTRACTALL("❺.*?(\-?\d+(?:\.\d+)?).*?❺🔞").NTH(4)
.VALUE()
-
[💰🤎赠送价到手详情]
.REGEXEXTRACTALL("❺.*?(?:价值|💰)(\-?\d+(?:\.\d+)?).*?❺🔞").NTH(4)
.VALUE())
.REGEXREPLACE("\-?\d+(?:\.\d+)?","❺$0❺🔞")

,CHAR(10),



([💰🤎到手价格（好返价）]
.REGEXEXTRACTALL("❺.*?(\-?\d+(?:\.\d+)?).*?❺🔞").NTH(5)
.VALUE()
-
[💰🤎赠送价到手详情]
.REGEXEXTRACTALL("❺.*?(?:价值|💰)(\-?\d+(?:\.\d+)?).*?❺🔞").NTH(5)
.VALUE())
.REGEXREPLACE("\-?\d+(?:\.\d+)?","❺$0❺🔞")


,CHAR(10),



([💰🤎到手价格（好返价）]
.REGEXEXTRACTALL("❻.*?(\-?\d+(?:\.\d+)?).*?❻🔞").NTH(1)
.VALUE()
-
[💰🤎赠送价到手详情]
.REGEXEXTRACTALL("❻.*?(?:价值|💰)(\-?\d+(?:\.\d+)?).*?❻🔞").NTH(1)
.VALUE())
.REGEXREPLACE("\-?\d+(?:\.\d+)?","❻$0❻🔞")

,CHAR(10),

([💰🤎到手价格（好返价）]
.REGEXEXTRACTALL("❻.*?(\-?\d+(?:\.\d+)?).*?❻🔞").NTH(2)
.VALUE()
-
[💰🤎赠送价到手详情]
.REGEXEXTRACTALL("❻.*?(?:价值|💰)(\-?\d+(?:\.\d+)?).*?❻🔞").NTH(2)
.VALUE())
.REGEXREPLACE("\-?\d+(?:\.\d+)?","❻$0❻🔞")

,CHAR(10),

([💰🤎到手价格（好返价）]
.REGEXEXTRACTALL("❻.*?(\-?\d+(?:\.\d+)?).*?❻🔞").NTH(3)
.VALUE()
-
[💰🤎赠送价到手详情]
.REGEXEXTRACTALL("❻.*?(?:价值|💰)(\-?\d+(?:\.\d+)?).*?❻🔞").NTH(3)
.VALUE())
.REGEXREPLACE("\-?\d+(?:\.\d+)?","❻$0❻🔞")

,CHAR(10),



([💰🤎到手价格（好返价）]
.REGEXEXTRACTALL("❻.*?(\-?\d+(?:\.\d+)?).*?❻🔞").NTH(4)
.VALUE()
-
[💰🤎赠送价到手详情]
.REGEXEXTRACTALL("❻.*?(?:价值|💰)(\-?\d+(?:\.\d+)?).*?❻🔞").NTH(4)
.VALUE())
.REGEXREPLACE("\-?\d+(?:\.\d+)?","❻$0❻🔞")

,CHAR(10),



([💰🤎到手价格（好返价）]
.REGEXEXTRACTALL("❼.*?(\-?\d+(?:\.\d+)?).*?❼🔞").NTH(1)
.VALUE()
-
[💰🤎赠送价到手详情]
.REGEXEXTRACTALL("❼.*?(?:价值|💰)(\-?\d+(?:\.\d+)?).*?❼🔞").NTH(1)
.VALUE())
.REGEXREPLACE("\-?\d+(?:\.\d+)?","❼$0❼🔞")

,CHAR(10),

([💰🤎到手价格（好返价）]
.REGEXEXTRACTALL("❼.*?(\-?\d+(?:\.\d+)?).*?❼🔞").NTH(2)
.VALUE()
-
[💰🤎赠送价到手详情]
.REGEXEXTRACTALL("❼.*?(?:价值|💰)(\-?\d+(?:\.\d+)?).*?❼🔞").NTH(2)
.VALUE())
.REGEXREPLACE("\-?\d+(?:\.\d+)?","❼$0❼🔞")

,CHAR(10),

([💰🤎到手价格（好返价）]
.REGEXEXTRACTALL("❼.*?(\-?\d+(?:\.\d+)?).*?❼🔞").NTH(3)
.VALUE()
-
[💰🤎赠送价到手详情]
.REGEXEXTRACTALL("❼.*?(?:价值|💰)(\-?\d+(?:\.\d+)?).*?❼🔞").NTH(3)
.VALUE())
.REGEXREPLACE("\-?\d+(?:\.\d+)?","❼$0❼🔞")

,CHAR(10),

([💰🤎到手价格（好返价）]
.REGEXEXTRACTALL("❼.*?(\-?\d+(?:\.\d+)?).*?❼🔞").NTH(4)
.VALUE()
-
[💰🤎赠送价到手详情]
.REGEXEXTRACTALL("❼.*?(?:价值|💰)(\-?\d+(?:\.\d+)?).*?❼🔞").NTH(4)
.VALUE())
.REGEXREPLACE("\-?\d+(?:\.\d+)?","❼$0❼🔞")


,CHAR(10),

([💰🤎到手价格（好返价）]
.REGEXEXTRACTALL("❽.*?(\-?\d+(?:\.\d+)?).*?❽🔞").NTH(1)
.VALUE()
-
[💰🤎赠送价到手详情]
.REGEXEXTRACTALL("❽.*?(?:价值|💰)(\-?\d+(?:\.\d+)?).*?❽🔞").NTH(1)
.VALUE())
.REGEXREPLACE("\-?\d+(?:\.\d+)?","❽$0❽🔞")

,CHAR(10),

([💰🤎到手价格（好返价）]
.REGEXEXTRACTALL("❽.*?(\-?\d+(?:\.\d+)?).*?❽🔞").NTH(2)
.VALUE()
-
[💰🤎赠送价到手详情]
.REGEXEXTRACTALL("❽.*?(?:价值|💰)(\-?\d+(?:\.\d+)?).*?❽🔞").NTH(2)
.VALUE())
.REGEXREPLACE("\-?\d+(?:\.\d+)?","❽$0❽🔞")

,CHAR(10),

([💰🤎到手价格（好返价）]
.REGEXEXTRACTALL("❽.*?(\-?\d+(?:\.\d+)?).*?❽🔞").NTH(3)
.VALUE()
-
[💰🤎赠送价到手详情]
.REGEXEXTRACTALL("❽.*?(?:价值|💰)(\-?\d+(?:\.\d+)?).*?❽🔞").NTH(3)
.VALUE())
.REGEXREPLACE("\-?\d+(?:\.\d+)?","❽$0❽🔞")

,CHAR(10),

([💰🤎到手价格（好返价）]
.REGEXEXTRACTALL("❽.*?(\-?\d+(?:\.\d+)?).*?❽🔞").NTH(4)
.VALUE()
-
[💰🤎赠送价到手详情]
.REGEXEXTRACTALL("❽.*?(?:价值|💰)(\-?\d+(?:\.\d+)?).*?❽🔞").NTH(4)
.VALUE())
.REGEXREPLACE("\-?\d+(?:\.\d+)?","❽$0❽🔞")



,CHAR(10),

([💰🤎到手价格（好返价）]
.REGEXEXTRACTALL("❾.*?(\-?\d+(?:\.\d+)?).*?❾🔞").NTH(1)
.VALUE()
-
[💰🤎赠送价到手详情]
.REGEXEXTRACTALL("❾.*?(?:价值|💰)(\-?\d+(?:\.\d+)?).*?❾🔞").NTH(1)
.VALUE())
.REGEXREPLACE("\-?\d+(?:\.\d+)?","❾$0❾🔞")

,CHAR(10),

([💰🤎到手价格（好返价）]
.REGEXEXTRACTALL("❾.*?(\-?\d+(?:\.\d+)?).*?❾🔞").NTH(2)
.VALUE()
-
[💰🤎赠送价到手详情]
.REGEXEXTRACTALL("❾.*?(?:价值|💰)(\-?\d+(?:\.\d+)?).*?❾🔞").NTH(2)
.VALUE())
.REGEXREPLACE("\-?\d+(?:\.\d+)?","❾$0❾🔞")

,CHAR(10),

([💰🤎到手价格（好返价）]
.REGEXEXTRACTALL("❾.*?(\-?\d+(?:\.\d+)?).*?❾🔞").NTH(3)
.VALUE()
-
[💰🤎赠送价到手详情]
.REGEXEXTRACTALL("❾.*?(?:价值|💰)(\-?\d+(?:\.\d+)?).*?❾🔞").NTH(3)
.VALUE())
.REGEXREPLACE("\-?\d+(?:\.\d+)?","❾$0❾🔞")

,CHAR(10),

([💰🤎到手价格（好返价）]
.REGEXEXTRACTALL("❾.*?(\-?\d+(?:\.\d+)?).*?❾🔞").NTH(4)
.VALUE()
-
[💰🤎赠送价到手详情]
.REGEXEXTRACTALL("❾.*?(?:价值|💰)(\-?\d+(?:\.\d+)?).*?❾🔞").NTH(4)
.VALUE())
.REGEXREPLACE("\-?\d+(?:\.\d+)?","❾$0❾🔞")


)


.REGEXREPLACE("-\d+(?:\.\d+)?",0)
.REGEXREPLACE("[❶-❾]0[❶-❾]🔞","")

.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
💰🤎到手价格（好返价）
CONCATENATE(
IF(
[💰🤎是否有好返价]
.REGEXEXTRACTALL("❶.*?❶🔞")
.REGEXMATCH("待计算")

，
([💰🤎原始低价]
.REGEXEXTRACTALL("❶.*?(\d+(?:\.\d+)?).*?❶🔞")
.VALUE()
-
[💰🤎返现价到手详情]
.REGEXEXTRACTALL("❶.*?(\d+(?:\.\d+)?).*?❶🔞")
.VALUE())
.REGEXREPLACE("-?\d+(?:\.\d+)?","❶$0❶🔞")

，

[💰🤎原始低价]
.REGEXEXTRACTALL("❶.*?❶🔞"))

,CHAR(10),

IF(
[💰🤎是否有好返价]
.REGEXEXTRACTALL("❷.*?❷🔞")
.ISBLANK()
,
[💰🤎原始低价]
.REGEXEXTRACTALL("❷.*?❷🔞")
,
IF(
[💰🤎是否有好返价]
.REGEXEXTRACTALL("❷.*?❷🔞")
.REGEXMATCH("直接提取")
,
[💰🤎返现价到手详情]
.REGEXEXTRACTALL("❷.*?❷🔞")
.REGEXREPLACE("([❶-❾]).*?💰(\d+(?:\.\d+)?).*?([❶-❾]🔞)","$1$2$3")
,
IF(
[💰🤎是否有好返价]
.REGEXEXTRACTALL("❷.*?❷🔞")
.REGEXMATCH("待计算")
,
([💰🤎原始低价]
.REGEXEXTRACTALL("❷.*?(\d+(?:\.\d+)?).*?❷🔞")
.VALUE()
-
[💰🤎返现价到手详情]
.REGEXEXTRACTALL("❷.*?(\d+(?:\.\d+)?).*?❷🔞")
.VALUE())
.REGEXREPLACE("\-?\d+(?:\.\d+)?","❷$0❷🔞")
,
""
)
)
)

,CHAR(10),

IF(
[💰🤎是否有好返价]
.REGEXEXTRACTALL("❸.*?❸🔞")
.ISBLANK()
,
[💰🤎原始低价]
.REGEXEXTRACTALL("❸.*?❸🔞")
,
IF(
[💰🤎是否有好返价]
.REGEXEXTRACTALL("❸.*?❸🔞")
.REGEXMATCH("直接提取")
,
[💰🤎返现价到手详情]
.REGEXEXTRACTALL("❸.*?❸🔞")
.REGEXREPLACE("([❶-❾]).*?💰(\d+(?:\.\d+)?).*?([❶-❾]🔞)","$1$2$3")
,
IF(
[💰🤎是否有好返价]
.REGEXEXTRACTALL("❸.*?❸🔞")
.REGEXMATCH("待计算")
,
([💰🤎原始低价]
.REGEXEXTRACTALL("❸.*?(\d+(?:\.\d+)?).*?❸🔞")
.VALUE()
-
[💰🤎返现价到手详情]
.REGEXEXTRACTALL("❸.*?(\d+(?:\.\d+)?).*?❸🔞")
.VALUE())
.REGEXREPLACE("\-?\d+(?:\.\d+)?","❸$0❸🔞")
,
""
)
)
)
,CHAR(10),

IF(
[💰🤎是否有好返价]
.REGEXEXTRACTALL("❹.*?❹🔞")
.ISBLANK()
,
[💰🤎原始低价]
.REGEXEXTRACTALL("❹.*?❹🔞")
,
IF(
[💰🤎是否有好返价]
.REGEXEXTRACTALL("❹.*?❹🔞")
.REGEXMATCH("直接提取")
,
[💰🤎返现价到手详情]
.REGEXEXTRACTALL("❹.*?❹🔞")
.REGEXREPLACE("([❶-❾]).*?💰(\d+(?:\.\d+)?).*?([❶-❾]🔞)","$1$2$3")
,
IF(
[💰🤎是否有好返价]
.REGEXEXTRACTALL("❹.*?❹🔞")
.REGEXMATCH("待计算")
,
([💰🤎原始低价]
.REGEXEXTRACTALL("❹.*?(\d+(?:\.\d+)?).*?❹🔞")
.VALUE()
-
[💰🤎返现价到手详情]
.REGEXEXTRACTALL("❹.*?(\d+(?:\.\d+)?).*?❹🔞")
.VALUE())
.REGEXREPLACE("\-?\d+(?:\.\d+)?","❹$0❹🔞")
,
""
)
)
)
,CHAR(10),

IF(
[💰🤎是否有好返价]
.REGEXEXTRACTALL("❺.*?❺🔞")
.ISBLANK()
,
[💰🤎原始低价]
.REGEXEXTRACTALL("❺.*?❺🔞")
,
IF(
[💰🤎是否有好返价]
.REGEXEXTRACTALL("❺.*?❺🔞")
.REGEXMATCH("直接提取")
,
[💰🤎返现价到手详情]
.REGEXEXTRACTALL("❺.*?❺🔞")
.REGEXREPLACE("([❶-❾]).*?💰(\d+(?:\.\d+)?).*?([❶-❾]🔞)","$1$2$3")
,
IF(
[💰🤎是否有好返价]
.REGEXEXTRACTALL("❺.*?❺🔞")
.REGEXMATCH("待计算")
,
([💰🤎原始低价]
.REGEXEXTRACTALL("❺.*?(\d+(?:\.\d+)?).*?❺🔞")
.VALUE()
-
[💰🤎返现价到手详情]
.REGEXEXTRACTALL("❺.*?(\d+(?:\.\d+)?).*?❺🔞")
.VALUE())
.REGEXREPLACE("\-?\d+(?:\.\d+)?","❺$0❺🔞")
,
""
)
)
)
,CHAR(10),

IF(
[💰🤎是否有好返价]
.REGEXEXTRACTALL("❻.*?❻🔞")
.ISBLANK()
,
[💰🤎原始低价]
.REGEXEXTRACTALL("❻.*?❻🔞")
,
IF(
[💰🤎是否有好返价]
.REGEXEXTRACTALL("❻.*?❻🔞")
.REGEXMATCH("直接提取")
,
[💰🤎返现价到手详情]
.REGEXEXTRACTALL("❻.*?❻🔞")
.REGEXREPLACE("([❶-❾]).*?💰(\d+(?:\.\d+)?).*?([❶-❾]🔞)","$1$2$3")
,
IF(
[💰🤎是否有好返价]
.REGEXEXTRACTALL("❻.*?❻🔞")
.REGEXMATCH("待计算")
,
([💰🤎原始低价]
.REGEXEXTRACTALL("❻.*?(\d+(?:\.\d+)?).*?❻🔞")
.VALUE()
-
[💰🤎返现价到手详情]
.REGEXEXTRACTALL("❻.*?(\d+(?:\.\d+)?).*?❻🔞")
.VALUE())
.REGEXREPLACE("\-?\d+(?:\.\d+)?","❻$0❻🔞")
,
""
)
)
)


,CHAR(10),

IF(
[💰🤎是否有好返价]
.REGEXEXTRACTALL("❼.*?❼🔞")
.ISBLANK()
,
[💰🤎原始低价]
.REGEXEXTRACTALL("❼.*?❼🔞")
,
IF(
[💰🤎是否有好返价]
.REGEXEXTRACTALL("❼.*?❼🔞")
.REGEXMATCH("直接提取")
,
[💰🤎返现价到手详情]
.REGEXEXTRACTALL("❼.*?❼🔞")
.REGEXREPLACE("([❶-❾]).*?💰(\d+(?:\.\d+)?).*?([❶-❾]🔞)","$1$2$3")
,
IF(
[💰🤎是否有好返价]
.REGEXEXTRACTALL("❼.*?❼🔞")
.REGEXMATCH("待计算")
,
([💰🤎原始低价]
.REGEXEXTRACTALL("❼.*?(\d+(?:\.\d+)?).*?❼🔞")
.VALUE()
-
[💰🤎返现价到手详情]
.REGEXEXTRACTALL("❼.*?(\d+(?:\.\d+)?).*?❼🔞")
.VALUE())
.REGEXREPLACE("\-?\d+(?:\.\d+)?","❼$0❼🔞")
,
""
)
)
)


,CHAR(10),

IF(
[💰🤎是否有好返价]
.REGEXEXTRACTALL("❽.*?❽🔞")
.ISBLANK()
,
[💰🤎原始低价]
.REGEXEXTRACTALL("❽.*?❽🔞")
,
IF(
[💰🤎是否有好返价]
.REGEXEXTRACTALL("❽.*?❽🔞")
.REGEXMATCH("直接提取")
,
[💰🤎返现价到手详情]
.REGEXEXTRACTALL("❽.*?❽🔞")
.REGEXREPLACE("([❶-❾]).*?💰(\d+(?:\.\d+)?).*?([❶-❾]🔞)","$1$2$3")
,
IF(
[💰🤎是否有好返价]
.REGEXEXTRACTALL("❽.*?❽🔞")
.REGEXMATCH("待计算")
,
([💰🤎原始低价]
.REGEXEXTRACTALL("❽.*?(\d+(?:\.\d+)?).*?❽🔞")
.VALUE()
-
[💰🤎返现价到手详情]
.REGEXEXTRACTALL("❽.*?(\d+(?:\.\d+)?).*?❽🔞")
.VALUE())
.REGEXREPLACE("\-?\d+(?:\.\d+)?","❽$0❽🔞")
,
""
)
)
)


,CHAR(10),

IF(
[💰🤎是否有好返价]
.REGEXEXTRACTALL("❾.*?❾🔞")
.ISBLANK()
,
[💰🤎原始低价]
.REGEXEXTRACTALL("❾.*?❾🔞")
,
IF(
[💰🤎是否有好返价]
.REGEXEXTRACTALL("❾.*?❾🔞")
.REGEXMATCH("直接提取")
,
[💰🤎返现价到手详情]
.REGEXEXTRACTALL("❾.*?❾🔞")
.REGEXREPLACE("([❶-❾]).*?💰(\d+(?:\.\d+)?).*?([❶-❾]🔞)","$1$2$3")
,
IF(
[💰🤎是否有好返价]
.REGEXEXTRACTALL("❾.*?❾🔞")
.REGEXMATCH("待计算")
,
([💰🤎原始低价]
.REGEXEXTRACTALL("❾.*?(\d+(?:\.\d+)?).*?❾🔞")
.VALUE()
-
[💰🤎返现价到手详情]
.REGEXEXTRACTALL("❾.*?(\d+(?:\.\d+)?).*?❾🔞")
.VALUE())
.REGEXREPLACE("\-?\d+(?:\.\d+)?","❾$0❾🔞")
,
""
)
)
)



)

.REGEXREPLACE("([❶-❾]🔞)\s?([❶-❾])","$1"&CHAR(10)&"$2")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
💰🤎会员价到手详情
[💰🤎商品价格（清洗版）]
.REGEXEXTRACTALL("([❶-❾].*?(?:88VIP|88Vip|88vip|PLUS|Plus|plus).*?\d+(?:\.\d+)?.*?[❶-❾]🔞)")
.ARRAYJOIN(CHAR(10))
.REGEXREPLACE("([❶-❾]).*?(88VIP|88Vip|88vip|PLUS|Plus|plus)(.*?)(💰\d+(?:\.\d+)?).*?([❶-❾]🔞)","$1$2$3$4$5")

.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
💰🤎金币价到手详情
[💰🤎商品价格（清洗版）]
.REGEXEXTRACTALL("([❶-❾]).*?(金币.*?\d+(?:\.\d+)?).*?([❶-❾]🔞)")
.ARRAYJOIN(CHAR(10))
.REGEXREPLACE(","，"")

.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
💰🤎赠送价到手详情
IF(
OR(
[💰🤎商品价格（清洗版）]
.REGEXEXTRACTALL("([❶-❾]).*?((?:入会|好评|会员|\+)?再?送.*?(?:价值|💰)\d+(?:\.\d+)?).*?([❶-❾]🔞)|([❶-❾]).*?((?:赠品|[赠送]).*?(?:价值|💰)\d+(?:\.\d+)?).*?([❶-❾]🔞)")
.ARRAYJOIN(CHAR(10))

.REGEXEXTRACTALL("💰\d+(?:\.\d+)?")
.ARRAYJOIN(CHAR(10))

=

[💰🩶原始高价]
.REGEXEXTRACTALL("💰\d+(?:\.\d+)?")
.ARRAYJOIN(CHAR(10))
，

[💰🤎商品价格（清洗版）]
.REGEXEXTRACTALL("([❶-❾]).*?((?:入会|好评|会员|\+)?再?送.*?(?:价值|💰)\d+(?:\.\d+)?).*?([❶-❾]🔞)|([❶-❾]).*?((?:赠品|[赠送]).*?(?:价值|💰)\d+(?:\.\d+)?).*?([❶-❾]🔞)")
.ARRAYJOIN(CHAR(10))

.REGEXEXTRACTALL("💰\d+(?:\.\d+)?")
.ARRAYJOIN(CHAR(10))

=

[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[💰编号（最终）]).[🔪🤍😀商品价格（原始版）]
.REGEXEXTRACTALL("💰\d+(?:\.\d+)?")
.ARRAYJOIN(CHAR(10))
）

，
[💰🤎商品价格（清洗版）]
.REGEXREPLACE("[买拍](\d+).*?[赠发送](\d+)","")
.REGEXREPLACE("[，,](价值)","👉$1")
.REGEXEXTRACTALL("([❶-❾]).*?((?:入会|好评|会员|\+)?再?送.*?(?:价值|💰)\d+(?:\.\d+)?).*?([❶-❾]🔞)|([❶-❾]).*?((?:赠品|[赠送]).*?(?:价值|💰)\d+(?:\.\d+)?).*?([❶-❾]🔞)")
.ARRAYJOIN(CHAR(10))
.REGEXREPLACE("\,","")
.REGEXREPLACE("(?:价值|到手)?(?:💰.*?)([❶-❾]🔞)","$1")

，

[💰🤎商品价格（清洗版）]
.REGEXREPLACE("买([一-十]|\d+)送([一-十]|\d+)","")
.REGEXREPLACE("[，,](价值)","👉$1")
.REGEXEXTRACTALL("([❶-❾]).*?((?:入会|好评|会员|\+)?再?送.*?(?:价值|💰)\d+(?:\.\d+)?).*?([❶-❾]🔞)|([❶-❾]).*?((?:赠品|[赠送]).*?(?:价值|💰)\d+(?:\.\d+)?).*?([❶-❾]🔞)")
.ARRAYJOIN(CHAR(10))
.REGEXREPLACE(",","")
.REGEXREPLACE("，＋","+")

)

.REGEXREPLACE("👉","，")

.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
💰🤎返现价到手详情
[💰🤎商品价格（清洗版）]
.REGEXEXTRACTALL("([❶-❾](?:首次|晒图|晒单|入会|(?:店铺)?会员)?(?:好评?)?再?返.*?[❶-❾]🔞)|♎️
|([❶-❾]).*?((?:首次|晒图|晒单|入会|(?:店铺)会员)?(?:好评?)?再?返.*?[❶-❾]🔞)|♎️
|([❶-❾]).*?((?:晒图|晒单|好返|好评)再?返?\s*\d+(?:\.\d+)?.*?[❶-❾]🔞)|♎️

")
.ARRAYJOIN(CHAR(10))
.REGEXREPLACE("\,","")

.REGEXREPLACE("\d+(?:\.\d+)?\-","最多")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
💰🤎凑单价到手详情
[💰🤎商品价格（清洗版）]
.REGEXEXTRACTALL("([❶-❾]).*?((?:首单|88vip\+|礼金\+)?凑单.*?💰\d+(?:\.\d+)?.*?[❶-❾]🔞)")
.ARRAYJOIN(CHAR(10))
.REGEXREPLACE("\,","")
.REGEXREPLACE("([❶-❾]).*?(首单|88vip\+|礼金\+)?(凑单.*?💰\d+(?:\.\d+)?).*?([❶-❾]🔞)","$1$2$3$4")


.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
💰🤎付款方式详情
[💰🤎商品价格（清洗版）]
.REGEXEXTRACTALL("([❶-❾]).*?((?:[幼成][猫狗犬])?(?:(?:[订定]金|[订定]\+).*?)?尾款💰?\s*\d+(?:\.\d+)?).*?([❶-❾]🔞)")
.ARRAYJOIN(CHAR(10))
.REGEXREPLACE(",","")


.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
⭕️💰💜折扣到手价详情
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[💰编号（最终）]).[🔪💜折算价格提取].LISTCOMBINE()
.REGEXEXTRACTALL("([❶-❾].*?(?:\d+折|折扣).*?[❶-❾]🔞)")
.ARRAYJOIN(CHAR(10))
.REGEXREPLACE("\,","")

.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
💰🤎付款方式文案(信息重组专用)IF(
[💰🤎付款方式详情]
.ISBLANK()
.NOT(),

[💰🤎付款方式详情]
.REGEXREPLACE("(定金|尾款)💰?(\d+(?:\.\d+)?)","$1¥$2")
.REGEXREPLACE("([❶-❾])(.*?)([❶-❾]🔞)","$1$2🤿$3")

,""
)

.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
💰起拍文案（规格中提取版）
CONCATENATE(

IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.REGEXMATCH("礼包")
，""，


[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("([❶])(?:\d+(?:\.\d+)?(?:"&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")?\*)+(\d+(?:\.\d+)?.*?[❶]🔞)")
.ARRAYJOIN(CHAR(10))
.REGEXREPLACE(",","")
)

,CHAR(10),

IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.REGEXMATCH("礼包")
，""，


[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("([❷])(?:\d+(?:\.\d+)?(?:"&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")?\*)+(\d+(?:\.\d+)?.*?[❷]🔞)")
.ARRAYJOIN(CHAR(10))
.REGEXREPLACE(",","")
)



,CHAR(10),

IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.REGEXMATCH("礼包")
，""，


[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("([❸])(?:\d+(?:\.\d+)?(?:"&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")?\*)+(\d+(?:\.\d+)?.*?[❸]🔞)")
.ARRAYJOIN(CHAR(10))
.REGEXREPLACE(",","")
)



,CHAR(10),

IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.REGEXMATCH("礼包")
，""，


[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("([❹])(?:\d+(?:\.\d+)?(?:"&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")?\*)+(\d+(?:\.\d+)?.*?[❹]🔞)")
.ARRAYJOIN(CHAR(10))
.REGEXREPLACE(",","")
)


,CHAR(10),

IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.REGEXMATCH("礼包")
，""，


[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("([❺])(?:\d+(?:\.\d+)?(?:"&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")?\*)+(\d+(?:\.\d+)?.*?[❺]🔞)")
.ARRAYJOIN(CHAR(10))
.REGEXREPLACE(",","")
)


,CHAR(10),

IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.REGEXMATCH("礼包")
，""，


[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("([❻])(?:\d+(?:\.\d+)?(?:"&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")?\*)+(\d+(?:\.\d+)?.*?[❻]🔞)")
.ARRAYJOIN(CHAR(10))
.REGEXREPLACE(",","")
)


,CHAR(10),

IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.REGEXMATCH("礼包")
，""，


[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("([❼])(?:\d+(?:\.\d+)?(?:"&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")?\*)+(\d+(?:\.\d+)?.*?[❼]🔞)")
.ARRAYJOIN(CHAR(10))
.REGEXREPLACE(",","")
)



,CHAR(10),

IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.REGEXMATCH("礼包")
，""，


[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("([❽])(?:\d+(?:\.\d+)?(?:"&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")?\*)+(\d+(?:\.\d+)?.*?[❽]🔞)")
.ARRAYJOIN(CHAR(10))
.REGEXREPLACE(",","")
)




,CHAR(10),

IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.REGEXMATCH("礼包")
，""，


[🧪🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("([❾])(?:\d+(?:\.\d+)?(?:"&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")?\*)+(\d+(?:\.\d+)?.*?[❾]🔞)")
.ARRAYJOIN(CHAR(10))
.REGEXREPLACE(",","")
)

)


.REGEXREPLACE("([❶-❾])(.*?[❶-❾]🔞)","$1拍$2")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
💰🤍😀起拍文案（起拍份数提取版）
[🧪 规格解构（基建）].FILTER(CurrentValue.[🧪编号（最终）]=[💰编号（最终）]).[🧪🤍😀起拍份数（原始版）]

.REGEXREPLACE([🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[💰编号（最终）]).[🧠下单文案-逻辑],"")

.REGEXEXTRACTALL(".*?拍.*")
.ARRAYJOIN(CHAR(10))

.REGEXREPLACE("([❶-❾])💰\d+(?:\.\d+)?[,。；，]?","$1")
.REGEXREPLACE("返.*?([❶-❾]🔞)","$1")
.REGEXREPLACE("([拍凑]\d+)[:：]","$1")

.REGEXREPLACE("(加[购车]|任?[拍买])1件?","")

.REGEXREPLACE("[❶-❾]\s*[❶-❾]🔞","")
.REGEXREPLACE("💰","¥")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
💰🤍😀起拍文案（信息重组专用）
CONCATENATE(
IF(
[💰🤍😀起拍文案（起拍份数提取版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.ISBLANK()
.NOT(),

[💰🤍😀起拍文案（起拍份数提取版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.ARRAYJOIN(CHAR(10))
,

[💰起拍文案（规格中提取版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.ARRAYJOIN(CHAR(10))
)

,CHAR(10),
IF(
[💰🤍😀起拍文案（起拍份数提取版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.ISBLANK()
.NOT(),
[💰🤍😀起拍文案（起拍份数提取版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.ARRAYJOIN(CHAR(10))
,
[💰起拍文案（规格中提取版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.ARRAYJOIN(CHAR(10))
)


,CHAR(10),
IF(
[💰🤍😀起拍文案（起拍份数提取版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.ISBLANK()
.NOT(),
[💰🤍😀起拍文案（起拍份数提取版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.ARRAYJOIN(CHAR(10))
,
[💰起拍文案（规格中提取版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.ARRAYJOIN(CHAR(10))
)


,CHAR(10),
IF(
[💰🤍😀起拍文案（起拍份数提取版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.ISBLANK()
.NOT(),
[💰🤍😀起拍文案（起拍份数提取版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.ARRAYJOIN(CHAR(10))
,
[💰起拍文案（规格中提取版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.ARRAYJOIN(CHAR(10))
)




,CHAR(10),
IF(
[💰🤍😀起拍文案（起拍份数提取版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.ISBLANK()
.NOT(),
[💰🤍😀起拍文案（起拍份数提取版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.ARRAYJOIN(CHAR(10))
,
[💰起拍文案（规格中提取版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.ARRAYJOIN(CHAR(10))
)


,CHAR(10),
IF(
[💰🤍😀起拍文案（起拍份数提取版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.ISBLANK()
.NOT(),
[💰🤍😀起拍文案（起拍份数提取版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.ARRAYJOIN(CHAR(10))
,
[💰起拍文案（规格中提取版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.ARRAYJOIN(CHAR(10))
)


,CHAR(10),
IF(
[💰🤍😀起拍文案（起拍份数提取版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.ISBLANK()
.NOT(),
[💰🤍😀起拍文案（起拍份数提取版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.ARRAYJOIN(CHAR(10))
,
[💰起拍文案（规格中提取版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.ARRAYJOIN(CHAR(10))
)



,CHAR(10),
IF(
[💰🤍😀起拍文案（起拍份数提取版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.ISBLANK()
.NOT(),
[💰🤍😀起拍文案（起拍份数提取版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.ARRAYJOIN(CHAR(10))
,
[💰起拍文案（规格中提取版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.ARRAYJOIN(CHAR(10))
)


,CHAR(10),
IF(
[💰🤍😀起拍文案（起拍份数提取版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.ISBLANK()
.NOT(),
[💰🤍😀起拍文案（起拍份数提取版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.ARRAYJOIN(CHAR(10))
,
[💰起拍文案（规格中提取版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.ARRAYJOIN(CHAR(10))
)



)


.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🔪💚领券文案 （信息重组专用版）
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[💰编号（最终）]).[🔪💚领券文案 （信息重组专用版）].LISTCOMBINE()
=====
💰💚叠券文案（信息重组专用）
[🔪💚领券文案 （信息重组专用版）]
.REGEXEXTRACTALL("([❶-❾].*?[❶-❾]🔞)")
.ARRAYJOIN(CHAR(10))
.REGEXREPLACE("\,","")

.REGEXREPLACE("([❶-❾]).*?([❶-❾]🔞)","$1，叠券$2")
.REGEXEXTRACTALL(".*")
.UNIQUE()

.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
💰🤎首单价到手文案（信息重组专用）
[💰🤎商品价格（清洗版）]
.REGEXEXTRACTALL("([❶-❾]).*?(首单|首购).*?([❶-❾]🔞)")
.ARRAYJOIN(CHAR(10))
.REGEXREPLACE("\,","")

.REGEXREPLACE("([❶-❾])(.*?[❶-❾]🔞)","$1+叠$2")

.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
💰🖤会员价类型（字段维护专用）
[💰🤎会员价到手详情]
.REGEXEXTRACTALL("([❶-❾]).*?(88VIP|88Vip|88vip|PLUS|Plus|plus).*?([❶-❾]🔞)")
.ARRAYJOIN(CHAR(10))
.REGEXREPLACE("\,","")


.REGEXREPLACE("vip|Vip","VIP")
.REGEXREPLACE("plus|Plus","PLUS")


.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
💰🤎会员价到手文案（信息重组专用）
[💰🤎会员价到手详情]
.REGEXEXTRACTALL("([❶-❾]).*?(88VIP|88Vip|88vip|PLUS|Plus|plus).*?([❶-❾]🔞)")
.ARRAYJOIN(CHAR(10))
.REGEXREPLACE("\,","")


.REGEXREPLACE("vip|Vip","VIP")
.REGEXREPLACE("plus|Plus","PLUS")


.REGEXREPLACE("([❶-❾])(.*?[❶-❾]🔞)","$1+叠$2")

.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
💰🤎金币价到手文案（信息重组专用）
[💰🤎金币价到手详情]
.REGEXEXTRACTALL("([❶-❾]).*?(金币).*?([❶-❾]🔞)")
.ARRAYJOIN(CHAR(10))
.REGEXREPLACE("\,","")


.REGEXREPLACE("([❶-❾])(.*?[❶-❾]🔞)","$1+叠$2")

.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
💰🤎凑单价到手文案（信息重组专用）
[💰🤎凑单价到手详情]
.REGEXEXTRACTALL("([❶-❾]).*?((?:凑单)(?:\+金币)?).*?([❶-❾]🔞)")
.ARRAYJOIN(CHAR(10))
.REGEXREPLACE("\,","")
.REGEXREPLACE("([❶-❾])(.*?[❶-❾]🔞)","$1+叠$2")


.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
💰🤎是否有赠品价
CONCATENATE(
IF(
[💰🤎赠送价到手详情]
.REGEXEXTRACTALL("❶.*?❶🔞")
.REGEXMATCH("(💰|价值).*?(\d+)")
，"❶📕有赠品价值❶🔞"
，"")
，CHAR(10)，

IF(
[💰🤎赠送价到手详情]
.REGEXEXTRACTALL("❷.*?❷🔞")
.REGEXMATCH("(💰|价值).*?(\d+)")
,"❷📕有赠品价值❷🔞"
,"")

，CHAR(10)，


IF(
[💰🤎赠送价到手详情]
.REGEXEXTRACTALL("❸.*?❸🔞")
.REGEXMATCH("(💰|价值).*?(\d+)")
,"❸📕有赠品价值❸🔞"
,"")

，CHAR(10)，


IF(
[💰🤎赠送价到手详情]
.REGEXEXTRACTALL("❹.*?❹🔞")
.REGEXMATCH("(💰|价值).*?(\d+)")
,"❹📕有赠品价值❹🔞"
,"")

，CHAR(10)，


IF(
[💰🤎赠送价到手详情]
.REGEXEXTRACTALL("❺.*?❺🔞")
.REGEXMATCH("(💰|价值).*?(\d+)")
,"❺📕有赠品价值❺🔞"
,"")

，CHAR(10)，


IF(
[💰🤎赠送价到手详情]
.REGEXEXTRACTALL("❻.*?❻🔞")
.REGEXMATCH("(💰|价值).*?(\d+)")
,"❻📕有赠品价值❻🔞"
,"")


，CHAR(10)，

IF(
[💰🤎赠送价到手详情]
.REGEXEXTRACTALL("❼.*?❼🔞")
.REGEXMATCH("(💰|价值).*?(\d+)")
,"❼📕有赠品价值❼🔞"
,"")



，CHAR(10)，

IF(
[💰🤎赠送价到手详情]
.REGEXEXTRACTALL("❽.*?❽🔞")
.REGEXMATCH("(💰|价值).*?(\d+)")
,"❽📕有赠品价值❽🔞"
,"")


，CHAR(10)，

IF(
[💰🤎赠送价到手详情]
.REGEXEXTRACTALL("❾.*?❾🔞")
.REGEXMATCH("(💰|价值).*?(\d+)")
,"❾📕有赠品价值❾🔞"
,"")




)

.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
💰🤎赠送到手文案(信息重组专用)
IF(
[💰🤎是否有赠品价]
.ISBLANK()
.NOT(),

[💰🤎赠送价到手详情]
.REGEXREPLACE("([❶-❾]).*?(?:💰|价值)(\d+(?:\.\d+)?).*?([❶-❾]🔞)","$1+赠品价值$2，$3")

,""
)
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
💰🤎是否有好返价
CONCATENATE(

IF(
[💰🤎返现价到手详情]
.REGEXEXTRACTALL("❶.*?❶🔞")
.ISBLANK()

,""
,

IF(
[💰🤎返现价到手详情]
.REGEXEXTRACTALL("❶.*?❶🔞")
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.COUNTA()>1

，"❶📗有好返价，直接提取❶🔞"
，
IF(

[💰🤎返现价到手详情]
.REGEXEXTRACTALL("❶.*?❶🔞")
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.COUNTA()=1

，"❶📕有好返价,待计算❶🔞"
，"❶📙无好返价❶🔞")))

，CHAR(10)，

IF(
[💰🤎返现价到手详情]
.REGEXEXTRACTALL("❷.*?❷🔞")
.ISBLANK()
,""
,
IF(
[💰🤎返现价到手详情]
.REGEXEXTRACTALL("❷.*?❷🔞")
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.COUNTA()>1

,"❷📗有好返价，直接提取❷🔞"
,
IF(

[💰🤎返现价到手详情]
.REGEXEXTRACTALL("❷.*?❷🔞")
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.COUNTA()=1

,"❷📕有好返价,待计算❷🔞"
,"❷📙无好返价❷🔞"
)
)
)

，CHAR(10)，
IF(
[💰🤎返现价到手详情]
.REGEXEXTRACTALL("❸.*?❸🔞")
.ISBLANK()
,""
,
IF(
[💰🤎返现价到手详情]
.REGEXEXTRACTALL("❸.*?❸🔞")
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.COUNTA()>1
,"❸📗有好返价，直接提取❸🔞"
,
IF(
[💰🤎返现价到手详情]
.REGEXEXTRACTALL("❸.*?❸🔞")
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.COUNTA()=1

,"❸📕有好返价,待计算❸🔞"
,"❸📙无好返价❸🔞"
)
)
)

，CHAR(10)，

IF(
[💰🤎返现价到手详情]
.REGEXEXTRACTALL("❹.*?❹🔞")
.ISBLANK()
,""
,
IF(
[💰🤎返现价到手详情]
.REGEXEXTRACTALL("❹.*?❹🔞")
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.COUNTA()>1
,"❹📗有好返价，直接提取❹🔞"
,
IF(

[💰🤎返现价到手详情]
.REGEXEXTRACTALL("❹.*?❹🔞")
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.COUNTA()=1

,"❹📕有好返价,待计算❹🔞"
,"❹📙无好返价❹🔞"
)
)
)

，CHAR(10)，

IF(
[💰🤎返现价到手详情]
.REGEXEXTRACTALL("❺.*?❺🔞")
.ISBLANK()
,""
,
IF(
[💰🤎返现价到手详情]
.REGEXEXTRACTALL("❺.*?❺🔞")
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.COUNTA()>1
,"❺📗有好返价，直接提取❺🔞"
,
IF(
[💰🤎返现价到手详情]
.REGEXEXTRACTALL("❺.*?❺🔞")
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.COUNTA()=1

,"❺📕有好返价,待计算❺🔞"
,"❺📙无好返价❺🔞"
)
)
)

，CHAR(10)，


IF(
[💰🤎返现价到手详情]
.REGEXEXTRACTALL("❻.*?❻🔞")
.ISBLANK()
,""
,
IF(
[💰🤎返现价到手详情]
.REGEXEXTRACTALL("❻.*?❻🔞")
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.COUNTA()>1
,"❻📗有好返价，直接提取❻🔞"
,
IF(
[💰🤎返现价到手详情]
.REGEXEXTRACTALL("❻.*?❻🔞")
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.COUNTA()=1

,"❻📕有好返价,待计算❻🔞"
,"❻📙无好返价❻🔞"
)
)
)


，CHAR(10)，

IF(
[💰🤎返现价到手详情]
.REGEXEXTRACTALL("❼.*?❼🔞")
.ISBLANK()
,""
,
IF(
[💰🤎返现价到手详情]
.REGEXEXTRACTALL("❼.*?❼🔞")
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.COUNTA()>1
,"❼📗有好返价，直接提取❼🔞"
,
IF(
[💰🤎返现价到手详情]
.REGEXEXTRACTALL("❼.*?❼🔞")
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.COUNTA()=1

,"❼📕有好返价,待计算❼🔞"
,"❼📙无好返价❼🔞"
)
)
)






，CHAR(10)，

IF(
[💰🤎返现价到手详情]
.REGEXEXTRACTALL("❽.*?❽🔞")
.ISBLANK()
,""
,
IF(
[💰🤎返现价到手详情]
.REGEXEXTRACTALL("❽.*?❽🔞")
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.COUNTA()>1
,"❽📗有好返价，直接提取❽🔞"
,
IF(
[💰🤎返现价到手详情]
.REGEXEXTRACTALL("❽.*?❽🔞")
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.COUNTA()=1

,"❽📕有好返价,待计算❽🔞"
,"❽📙无好返价❽🔞"
)
)
)

，CHAR(10)，

IF(
[💰🤎返现价到手详情]
.REGEXEXTRACTALL("❾.*?❾🔞")
.ISBLANK()
,""
,
IF(
[💰🤎返现价到手详情]
.REGEXEXTRACTALL("❾.*?❾🔞")
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.COUNTA()>1
,"❾📗有好返价，直接提取❾🔞"
,
IF(
[💰🤎返现价到手详情]
.REGEXEXTRACTALL("❾.*?❾🔞")
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.COUNTA()=1

,"❾📕有好返价,待计算❾🔞"
,"❾📙无好返价❾🔞"
)
)
)




)

.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
💰🤎返现到手文案(信息重组专用)
[💰🤎返现价到手详情]
.REGEXREPLACE("[,，。].*?([❶-❾]🔞)","$1")
.REGEXREPLACE("(\d+(?:\.\d+)?((超市)?卡|现金(红包)?|(现金)?红包)?)(最[终后])?(到手)?仅?💰\d+(?:\.\d+)?","$1")

.REGEXREPLACE("([❶-❾])(.*?[❶-❾]🔞)","$1+$2")
.REGEXREPLACE("🔥","")

.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
💰🤎到手价格(信息重组专用)
CONCATENATE(

IF(
OR(
[💰🤎赠送到手文案(信息重组专用)]
.REGEXEXTRACTALL("❶.*❶🔞")
.ISBLANK().NOT()
，

[💰🤎返现到手文案(信息重组专用)]
.REGEXEXTRACTALL("❶.*❶🔞")
.ISBLANK().NOT()
）
，
[💰🤎到手价格（终版）]
.REGEXEXTRACTALL("❶.*❶🔞")
.REGEXREPLACE("([❶-❾])(\d+(?:\.\d+)?[❶-❾]🔞)","$1相当于到手¥$2")

，
[💰🤎到手价格（终版）]
.REGEXEXTRACTALL("❶.*❶🔞")
.REGEXREPLACE("([❶-❾])(\d+(?:\.\d+)?[❶-❾]🔞)","$1低至¥$2")

)


，CHAR(10)，

IF(
OR(
[💰🤎赠送到手文案(信息重组专用)]
.REGEXEXTRACTALL("❷.*❷🔞")
.ISBLANK().NOT()
，

[💰🤎返现到手文案(信息重组专用)]
.REGEXEXTRACTALL("❷.*❷🔞")
.ISBLANK().NOT()
）
，
[💰🤎到手价格（终版）]
.REGEXEXTRACTALL("❷.*❷🔞")
.REGEXREPLACE("([❶-❾])(\d+(?:\.\d+)?[❶-❾]🔞)","$1相当于到手¥$2")
，
[💰🤎到手价格（终版）]
.REGEXEXTRACTALL("❷.*❷🔞")
.REGEXREPLACE("([❶-❾])(\d+(?:\.\d+)?[❶-❾]🔞)","$1低至¥$2")
)



，CHAR(10)，

IF(
OR(
[💰🤎赠送到手文案(信息重组专用)]
.REGEXEXTRACTALL("❸.*❸🔞")
.ISBLANK().NOT()
，

[💰🤎返现到手文案(信息重组专用)]
.REGEXEXTRACTALL("❸.*❸🔞")
.ISBLANK().NOT()
）
，
[💰🤎到手价格（终版）]
.REGEXEXTRACTALL("❸.*❸🔞")
.REGEXREPLACE("([❶-❾])(\d+(?:\.\d+)?[❶-❾]🔞)","$1相当于到手¥$2")
，
[💰🤎到手价格（终版）]
.REGEXEXTRACTALL("❸.*❸🔞")
.REGEXREPLACE("([❶-❾])(\d+(?:\.\d+)?[❶-❾]🔞)","$1低至¥$2")
)



，CHAR(10)，

IF(
OR(
[💰🤎赠送到手文案(信息重组专用)]
.REGEXEXTRACTALL("❹.*❹🔞")
.ISBLANK().NOT()
，

[💰🤎返现到手文案(信息重组专用)]
.REGEXEXTRACTALL("❹.*❹🔞")
.ISBLANK().NOT()
）
，
[💰🤎到手价格（终版）]
.REGEXEXTRACTALL("❹.*❹🔞")
.REGEXREPLACE("([❶-❾])(\d+(?:\.\d+)?[❶-❾]🔞)","$1相当于到手¥$2")
，
[💰🤎到手价格（终版）]
.REGEXEXTRACTALL("❹.*❹🔞")
.REGEXREPLACE("([❶-❾])(\d+(?:\.\d+)?[❶-❾]🔞)","$1低至¥$2")
)


，CHAR(10)，

IF(
OR(
[💰🤎赠送到手文案(信息重组专用)]
.REGEXEXTRACTALL("❺.*❺🔞")
.ISBLANK().NOT()
，

[💰🤎返现到手文案(信息重组专用)]
.REGEXEXTRACTALL("❺.*❺🔞")
.ISBLANK().NOT()
）
，
[💰🤎到手价格（终版）]
.REGEXEXTRACTALL("❺.*❺🔞")
.REGEXREPLACE("([❶-❾])(\d+(?:\.\d+)?[❶-❾]🔞)","$1相当于到手¥$2")
，
[💰🤎到手价格（终版）]
.REGEXEXTRACTALL("❺.*❺🔞")
.REGEXREPLACE("([❶-❾])(\d+(?:\.\d+)?[❶-❾]🔞)","$1低至¥$2")
)



，CHAR(10)，

IF(
OR(
[💰🤎赠送到手文案(信息重组专用)]
.REGEXEXTRACTALL("❻.*❻🔞")
.ISBLANK().NOT()
，

[💰🤎返现到手文案(信息重组专用)]
.REGEXEXTRACTALL("❻.*❻🔞")
.ISBLANK().NOT()
）
，
[💰🤎到手价格（终版）]
.REGEXEXTRACTALL("❻.*❻🔞")
.REGEXREPLACE("([❶-❾])(\d+(?:\.\d+)?[❶-❾]🔞)","$1相当于到手¥$2")
，
[💰🤎到手价格（终版）]
.REGEXEXTRACTALL("❻.*❻🔞")
.REGEXREPLACE("([❶-❾])(\d+(?:\.\d+)?[❶-❾]🔞)","$1低至¥$2")
)



，CHAR(10)，

IF(
OR(
[💰🤎赠送到手文案(信息重组专用)]
.REGEXEXTRACTALL("❼.*❼🔞")
.ISBLANK().NOT()
，

[💰🤎返现到手文案(信息重组专用)]
.REGEXEXTRACTALL("❼.*❼🔞")
.ISBLANK().NOT()
）
，
[💰🤎到手价格（终版）]
.REGEXEXTRACTALL("❼.*❼🔞")
.REGEXREPLACE("([❶-❾])(\d+(?:\.\d+)?[❶-❾]🔞)","$1相当于到手¥$2")
，
[💰🤎到手价格（终版）]
.REGEXEXTRACTALL("❼.*❼🔞")
.REGEXREPLACE("([❶-❾])(\d+(?:\.\d+)?[❶-❾]🔞)","$1低至¥$2")
)



，CHAR(10)，

IF(
OR(
[💰🤎赠送到手文案(信息重组专用)]
.REGEXEXTRACTALL("❽.*❽🔞")
.ISBLANK().NOT()
，

[💰🤎返现到手文案(信息重组专用)]
.REGEXEXTRACTALL("❽.*❽🔞")
.ISBLANK().NOT()
）
，
[💰🤎到手价格（终版）]
.REGEXEXTRACTALL("❽.*❽🔞")
.REGEXREPLACE("([❶-❾])(\d+(?:\.\d+)?[❶-❾]🔞)","$1相当于到手¥$2")
，
[💰🤎到手价格（终版）]
.REGEXEXTRACTALL("❽.*❽🔞")
.REGEXREPLACE("([❶-❾])(\d+(?:\.\d+)?[❶-❾]🔞)","$1低至¥$2")
)



，CHAR(10)，

IF(
OR(
[💰🤎赠送到手文案(信息重组专用)]
.REGEXEXTRACTALL("❾.*❾🔞")
.ISBLANK().NOT()
，

[💰🤎返现到手文案(信息重组专用)]
.REGEXEXTRACTALL("❾.*❾🔞")
.ISBLANK().NOT()
）
，
[💰🤎到手价格（终版）]
.REGEXEXTRACTALL("❾.*❾🔞")
.REGEXREPLACE("([❶-❾])(\d+(?:\.\d+)?[❶-❾]🔞)","$1相当于到手¥$2")
，
[💰🤎到手价格（终版）]
.REGEXEXTRACTALL("❾.*❾🔞")
.REGEXREPLACE("([❶-❾])(\d+(?:\.\d+)?[❶-❾]🔞)","$1低至¥$2")
)



）

.REGEXREPLACE("([❶-❾])(.*?)([❶-❾]🔞)","$1，$2，$3")

.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
💰到手价格详情（重组版）（1-3）
CONCATENATE(

[💰🤎付款方式文案(信息重组专用)]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(1)
，

[💰🤍😀起拍文案（信息重组专用）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(1)
，
[💰💚叠券文案（信息重组专用）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(1)
，
[💰🤎首单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(1)
,

[💰🤎会员价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(1)

,

[💰🤎金币价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(1)
，
[💰🤎凑单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(1)
，

[💰🤎赠送到手文案(信息重组专用)]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(1)
,
[💰🤎返现到手文案(信息重组专用)]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(1)
，

[💰🤎到手价格(信息重组专用)]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(1)


，
[💻🖤单价（字段维护专用）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(1)

，CHAR(10)，


[💰🤎付款方式文案(信息重组专用)]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(2)
，

[💰🤍😀起拍文案（信息重组专用）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(2)
，
[💰💚叠券文案（信息重组专用）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(2)
，

[💰🤎首单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(2)
,

[💰🤎会员价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(2)
，
[💰🤎金币价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(2)
，
[💰🤎凑单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(2)
，

[💰🤎赠送到手文案(信息重组专用)]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(2)
,
[💰🤎返现到手文案(信息重组专用)]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(2)
，

[💰🤎到手价格(信息重组专用)]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(2)

，
[💻🖤单价（字段维护专用）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(2)

，CHAR(10)，


[💰🤎付款方式文案(信息重组专用)]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(3)
，

[💰🤍😀起拍文案（信息重组专用）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(3)
，
[💰💚叠券文案（信息重组专用）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(3)
，

[💰🤎首单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(3)
,

[💰🤎会员价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(3)

，
[💰🤎金币价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(3)
，
[💰🤎凑单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(3)
，

[💰🤎赠送到手文案(信息重组专用)]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(3)
,
[💰🤎返现到手文案(信息重组专用)]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(3)
，

[💰🤎到手价格(信息重组专用)]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(3)

，
[💻🖤单价（字段维护专用）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(3)

，CHAR(10)，


[💰🤎付款方式文案(信息重组专用)]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(4)
，

[💰🤍😀起拍文案（信息重组专用）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(4)
，
[💰💚叠券文案（信息重组专用）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(4)
，

[💰🤎首单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(4)
,

[💰🤎会员价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(4)

，
[💰🤎金币价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(4)
，
[💰🤎凑单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(4)
，

[💰🤎赠送到手文案(信息重组专用)]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(4)
,
[💰🤎返现到手文案(信息重组专用)]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(4)
，

[💰🤎到手价格(信息重组专用)]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(4)

，
[💻🖤单价（字段维护专用）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(4)

，CHAR(10)，


[💰🤎付款方式文案(信息重组专用)]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(1)
，

[💰🤍😀起拍文案（信息重组专用）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(1)

，

[💰💚叠券文案（信息重组专用）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(1)
，

[💰🤎首单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(1)
,

[💰🤎会员价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(1)

,

[💰🤎金币价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(1)
，
[💰🤎凑单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(1)
，

[💰🤎赠送到手文案(信息重组专用)]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(1)
,
[💰🤎返现到手文案(信息重组专用)]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(1)
，

[💰🤎到手价格(信息重组专用)]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(1)


，
[💻🖤单价（字段维护专用）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(1)

，CHAR(10)，


[💰🤎付款方式文案(信息重组专用)]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(2)
，

[💰🤍😀起拍文案（信息重组专用）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(2)

，

[💰💚叠券文案（信息重组专用）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(2)
，

[💰🤎首单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(2)
,

[💰🤎会员价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(2)

,

[💰🤎金币价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(2)
，
[💰🤎凑单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(2)
，

[💰🤎赠送到手文案(信息重组专用)]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(2)
,
[💰🤎返现到手文案(信息重组专用)]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(2)
，

[💰🤎到手价格(信息重组专用)]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(2)
，
[💻🖤单价（字段维护专用）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(2)

，CHAR(10)，


[💰🤎付款方式文案(信息重组专用)]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(3)
，

[💰🤍😀起拍文案（信息重组专用）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(3)

，

[💰💚叠券文案（信息重组专用）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(3)
，

[💰🤎首单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(3)
,

[💰🤎会员价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(3)

,

[💰🤎金币价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(3)
，
[💰🤎凑单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(3)
，

[💰🤎赠送到手文案(信息重组专用)]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(3)
,
[💰🤎返现到手文案(信息重组专用)]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(3)
，

[💰🤎到手价格(信息重组专用)]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(3)


，
[💻🖤单价（字段维护专用）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(3)

，CHAR(10)，


[💰🤎付款方式文案(信息重组专用)]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(4)
，

[💰🤍😀起拍文案（信息重组专用）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(4)

，

[💰💚叠券文案（信息重组专用）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(4)
，

[💰🤎首单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(4)
,

[💰🤎会员价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(4)
,

[💰🤎金币价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(4)
，
[💰🤎凑单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(4)
，

[💰🤎赠送到手文案(信息重组专用)]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(4)
,
[💰🤎返现到手文案(信息重组专用)]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(4)
，

[💰🤎到手价格(信息重组专用)]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(4)


，
[💻🖤单价（字段维护专用）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(4)

，CHAR(10)，


[💰🤎付款方式文案(信息重组专用)]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(1)
，

[💰🤍😀起拍文案（信息重组专用）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(1)

，

[💰💚叠券文案（信息重组专用）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(1)
，

[💰🤎首单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(1)
,

[💰🤎会员价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(1)

,

[💰🤎金币价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(1)

，
[💰🤎凑单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(1)
，

[💰🤎赠送到手文案(信息重组专用)]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(1)
,
[💰🤎返现到手文案(信息重组专用)]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(1)
，

[💰🤎到手价格(信息重组专用)]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(1)

，
[💻🖤单价（字段维护专用）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(1)

，CHAR(10)，


[💰🤎付款方式文案(信息重组专用)]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(2)
，

[💰🤍😀起拍文案（信息重组专用）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(2)

，

[💰💚叠券文案（信息重组专用）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(2)
，

[💰🤎首单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(2)
,

[💰🤎会员价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(2)

,

[💰🤎金币价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(2)
，
[💰🤎凑单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(2)
，

[💰🤎赠送到手文案(信息重组专用)]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(2)
,
[💰🤎返现到手文案(信息重组专用)]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(2)
，

[💰🤎到手价格(信息重组专用)]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(2)
，
[💻🖤单价（字段维护专用）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(2)

，CHAR(10)，


[💰🤎付款方式文案(信息重组专用)]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(3)
，

[💰🤍😀起拍文案（信息重组专用）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(3)

，

[💰💚叠券文案（信息重组专用）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(3)
，

[💰🤎首单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(3)
,

[💰🤎会员价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(3)

,

[💰🤎金币价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(3)
，
[💰🤎凑单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(3)
，

[💰🤎赠送到手文案(信息重组专用)]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(3)
,
[💰🤎返现到手文案(信息重组专用)]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(3)
，

[💰🤎到手价格(信息重组专用)]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(3)

，
[💻🖤单价（字段维护专用）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(3)

，CHAR(10)，


[💰🤎付款方式文案(信息重组专用)]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(4)
，

[💰🤍😀起拍文案（信息重组专用）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(4)

，

[💰💚叠券文案（信息重组专用）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(4)
，

[💰🤎首单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(4)
,

[💰🤎会员价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(4)

,

[💰🤎金币价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(4)
，
[💰🤎凑单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(4)
，

[💰🤎赠送到手文案(信息重组专用)]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(4)
,
[💰🤎返现到手文案(信息重组专用)]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(4)
，

[💰🤎到手价格(信息重组专用)]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(4)

，
[💻🖤单价（字段维护专用）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(4)


）


.REGEXREPLACE("[❶-❾]🔞[❶-❾]","")
.REGEXREPLACE("([❶-❾])[,，；;。]\s*(低至|相当于到手)¥\d+(?:\.\d+)?","$1")
.REGEXREPLACE("([❶-❾])[,，；;。]","$1")
.REGEXREPLACE("[,，；;。]([❶-❾]🔞)","$1")


.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
💰到手价格详情（重组版）（4-6）
CONCATENATE(

[💰🤎付款方式文案(信息重组专用)]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(1)
，

[💰🤍😀起拍文案（信息重组专用）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(1)

，

[💰💚叠券文案（信息重组专用）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(1)
，

[💰🤎首单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(1)
,

[💰🤎会员价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(1)

,

[💰🤎金币价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(1)
，
[💰🤎凑单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(1)
，

[💰🤎赠送到手文案(信息重组专用)]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(1)
,
[💰🤎返现到手文案(信息重组专用)]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(1)
，

[💰🤎到手价格(信息重组专用)]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(1)

，
[💻🖤单价（字段维护专用）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(1)

，CHAR(10)，


[💰🤎付款方式文案(信息重组专用)]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(2)
，

[💰🤍😀起拍文案（信息重组专用）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(2)

，

[💰💚叠券文案（信息重组专用）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(2)
，

[💰🤎首单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(2)
,

[💰🤎会员价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(2)

,

[💰🤎金币价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(2)
，
[💰🤎凑单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(2)
，

[💰🤎赠送到手文案(信息重组专用)]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(2)
,
[💰🤎返现到手文案(信息重组专用)]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(2)
，

[💰🤎到手价格(信息重组专用)]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(2)


，
[💻🖤单价（字段维护专用）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(2)

，CHAR(10)，


[💰🤎付款方式文案(信息重组专用)]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(3)
，

[💰🤍😀起拍文案（信息重组专用）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(3)

，

[💰💚叠券文案（信息重组专用）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(3)
，

[💰🤎首单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(3)
,

[💰🤎会员价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(3)

,

[💰🤎金币价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(3)
，
[💰🤎凑单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(3)
，

[💰🤎赠送到手文案(信息重组专用)]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(3)
,
[💰🤎返现到手文案(信息重组专用)]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(3)
，

[💰🤎到手价格(信息重组专用)]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(3)

，
[💻🖤单价（字段维护专用）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(3)

，CHAR(10)，


[💰🤎付款方式文案(信息重组专用)]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(4)
，

[💰🤍😀起拍文案（信息重组专用）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(4)

，

[💰💚叠券文案（信息重组专用）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(4)
，

[💰🤎首单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(4)
,

[💰🤎会员价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(4)

,

[💰🤎金币价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(4)
，
[💰🤎凑单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(4)
，

[💰🤎赠送到手文案(信息重组专用)]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(4)
,
[💰🤎返现到手文案(信息重组专用)]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(4)
，

[💰🤎到手价格(信息重组专用)]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(4)

，
[💻🖤单价（字段维护专用）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(4)

，CHAR(10)，


[💰🤎付款方式文案(信息重组专用)]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(1)
，

[💰🤍😀起拍文案（信息重组专用）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(1)

，

[💰💚叠券文案（信息重组专用）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(1)
，

[💰🤎首单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(1)
,

[💰🤎会员价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(1)

,

[💰🤎金币价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(1)

，
[💰🤎凑单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(1)
，

[💰🤎赠送到手文案(信息重组专用)]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(1)
,
[💰🤎返现到手文案(信息重组专用)]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(1)
，

[💰🤎到手价格(信息重组专用)]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(1)

，
[💻🖤单价（字段维护专用）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(1)

，CHAR(10)，


[💰🤎付款方式文案(信息重组专用)]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(2)
，

[💰🤍😀起拍文案（信息重组专用）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(2)

，

[💰💚叠券文案（信息重组专用）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(2)
，

[💰🤎首单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(2)

,

[💰🤎金币价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(2)
,

[💰🤎会员价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(2)
，
[💰🤎凑单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(2)
，

[💰🤎赠送到手文案(信息重组专用)]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(2)
,
[💰🤎返现到手文案(信息重组专用)]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(2)
，

[💰🤎到手价格(信息重组专用)]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(2)

，
[💻🖤单价（字段维护专用）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(2)

，CHAR(10)，


[💰🤎付款方式文案(信息重组专用)]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(3)
，

[💰🤍😀起拍文案（信息重组专用）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(3)
，

[💰💚叠券文案（信息重组专用）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(3)
，

[💰🤎首单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(3)
,

[💰🤎会员价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(3)

,

[💰🤎金币价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(3)
，
[💰🤎凑单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(3)
，

[💰🤎赠送到手文案(信息重组专用)]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(3)
,
[💰🤎返现到手文案(信息重组专用)]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(3)
，

[💰🤎到手价格(信息重组专用)]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(3)


，
[💻🖤单价（字段维护专用）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(3)

，CHAR(10)，


[💰🤎付款方式文案(信息重组专用)]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(4)
，

[💰🤍😀起拍文案（信息重组专用）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(4)

，

[💰💚叠券文案（信息重组专用）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(4)
，

[💰🤎首单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(4)
,

[💰🤎会员价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(4)

,

[💰🤎金币价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(4)
，
[💰🤎凑单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(4)
，

[💰🤎赠送到手文案(信息重组专用)]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(4)
,
[💰🤎返现到手文案(信息重组专用)]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(4)
，

[💰🤎到手价格(信息重组专用)]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(4)

，
[💻🖤单价（字段维护专用）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(4)

，CHAR(10)，


[💰🤎付款方式文案(信息重组专用)]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(1)
，

[💰🤍😀起拍文案（信息重组专用）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(1)

，

[💰💚叠券文案（信息重组专用）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(1)
，

[💰🤎首单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(1)
,

[💰🤎会员价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(1)

,

[💰🤎金币价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(1)
，
[💰🤎凑单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(1)
，

[💰🤎赠送到手文案(信息重组专用)]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(1)
,
[💰🤎返现到手文案(信息重组专用)]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(1)
，

[💰🤎到手价格(信息重组专用)]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(1)

，
[💻🖤单价（字段维护专用）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(1)

，CHAR(10)，


[💰🤎付款方式文案(信息重组专用)]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(2)
，

[💰🤍😀起拍文案（信息重组专用）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(2)

，

[💰💚叠券文案（信息重组专用）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(2)
，

[💰🤎首单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(2)
,

[💰🤎会员价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(2)

,

[💰🤎金币价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(2)
，
[💰🤎凑单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(2)
，

[💰🤎赠送到手文案(信息重组专用)]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(2)
,
[💰🤎返现到手文案(信息重组专用)]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(2)
，

[💰🤎到手价格(信息重组专用)]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(2)

，
[💻🖤单价（字段维护专用）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(2)

，CHAR(10)，


[💰🤎付款方式文案(信息重组专用)]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(3)
，

[💰🤍😀起拍文案（信息重组专用）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(3)

，

[💰💚叠券文案（信息重组专用）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(3)
，

[💰🤎首单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(3)
,

[💰🤎会员价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(3)

,

[💰🤎金币价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(3)
，
[💰🤎凑单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(3)
，

[💰🤎赠送到手文案(信息重组专用)]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(3)
,
[💰🤎返现到手文案(信息重组专用)]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(3)
，

[💰🤎到手价格(信息重组专用)]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(3)

，
[💻🖤单价（字段维护专用）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(3)

，CHAR(10)，


[💰🤎付款方式文案(信息重组专用)]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(4)
，

[💰🤍😀起拍文案（信息重组专用）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(4)

，

[💰💚叠券文案（信息重组专用）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(4)
，

[💰🤎首单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(4)
,

[💰🤎会员价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(4)

,

[💰🤎金币价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(4)
，
[💰🤎凑单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(4)
，

[💰🤎赠送到手文案(信息重组专用)]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(4)
,
[💰🤎返现到手文案(信息重组专用)]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(4)
，

[💰🤎到手价格(信息重组专用)]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(4)

，
[💻🖤单价（字段维护专用）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(4)


）
.REGEXREPLACE("[❶-❾]🔞[❶-❾]","")
.REGEXREPLACE("([❶-❾])[,，；;。]\s*(低至|相当于到手)¥\d+(?:\.\d+)?","$1")
.REGEXREPLACE("([❶-❾])[,，；;。]","$1")
.REGEXREPLACE("[,，；;。]([❶-❾]🔞)","$1")


.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
💰到手价格详情（重组版）（7-9）
CONCATENATE(
[💰🤎付款方式文案(信息重组专用)]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(1)
，

[💰🤍😀起拍文案（信息重组专用）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(1)

，

[💰💚叠券文案（信息重组专用）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(1)
，

[💰🤎首单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(1)
,

[💰🤎会员价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(1)

,

[💰🤎金币价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(1)


，
[💰🤎凑单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(1)
，

[💰🤎赠送到手文案(信息重组专用)]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(1)
,
[💰🤎返现到手文案(信息重组专用)]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(1)
，

[💰🤎到手价格(信息重组专用)]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(1)

，
[💻🖤单价（字段维护专用）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(1)

，CHAR(10)，


[💰🤎付款方式文案(信息重组专用)]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(2)
，

[💰🤍😀起拍文案（信息重组专用）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(2)

，

[💰💚叠券文案（信息重组专用）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(2)
，

[💰🤎首单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(2)
,

[💰🤎会员价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(2)

,

[💰🤎金币价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(2)
，
[💰🤎凑单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(2)
，

[💰🤎赠送到手文案(信息重组专用)]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(2)
,
[💰🤎返现到手文案(信息重组专用)]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(2)
，

[💰🤎到手价格(信息重组专用)]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(2)

，
[💻🖤单价（字段维护专用）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(2)

，CHAR(10)，


[💰🤎付款方式文案(信息重组专用)]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(3)
，

[💰🤍😀起拍文案（信息重组专用）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(3)
，

[💰💚叠券文案（信息重组专用）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(3)
，

[💰🤎首单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(3)
,

[💰🤎会员价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(3)

,

[💰🤎金币价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(3)
，
[💰🤎凑单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(3)
，

[💰🤎赠送到手文案(信息重组专用)]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(3)
,
[💰🤎返现到手文案(信息重组专用)]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(3)
，

[💰🤎到手价格(信息重组专用)]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(3)


，
[💻🖤单价（字段维护专用）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(3)

，CHAR(10)，


[💰🤎付款方式文案(信息重组专用)]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(4)
，

[💰🤍😀起拍文案（信息重组专用）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(4)

，

[💰💚叠券文案（信息重组专用）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(4)
，

[💰🤎首单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(4)
,

[💰🤎会员价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(4)

,

[💰🤎金币价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(4)
，
[💰🤎凑单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(4)
，

[💰🤎赠送到手文案(信息重组专用)]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(4)
,
[💰🤎返现到手文案(信息重组专用)]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(4)
，

[💰🤎到手价格(信息重组专用)]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(4)


，
[💻🖤单价（字段维护专用）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(4)

，CHAR(10)，


[💰🤎付款方式文案(信息重组专用)]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(1)
，

[💰🤍😀起拍文案（信息重组专用）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(1)

，

[💰💚叠券文案（信息重组专用）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(1)
，

[💰🤎首单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(1)
,

[💰🤎会员价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(1)

,

[💰🤎金币价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(1)
，
[💰🤎凑单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(1)
，

[💰🤎赠送到手文案(信息重组专用)]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(1)
,
[💰🤎返现到手文案(信息重组专用)]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(1)
，

[💰🤎到手价格(信息重组专用)]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(1)
，
[💻🖤单价（字段维护专用）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(1)

，CHAR(10)，


[💰🤎付款方式文案(信息重组专用)]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(2)
，

[💰🤍😀起拍文案（信息重组专用）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(2)

，

[💰💚叠券文案（信息重组专用）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(2)
，

[💰🤎首单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(2)
,

[💰🤎会员价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(2)

,

[💰🤎金币价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(2)
，
[💰🤎凑单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(2)
，

[💰🤎赠送到手文案(信息重组专用)]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(2)
,
[💰🤎返现到手文案(信息重组专用)]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(2)
，

[💰🤎到手价格(信息重组专用)]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(2)


，
[💻🖤单价（字段维护专用）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(2)

，CHAR(10)，


[💰🤎付款方式文案(信息重组专用)]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(3)
，

[💰🤍😀起拍文案（信息重组专用）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(3)

，

[💰💚叠券文案（信息重组专用）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(3)
，

[💰🤎首单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(3)
,

[💰🤎会员价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(3)

,

[💰🤎金币价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(3)
，
[💰🤎凑单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(3)
，

[💰🤎赠送到手文案(信息重组专用)]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(3)
,
[💰🤎返现到手文案(信息重组专用)]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(3)
，

[💰🤎到手价格(信息重组专用)]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(3)

，
[💻🖤单价（字段维护专用）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(3)

，CHAR(10)，


[💰🤎付款方式文案(信息重组专用)]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(4)
，

[💰🤍😀起拍文案（信息重组专用）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(4)

，

[💰💚叠券文案（信息重组专用）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(4)
，

[💰🤎首单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(4)
,

[💰🤎会员价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(4)

,

[💰🤎金币价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(4)
，
[💰🤎凑单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(4)
，

[💰🤎赠送到手文案(信息重组专用)]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(4)
,
[💰🤎返现到手文案(信息重组专用)]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(4)
，

[💰🤎到手价格(信息重组专用)]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(4)

，
[💻🖤单价（字段维护专用）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(4)

，CHAR(10)，


[💰🤎付款方式文案(信息重组专用)]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(1)
，

[💰🤍😀起拍文案（信息重组专用）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(1)

，

[💰💚叠券文案（信息重组专用）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(1)
，

[💰🤎首单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(1)
,

[💰🤎会员价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(1)

,

[💰🤎金币价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(1)
，
[💰🤎凑单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(1)
，

[💰🤎赠送到手文案(信息重组专用)]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(1)
,
[💰🤎返现到手文案(信息重组专用)]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(1)
，

[💰🤎到手价格(信息重组专用)]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(1)

，
[💻🖤单价（字段维护专用）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(1)

，CHAR(10)，


[💰🤎付款方式文案(信息重组专用)]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(2)
，

[💰🤍😀起拍文案（信息重组专用）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(2)

，

[💰💚叠券文案（信息重组专用）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(2)
，

[💰🤎首单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(2)
,

[💰🤎会员价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(2)

,

[💰🤎金币价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(2)
，
[💰🤎凑单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(2)
，

[💰🤎赠送到手文案(信息重组专用)]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(2)
,
[💰🤎返现到手文案(信息重组专用)]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(2)
，

[💰🤎到手价格(信息重组专用)]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(2)

，
[💻🖤单价（字段维护专用）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(2)

，CHAR(10)，


[💰🤎付款方式文案(信息重组专用)]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(3)
，

[💰🤍😀起拍文案（信息重组专用）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(3)
，

[💰💚叠券文案（信息重组专用）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(3)
，

[💰🤎首单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(3)
,

[💰🤎会员价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(3)

,

[💰🤎金币价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(3)
，
[💰🤎凑单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(3)
，

[💰🤎赠送到手文案(信息重组专用)]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(3)
,
[💰🤎返现到手文案(信息重组专用)]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(3)
，

[💰🤎到手价格(信息重组专用)]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(3)

，
[💻🖤单价（字段维护专用）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(3)

，CHAR(10)，


[💰🤎付款方式文案(信息重组专用)]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(4)
，

[💰🤍😀起拍文案（信息重组专用）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(4)
，

[💰💚叠券文案（信息重组专用）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(4)
，

[💰🤎首单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(4)
,

[💰🤎会员价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(4)

,

[💰🤎金币价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(4)
，
[💰🤎凑单价到手文案（信息重组专用）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(4)
，

[💰🤎赠送到手文案(信息重组专用)]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(4)
,
[💰🤎返现到手文案(信息重组专用)]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(4)
，

[💰🤎到手价格(信息重组专用)]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(4)

，
[💻🖤单价（字段维护专用）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(4)

，CHAR(10)，


）
.REGEXREPLACE("[❶-❾]🔞[❶-❾]","")
.REGEXREPLACE("([❶-❾])[,，；;。]\s*(低至|相当于到手)¥\d+(?:\.\d+)?","$1")
.REGEXREPLACE("([❶-❾])[,，；;。]","$1")
.REGEXREPLACE("[,，；;。]([❶-❾]🔞)","$1")


.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
💰🖤到手价格详情（字段维护专用）
CONCATENATE(
[💰到手价格详情（重组版）（1-3）]
，CHAR(10),
[💰到手价格详情（重组版）（4-6）]
，CHAR(10),
[💰到手价格详情（重组版）（7-9）])

.REGEXREPLACE("，+","，")
.REGEXREPLACE("到手¥0","相当于0元购")
.REGEXREPLACE("返最多","最高返")
.REGEXREPLACE("(\d+(?:\.\d+)?)元?(?:现金)?(红包)","$1")

.REGEXREPLACE("([❶-❾])(\+)","$1")
.REGEXREPLACE("[❶-❾][❶-❾]🔞","")

.REGEXREPLACE("🤿",CHAR(10))
.REGEXREPLACE("(.{17,22})([,，。;；])", "$1"&CHAR(10))

.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====

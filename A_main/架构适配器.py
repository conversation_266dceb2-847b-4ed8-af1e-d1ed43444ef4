"""
架构适配器
解决emoji文件路径识别问题，保持中文文件夹结构的同时确保代码稳定运行
"""

import os
import sys
import importlib.util
from pathlib import Path
from typing import Dict, List, Any, Optional
import logging

class 架构适配器:
    """架构适配器 - 处理emoji路径和中文文件夹"""
    
    def __init__(self):
        self.logger = logging.getLogger('架构适配器')
        self.project_root = Path(__file__).parent.parent
        
        # 表格映射：emoji名称 -> 安全路径名称
        self.table_mapping = {
            "🧠 逻辑表（底库）": {
                "safe_name": "logic_table",
                "folder_path": "01_🧠 逻辑表（底库）",
                "processor_file": "logic_processor.py",
                "model_file": "logic_table_model.py"
            },
            "📁 信息获取（基建）": {
                "safe_name": "info_table", 
                "folder_path": "02_📁｜✍️ 信息获取(基建）",
                "processor_file": "info_processor.py",
                "model_file": "info_table_model.py"
            },
            "🎫 优惠券管理（底库）": {
                "safe_name": "coupon_table",
                "folder_path": "03_🎫 优惠券管理（底库）",
                "processor_file": "coupon_processor.py",
                "model_file": "coupon_table_model.py"
            },
            "Ⓜ️ 品牌识别（基建）": {
                "safe_name": "brand_table",
                "folder_path": "04_Ⓜ️｜✍️ 品牌识别（基建）",
                "processor_file": "brand_processor.py",
                "model_file": "brand_table_model.py"
            },
            "🛍️ 品类管理（基建）": {
                "safe_name": "category_table",
                "folder_path": "05_🛍️｜✍️ 品类｜口味｜制作工艺 ｜ 使用对象 ｜ 成长期（基建）",
                "processor_file": "category_processor.py", 
                "model_file": "category_table_model.py"
            },
            "🔪 SKU解构（基建）": {
                "safe_name": "sku_table",
                "folder_path": "06_🔪｜✍️ sku解构(基建)",
                "processor_file": "sku_processor.py",
                "model_file": "sku_table_model.py"
            },
            "🐤 SKU重组-网站专用（基建）": {
                "safe_name": "web_reorg_table",
                "folder_path": "07_🐤 sku重组-网站专用（基建）",
                "processor_file": "web_reorg_processor.py",
                "model_file": "web_reorg_table_model.py"
            },
            "🐽 SKU重组-社群专用（基建）": {
                "safe_name": "social_reorg_table", 
                "folder_path": "08_🐽 sku重组-社群专用 (基建)",
                "processor_file": "social_reorg_processor.py",
                "model_file": "social_reorg_table_model.py"
            },
            "🧪 规格解构（基建）": {
                "safe_name": "spec_table",
                "folder_path": "09_🧪 规格解构（基建）",
                "processor_file": "spec_processor.py",
                "model_file": "spec_table_model.py"
            },
            "💰 价格解构（基建）": {
                "safe_name": "price_table",
                "folder_path": "10_💰 价格解构（基建）",
                "processor_file": "price_processor.py",
                "model_file": "price_table_model.py"
            },
            "💻 单价计算（基建）": {
                "safe_name": "unit_price_table",
                "folder_path": "11_💻 单价计算（基建）",
                "processor_file": "unit_price_processor.py",
                "model_file": "unit_price_table_model.py"
            },
            "🔥 价格热度计算（基建）": {
                "safe_name": "price_trend_table",
                "folder_path": "12_🔥 价格热度计算（基建）",
                "processor_file": "price_trend_processor.py",
                "model_file": "price_trend_table_model.py"
            },
            "🤖 中转表（工具）": {
                "safe_name": "transfer_table",
                "folder_path": "13_🤖｜✍️ 中转表（工具）",
                "processor_file": "transfer_processor.py",
                "model_file": "transfer_table_model.py"
            }
        }
        
        # Emoji字段映射
        self.emoji_mapping = {
            "🧠": "logic",
            "📁": "info", 
            "🎫": "coupon",
            "Ⓜ️": "brand",
            "🛍️": "category",
            "🔪": "sku",
            "🐤": "web_reorg",
            "🐽": "social_reorg", 
            "🧪": "spec",
            "💰": "price",
            "💻": "unit_price",
            "🔥": "price_trend",
            "🤖": "transfer",
            "🍬": "mongo_sync",  # MongoDB同步字段
            "🥝": "mongo_sync",  # MongoDB同步字段备用
            "✍️": "manual"       # 手动输入字段标识
        }
    
    def get_safe_table_name(self, emoji_table_name: str) -> str:
        """获取表格的安全名称（用于代码中）"""
        mapping = self.table_mapping.get(emoji_table_name)
        return mapping["safe_name"] if mapping else emoji_table_name.replace(" ", "_")
    
    def get_table_folder_path(self, emoji_table_name: str) -> Path:
        """获取表格文件夹的完整路径"""
        mapping = self.table_mapping.get(emoji_table_name)
        if mapping:
            return self.project_root / "C_公式代码" / mapping["folder_path"]
        return None
    
    def get_processor_path(self, emoji_table_name: str) -> Path:
        """获取处理器文件的完整路径"""
        folder_path = self.get_table_folder_path(emoji_table_name)
        mapping = self.table_mapping.get(emoji_table_name)
        if folder_path and mapping:
            return folder_path / mapping["processor_file"]
        return None
    
    def get_model_path(self, emoji_table_name: str) -> Path:
        """获取模型文件的完整路径"""
        folder_path = self.get_table_folder_path(emoji_table_name)
        mapping = self.table_mapping.get(emoji_table_name)
        if folder_path and mapping:
            return folder_path / mapping["model_file"]
        return None
    
    def safe_import_processor(self, emoji_table_name: str):
        """安全导入处理器模块"""
        try:
            processor_path = self.get_processor_path(emoji_table_name)
            if processor_path and processor_path.exists():
                # 使用importlib动态导入
                spec = importlib.util.spec_from_file_location(
                    f"{self.get_safe_table_name(emoji_table_name)}_processor",
                    processor_path
                )
                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)
                
                # 查找处理器类
                for attr_name in dir(module):
                    attr = getattr(module, attr_name)
                    if (isinstance(attr, type) and 
                        attr_name.endswith('Processor') and 
                        attr_name != 'Processor'):
                        return attr()
                
                self.logger.warning(f"未找到处理器类: {emoji_table_name}")
                return None
                
            else:
                self.logger.warning(f"处理器文件不存在: {processor_path}")
                return None
                
        except Exception as e:
            self.logger.error(f"导入处理器失败 {emoji_table_name}: {e}")
            return None
    
    def safe_import_model(self, emoji_table_name: str):
        """安全导入模型模块"""
        try:
            model_path = self.get_model_path(emoji_table_name)
            if model_path and model_path.exists():
                spec = importlib.util.spec_from_file_location(
                    f"{self.get_safe_table_name(emoji_table_name)}_model",
                    model_path
                )
                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)
                
                # 查找模型类
                for attr_name in dir(module):
                    attr = getattr(module, attr_name)
                    if (isinstance(attr, type) and 
                        attr_name.endswith('Model') and 
                        attr_name != 'Model'):
                        return attr()
                
                self.logger.warning(f"未找到模型类: {emoji_table_name}")
                return None
                
            else:
                self.logger.warning(f"模型文件不存在: {model_path}")
                return None
                
        except Exception as e:
            self.logger.error(f"导入模型失败 {emoji_table_name}: {e}")
            return None
    
    def normalize_field_name(self, field_name: str) -> str:
        """标准化字段名称（用于代码中）"""
        # 替换emoji为安全字符
        normalized = field_name
        for emoji, safe_name in self.emoji_mapping.items():
            normalized = normalized.replace(emoji, safe_name)
        
        # 清理特殊字符
        normalized = normalized.replace("｜", "_")
        normalized = normalized.replace("✍️", "_manual")
        normalized = normalized.replace("（", "_")
        normalized = normalized.replace("）", "_")
        normalized = normalized.replace(" ", "_")
        normalized = normalized.replace("-", "_")
        
        # 移除多余的下划线
        while "__" in normalized:
            normalized = normalized.replace("__", "_")
        
        normalized = normalized.strip("_")
        
        return normalized
    
    def get_field_emoji(self, field_name: str) -> str:
        """从字段名称中提取emoji标识"""
        for emoji in self.emoji_mapping.keys():
            if field_name.startswith(emoji):
                return emoji
        return ""
    
    def is_manual_field(self, field_name: str) -> bool:
        """判断是否为手动输入字段"""
        return "✍️" in field_name
    
    def is_mongo_sync_field(self, field_name: str) -> bool:
        """判断是否为MongoDB同步字段"""
        return field_name.startswith("🍬") or field_name.startswith("🥝")
    
    def create_safe_file_structure(self):
        """创建安全的文件结构（如果需要）"""
        self.logger.info("检查文件结构...")
        
        missing_files = []
        
        for table_name, mapping in self.table_mapping.items():
            folder_path = self.get_table_folder_path(table_name)
            processor_path = self.get_processor_path(table_name)
            model_path = self.get_model_path(table_name)
            
            if not folder_path.exists():
                self.logger.warning(f"文件夹不存在: {folder_path}")
                continue
            
            if not processor_path.exists():
                missing_files.append(f"处理器: {processor_path}")
            
            if not model_path.exists():
                missing_files.append(f"模型: {model_path}")
        
        if missing_files:
            self.logger.info("缺失的文件:")
            for file in missing_files:
                self.logger.info(f"  - {file}")
        else:
            self.logger.info("✅ 文件结构完整")
    
    def get_all_table_names(self) -> List[str]:
        """获取所有表格名称"""
        return list(self.table_mapping.keys())
    
    def get_processing_order(self) -> List[str]:
        """获取处理顺序（按文件夹编号排序）"""
        return sorted(self.table_mapping.keys(), 
                     key=lambda x: self.table_mapping[x]["folder_path"])

# 全局实例
架构适配器实例 = 架构适配器()

# 便捷函数
def 获取安全表名(emoji_table_name: str) -> str:
    """获取表格的安全名称"""
    return 架构适配器实例.get_safe_table_name(emoji_table_name)

def 安全导入处理器(emoji_table_name: str):
    """安全导入处理器"""
    return 架构适配器实例.safe_import_processor(emoji_table_name)

def 标准化字段名(field_name: str) -> str:
    """标准化字段名称"""
    return 架构适配器实例.normalize_field_name(field_name)

def 获取字段emoji(field_name: str) -> str:
    """获取字段的emoji标识"""
    return 架构适配器实例.get_field_emoji(field_name)

# 测试代码
if __name__ == "__main__":
    adapter = 架构适配器()
    
    print("🧪 架构适配器测试")
    print("=" * 50)
    
    # 测试表格映射
    test_table = "🧪 规格解构（基建）"
    print(f"表格: {test_table}")
    print(f"安全名称: {adapter.get_safe_table_name(test_table)}")
    print(f"文件夹路径: {adapter.get_table_folder_path(test_table)}")
    print(f"处理器路径: {adapter.get_processor_path(test_table)}")
    print()
    
    # 测试字段名称标准化
    test_fields = [
        "🧪🤍规格✖️数量✖️起拍数量（终版）",
        "Ⓜ️🤍‼️品牌（标准格式）",
        "📁｜✍️商品信息（手动输入）",
        "🍬商品信息（原始版）"
    ]
    
    print("字段名称标准化测试:")
    for field in test_fields:
        normalized = adapter.normalize_field_name(field)
        emoji = adapter.get_field_emoji(field)
        is_manual = adapter.is_manual_field(field)
        is_mongo = adapter.is_mongo_sync_field(field)
        
        print(f"  原始: {field}")
        print(f"  标准: {normalized}")
        print(f"  Emoji: {emoji}")
        print(f"  手动: {is_manual}")
        print(f"  同步: {is_mongo}")
        print()
    
    # 检查文件结构
    adapter.create_safe_file_structure()
    
    print("✅ 架构适配器测试完成")

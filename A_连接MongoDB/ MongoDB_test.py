#日常测试用 MongoDB_test.py：双击运行，快速验证数据库是否能连接、数据是否正常；
#代码中连接数据库用 MongoDB_connector.py：所有表格的模型类都通过这个工具类连接，保证连接逻辑统一；
#配置信息优先用你的实际参数：工具类的默认参数已经填好了你的服务器地址、密码等，不用修改。

# #!/usr/local/bin/python3
# -*- coding: utf-8 -*-
import os
from pymongo import MongoClient
from pymongo.errors import ConnectionFailure, PyMongoError  # 仅保留支持的错误类

# 配置信息
CALC_CONFIG = {
    "date": "",
    "conditions": {},
    "limit": 0,
    "batch_size": 1000,
    "process_all": True
}

OUTPUT_FILE = r"/Volumes/阿方方备份/Nars-个人wiki/Nars-代码管理/📊计算结果合集/MongoDB.csv"
OUTPUT_DIR = os.path.dirname(OUTPUT_FILE)
os.makedirs(OUTPUT_DIR, exist_ok=True)

LOG_FILE = r"/Volumes/阿方方备份/Nars-个人wiki/Nars-代码管理/🗑️归档/MongoDB.log"
ARCHIVE_DIR = r"/Volumes/阿方方备份/Nars-个人wiki/Nars-代码管理/🗑️归档"
os.makedirs(ARCHIVE_DIR, exist_ok=True)

# MongoDB配置
MONGO_CONFIG = {
    "host": "*************",
    "port": 30017,
    "db": "petgo_prod",
    "user": "petgo_prod",
    "pwd": "@qweDec23p3",
    "collection": "wxauto",
    "auth_source": "petgo_prod"
}

FIELD_MAPPING = {
    "who": "🍬信息来源/发车人",
    "content": "🍬商品信息（原始）",
    "wxname": "🍬信息收集账号/人",
    "uuid": "🍬商品信息ID",
    "create_date": "🍬源信息发布时间",
    "platform": "📁所属平台",
    "insert_time": "📁入本表时间"
}

def main():
    try:
        # 连接MongoDB
        client = MongoClient(
            host=MONGO_CONFIG["host"],
            port=MONGO_CONFIG["port"],
            username=MONGO_CONFIG["user"],
            password=MONGO_CONFIG["pwd"],
            authSource=MONGO_CONFIG["auth_source"]
        )
        
        # 验证连接
        client.admin.command('ping')
        print("✅ 成功连接到MongoDB服务器")
        
        # 访问数据库和集合
        db = client[MONGO_CONFIG["db"]]
        collection = db[MONGO_CONFIG["collection"]]
        print(f"正在操作：数据库={MONGO_CONFIG['db']}，集合={MONGO_CONFIG['collection']}")
        
        # 配置查询字段
        projection = {field: 1 for field in FIELD_MAPPING.keys()}
        projection["_id"] = 0
        
        # 读取前5条数据测试
        print("\n===== 测试数据（前5条） =====")
        for i, doc in enumerate(collection.find({}, projection).limit(5), 1):
            print(f"\n第{i}条记录：")
            for eng_field, chn_field in FIELD_MAPPING.items():
                value = doc.get(eng_field, "【无数据】")
                if isinstance(value, str) and len(value) > 80:
                    value = value[:80] + "..."
                print(f"  {chn_field}: {value}")
        
        total = collection.count_documents({})
        print(f"\n===== 集合中共有 {total} 条记录 =====")

    except ConnectionFailure:
        print("❌ 连接失败：请检查服务器地址和端口是否正确")
    except PyMongoError as e:
        if "authentication failed" in str(e).lower():
            print("❌ 认证失败：请检查用户名、密码是否正确")
        else:
            print(f"❌ 操作出错：{str(e)}")
    except Exception as e:
        print(f"❌ 未知错误：{str(e)}")

if __name__ == "__main__":
    main()
    input("\n按任意键退出...")

🐽编号（最终）
TEXT([🐽 sku重组-社群专用 (基建)].COUNTIF(CurrentValue.[🐽编号（参考勿删！）]<[🐽编号（参考勿删！）])+1,"👀000000000")
=====
🐽编号（参考勿删！）
自增数字
=====
🍬商品信息（原始版）
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🐽编号（最终）]).[🍬商品信息（原始版）].LISTCOMBINE()
=====
🍬商品信息ID
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🐽编号（最终）]).[🍬商品信息ID].LISTCOMBINE()
=====
🧠品牌-逻辑
IF([🍬商品信息（原始版）].ISBLANK(),"",[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠品牌-逻辑]!="").[🧠品牌-逻辑].LISTCOMBINE().UNIQUE())
=====
🧠加法符号
IF([🍬商品信息（原始版）].ISBLANK(),"",[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠加法符号]!="").[🧠加法符号].LISTCOMBINE().UNIQUE())
=====
🧠商品价格-逻辑
IF([🍬商品信息（原始版）].ISBLANK(),"",[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠商品价格-逻辑]!="").[🧠商品价格-逻辑].LISTCOMBINE().UNIQUE())
=====
🧠商品介绍-逻辑
IF([🍬商品信息（原始版）].ISBLANK(),"",[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠商品介绍-逻辑]!="").[🧠商品介绍-逻辑].LISTCOMBINE().UNIQUE())
=====
🧠口味-逻辑
IF([🍬商品信息（原始版）].ISBLANK(),"",[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠口味-逻辑]!="").[🧠口味-逻辑].LISTCOMBINE().UNIQUE())
=====
🧠基础单位
IF([🍬商品信息（原始版）].ISBLANK(),"",[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠基础单位]!="").[🧠基础单位].LISTCOMBINE().UNIQUE())
=====
🧠初级单位
IF([🍬商品信息（原始版）].ISBLANK(),"",[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠初级单位]!="").[🧠初级单位].LISTCOMBINE().UNIQUE())
=====
🧠进阶单位
IF([🍬商品信息（原始版）].ISBLANK(),"",[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠进阶单位]!="").[🧠进阶单位].LISTCOMBINE().UNIQUE())
=====
🧠终极单位
IF([🍬商品信息（原始版）].ISBLANK(),"",[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠终极单位]!="").[🧠终极单位].LISTCOMBINE().UNIQUE())
=====
🧠高阶单位
IF([🍬商品信息（原始版）].ISBLANK(),"",[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠高阶单位]!="").[🧠高阶单位].LISTCOMBINE().UNIQUE())
=====
🧠单位合集
IF([🍬商品信息（原始版）].ISBLANK(),"",[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠单位合集]!="").[🧠单位合集].LISTCOMBINE().UNIQUE())
=====
🧠保质期-逻辑
IF([🍬商品信息（原始版）].ISBLANK(),"",[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠保质期-逻辑]!="").[🧠保质期-逻辑].LISTCOMBINE().UNIQUE())
=====
🧠赠送信息-逻辑
IF([🍬商品信息（原始版）].ISBLANK(),"",[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠赠送信息-逻辑]!="").[🧠赠送信息-逻辑].LISTCOMBINE().UNIQUE())
=====
🧠下单口令/链接-逻辑
IF([🍬商品信息（原始版）].ISBLANK(),"",[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠下单口令/链接-逻辑]!="").[🧠下单口令/链接-逻辑].LISTCOMBINE().UNIQUE())
=====
🧠拍凑份数-逻辑
IF([🍬商品信息（原始版）].ISBLANK(),"",[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠拍凑份数-逻辑]!="").[🧠拍凑份数-逻辑].LISTCOMBINE().UNIQUE())
=====
🧠商品名称-逻辑
IF([🍬商品信息（原始版）].ISBLANK(),"",[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠商品名称-逻辑]!="").[🧠商品名称-逻辑].LISTCOMBINE().UNIQUE())
=====
🧠下单文案-逻辑
IF([🍬商品信息（原始版）].ISBLANK(),"",[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠下单文案-逻辑]!="").[🧠下单文案-逻辑].LISTCOMBINE().UNIQUE())
=====
🧠需转义符号-逻辑
IF([🍬商品信息（原始版）].ISBLANK(),"",[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠需转义符号-逻辑]!="").[🧠需转义符号-逻辑].LISTCOMBINE().UNIQUE())
=====
🧠折算价格-逻辑
IF([🍬商品信息（原始版）].ISBLANK(),"",[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠折算价格-逻辑]!="").[🧠折算价格-逻辑].LISTCOMBINE().UNIQUE())
=====
🧠价格优势-逻辑
IF([🍬商品信息（原始版）].ISBLANK(),"",[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠价格优势-逻辑]!="").[🧠价格优势-逻辑].LISTCOMBINE().UNIQUE())
=====
🧠领券文案干扰信息-逻辑
IF([🍬商品信息（原始版）].ISBLANK(),"",[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠领券文案干扰信息-逻辑]!="").[🧠领券文案干扰信息-逻辑].LISTCOMBINE().UNIQUE())
=====
🧠领券文案-逻辑
IF([🍬商品信息（原始版）].ISBLANK(),"",[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠领券文案-逻辑]!="").[🧠领券文案-逻辑].LISTCOMBINE().UNIQUE())
=====
📁💜商品信息（终版分割版）
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🐽编号（最终）]).[📁💜商品信息（终版分割版）].LISTCOMBINE()
=====
📁🤍😀商品信息（纯净分割版）
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🐽编号（最终）]).[📁🤍😀商品信息（纯净分割版） -商品名称&价格专享版].LISTCOMBINE()
=====
📁🤍商品信息（纯净分割版）
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🐽编号（最终）]).[📁🤍商品信息（纯净分割版）].LISTCOMBINE()
=====
📁🧡SKU组合
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🐽编号（最终）]).[📁🧡SKU组合].LISTCOMBINE()
=====
📁选品定位符
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🐽编号（最终）]).[📁选品定位符].LISTCOMBINE()
=====
📁所属平台
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🐽编号（最终）]).[📁所属平台].LISTCOMBINE()
=====
📁源信息新鲜度(天)
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🐽编号（最终）]).[📁源信息新鲜度(天)]
=====
🔪💚商品名称（信息重组专用版）
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[🐽编号（最终）]).[🔪💚商品名称（信息重组专用版）].LISTCOMBINE()
=====
🔪💚赠送信息（信息重组专用版）
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[🐽编号（最终）]).[🔪💚赠送信息（信息重组专用版）].LISTCOMBINE()
=====
🔪💚商品价格（信息重组专用版）
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[🐽编号（最终）]).[🔪💚商品价格（信息重组专用版）].LISTCOMBINE()
=====
🔪💚保质期（信息重组专用版）
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[🐽编号（最终）]).[🔪💚保质期（信息重组专用版）].LISTCOMBINE()
=====
🔪💚价格优势（信息重组专用版）
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[🐽编号（最终）]).[🔪💚价格优势（信息重组专用版）].LISTCOMBINE()
=====
🔪💛先导文案
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[🐽编号（最终）]).[🔪💛先导文案].LISTCOMBINE()
=====
🔪💛先导文案(阉割版)
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[🐽编号（最终）]).[🔪💛先导文案(阉割版)].LISTCOMBINE()
=====
🔪💛推荐理由（合并版）
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[🐽编号（最终）]).[🔪💛推荐理由（合并版）].LISTCOMBINE()
=====
🔪💚领券文案 （信息重组专用版）
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[🐽编号（最终）]).[🔪💚领券文案 （信息重组专用版）].LISTCOMBINE()
=====
🔪💚商品介绍 （信息重组专用版）
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[🐽编号（最终）]).[🔪💚商品介绍 （信息重组专用版）].LISTCOMBINE()
=====
🔪💚下单文案 （信息重组专用版）
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[🐽编号（最终）]).[🔪💚下单文案 （信息重组专用版）].LISTCOMBINE()
=====
🔪💚拍凑份数（信息重组专用版）
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[🐽编号（最终）]).[🔪💚拍凑份数（信息重组专用版）].LISTCOMBINE()
=====
🔪🧡下单口令/链接提取
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[🐽编号（最终）]).[🔪🧡下单口令/链接提取].LISTCOMBINE()
=====
💻💚单价（信息重组专用版）
[💻 单价计算（基建）].FILTER(CurrentValue.[💻编号（最终）]=[🐽编号（最终）]).[💻💚单价（信息重组专用版）].LISTCOMBINE()
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
💰🤍😀付款类型
[💰 价格解构（基建）].FILTER(CurrentValue.[💰编号（最终）]=[🐽编号（最终）]).[💰🤍😀付款类型].LISTCOMBINE()
=====
🐽💛价格+单价
CONCATENATE(
[🔪💚商品价格（信息重组专用版）]
.REGEXEXTRACTALL("❶[\s\S]*?❶🔞")
,
[💻💚单价（信息重组专用版）]
.REGEXEXTRACTALL("❶[\s\S]*?❶🔞")


，CHAR(10),


[🔪💚商品价格（信息重组专用版）]
.REGEXEXTRACTALL("❷[\s\S]*?❷🔞")
,
[💻💚单价（信息重组专用版）]
.REGEXEXTRACTALL("❷[\s\S]*?❷🔞")



，CHAR(10),


[🔪💚商品价格（信息重组专用版）]
.REGEXEXTRACTALL("❸[\s\S]*?❸🔞")
,
[💻💚单价（信息重组专用版）]
.REGEXEXTRACTALL("❸[\s\S]*?❸🔞")


，CHAR(10),


[🔪💚商品价格（信息重组专用版）]
.REGEXEXTRACTALL("❹[\s\S]*?❹🔞")
,
[💻💚单价（信息重组专用版）]
.REGEXEXTRACTALL("❹[\s\S]*?❹🔞")


，CHAR(10),


[🔪💚商品价格（信息重组专用版）]
.REGEXEXTRACTALL("❺[\s\S]*?❺🔞")
,
[💻💚单价（信息重组专用版）]
.REGEXEXTRACTALL("❺[\s\S]*?❺🔞")


，CHAR(10),


[🔪💚商品价格（信息重组专用版）]
.REGEXEXTRACTALL("❻[\s\S]*?❻🔞")
,
[💻💚单价（信息重组专用版）]
.REGEXEXTRACTALL("❻[\s\S]*?❻🔞")


，CHAR(10),


[🔪💚商品价格（信息重组专用版）]
.REGEXEXTRACTALL("❼[\s\S]*?❼🔞")
,
[💻💚单价（信息重组专用版）]
.REGEXEXTRACTALL("❼[\s\S]*?❼🔞")


，CHAR(10),


[🔪💚商品价格（信息重组专用版）]
.REGEXEXTRACTALL("❽[\s\S]*?❽🔞")
,
[💻💚单价（信息重组专用版）]
.REGEXEXTRACTALL("❽[\s\S]*?❽🔞")

，CHAR(10),


[🔪💚商品价格（信息重组专用版）]
.REGEXEXTRACTALL("❾[\s\S]*?❾🔞")
,
[🔪💚商品价格（信息重组专用版）]
.REGEXEXTRACTALL("❾[\s\S]*?❾🔞")


)

.REGEXREPLACE("㊙️将不同字段的同一个定位符对应的信息合并为一个组合"，"")
.REGEXREPLACE("❶🔞\s*"&CHAR(10)&"❶",CHAR(10))
.REGEXREPLACE("❷🔞\s*"&CHAR(10)&"❷",CHAR(10))
.REGEXREPLACE("❸🔞\s*"&CHAR(10)&"❸",CHAR(10))
.REGEXREPLACE("❹🔞\s*"&CHAR(10)&"❹",CHAR(10))
.REGEXREPLACE("❺🔞\s*"&CHAR(10)&"❺",CHAR(10))
.REGEXREPLACE("❻🔞\s*"&CHAR(10)&"❻",CHAR(10))
.REGEXREPLACE("❼🔞\s*"&CHAR(10)&"❼",CHAR(10))
.REGEXREPLACE("❽🔞\s*"&CHAR(10)&"❽",CHAR(10))
.REGEXREPLACE("❾🔞\s*"&CHAR(10)&"❾",CHAR(10))

.REGEXREPLACE("㊙️将一个字段中的多个sku的中间定位符清除，同时将其拼合成一行而不是分行"，"")
.REGEXREPLACE("([❶-❾]🔞)([❶-❾])","，")

.REGEXREPLACE("㊙️多余字符清除"，"")
.REGEXREPLACE("[🔥✅]","，")
.REGEXREPLACE("([,，。；])+","$1")
.REGEXREPLACE("[,，。；]"&CHAR(10),CHAR(10))

.REGEXREPLACE("㊙️增加分隔符"，"")
.REGEXEXTRACTALL("[❶-❾][\s\S]*?[❶-❾]🔞")
.ARRAYJOIN(CHAR(10)&"〰️〰️〰️"&CHAR(10))

.REGEXREPLACE("㊙️清除空白空行"，"")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🐽💛商品名称+赠送信息
IF(
[🍬商品信息（原始版）]
.ISBLANK()
，""，
CONCATENATE(
[🔪💚商品名称（信息重组专用版）]
.REGEXEXTRACTALL("❶[\s\S]*?❶🔞")
,CHAR(10)，
[🔪💚赠送信息（信息重组专用版）]
.REGEXEXTRACTALL("❶[\s\S]*?❶🔞")


，CHAR(10),


[🔪💚商品名称（信息重组专用版）]
.REGEXEXTRACTALL("❷[\s\S]*?❷🔞")
,CHAR(10)，
[🔪💚赠送信息（信息重组专用版）]
.REGEXEXTRACTALL("❷[\s\S]*?❷🔞")



，CHAR(10),


[🔪💚商品名称（信息重组专用版）]
.REGEXEXTRACTALL("❸[\s\S]*?❸🔞")
,CHAR(10)，
[🔪💚赠送信息（信息重组专用版）]
.REGEXEXTRACTALL("❸[\s\S]*?❸🔞")


，CHAR(10),


[🔪💚商品名称（信息重组专用版）]
.REGEXEXTRACTALL("❹[\s\S]*?❹🔞")
,CHAR(10)，
[🔪💚赠送信息（信息重组专用版）]
.REGEXEXTRACTALL("❹[\s\S]*?❹🔞")


，CHAR(10),


[🔪💚商品名称（信息重组专用版）]
.REGEXEXTRACTALL("❺[\s\S]*?❺🔞")
,CHAR(10)，
[🔪💚赠送信息（信息重组专用版）]
.REGEXEXTRACTALL("❺[\s\S]*?❺🔞")


，CHAR(10),


[🔪💚商品名称（信息重组专用版）]
.REGEXEXTRACTALL("❻[\s\S]*?❻🔞")
,CHAR(10)，
[🔪💚赠送信息（信息重组专用版）]
.REGEXEXTRACTALL("❻[\s\S]*?❻🔞")


，CHAR(10),


[🔪💚商品名称（信息重组专用版）]
.REGEXEXTRACTALL("❼[\s\S]*?❼🔞")
,CHAR(10)，
[🔪💚赠送信息（信息重组专用版）]
.REGEXEXTRACTALL("❼[\s\S]*?❼🔞")


，CHAR(10),


[🔪💚商品名称（信息重组专用版）]
.REGEXEXTRACTALL("❽[\s\S]*?❽🔞")
,CHAR(10)，
[🔪💚赠送信息（信息重组专用版）]
.REGEXEXTRACTALL("❽[\s\S]*?❽🔞")

，CHAR(10),


[🔪💚商品名称（信息重组专用版）]
.REGEXEXTRACTALL("❾[\s\S]*?❾🔞")
,CHAR(10)，
[🔪💚赠送信息（信息重组专用版）]
.REGEXEXTRACTALL("❾[\s\S]*?❾🔞")


))

.REGEXREPLACE("㊙️将不同字段的同一个定位符对应的信息合并为一个组合"，"")
.REGEXREPLACE("❶🔞\s*"&CHAR(10)&"❶",CHAR(10))
.REGEXREPLACE("❷🔞\s*"&CHAR(10)&"❷",CHAR(10))
.REGEXREPLACE("❸🔞\s*"&CHAR(10)&"❸",CHAR(10))
.REGEXREPLACE("❹🔞\s*"&CHAR(10)&"❹",CHAR(10))
.REGEXREPLACE("❺🔞\s*"&CHAR(10)&"❺",CHAR(10))
.REGEXREPLACE("❻🔞\s*"&CHAR(10)&"❻",CHAR(10))
.REGEXREPLACE("❼🔞\s*"&CHAR(10)&"❼",CHAR(10))
.REGEXREPLACE("❽🔞\s*"&CHAR(10)&"❽",CHAR(10))
.REGEXREPLACE("❾🔞\s*"&CHAR(10)&"❾",CHAR(10))

.REGEXREPLACE("㊙️将一个字段中的多个sku的中间定位符清除"，"")
.REGEXREPLACE("([❶-❾]🔞)([❶-❾])",CHAR(10))

.REGEXREPLACE("㊙️多余字符清除"，"")
.REGEXREPLACE("[🔥✅]","，")
.REGEXREPLACE("([,，。；])+","$1")
.REGEXREPLACE("[,，。；]"&CHAR(10),CHAR(10))



.REGEXREPLACE("㊙️增加分隔符"，"")
.REGEXEXTRACTALL("[❶-❾][\s\S]*?[❶-❾]🔞")
.ARRAYJOIN(CHAR(10)&"〰️〰️〰️"&CHAR(10))

.REGEXREPLACE("㊙️清除空白空行"，"")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🐽💛下单文案+拍凑+下单口令+商品介绍
IF(
[🍬商品信息（原始版）]
.ISBLANK()
，""，
CONCATENATE(
[🔪💚下单文案 （信息重组专用版）]
.REGEXEXTRACTALL("❶[\s\S]*?❶🔞")
,CHAR(10)，
[🔪💚拍凑份数（信息重组专用版）]
.REGEXEXTRACTALL("❶[\s\S]*?❶🔞")
,CHAR(10)，
[🔪🧡下单口令/链接提取]
.REGEXEXTRACTALL("❶[\s\S]*?❶🔞")
,CHAR(10)，
[🔪💚商品介绍 （信息重组专用版）]
.REGEXEXTRACTALL("❶[\s\S]*?❶🔞")


，CHAR(10),

[🔪💚下单文案 （信息重组专用版）]
.REGEXEXTRACTALL("❷[\s\S]*?❷🔞")
,CHAR(10)，
[🔪💚拍凑份数（信息重组专用版）]
.REGEXEXTRACTALL("❷[\s\S]*?❷🔞")
,CHAR(10)，
[🔪🧡下单口令/链接提取]
.REGEXEXTRACTALL("❷[\s\S]*?❷🔞")
,CHAR(10)，
[🔪💚商品介绍 （信息重组专用版）]
.REGEXEXTRACTALL("❷[\s\S]*?❷🔞")



，CHAR(10),


[🔪💚下单文案 （信息重组专用版）]
.REGEXEXTRACTALL("❸[\s\S]*?❸🔞")
,CHAR(10)，
[🔪💚拍凑份数（信息重组专用版）]
.REGEXEXTRACTALL("❸[\s\S]*?❸🔞")
,CHAR(10)，
[🔪🧡下单口令/链接提取]
.REGEXEXTRACTALL("❸[\s\S]*?❸🔞")
,CHAR(10)，
[🔪💚商品介绍 （信息重组专用版）]
.REGEXEXTRACTALL("❸[\s\S]*?❸🔞")


，CHAR(10),

[🔪💚下单文案 （信息重组专用版）]
.REGEXEXTRACTALL("❹[\s\S]*?❹🔞")
,CHAR(10)，
[🔪💚拍凑份数（信息重组专用版）]
.REGEXEXTRACTALL("❹[\s\S]*?❹🔞")
,CHAR(10)，
[🔪🧡下单口令/链接提取]
.REGEXEXTRACTALL("❹[\s\S]*?❹🔞")
,CHAR(10)，
[🔪💚商品介绍 （信息重组专用版）]
.REGEXEXTRACTALL("❹[\s\S]*?❹🔞")


，CHAR(10),

[🔪💚下单文案 （信息重组专用版）]
.REGEXEXTRACTALL("❺[\s\S]*?❺🔞")
,CHAR(10)，
[🔪💚拍凑份数（信息重组专用版）]
.REGEXEXTRACTALL("❺[\s\S]*?❺🔞")
,CHAR(10)，
[🔪🧡下单口令/链接提取]
.REGEXEXTRACTALL("❺[\s\S]*?❺🔞")
,CHAR(10)，
[🔪💚商品介绍 （信息重组专用版）]
.REGEXEXTRACTALL("❺[\s\S]*?❺🔞")


，CHAR(10),

[🔪💚下单文案 （信息重组专用版）]
.REGEXEXTRACTALL("❻[\s\S]*?❻🔞")
,CHAR(10)，
[🔪💚拍凑份数（信息重组专用版）]
.REGEXEXTRACTALL("❻[\s\S]*?❻🔞")
,CHAR(10)，
[🔪🧡下单口令/链接提取]
.REGEXEXTRACTALL("❻[\s\S]*?❻🔞")
,CHAR(10)，
[🔪💚商品介绍 （信息重组专用版）]
.REGEXEXTRACTALL("❻[\s\S]*?❻🔞")


，CHAR(10),

[🔪💚下单文案 （信息重组专用版）]
.REGEXEXTRACTALL("❼[\s\S]*?❼🔞")
,CHAR(10)，
[🔪💚拍凑份数（信息重组专用版）]
.REGEXEXTRACTALL("❼[\s\S]*?❼🔞")
,CHAR(10)，
[🔪🧡下单口令/链接提取]
.REGEXEXTRACTALL("❼[\s\S]*?❼🔞")
,CHAR(10)，
[🔪💚商品介绍 （信息重组专用版）]
.REGEXEXTRACTALL("❼[\s\S]*?❼🔞")


，CHAR(10),



[🔪💚下单文案 （信息重组专用版）]
.REGEXEXTRACTALL("❽[\s\S]*?❽🔞")
,CHAR(10)，
[🔪💚拍凑份数（信息重组专用版）]
.REGEXEXTRACTALL("❽[\s\S]*?❽🔞")
,CHAR(10)，
[🔪🧡下单口令/链接提取]
.REGEXEXTRACTALL("❽[\s\S]*?❽🔞")
,CHAR(10)，
[🔪💚商品介绍 （信息重组专用版）]
.REGEXEXTRACTALL("❽[\s\S]*?❽🔞")

，CHAR(10),

[🔪💚下单文案 （信息重组专用版）]
.REGEXEXTRACTALL("❾[\s\S]*?❾🔞")
,CHAR(10)，
[🔪💚拍凑份数（信息重组专用版）]
.REGEXEXTRACTALL("❾[\s\S]*?❾🔞")
,CHAR(10)，
[🔪🧡下单口令/链接提取]
.REGEXEXTRACTALL("❾[\s\S]*?❾🔞")
,CHAR(10)，
[🔪💚商品介绍 （信息重组专用版）]
.REGEXEXTRACTALL("❾[\s\S]*?❾🔞")


))

.REGEXREPLACE("㊙️将不同字段的同一个定位符对应的信息合并为一个组合"，"")
.REGEXREPLACE("❶🔞\s*"&CHAR(10)&"❶",CHAR(10))
.REGEXREPLACE("❷🔞\s*"&CHAR(10)&"❷",CHAR(10))
.REGEXREPLACE("❸🔞\s*"&CHAR(10)&"❸",CHAR(10))
.REGEXREPLACE("❹🔞\s*"&CHAR(10)&"❹",CHAR(10))
.REGEXREPLACE("❺🔞\s*"&CHAR(10)&"❺",CHAR(10))
.REGEXREPLACE("❻🔞\s*"&CHAR(10)&"❻",CHAR(10))
.REGEXREPLACE("❼🔞\s*"&CHAR(10)&"❼",CHAR(10))
.REGEXREPLACE("❽🔞\s*"&CHAR(10)&"❽",CHAR(10))
.REGEXREPLACE("❾🔞\s*"&CHAR(10)&"❾",CHAR(10))

.REGEXREPLACE("㊙️将一个字段中的多个sku的中间定位符清除"，"")
.REGEXREPLACE("([❶-❾]🔞)([❶-❾])",CHAR(10))

.REGEXREPLACE("㊙️多余字符清除"，"")
.REGEXREPLACE("[🔥✅]","，")
.REGEXREPLACE("([,，。；])+","$1")
.REGEXREPLACE("[,，。；]"&CHAR(10),CHAR(10))

.REGEXREPLACE("㊙️增加分隔符"，"")
.REGEXEXTRACTALL("[❶-❾][\s\S]*?[❶-❾]🔞")
.ARRAYJOIN(CHAR(10)&"〰️〰️〰️"&CHAR(10))

.REGEXREPLACE("㊙️清除空白空行"，"")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🐽💛商品名称+价格+保质期（信息重组专用版）
IF(
[🍬商品信息（原始版）]
.ISBLANK()
，""，

CONCATENATE(
CONCATENATE(
[🐽💛商品名称+赠送信息]
.REGEXEXTRACTALL("❶[\s\S]*?❶🔞")
，CHAR(10)，
[🐽💛价格+单价]
.REGEXEXTRACTALL("❶[\s\S]*?❶🔞")
，CHAR(10)，
[🔪💚保质期（信息重组专用版）]
.REGEXEXTRACTALL("❶[\s\S]*?❶🔞")
)


,CHAR(10),

CONCATENATE(
[🐽💛商品名称+赠送信息]
.REGEXEXTRACTALL("❷[\s\S]*?❷🔞")
，CHAR(10)，
[🐽💛价格+单价]
.REGEXEXTRACTALL("❷[\s\S]*?❷🔞")
，CHAR(10)，

[🔪💚保质期（信息重组专用版）]
.REGEXEXTRACTALL("❷[\s\S]*?❷🔞")
)


,CHAR(10),

CONCATENATE(
[🐽💛商品名称+赠送信息]
.REGEXEXTRACTALL("❸[\s\S]*?❸🔞")
，CHAR(10)，
[🐽💛价格+单价]
.REGEXEXTRACTALL("❸[\s\S]*?❸🔞")
，CHAR(10)，

[🔪💚保质期（信息重组专用版）]
.REGEXEXTRACTALL("❸[\s\S]*?❸🔞")
)


,CHAR(10),

CONCATENATE(
[🐽💛商品名称+赠送信息]
.REGEXEXTRACTALL("❹[\s\S]*?❹🔞")
，CHAR(10)，
[🐽💛价格+单价]
.REGEXEXTRACTALL("❹[\s\S]*?❹🔞")
，CHAR(10)，

[🔪💚保质期（信息重组专用版）]
.REGEXEXTRACTALL("❹[\s\S]*?❹🔞")

)


,CHAR(10),

CONCATENATE(
[🐽💛商品名称+赠送信息]
.REGEXEXTRACTALL("❺[\s\S]*?❺🔞")
，CHAR(10)，
[🐽💛价格+单价]
.REGEXEXTRACTALL("❺[\s\S]*?❺🔞")
，CHAR(10)，

[🔪💚保质期（信息重组专用版）]
.REGEXEXTRACTALL("❺[\s\S]*?❺🔞")
)


,CHAR(10),

CONCATENATE(
[🐽💛商品名称+赠送信息]
.REGEXEXTRACTALL("❻[\s\S]*?❻🔞")
，CHAR(10)，
[🐽💛价格+单价]
.REGEXEXTRACTALL("❻[\s\S]*?❻🔞")
，CHAR(10)，

[🔪💚保质期（信息重组专用版）]
.REGEXEXTRACTALL("❻[\s\S]*?❻🔞")

)


,CHAR(10),


CONCATENATE(
[🐽💛商品名称+赠送信息]
.REGEXEXTRACTALL("❼[\s\S]*?❼🔞")
，CHAR(10)，
[🐽💛价格+单价]
.REGEXEXTRACTALL("❼[\s\S]*?❼🔞")
，CHAR(10)，

[🔪💚保质期（信息重组专用版）]
.REGEXEXTRACTALL("❼[\s\S]*?❼🔞")
)


,CHAR(10),


CONCATENATE(
[🐽💛商品名称+赠送信息]
.REGEXEXTRACTALL("❽[\s\S]*?❽🔞")
，CHAR(10)，
[🐽💛价格+单价]
.REGEXEXTRACTALL("❽[\s\S]*?❽🔞")
，CHAR(10)，

[🔪💚保质期（信息重组专用版）]
.REGEXEXTRACTALL("❽[\s\S]*?❽🔞")

)

,CHAR(10),


CONCATENATE(
[🐽💛商品名称+赠送信息]
.REGEXEXTRACTALL("❾[\s\S]*?❾🔞")
，CHAR(10)，
[🐽💛价格+单价]
.REGEXEXTRACTALL("❾[\s\S]*?❾🔞")
，CHAR(10)，

[🔪💚保质期（信息重组专用版）]
.REGEXEXTRACTALL("❾[\s\S]*?❾🔞")
)

))


.REGEXREPLACE("㊙️将不同字段的同一个定位符对应的信息合并为一个组合"，"")
.REGEXREPLACE("❶🔞\s*"&CHAR(10)&"❶",CHAR(10))
.REGEXREPLACE("❷🔞\s*"&CHAR(10)&"❷",CHAR(10))
.REGEXREPLACE("❸🔞\s*"&CHAR(10)&"❸",CHAR(10))
.REGEXREPLACE("❹🔞\s*"&CHAR(10)&"❹",CHAR(10))
.REGEXREPLACE("❺🔞\s*"&CHAR(10)&"❺",CHAR(10))
.REGEXREPLACE("❻🔞\s*"&CHAR(10)&"❻",CHAR(10))
.REGEXREPLACE("❼🔞\s*"&CHAR(10)&"❼",CHAR(10))
.REGEXREPLACE("❽🔞\s*"&CHAR(10)&"❽",CHAR(10))
.REGEXREPLACE("❾🔞\s*"&CHAR(10)&"❾",CHAR(10))

.REGEXREPLACE("㊙️将一个字段中的多个sku的中间定位符清除"，"")
.REGEXREPLACE("([❶-❾]🔞)([❶-❾])",CHAR(10))

.REGEXREPLACE("㊙️多余字符清除"，"")
.REGEXREPLACE("[🔥✅]","，")
.REGEXREPLACE("([,，。；])+","$1")
.REGEXREPLACE("[,，。；]"&CHAR(10),CHAR(10))


.REGEXREPLACE("㊙️分行优化"，"")
.REGEXREPLACE("(.{14,18})([,，。;；])", "$1"&CHAR(10))
.REGEXREPLACE("(.{14,16})([+])", "$1"&CHAR(10)&"$2")
.REGEXREPLACE([🧠品牌-逻辑],CHAR(10)&"$0")


.REGEXREPLACE("㊙️增加分隔符"，"")
.REGEXEXTRACTALL("[❶-❾][\s\S]*?[❶-❾]🔞")
.ARRAYJOIN(CHAR(10)&"〰️〰️〰️"&CHAR(10))

.REGEXREPLACE("㊙️清除空白空行"，"")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🐽🧡信息结构详情
CONCATENATE(
[📁🧡SKU组合]
.REGEXEXTRACTALL("❶([\s\S]*?)❶🔞")
.REGEXREPLACE(".*?(?:"&[🧠价格优势-逻辑]&").*?","❶【价格优势🚗】❶🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠商品价格-逻辑]&").*?","❶【商品价格💰】❶🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠折算价格-逻辑]&").*?","❶【折算详情🧮】❶🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠赠送信息-逻辑]&").*?","❶【赠送详情🎁】❶🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠领券文案-逻辑]&").*?","❶【领券文案🎫】❶🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠拍凑份数-逻辑]&").*?","❶【拍凑详情🏓】❶🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠下单文案-逻辑]&").*?","❶【下单文案🛒】❶🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠下单口令/链接-逻辑]&").*?","❶【下单链接🔗】❶🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠商品名称-逻辑]&").*?","❶【商品名称🛍️】❶🔞"&CHAR(10))

，CHAR(10)，

[📁🧡SKU组合]
.REGEXEXTRACTALL("❷([\s\S]*?)❷🔞")
.REGEXREPLACE(".*?(?:"&[🧠价格优势-逻辑]&").*?","❷【价格优势🚗】❷🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠商品价格-逻辑]&").*?","❷【商品价格💰】❷🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠折算价格-逻辑]&").*?","❷【折算详情🧮】❷🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠赠送信息-逻辑]&").*?","❷【赠送详情🎁】❷🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠领券文案-逻辑]&").*?","❷【领券文案🎫】❷🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠拍凑份数-逻辑]&").*?","❷【拍凑详情🏓】❷🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠下单文案-逻辑]&").*?","❷【下单文案🛒】❷🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠下单口令/链接-逻辑]&").*?","❷【下单链接🔗】❷🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠商品名称-逻辑]&").*?","❷【商品名称🛍️】❷🔞"&CHAR(10))

，CHAR(10)，

[📁🧡SKU组合]
.REGEXEXTRACTALL("❸([\s\S]*?)❸🔞")
.REGEXREPLACE(".*?(?:"&[🧠价格优势-逻辑]&").*?","❸【价格优势🚗】❸🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠商品价格-逻辑]&").*?","❸【商品价格💰】❸🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠折算价格-逻辑]&").*?","❸【折算详情🧮】❸🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠赠送信息-逻辑]&").*?","❸【赠送详情🎁】❸🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠领券文案-逻辑]&").*?","❸【领券文案🎫】❸🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠拍凑份数-逻辑]&").*?","❸【拍凑详情🏓】❸🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠下单文案-逻辑]&").*?","❸【下单文案🛒】❸🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠下单口令/链接-逻辑]&").*?","❸【下单链接🔗】❸🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠商品名称-逻辑]&").*?","❸【商品名称🛍️】❸🔞"&CHAR(10))

，CHAR(10)，

[📁🧡SKU组合]
.REGEXEXTRACTALL("❹([\s\S]*?)❹🔞")
.REGEXREPLACE(".*?(?:"&[🧠价格优势-逻辑]&").*?","❹【价格优势🚗】❹🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠商品价格-逻辑]&").*?","❹【商品价格💰】❹🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠折算价格-逻辑]&").*?","❹【折算详情🧮】❹🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠赠送信息-逻辑]&").*?","❹【赠送详情🎁】❹🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠领券文案-逻辑]&").*?","❹【领券文案🎫】❹🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠拍凑份数-逻辑]&").*?","❹【拍凑详情🏓】❹🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠下单文案-逻辑]&").*?","❹【下单文案🛒】❹🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠下单口令/链接-逻辑]&").*?","❹【下单链接🔗】❹🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠商品名称-逻辑]&").*?","❹【商品名称🛍️】❹🔞"&CHAR(10))

，CHAR(10)，
[📁🧡SKU组合]
.REGEXEXTRACTALL("❺([\s\S]*?)❺🔞")
.REGEXREPLACE(".*?(?:"&[🧠价格优势-逻辑]&").*?","❺【价格优势🚗】❺🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠商品价格-逻辑]&").*?","❺【商品价格💰】❺🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠折算价格-逻辑]&").*?","❺【折算详情🧮】❺🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠赠送信息-逻辑]&").*?","❺【赠送详情🎁】❺🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠领券文案-逻辑]&").*?","❺【领券文案🎫】❺🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠拍凑份数-逻辑]&").*?","❺【拍凑详情🏓】❺🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠下单文案-逻辑]&").*?","❺【下单文案🛒】❺🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠下单口令/链接-逻辑]&").*?","❺【下单链接🔗】❺🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠商品名称-逻辑]&").*?","❺【商品名称🛍️】❺🔞"&CHAR(10))

，CHAR(10)，

[📁🧡SKU组合]
.REGEXEXTRACTALL("❻([\s\S]*?)❻🔞")
.REGEXREPLACE(".*?(?:"&[🧠价格优势-逻辑]&").*?","❻【价格优势🚗】❻🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠商品价格-逻辑]&").*?","❻【商品价格💰】❻🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠折算价格-逻辑]&").*?","❻【折算详情🧮】❻🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠赠送信息-逻辑]&").*?","❻【赠送详情🎁】❻🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠领券文案-逻辑]&").*?","❻【领券文案🎫】❻🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠拍凑份数-逻辑]&").*?","❻【拍凑详情🏓】❻🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠下单文案-逻辑]&").*?","❻【下单文案🛒】❻🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠下单口令/链接-逻辑]&").*?","❻【下单链接🔗】❻🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠商品名称-逻辑]&").*?","❻【商品名称🛍️】❻🔞"&CHAR(10))

，CHAR(10)，

[📁🧡SKU组合]
.REGEXEXTRACTALL("❼([\s\S]*?)❼🔞")
.REGEXREPLACE(".*?(?:"&[🧠价格优势-逻辑]&").*?","❼【价格优势🚗】❼🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠商品价格-逻辑]&").*?","❼【商品价格💰】❼🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠折算价格-逻辑]&").*?","❼【折算详情🧮】❼🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠赠送信息-逻辑]&").*?","❼【赠送详情🎁】❼🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠领券文案-逻辑]&").*?","❼【领券文案🎫】❼🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠拍凑份数-逻辑]&").*?","❼【拍凑详情🏓】❼🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠下单文案-逻辑]&").*?","❼【下单文案🛒】❼🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠下单口令/链接-逻辑]&").*?","❼【下单链接🔗】❼🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠商品名称-逻辑]&").*?","❼【商品名称🛍️】❼🔞"&CHAR(10))

，CHAR(10)，

[📁🧡SKU组合]
.REGEXEXTRACTALL("❽([\s\S]*?)❽🔞")
.REGEXREPLACE(".*?(?:"&[🧠价格优势-逻辑]&").*?","❽【价格优势🚗】❽🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠商品价格-逻辑]&").*?","❽【商品价格💰】❽🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠折算价格-逻辑]&").*?","❽【折算详情🧮】❽🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠赠送信息-逻辑]&").*?","❽【赠送详情🎁】❽🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠领券文案-逻辑]&").*?","❽【领券文案🎫】❽🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠拍凑份数-逻辑]&").*?","❽【拍凑详情🏓】❽🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠下单文案-逻辑]&").*?","❽【下单文案🛒】❽🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠下单口令/链接-逻辑]&").*?","❽【下单链接🔗】❽🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠商品名称-逻辑]&").*?","❽【商品名称🛍️】❽🔞"&CHAR(10))

，CHAR(10)，

[📁🧡SKU组合]
.REGEXEXTRACTALL("❾([\s\S]*?)❾🔞")
.REGEXREPLACE(".*?(?:"&[🧠价格优势-逻辑]&").*?","❾【价格优势🚗】❾🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠商品价格-逻辑]&").*?","❾【商品价格💰】❾🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠折算价格-逻辑]&").*?","❾【折算详情🧮】❾🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠赠送信息-逻辑]&").*?","❾【赠送详情🎁】❾🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠领券文案-逻辑]&").*?","❾【领券文案🎫】❾🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠拍凑份数-逻辑]&").*?","❾【拍凑详情🏓】❾🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠下单文案-逻辑]&").*?","❾【下单文案🛒】❾🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠下单口令/链接-逻辑]&").*?","❾【下单链接🔗】❾🔞"&CHAR(10))
.REGEXREPLACE(".*?(?:"&[🧠商品名称-逻辑]&").*?","❾【商品名称🛍️】❾🔞"&CHAR(10))

）
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞")
.UNIQUE()
.ARRAYJOIN(CHAR(10))

.REGEXREPLACE("(❶🔞)\s*"&CHAR(10)&"([❷❸❹❺❻❼❽❾])","$1"&CHAR(10)&"〰️〰️〰️"&CHAR(10)&"$2")
.REGEXREPLACE("(❷🔞)\s*"&CHAR(10)&"([❸❹❺❻❼❽❾])","$1"&CHAR(10)&"〰️〰️〰️"&CHAR(10)&"$2")
.REGEXREPLACE("(❸🔞)\s*"&CHAR(10)&"([❹❺❻❼❽❾])","$1"&CHAR(10)&"〰️〰️〰️"&CHAR(10)&"$2")
.REGEXREPLACE("(❹🔞)\s*"&CHAR(10)&"([❺❻❼❽❾])","$1"&CHAR(10)&"〰️〰️〰️"&CHAR(10)&"$2")
.REGEXREPLACE("(❺🔞)\s*"&CHAR(10)&"([❻❼❽❾])","$1"&CHAR(10)&"〰️〰️〰️"&CHAR(10)&"$2")
.REGEXREPLACE("(❻🔞)\s*"&CHAR(10)&"([❼❽❾])","$1"&CHAR(10)&"〰️〰️〰️"&CHAR(10)&"$2")
.REGEXREPLACE("(❼🔞)\s*"&CHAR(10)&"([❽❾])","$1"&CHAR(10)&"〰️〰️〰️"&CHAR(10)&"$2")
.REGEXREPLACE("(❽🔞)\s*"&CHAR(10)&"([❾])","$1"&CHAR(10)&"〰️〰️〰️"&CHAR(10)&"$2")

.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🐽💚商品信息（重组版）
[🐽🧡信息结构详情]
  .REGEXREPLACE("㊙️㊙️㊙️㊙️商品名称替换","")
  .REGEXREPLACE("❶.*?(商品名称).*?❶🔞",
[🐽💛商品名称+价格+保质期（信息重组专用版）].REGEXEXTRACTALL("❶[\s\S]*?❶🔞")&CHAR(10))
  .REGEXREPLACE("❷.*?(商品名称).*?❷🔞",
[🐽💛商品名称+价格+保质期（信息重组专用版）].REGEXEXTRACTALL("❷[\s\S]*?❷🔞")&CHAR(10))
  .REGEXREPLACE("❸.*?(商品名称).*?❸🔞",
[🐽💛商品名称+价格+保质期（信息重组专用版）].REGEXEXTRACTALL("❸[\s\S]*?❸🔞")&CHAR(10))
  .REGEXREPLACE("❹.*?(商品名称).*?❹🔞",
[🐽💛商品名称+价格+保质期（信息重组专用版）].REGEXEXTRACTALL("❹[\s\S]*?❹🔞")&CHAR(10))
  .REGEXREPLACE("❺.*?(商品名称).*?❺🔞",
[🐽💛商品名称+价格+保质期（信息重组专用版）].REGEXEXTRACTALL("❺[\s\S]*?❺🔞")&CHAR(10))
  .REGEXREPLACE("❻.*?(商品名称).*?❻🔞",
[🐽💛商品名称+价格+保质期（信息重组专用版）].REGEXEXTRACTALL("❻[\s\S]*?❻🔞")&CHAR(10))
  .REGEXREPLACE("❼.*?(商品名称).*?❼🔞",
[🐽💛商品名称+价格+保质期（信息重组专用版）].REGEXEXTRACTALL("❼[\s\S]*?❼🔞")&CHAR(10))
  .REGEXREPLACE("❾.*?(商品名称).*?❾🔞",
[🐽💛商品名称+价格+保质期（信息重组专用版）].REGEXEXTRACTALL("❾[\s\S]*?❾🔞")&CHAR(10))

  .REGEXREPLACE("㊙️㊙️㊙️㊙️拍凑详情替换","")

  .REGEXREPLACE("❶.*?(拍凑详情).*?❶🔞",
[🔪💚拍凑份数（信息重组专用版）].REGEXEXTRACTALL("❶[\s\S]*?❶🔞")&CHAR(10))
  .REGEXREPLACE("❷.*?(拍凑详情).*?❷🔞",
[🔪💚拍凑份数（信息重组专用版）].REGEXEXTRACTALL("❷[\s\S]*?❷🔞")&CHAR(10))
  .REGEXREPLACE("❸.*?(拍凑详情).*?❸🔞",
[🔪💚拍凑份数（信息重组专用版）].REGEXEXTRACTALL("❸[\s\S]*?❸🔞")&CHAR(10))
  .REGEXREPLACE("❹.*?(拍凑详情).*?❹🔞",
[🔪💚拍凑份数（信息重组专用版）].REGEXEXTRACTALL("❹[\s\S]*?❹🔞")&CHAR(10))
  .REGEXREPLACE("❺.*?(拍凑详情).*?❺🔞",
[🔪💚拍凑份数（信息重组专用版）].REGEXEXTRACTALL("❺[\s\S]*?❺🔞")&CHAR(10))
  .REGEXREPLACE("❻.*?(拍凑详情).*?❻🔞",
[🔪💚拍凑份数（信息重组专用版）].REGEXEXTRACTALL("❻[\s\S]*?❻🔞")&CHAR(10))
  .REGEXREPLACE("❼.*?(拍凑详情).*?❼🔞",
[🔪💚拍凑份数（信息重组专用版）].REGEXEXTRACTALL("❼[\s\S]*?❼🔞")&CHAR(10))
  .REGEXREPLACE("❾.*?(拍凑详情).*?❾🔞",
[🔪💚拍凑份数（信息重组专用版）].REGEXEXTRACTALL("❾[\s\S]*?❾🔞")&CHAR(10))

  .REGEXREPLACE("㊙️㊙️㊙️㊙️领券文案替换","")

  .REGEXREPLACE("❶.*?(拍凑详情).*?❶🔞",
[🔪💚领券文案 （信息重组专用版）].REGEXEXTRACTALL("❶[\s\S]*?❶🔞")&CHAR(10))
  .REGEXREPLACE("❷.*?(拍凑详情).*?❷🔞",
[🔪💚领券文案 （信息重组专用版）].REGEXEXTRACTALL("❷[\s\S]*?❷🔞")&CHAR(10))
  .REGEXREPLACE("❸.*?(拍凑详情).*?❸🔞",
[🔪💚领券文案 （信息重组专用版）].REGEXEXTRACTALL("❸[\s\S]*?❸🔞")&CHAR(10))
  .REGEXREPLACE("❹.*?(拍凑详情).*?❹🔞",
[🔪💚领券文案 （信息重组专用版）].REGEXEXTRACTALL("❹[\s\S]*?❹🔞")&CHAR(10))
  .REGEXREPLACE("❺.*?(拍凑详情).*?❺🔞",
[🔪💚领券文案 （信息重组专用版）].REGEXEXTRACTALL("❺[\s\S]*?❺🔞")&CHAR(10))
  .REGEXREPLACE("❻.*?(拍凑详情).*?❻🔞",
[🔪💚领券文案 （信息重组专用版）].REGEXEXTRACTALL("❻[\s\S]*?❻🔞")&CHAR(10))
  .REGEXREPLACE("❼.*?(拍凑详情).*?❼🔞",
[🔪💚领券文案 （信息重组专用版）].REGEXEXTRACTALL("❼[\s\S]*?❼🔞")&CHAR(10))
  .REGEXREPLACE("❾.*?(拍凑详情).*?❾🔞",
[🔪💚领券文案 （信息重组专用版）].REGEXEXTRACTALL("❾[\s\S]*?❾🔞")&CHAR(10))

  .REGEXREPLACE("㊙️㊙️㊙️㊙️加入不可见字符（避开下单口令/链接）","")
  .REGEXREPLACE(
"(.{"&RANDOMBETWEEN(5,15)&"})",
"$1"&RANDOMITEM(LIST(CHAR(8203)，CHAR(8204),CHAR(8288))))

  .REGEXREPLACE("㊙️㊙️㊙️㊙️下单口令替换，一定要在不可见字符添加后面操作","")

  .REGEXREPLACE("❶.*?(🔗).*?❶🔞",
[🐽💛商品名称+价格+保质期（信息重组专用版）].REGEXEXTRACTALL("❶[\s\S]*?❶🔞")&CHAR(10))
  .REGEXREPLACE("❷.*?(🔗).*?❷🔞",
[🐽💛商品名称+价格+保质期（信息重组专用版）].REGEXEXTRACTALL("❷[\s\S]*?❷🔞")&CHAR(10))
  .REGEXREPLACE("❸.*?(🔗).*?❸🔞",
[🐽💛商品名称+价格+保质期（信息重组专用版）].REGEXEXTRACTALL("❸[\s\S]*?❸🔞")&CHAR(10))
  .REGEXREPLACE("❹.*?(🔗).*?❹🔞",
[🐽💛商品名称+价格+保质期（信息重组专用版）].REGEXEXTRACTALL("❹[\s\S]*?❹🔞")&CHAR(10))
  .REGEXREPLACE("❺.*?(🔗).*?❺🔞",
[🐽💛商品名称+价格+保质期（信息重组专用版）].REGEXEXTRACTALL("❺[\s\S]*?❺🔞")&CHAR(10))
  .REGEXREPLACE("❻.*?(🔗).*?❻🔞",
[🐽💛商品名称+价格+保质期（信息重组专用版）].REGEXEXTRACTALL("❻[\s\S]*?❻🔞")&CHAR(10))
  .REGEXREPLACE("❼.*?(🔗).*?❼🔞",
[🐽💛商品名称+价格+保质期（信息重组专用版）].REGEXEXTRACTALL("❼[\s\S]*?❼🔞")&CHAR(10))
  .REGEXREPLACE("❾.*?(🔗).*?❾🔞",
[🐽💛商品名称+价格+保质期（信息重组专用版）].REGEXEXTRACTALL("❾[\s\S]*?❾🔞")&CHAR(10))

.REGEXREPLACE("㊙️㊙️㊙️㊙️干扰信息清除和处理","")
.REGEXREPLACE("[❶-❾🔞🔥🛒]；?","")
.REGEXREPLACE("𝗣𝗟𝗨𝗦","Plus")
.REGEXREPLACE("‼⁠️","‼️")
.REGEXREPLACE("[+﹢🞡🞢＋🞣🞤🞥🞦🞧➕]","🞣")

.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🐽💚商品信息（发车版）
IF(
[🐽💚商品信息（重组版）]
.ISBLANK()
，""，

IF([📁所属平台]
.REGEXMATCH("淘宝")，
CONCATENATE([🐽💚商品信息（重组版）],"
----
复制后打开𝙏𝘽下单
🎫信息实时变动，到手价格稍有波动属正常现象
超多养宠折扣，可访问：www.chongzhekou.com")，

IF([📁所属平台]
.REGEXMATCH("抖音")，
CONCATENATE([🐽💚商品信息（重组版）],"
----
复制后打开𝒅𝒐𝒖𝒚𝒊𝒏下单
🎫信息实时变动，到手价格稍有波动属正常现象
超多养宠折扣，可访问：www.chongzhekou.com")，

CONCATENATE([🐽💚商品信息（重组版）],"
----
根据提示点击🔗下单
🎫信息实时变动，到手价格稍有波动属正常现象
超多养宠折扣，可访问：www.chongzhekou.com"))))


 .REGEXREPLACE("凑", LIST("凑","湊").RANDOMITEM())
 .REGEXREPLACE("单", LIST("单","単").RANDOMITEM())
 .REGEXREPLACE("红包",LIST("红包","虹苞","红苞","🧧").RANDOMITEM())
 .REGEXREPLACE("价",LIST("价","扴","'忦").RANDOMITEM())
 .REGEXREPLACE("券",LIST("券","劵").RANDOMITEM())
 .REGEXREPLACE("量",LIST("量","糧").RANDOMITEM())
 .REGEXREPLACE("杀",LIST("沙").RANDOMITEM())
 .REGEXREPLACE("拼",LIST("拼"，"拚").RANDOMITEM())
 .REGEXREPLACE("口",LIST("口","□"，"囗").RANDOMITEM())
 .REGEXREPLACE("入",LIST("入","λ").RANDOMITEM())

 .REGEXREPLACE("元",LIST("元","亓"，"无").RANDOMITEM())
 .REGEXREPLACE("步",LIST("步","歩").RANDOMITEM())
 .REGEXREPLACE("好评",LIST("好评","好苹"，"好評").RANDOMITEM())
 .REGEXREPLACE("内",LIST("内","內").RANDOMITEM())
 .REGEXREPLACE("官旗",LIST("官旗"，"官期").RANDOMITEM())
 .REGEXREPLACE("优先",LIST("优先"，"U先").RANDOMITEM())
 .REGEXREPLACE("百亿",LIST("百亿"，"佰亿").RANDOMITEM())
 .REGEXREPLACE("复制",LIST("复制"，"復zhi"，"復制").RANDOMITEM())
 .REGEXREPLACE("食物链",LIST("食物链"，"食物涟"，"食物琏"，"食物裢").RANDOMITEM())
 .REGEXREPLACE("物",LIST("物"，"粅").RANDOMITEM())

 .REGEXREPLACE("格",LIST("格"，"挌").RANDOMITEM())
 .REGEXREPLACE("包",LIST("包"，"苞").RANDOMITEM())
 .REGEXREPLACE("店",LIST("店"，"痁").RANDOMITEM())
 .REGEXREPLACE("淘",LIST("淘"，"τao"，"🍑").RANDOMITEM())
 .REGEXREPLACE("蓝氏",LIST("蓝氏"，"篮氏").RANDOMITEM())
 .REGEXREPLACE("货",LIST("货"，"貨").RANDOMITEM())
 .REGEXREPLACE("抖音",LIST("抖音"，"斗音"，"𝒅𝒐𝒖𝒚𝒊𝒏").RANDOMITEM())
 .REGEXREPLACE("玩国",LIST("玩国"，"王国").RANDOMITEM())
 .REGEXREPLACE("国",LIST("国"，"帼").RANDOMITEM())
 .REGEXREPLACE("享",LIST("享"，"亨").RANDOMITEM())
 .REGEXREPLACE("宝",LIST("宝"，"寳").RANDOMITEM())
 .REGEXREPLACE("鸟",LIST("鸟"，"鳥").RANDOMITEM())
 .REGEXREPLACE("亿",LIST("亿"，"億").RANDOMITEM())
 .REGEXREPLACE("语",LIST("语"，"語").RANDOMITEM())
 .REGEXREPLACE("瓜洲牧",LIST("瓜洲牧"，"瓜州牧").RANDOMITEM())
 .REGEXREPLACE("八公良品",LIST("八公良品"，"八公粮品").RANDOMITEM())
 .REGEXREPLACE("虹",LIST("虹"，"荭").RANDOMITEM())
 .REGEXREPLACE("¥",LIST("¥"，"￥").RANDOMITEM())

 .REGEXREPLACE("(?m)^\s*$\r?\n?", "")
 =====

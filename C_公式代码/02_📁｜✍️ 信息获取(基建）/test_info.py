from info_processor import InfoProcessor

def test_info_table_full_process():
    # 初始化处理器
    processor = InfoProcessor()

    # 测试数据（原始源信息）
    raw_data = {
        "原始商品名称": "  爱肯拿 农场盛宴 全猫粮 1.8kg 💰128.5元 u.jd.com/abc123  "  # 带空格，用于测试清洗
    }

    print("===== 开始全流程处理 =====")
    print(f"原始数据: {raw_data}")

    try:
        result = processor.full_process(raw_data)
        print(f"处理结果: {result}")
    except Exception as e:
        print(f"处理失败: {e}")
        import traceback
        traceback.print_exc()

    print("\n===== 处理结束 =====")

def test_text_processing():
    """测试文本处理功能"""
    print("\n===== 测试文本处理 =====")

    processor = InfoProcessor()

    # 测试文本清洗
    test_texts = [
        "  爱肯拿 农场盛宴 全猫粮 1.8kg 💰128.5元  ",
        "皇家 室内成猫粮 2kg 券后💰89元 天猫超市",
        "渴望 六种鱼 猫粮 1.8kg 拍2件💰256元",
        "伟嘉 成猫粮 7kg 🍑宝贝 限时秒杀💰45.9"
    ]

    for text in test_texts:
        print(f"\n原文: {text}")
        cleaned = processor.text_processor.clean_text(text)
        print(f"清洗后: {cleaned}")

        # 提取信息
        prices = processor.text_processor.extract_prices(cleaned)
        brands = processor.text_processor.extract_brands(cleaned)
        specs = processor.text_processor.extract_specifications(cleaned)
        info_type = processor.text_processor.classify_info_type(cleaned)

        print(f"价格: {prices}")
        print(f"品牌: {brands}")
        print(f"规格: {specs}")
        print(f"类型: {info_type}")

if __name__ == "__main__":
    test_info_table_full_process()
    test_text_processing()
    input("按任意键退出...")

"""
文本处理工具类
将表格公式中的复杂文本处理逻辑转换为Python代码
"""

import re
from typing import List, Dict, Any, Optional, Union
import logging

class TextProcessor:
    """文本处理工具类"""
    
    def __init__(self):
        self.logger = logging.getLogger('TextProcessor')
        
        # 基础单位映射（从逻辑表提取）
        self.units = {
            "基础单位": ["g", "ml", "粒", "片", "颗", "个", "只", "条", "根", "块", "枚"],
            "初级单位": ["包", "袋", "盒", "瓶", "罐", "桶", "箱"],
            "进阶单位": ["组", "套", "份", "装"],
            "高阶单位": ["件", "批", "箱装"],
            "终极单位": ["整箱", "整批", "大包装"]
        }
        
        # 品牌正则表达式（简化版，完整版从逻辑表加载）
        self.brand_patterns = [
            r"(爱肯拿|皇家|伟嘉|渴望|巅峰|素力高|希尔思|冠能)",
            r"(NOW|Now|now|GO|Go|go|VE|Ve|ve)",
            r"(麦富迪|比瑞吉|宝路|珀萃|铂钻)"
        ]
        
        # 价格相关正则
        self.price_patterns = {
            "price_extract": r"💰(\d+(?:\.\d+)?)",
            "price_range": r"(\d+(?:\.\d+)?)-(\d+(?:\.\d+)?)",
            "discount": r"(\d+(?:\.\d+)?)折",
            "coupon": r"券后(\d+(?:\.\d+)?)"
        }
    
    def clean_text(self, text: str) -> str:
        """
        文本清洗主函数
        对应表格公式中的复杂清洗逻辑
        """
        if not text or not text.strip():
            return ""
        
        # 1. 清除不可见字符
        text = self._remove_invisible_chars(text)
        
        # 2. 标准化数字和符号
        text = self._normalize_numbers_and_symbols(text)
        
        # 3. 品牌名称标准化
        text = self._normalize_brands(text)
        
        # 4. 单位标准化
        text = self._normalize_units(text)
        
        # 5. 价格信息标准化
        text = self._normalize_prices(text)
        
        # 6. 清理多余空格和换行
        text = self._clean_whitespace(text)
        
        return text.strip()
    
    def _remove_invisible_chars(self, text: str) -> str:
        """清除不可见字符"""
        # 清除零宽字符
        text = re.sub(r'[\u200B-\u200D\uFE00-\uFE0F\u202A-\u202E\u2060-\u206F]', '', text)
        # 处理特殊换行符
        text = re.sub(r'[\u2028]', '\n', text)
        return text
    
    def _normalize_numbers_and_symbols(self, text: str) -> str:
        """标准化数字和符号"""
        # 全角转半角
        text = re.sub(r'＝', '=', text)
        text = re.sub(r'（', '(', text)
        text = re.sub(r'）', ')', text)
        
        # 特殊字符替换
        replacements = {
            'αρρ': 'app',
            'λ': '入',
            'π': '元',
            'τao': '淘',
            '𝑱𝑫': '京东',
            '𝟖𝟖𝐯𝐢𝐩': '88vip'
        }
        
        for old, new in replacements.items():
            text = re.sub(old, new, text)
        
        return text
    
    def _normalize_brands(self, text: str) -> str:
        """品牌名称标准化"""
        # 常见品牌错误修正
        brand_corrections = {
            '天喵': '天猫',
            '桃宝': '淘宝',
            '掏宝': '淘宝',
            '🍑宝': '淘宝',
            '蕞': '最',
            '單': '单',
            '現': '现',
            '湊': '凑',
            '貨': '货',
            '糧': '粮',
            '卷': '券',
            '劵': '券',
            '锩': '券'
        }
        
        for wrong, correct in brand_corrections.items():
            text = re.sub(wrong, correct, text)
        
        return text
    
    def _normalize_units(self, text: str) -> str:
        """单位标准化"""
        # 中文数字转阿拉伯数字
        chinese_numbers = {
            '一': '1', '二': '2', '三': '3', '四': '4', '五': '5',
            '六': '6', '七': '7', '八': '8', '九': '9', '十': '10',
            '两': '2'
        }
        
        # 构建所有单位的正则
        all_units = []
        for unit_type, units in self.units.items():
            all_units.extend(units)
        
        unit_pattern = '|'.join(all_units)
        
        # 中文数字 + 单位 -> 阿拉伯数字 + 单位
        for chinese, arabic in chinese_numbers.items():
            pattern = f'(买|发|拍|共|合|赠|送|到手|加购|凑单?)(?:{chinese})({unit_pattern})?'
            replacement = f'\\g<1>{arabic}\\g<2>'
            text = re.sub(pattern, replacement, text)
        
        return text
    
    def _normalize_prices(self, text: str) -> str:
        """价格信息标准化"""
        # 统一价格符号
        text = re.sub(r'币\s*💰(\d+(?:\.\d+)?)', r'金币💰\1', text)
        text = re.sub(r'金+', '金', text)
        
        # 清理价格干扰信息
        text = re.sub(r'(?:[【(（])?(券面|日常)(价|额)?仅?(?:[:：】）)])?💰?(\d+(?:\.\d+)?)元?[,，；;。]?', '', text)
        text = re.sub(r'[【\(（]?原价[:：】）\)]?💰?\d+(?:\.\d+)?[,，]?', '', text)
        
        # 标准化折扣表达
        text = re.sub(r'拆(💰|\d+)', r'折\1', text)
        text = re.sub(r'(折)+', r'\1', text)
        
        return text
    
    def _clean_whitespace(self, text: str) -> str:
        """清理空格和换行"""
        # 清理多余的空格
        text = re.sub(r'\s+', ' ', text)
        # 清理空行
        text = re.sub(r'(?m)^\s*$\r?\n?', '', text)
        # 清理行尾空格
        text = re.sub(r'[,，+➕～\s]+$', '', text)
        
        return text
    
    def extract_prices(self, text: str) -> List[float]:
        """提取价格信息"""
        prices = []
        
        for pattern_name, pattern in self.price_patterns.items():
            matches = re.findall(pattern, text)
            for match in matches:
                if isinstance(match, tuple):
                    prices.extend([float(m) for m in match if m.replace('.', '').isdigit()])
                elif match.replace('.', '').isdigit():
                    prices.append(float(match))
        
        return list(set(prices))  # 去重
    
    def extract_brands(self, text: str) -> List[str]:
        """提取品牌信息"""
        brands = []
        
        for pattern in self.brand_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            brands.extend(matches)
        
        return list(set(brands))  # 去重
    
    def extract_specifications(self, text: str) -> Dict[str, List[str]]:
        """提取规格信息"""
        specs = {
            "weights": [],
            "quantities": [],
            "packages": []
        }
        
        # 提取重量规格
        weight_pattern = r'(\d+(?:\.\d+)?)(g|kg|ml|l)'
        weights = re.findall(weight_pattern, text, re.IGNORECASE)
        specs["weights"] = [f"{w[0]}{w[1]}" for w in weights]
        
        # 提取数量规格
        quantity_pattern = r'(\d+)(粒|片|颗|个|只|条|根|块|枚)'
        quantities = re.findall(quantity_pattern, text)
        specs["quantities"] = [f"{q[0]}{q[1]}" for q in quantities]
        
        # 提取包装规格
        package_pattern = r'(\d+)(包|袋|盒|瓶|罐|桶|箱)'
        packages = re.findall(package_pattern, text)
        specs["packages"] = [f"{p[0]}{p[1]}" for p in packages]
        
        return specs
    
    def split_sku_info(self, text: str, max_parts: int = 9) -> List[str]:
        """
        分割SKU信息
        对应表格中的商品信息分割逻辑
        """
        if not text:
            return []
        
        # 使用特殊分隔符分割
        separator = "🙂‍↕️🅾️"
        
        # 先标记长英文字符串和链接
        text = re.sub(r'(.*?[a-zA-Z0-9\/ 《()]{11,99}.*)|(.*?[0-9a-zA-z]@[.0-9a-zA-z]{4,99}.*)|(https?://\S+)', 
                     r'\g<0>' + separator, text)
        
        # 添加结尾分隔符
        text += separator
        
        # 分割并清理
        parts = []
        for part in text.split(separator):
            cleaned_part = part.strip()
            if cleaned_part and len(parts) < max_parts:
                # 清理分割标记
                cleaned_part = re.sub(r'〰〰〰', '', cleaned_part)
                cleaned_part = re.sub(r'[,，+➕～\s]+$', '', cleaned_part)
                if cleaned_part:
                    parts.append(cleaned_part)
        
        return parts
    
    def classify_info_type(self, text: str) -> str:
        """
        分类信息类型
        对应表格中的信息类型判断逻辑
        """
        if not text:
            return ""
        
        # 检查是否为不可转链商品
        if re.search(r'cyg888|dwz\.cn', text):
            return "🍎不可转链商品信息"
        
        # 检查是否为领券信息
        coupon_patterns = [
            r'(?:领|券|楼上|(?:返|反).*?卡|凑单|🧧)',
            r'(?:328.*?(?:权益)?[券包]|开.*?(?:会员|卡)|入会)'
        ]
        
        for pattern in coupon_patterns:
            if re.search(pattern, text) and not re.search(r'💰(\d+(?:\.\d+)?)', text):
                return "🎫领券/凑单信息"
        
        # 检查是否为商品信息
        if (re.search(r'💰(\d+(?:\.\d+)?)', text) and 
            re.search(r'猫|狗|犬|喵|宠物|动物|兽|人宠', text.replace('天猫', '').replace('猫超', '').replace('猫人', ''))):
            return "🛒商品信息"
        
        # 默认为其他信息
        return "📦其他信息"

# 测试代码
if __name__ == "__main__":
    processor = TextProcessor()
    
    # 测试文本清洗
    test_text = "  爱肯拿 农场盛宴 猫粮 1.8kg 💰128.5元  "
    cleaned = processor.clean_text(test_text)
    print(f"清洗前: {test_text}")
    print(f"清洗后: {cleaned}")
    
    # 测试价格提取
    prices = processor.extract_prices(cleaned)
    print(f"提取价格: {prices}")
    
    # 测试品牌提取
    brands = processor.extract_brands(cleaned)
    print(f"提取品牌: {brands}")
    
    # 测试规格提取
    specs = processor.extract_specifications(cleaned)
    print(f"提取规格: {specs}")

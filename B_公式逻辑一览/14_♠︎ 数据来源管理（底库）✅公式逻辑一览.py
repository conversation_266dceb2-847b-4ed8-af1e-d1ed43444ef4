♠︎编号
自动生成
=====
♠︎✍️【原始】信息来源 / 发车人
手动输入
=====
♠︎【yg】信息来源 / 发车人
IF(
[♠︎✍️【原始】信息来源 / 发车人]
.ISBLANK(),""，

CONCATENATE("[yg]"，
[♠︎✍️【原始】信息来源 / 发车人]
.REGEXREPLACE("®️","")
.REGEXREPLACE("🈲","禁")
.REGEXREPLACE("\d+","[数字]")

.REGEXREPLACE("\p{emoji}","")
））

.REGEXREPLACE("\[数字]",[♠︎✍️【原始】信息来源 / 发车人].REGEXEXTRACTALL("\d+"))
.TRIM()
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
♠︎✍️社群定位
手动输入
=====
♠︎✍️信息抓取进度
手动输入
=====
♠︎群名长度
[♠︎【yg】信息来源 / 发车人]
.LEN()
=====
♠︎插眼号进群时间
自动生成
=====
♠︎是否开始监测/抓取数据
IF(
CONCATENATE(
[📁 ｜✍️信息获取(基建）].[🍬信息来源 / 发车人],[📁 ｜✍️信息获取(基建）].[🍬源信息发布时间（自动）].TEXT("yyyy/mm/dd"))=CONCATENATE([♠︎【yg】信息来源 / 发车人],TODAY().TEXT("yyyy/mm/dd")),"今日抓取中","")
=====

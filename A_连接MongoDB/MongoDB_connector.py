#日常测试用 MongoDB_test.py：双击运行，快速验证数据库是否能连接、数据是否正常；
#代码中连接数据库用 MongoDB_connector.py：所有表格的模型类都通过这个工具类连接，保证连接逻辑统一；
#配置信息优先用你的实际参数：工具类的默认参数已经填好了你的服务器地址、密码等，不用修改。

# 从pymongo库导入必要的工具和错误处理类
from pymongo import MongoClient
from pymongo.errors import ConnectionFailure, PyMongoError

class MongoDBConnector:
    """
    MongoDB连接工具类（供其他代码调用）
    作用：统一处理数据库连接，所有表格（如信息获取表、品牌识别表）都通过这个类连接数据库
    """
    
    def __init__(self, host="*************", port=30017, 
                 db_name="petgo_prod", user="petgo_prod", 
                 pwd="@qweDec23p3", auth_source="petgo_prod"):
        """
        初始化连接参数（用你的实际数据库信息作为默认值）
        :param host: 数据库地址（你的服务器地址：*************）
        :param port: 端口（你的服务器端口：30017）
        :param db_name: 数据库名（你的数据库：petgo_prod）
        :param user: 用户名（你的账号：petgo_prod）
        :param pwd: 密码（你的密码：@qweDec23p3）
        :param auth_source: 认证数据库（和你的db_name一致：petgo_prod）
        """
        # 保存连接参数
        self.host = host
        self.port = port
        self.db_name = db_name
        self.user = user
        self.pwd = pwd
        self.auth_source = auth_source
        # 初始化客户端（暂时不连接，等调用connect()时再连接）
        self.client = None
        self.db = None
    
    def connect(self):
        """
        实际连接数据库（调用这个方法才会真正建立连接）
        :return: 成功返回True，失败返回False
        """
        try:
            # 创建连接客户端（相当于输入账号密码登录）
            self.client = MongoClient(
                host=self.host,
                port=self.port,
                username=self.user,
                password=self.pwd,
                authSource=self.auth_source
            )
            # 验证连接是否成功（发送一个ping命令）
            self.client.admin.command('ping')
            # 连接成功后，选择数据库
            self.db = self.client[self.db_name]
            print("✅ MongoDB连接成功")
            return True
        except ConnectionFailure:
            print("❌ 连接失败：请检查服务器地址和端口是否正确")
            return False
        except PyMongoError as e:
            if "authentication failed" in str(e).lower():
                print("❌ 认证失败：请检查用户名、密码是否正确")
            else:
                print(f"❌ 连接出错：{str(e)}")
            return False
    
    def get_collection(self, collection_name):
        """
        获取数据库中的集合（相当于打开某个表格）
        :param collection_name: 集合名称（如"info_table"、"brand_table"）
        :return: 集合对象（可用于增删改查），如果未连接则返回None
        """
        if self.db is None:  # 正确的写法
            print("⚠️ 请先调用connect()连接数据库")
            return None
        return self.db[collection_name]

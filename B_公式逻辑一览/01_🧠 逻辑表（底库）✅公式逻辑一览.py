🧠编号
自增数字
=====
🧠信息类型参考-逻辑-1
CONCATENATE("(?:领|券|楼上|(返|反).*?卡|凑单|🧧)")
=====
🧠信息类型参考-逻辑-2
CONCATENATE("(?:328.*?(?:权益)?[券包]|开.*?(?:会员|卡)|入会)")
=====
🧠品牌-逻辑
CONCATENATE("(
♎️ A|爱肯拿|奥蕾|澳博天纯|艾禾美|爱喜雅|奥鲑冠|爱欧乐|艾宠倍宠|艾什卡普|爱堡仕|爱旺斯|爱丽友|爱那多|爱多多|艾维柯|阿飞和巴弟|爱立方|奥丁|澳泰克|奥芬|奥莉特|奥奇|爱丽思|安宠冠|埃尔西博士|爱沃克|艾窝|爱诺德|爱沃克|爱宠兴|爱纳它|澳龙|啊牧|艾尔|

♎️ B|百利|珀萃|铂钻|比利玛格|宝路|佰瑞迪|倍鲜法则|波比|倍迪家|笨初初|佰萃|贝洛佳|斑队长|百宠|贝恩兰|蓓利|比亿奇|倍贝宠|倍泰吉|贝蒂|宝诺滋|宝迪路|布拉迪|布兰德|不劳|比乐|伯纳天纯|比瑞吉|宝顿|白一万是只猫|邦咻昔|倍内菲|白月光|倍帮|吧唧一口|班奈|贝卫多|百灵金方|波多宝|卜尼|宝之龙|拜达尔|笨宠日记|邦崽|八公良品|舒拜|不捣蛋|抱乐宠|笨猫一尾|贝里奥|拜耳|拜狮|博来恩|拜达尔|倍弗特|博乐丹|贝因美|百分之六十六|蓓醇|宝诺兹|霸王虎|八芙猫|比利玛|

♎️ C|草本魔力|海洋彩虹|宠康优品|宠匠|超喜|宠爱有家|宠哆哆|创臣|厂花小姐|闯荡|宠鲜妃|宠胖子|宠茁园|宠幸|宠之鲜|宠希望|宠贝思|宠亿嘉|宠贝智|宠洁优|宠确幸|宠超人|宠出没|宠物帝国|宠辞典|宠王星|宠有为|宠贵诺|馋嘴小怪兽|纯皓|纯畅|纯刻|纯想|纯福|查理狼|醇粹|诚实一口|城市理享|宠百思|宠顿|宠之源|茶哩仔|财咪|宠咕咕|宠心语|宠儿香|齿能|超安健|茶花|宠亮|宠小嘉|超可信|齿一生|宠倍安|超宠爱|宠医到|宠莱|宠兜仔|馋大嘴|馋喜哆|宠沁|宠此无忧|宠尼逸笙|宠卫队|宠大师|宠小奇|

♎️ D|巅峰|大西北|大猫日记|迪普尔|典赞|大[Pp]便当|多可特|大食怪|大玛仕|大王巡山|都乐时|朵壳|蒂乐思|地狱厨房|豆柴|兜可|德青源|大猫座|豆壳|豆七七|大眼仔|东边|滴露|大用|敌特|黛丝媞芙|杜邦卫可|呆蒙崽|多尼斯|得酷|滴乐维|大宠爱|丹特医生|捣乱派对|多特思|

♎️ E|二元物种|二哈传奇|茸伴|

♎️ F|富力鲜|福摩|菲力|菲喜多|肥猋儿|福瑞诗|翡冷翠|枫趣|疯狂小狗|复初|福乐迪|富力鲜|弗列加特|法米娜|福丸|俘获|凡世|富贵与八斤|菲宠妮|夫人的猫|福乐迪|福来恩|合宠

♎️ G|戈亚|冠能|瑰宠|捕味怪兽|格尔孚|格吾安|谷登|关谷庄|贵为|高爷家|瓜洲牧|拐角零点|贵族|果心品|贡宠|故思醇|咕噜星球|

♎️ H|海洋之星|荒野秘境|荒野盛宴|红狗|皇家|嗨查顿|海陆空|浣努|火星豆|荒野密境|憨喵先生|皇品|欢悦|憨憨宠|焕茵|黄天鹅|华驰千盛|哈仔|海尔仕|欢鱼|憨仕|皇玛|怀特喵|魔宝|好命天生|好适嘉|哈乐威|和猫住|黑鼻头|好主人|华都|华英|欢虎仔|欢喜部落|海尔兄弟|霍曼|海正动保|海乐旺|黑精灵|华畜|胡须与尾巴|惠逛街|哈哌|

♎️ J|佳乐滋|简粒|佳贝|倔强尾巴|金牌奶爸|九命|觉醒猫|桔派|佳伦|简简单单|晶乐客|简芯|俊宝|江小傲|简沫(A+)?|囧宝|洁客|倔强的尾巴|金故|爵宴|简简单单|京鲜生|渐宠|简赋|京喜|家安|金澳康威|洁客|喆它乐|金盾|绝魅|加里卡|吉思喵|

♎️ K|可努|克劳德|渴望|咔蔻|卡斯菲|考拉胖胖|克莉斯汀|卡纯思|卡比|咖妃喵|可噜噜|克鲁西亚|卡妮宝利|卡比兽|凯锐思|可努猫|特爱|库卫|康多乐|宽福|可莉丝汀|口袋厨房|开玩时刻|可鲁|卡尔|夸克|卡尼沃尔|康必持|凯特思|淘灵|卡玛尼|肯德基|

♎️ L|猎奇|罗斯|领先|蓝馔|蓝挚|蓝氏|朗生|礼佳乐|猎客|莱野|蓝梦|珞琤|乐斯尼|乐享|乐摩|乐乐猫|力狼|路斯|蓝钻|朗诺|雷米高|撸宠|六书丰居|来旺兄弟|里兜|洛迪|蓝爪|狼孩儿|恋猫恋狗|领跑|林兜猫|溜达猫|楼南灵猫|羚馋|六合|徕本|灵动|路可丝|玲贝宠|雷卫|乐爪|礼蓝动保|朗博特|怜猫|乐事宠|林兜|

♎️ M|美仕唐纳滋|美士|麦都|美国之旅|梦吉|明质|玫斯|麻利|牧野奇迹|麦斯蒂|麦瑞喜|萌笛|喵芦|喵采采|猫宅一生|喵知味|迷宠滋|墨非|喵不二|萌探|米尼小镇|莓烦恼|喵仙儿|妙冠|木天蓼|猫卷叮当|茂芃|猫大力|喵招|迈阿咪|迈阿密|萌仔|喵汪国度|喵殿下|萌战队|魔核|墨兜|蒙贝|萌宠志|萌宗|魔语|喵三餐|妙趣空格|煤球妈妈|莓小萌|萌宠出动|喵彩|萌尾|名创优品|麦富迪|名创优品|喵梵思|喵铮铮|喵大嘴|米米伙伴|猫卫士|猫殿下|名花有主|猫乐适|萌猫警长|明星猫|喵斯拉|萌爪|氓宠|喵弋安|咪恩|每宠|猫太郎|美臣计|美斯|猫和友|冒险与它|喵丽丝|美叮馋妮子|毛球引力|喵星仔|莫可|萌宠足迹|喵山人|喵星宿|猫界|萌惜贝宠|喵乐宝|喵小弟|妙汪天下|蒙牛|喵鲜食坊|沐森堂|猫警官|麦宝隆|麦德豪|牧迪圣宠|毛羽|美里美亚|妙多乐|麦德氏|妙三多|莫迪医生|喵小胖|萌维兔|喵哆福|萌心岛|麻球说|萌小爵|美人喵|美滋元|喵魅儿|喵达|妙宠|麦当劳|

♎️ N|纽翠斯|纽冠|纽滋|奈斯佰特|纳瑞施|耐威克|诺迪威|诺亚飞象|农夫牧场|昵趣|妮吖|妮可露|耐萌|奶思|年宠膳|觅能|聂博士|纽顿|呢咔|那逸乐|鸟语花香|那非普|纽然氏|诺特|瑞德医生|纽衡|你的猫|

♎️ O|欧恩焙|欧僖乐|欧尔|

♎️ P|派膳师|沛乐多|帕美拉|胖十七|皮皮淘|佩妮|谱拉思|派阿哥|派兮|帕缇朵|扑岛|佩玛思特|佩内特|帕特|泡咔|帕蓝姆|派得|盼四季|璞斯|陪伴岁月|[Pp][Nn][Zz]牧场(之味)?|品益多|泡泡可儿|皮克方|派迪诺|派派|

♎️ Q|柒哦|全硕|亲宠|青鱼老弟|圈宠|浅宠|俏贝丽|趣友|俏九娘|趣加喵|犬心保|强生宠儿|轻养喵|

♎️ R|瑞梦迪|肉璞|肉小满|肉垫|日和优宠|荣宠|
♎️ S|素力高|善待|兽护神|仨喜米|塞外岚|三哈|森森|山下有猫|邵可可|邵小布|森熹|莎美特|探索|圣迪乐|舒纯|丝蓓绮|圣帝克|室宠|森行|生生不息|酥醒|食物链|圣农|膳魔师|唐米吉吉|四月名|上鲜|邵可可|食极|索来多|上且|苏苏宠物|拓宠|速诺|施昂|菩施康|狮王|拾尚物语|神经猫|三又二分之一猫|狮子医生|舒医熊|三生欢喜|

♎️ T|添赐力|太一宠|托玛仕|天衡宝|特喵德|汤姆先生|图咔拉|淘豆玩国|淘派特|添一口|坦克小希|汤恩贝|探味计划|它洁|它帮|天使翅膀|泰格|它集|贪吃的毛毛|淘气总动员|它医生|泰淘气|天净|

♎️ W|伟嘉|维倍思|维采康|微露滋|旺喜猫|王漂亮|汪爸爸|无尾|维采康延|威比奇|乌力乌力|万物一口|歪耳朵|丸味|未卡|维谱拉思|味道印记|维利亚|网易|顽皮|卫仕|吾皇御赐|尾巴生活|吾尾|味当家|窝卜|王贵皮|吾岛|尾优|王如花|未来守护者|沃夫妙|味臻纯|沃尔玛|卫龙|威隆|唯特医生|维克|唯特适|汪臻醇|五爪破碎|唯宠而生|

♎️ X|希尔思|鲜朗|星益生趣|希兰蒂|欧僖乐|西北天成|樱闲|小巴虎|喜崽|小末|小虎趣|纤臣|新宠之康|喜小兽|香吉仕|小致|小面儿|鲜生拾刻|小甜橙|小宠|小壳|小罗|小她|小胖爪|小猫日记|昕鹏|玄奇|心粮|希瑞尔|心伴蜜语|笑宠|喜萌|鲜粮说|许翠花|小李子|希宝|熊出没|鑫安|嘻哆宝|雪诗雅|西西来了|星宠世界|星禾|喜喵|鲜京采|雪貂留香|雪貂|星宠爱|小狗在家|小佩|嬉皮狗|鑫盾|小许医生|香吉仕|寻咪|小奶猫|哮天犬|

♎️ Y|野性魅力|原野牧歌|渔夫牧场|益顿|依宝|伊菲|优瑞派|英拿|尤品滋|优思韦|元气怪兽|养了个毛孩|有宠日纪|溢本良|优曼|伊纳宝|怡乐宠|悠歌|优之宠|原粹时代|月半能|有鱼|怡乐宠|壹伴|伊利|有哈|亚禾|宇宙尽头|悦天有派|优朗|伊萨|益和|约克先生|友立方|一只喜欢|杳炅|扬宠天下|圆滚与胖嘟|虞央|一撇|伊珊娜|跃希|伊利|益伴|佑多萌|杨医生|又宠|原本|

♎️ Z|自由牧场|甄萃|珍致|自然光环|挚好|自然魔法|至萃|致一|尊佑|藻趣儿|智它|最宠|赞乐|臻的|滋奇|衷鸣|喳达|自然逻辑|爪爪印记|爪子心选|再三|张爷家|挚野|自然价值|樱恋果|中博特|中润|正大|智他|志高|爪小囡|浙宠|致滋|造砂工坊|


♎|NOW|Now|now|Nowfoods|nowfoods|诺奥|
♎|𝑽𝑬|VitalEssentials|
♎️|𝑮𝑶|
♎️|𝑵1|
♎️|𝑲9|
♎️|𝑶𝑫𝑬|
♎️|𝑴𝑨𝑮|
♎️|𝑵𝑨𝑺|
♎️|𝑺𝑨𝑬|
♎️|𝑰𝑺𝑩|

️
♎️|3M5PET|3M5Pet|3m5pet|

♎️|ANGELAMIAO|Angelamiao|angelamiao|
♎️|[Aa][Mm][Oo]\s*[Pp][Ee][Tt][Rr][Ii][Cc]|
♎️|[Aa][Nn][Ii][Ff][Oo][Rr][Tt][Ee]|
♎️|AIRFUN|Airfun|airfun|
♎️|Animonda|animonda|
♎️|AFP|Afp|afp|



♎️|BETAPAW|BetaPaw|Betapaw|betapaw|
♎️|BOBLUSH|BobLush|boblush|
♎️|BIBS|Bibs|bibs|

♎️|BIXBI|Bixbi|bixbi|
♎️|BFUN|Bfun|bfun|
♎️|BOTH|Both|both|

♎️|[Cc][Rr][Aa][nn][Ii][Mm][Aa][Ll][Ss]|
♎️|CANDYPETI|Candypeti|candypeti|
♎️|[Cc][Aa][Tt][Aa][Ii][Rr]|
♎️|COMBEATS|Combeats|combeats|
♎️|CASSLIFE|Casslife|casslife|
♎️|CATLINK|CatLink|catlink|
♎️|CHASUP|Chasup|chasup|
♎️|CENO|Ceno|ceno|

♎️|DESTIFE|Destife|destife|

♎️|EVERCLEAN|EverClean|Everclean|everclean|


♎️|[Ff][Ii][Vv][Ee]\s*[Bb][Oo][Yy]|
♎️|[Ff][Ii][Ss][Hh][4][Cc][Aa][Tt][Ss]|
♎️|[Ff][Ii][Ss][Hh][4][Dd][Oo][Gg][Ss]|

♎️|[Jj][Ii][Rr][Pp][Ee][Tt]|

♎️|KITTYYOYO|KittyYoyo|kittyyoyo|
♎️|KATOAI|Kato Ai|kato ai|katoai|
♎️|KONOKO|Konoko|konoko|
♎️|KIMPETS|KimPets|kimpets|
♎️|KOJIMA|Kojima|kojima|
♎️|kiwi|Kiwi|KIWI|

♎️|LFLLAMA|Lfllama|lfllama|



♎️|OXYFRESH|OxyFresh|Oxyfresh|Oxyfresh|oxyfresh|
♎️|OARMILK|OarmiLk|Oarmilk|oarmilk|
♎️|OWNAT|Ownat|ownat|
♎️|OTES|Otes|otes|


♎️|PEISIMA|PeiSima|Peisima|peisima|
♎️|PETSMILE|PetSmile|Petsmile|petsmile|
♎️|[Pp][Ii][Ss][Ss][Aa]|
♎️|[Pp][Ee][Tt][Ss][Ee][Nn]|
♎️|[Pp][Oo][Gg][Ee][Pp][Ee][Tt]|
♎️|[Pp][Aa][Ii][Pp][Ee][Tt]|
♎️|[Pp][Uu][Pp][Pp][Yy][Tt][Ii][Ee]|
♎️|PTFRESH|Ptfresh|ptfresh|
♎️|PURENER|Purener|purener|
♎️|PRIMAL|Primal|primal|
♎️|petshy|Petshy|PETSHY|
♎️|pidan|Pidan|PIDAN|


♎️|ONEMAX|Onemax|onemax|
♎️|OLITB|OliTB|olitb|
♎️|OKZOO|Okzoo|okzoo|

♎️|[Pp][Aa][Ii][pp][Ee][Tt]|


♎️|SolidGold|solidgold|
♎️|STAMINON|Staminon|staminon|
♎️|SMART|Smart|smart|
♎️|[ss][Aa][Ss][Hh][Aa][Ss]|

♎️|TELATE|Telate|telate|
♎️|[Tt][ww][Ii][Nn][Kk][Ll][ii][Nn][Gg]\s*[Ss][Tt][Aa][Rr]|


♎️|MOLLYBOX|MollyBox|Mollybox|mollybox|
♎️|MJAMJAM|Mjamjam|mjamjam|
♎️|MamyPets|Mamypets|mamypets|
♎️|[Mm][Uu][Kk][Oo][Oo]|
♎️|MPETS|Mpets|mpets|
♎️|MISO|Miso|miso|
♎️|MEDHO|Mdeho|mdeho|
♎️|MAG|Mag|mag|



♎️|NaturalBalance||Natural Balance|Natural balance|natural balance|
♎️|[Nn][Uu][Tt][Rr][Ii]\s*[Ss][Oo][Uu][Rr][Cc][Ee]|
♎️|[Nn][Ee][Oo]\s*(?:[Cc][Ll][Ee][Aa][Nn])?|
♎️|[Nn][Ee][Ee][Dd][Cc][Aa][Tt]|
♎️|nulo|Nulo|NULO|


♎️|UOMIPET|UomiPet|uomipet|
♎️|UPKEYE|UpKeye|upkeye|
♎️|[Uu][Bb][Aa][Yy]|
♎️|UAH|Uah|uah|



♎️|VETRISCIENCE|VetriScience|Vetriscience|vetriscience|
♎️|VIRTAVO|Virtavo|virtavo|
♎️|VIVA|Viva|viva|
♎️|VCARE|VCare|Vcare|️vcare|


♎️|[Ww][Oo][Oo][Ll][Ff]|

♎️|PETSNACKS|Petsnacks|petsnacks|♎
♎️|PETCHICE|PetChoice|petchoice|
♎️|PURIZON|Purizon|purizon|
♎️|PARLMU|Parlmu|parlmu|

♎️|LOVELYDOGGY|LovelyDoggy|LovelyDoggy|
♎️|ILUMOS|ILumos|ilumos|

♎️|LittleNoo|littleNoo|littlenoo|
♎️|LODI|Lodi|lodi|



♎️|JIULNING|Jiulning|jiulning|
♎️|JKULNING|Jkulning|jkulning|
♎️|JARROW|Jarrow|JarRow|jarrow|




♎️|HONEYCARE|HoneyCare|honeycare|
♎️|HoneyGuaridan|honeyguaridan|
♎️|Hound&Gatos|hound&gatos|
♎️|HOOPET|Hoopet|hoopet|
♎️|HALO|Halo|halo|
♎️|HOCC|Hocc|hocc|
♎️|HWKW|Hwkw|hwkw|


♎️|KING|King|king|
♎️|KAIKOA|KaiKoa|kaikoa|

♎️|TAIGE|Taige|taige|

♎️|UCAT|UCat|Ucat|ucat|


♎️|WINKHONEY|WinkHoney|Winkhoney|winkhoney|
♎️|[Ww][Oo][Nn][Dd][Ee][Rr][Ff][Uu][Rr]|
♎️|WELLNESS|Wellness|wellness|
♎️|WAKYTU|Wakytu|wakytu|
♎️|WOWO|WoWo|Wowo|wowo|
♎️|WOAO|Woao|woao|

♎️|[Yy][Nn][Ss]|

♎️|ZEVELO|Zevelo|zevelo|
♎️|ZIWI|ZiWi|ziwi|
♎️|zeal|Zeal|ZEAL|
♎️|ZEZE|zeze|Zeze
)


")
.REGEXREPLACE("[,，+➕～\s]+$","").REGEXREPLACE("(?m)^\s*$\r?\n?","")
=====
🧠商品名称-逻辑
CONCATENATE("(?:(?:"&[🧠品牌-逻辑]&")|♎️
|(?:\d+(?:\.\d+)?(?:"&[🧠单位合集]&"))|♎️
|(?:[猫狗犬]{1,2}|♎️
|[XMSLxmsl]{1,2}码|♎️
|宠物|冻干|罐头|主食|主粮|餐盒|猫砂|驱虫|零食|玩具|伊丽莎白圈|牵引绳))")
=====
🧠二级分类-逻辑
CONCATENATE("(?:主粮|♎️
|猫砂|♎️
|罐头|餐盒|餐包|鲜肉辅食|猫条|狗条|♎️
|冻干|乳制品|肉干|蔬菜干|猫薄荷|其他零食|♎️
|驱虫药|皮肤用药|其他药品|♎️
|关节护理|肠胃护理|毛发护理|其他补剂|♎️
|清洁用品|除臭剂|消毒剂|♎️
|洗护工具|修剪工具|牵引工具|宠物厕所|猫抓板|猫爬架|宠物服饰|宠物玩具|宠物窝|宠物包|宠物碗|宠物餐垫|伊丽莎白圈|沙发贴|摄像头|干燥剂|储粮桶|餐垫|其他|♎️
)")

.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🧠口味-逻辑
CONCATENATE("(鲜肉)|(生骨肉?)|(火鸡)|([鸡鸭鹅虾蟹鱼蛋鸽牛兔羊驼兔鹿])|(鹌鹑)|(袋鼠)|(豌豆)|(胡萝卜)|(山药)|(凤梨)|(梨)|(苹果)|(南瓜)|(红薯)|(西兰花)|(秋葵)|(牡蛎)|(海苔)|(玉米)|(石榴)|(蛋黄)|(柿子)|(草莓)|(蓝莓)|(鲜酪)|(奶酪)|(果蔬)|(菠菜)|(木天蓼)|(浆果)|(叶酸)|(益生菌)|(猫草)|([白绿红]茶)|(山茶花)|(栀子)|(茉莉)|(椰壳)|(椰子)|(咖啡)|(乌木)|(小米)|(豆腐)|(植物)|(小苏打)|(膨润土)|(原?矿.*?砂?)|(木薯)|(石墨烯)|(燕麦)|(海盐)|(柠檬)|(竹纤维)|(竹)|(活性炭)|(稻壳)|(豆壳)|(碳晶)|(樱花)|(木天蓼)|(苍兰)|((?:\d+|[一-十两双])拼)
")
.REGEXREPLACE("[,，+➕～\s]+$","")
.REGEXREPLACE("(?m)^\s*$\r?\n?","")
=====
🧠成长期-逻辑
CONCATENATE("(成猫|成犬|成年期|幼猫|幼犬|老年猫|老年犬|老年期)
")
.REGEXREPLACE("[,，+➕～\s]+$","")
.REGEXREPLACE("(?m)^\s*$\r?\n?","")
=====
🧠制作工艺-逻辑
CONCATENATE("((?:低温)?烘焙)|(膨化)|(风干)|(冻干)|((?:低温)冷压)|(湿粮)
")
.REGEXREPLACE("[,，+➕～\s]+$","")
.REGEXREPLACE("(?m)^\s*$\r?\n?","")
=====
🧠使用对象-逻辑（人）
CONCATENATE("(小孩|儿童|婴儿|孕妇|成人|老少皆宜|男女|老年人|小时候|♎️
|上班|上学|♎️
|瑞幸|蜜雪(冰城)?|霸王茶姬|茶百道|古茗|爷爷不泡茶|库迪|星巴克|必胜客|麦当劳|肯德基|达美乐|巧乐兹|♎️
|炒菜|煲汤|煮粥|生抽|老抽|蚝油|料酒|鸡精|非油炸|下剧|下饭|旅行装|围裙|♎️
|内衣|内裤|袜|护垫|姨妈巾|卫生巾|牙线|冷水壶|电池|大米|新米|抽纸|餐巾纸|卷纸|薄荷糖|熊猫鞋|热狗|凤爪|冰激凌|雪糕|甜筒|棒冰|冰淇淋|披萨|美式|拿铁|手抓饭|手抓[牛羊]?排|♎️
|芒[Tt][Vv]|芒果会员|芒果𝙏𝙑|爱奇艺|腾讯|♎️
|冷酸灵|良品铺子|劲仔|星鲨|雅客|佬食仁|猫人|猫人|♎️
|家用|保鲜盒|纯净水|矿泉水|化痰|砂锅|蚊帐|红糖|收纳|绿豆饼|绞肉机|拿铁|中性笔|(新鲜|现摘).*?水果|果园)
"）

.REGEXREPLACE("[,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🧠商品价格-逻辑
CONCATENATE("(?:使?用.*?立减💰?\d+(?:\.\d+)?)|♎️
|((?:.*?💰\d+(?:\.\d+)?)[,，。]*(?:(?:88vip|88VIP|plus|Plus|拍\d+[件份]|部分人)💰\d+(?:\.\d+)?)?[,，。]*(?:(?:无|没)?(?:首单|淘?金币|凑单|部分人|凑?消费券)?(?:贵|少|多|便宜)?.*?\d+(?:\.\d+)?)?[,，。]*(?:.*?(?:好评|晒图|晒单|入会|会员)?[有礼返卡现送]{1,4}.*?\d+(?:\.\d+)?)?[,，。]*(?:.*?价值💰?\d+(?:\.\d+)?)?[,，。]*(?:(?:.*?到手💰\d+(?:\.\d+)?)?.*))|♎️
|(.*?(?:好评|晒图|晒单|入会|会员)?(?:有礼|返卡?|返现送?).*?\d+(?:\.\d+)?.*)|♎️
|(.*(?:价值|[定订]金|充值|充购物金|\d+折)💰?.*?\d+(?:\.\d+)?.*)|♎️

"

)
.REGEXREPLACE("(?m)^\s*$\r?\n?","")
=====
🧠加法符号
CONCATENATE("(?:[+﹢🞡🞢＋🞣🞤🞥🞦🞧➕º])")
.REGEXREPLACE("(?m)^\s*$\r?\n?","")
=====
🧠赠送信息-逻辑
CONCATENATE("
((?:任选|拍下|[赠送]|"&[🧠加法符号]&").*?(?:试吃).*\r?\n\s*(?:"&[🧠加法符号]&").*)+|

♎️

|((?:\d+\s*点前|前\d+名?|"&[🧠加法符号]&")?[【]?(?:(?:提交页面)?自?选.*?赠品[，。,]\s?)?(?:入会|店铺会员|会员|付尾款|首单|晒图|晒单|拍下|下单|充值?|充.*?购物金|(?:[买拍]|加购|凑单)\d+(?:"&[🧠单位合集]&")?)?(?:额外|限时)?(?:"&[🧠加法符号]&"|再|加|配|还)?[赠送]{1,2}.*?"&[🧠加法符号]&""&CHAR(10)&"(?:.*))|

♎️

|((?:\d+\s*点前|前\d+名?|"&[🧠加法符号]&")?[【]?(?:(?:提交页面)?自?选.*?赠品[，。,]\s?)?(?:入会|店铺会员|会员|首单|付尾款|晒图|晒单|拍下|下单|充值?|充.*?购物金|(?:[买拍]|加购|凑单)\d+(?:"&[🧠单位合集]&")?)?(?:额外|限时)?(?:"&[🧠加法符号]&"|再|加|配|还)?[赠送]{1,2}.*(?:"&CHAR(10)&"(?:(?:[买拍]|加购|凑单)\d+(?:"&[🧠单位合集]&")?)?(?:"&[🧠加法符号]&"|试[吃用]).*)*)|(.*?(?:"&[🧠加法符号]&"?试[吃用]|价值\d+).*)|((?:[买拍]|加购|凑单)\d+(?:"&[🧠单位合集]&")?"&[🧠加法符号]&".*)|

♎️

|(.*?充.*?\d+.*?送.*)|

♎️

|([【]?(?:提交页面)?自?选.*?赠品)



")
=====
🧠领券文案-逻辑
CONCATENATE("(?:.*?(?:建档)?(?:[先再]?下拉|[左右滑划]{2}|券|红包|砸蛋|抽奖|0元入会|🧧|[先再]领|领优惠|页面.*?弹|[先再].*?入会|(?:[抽领兑换]{1,2}).*?\d+\-\d+).*)")
=====
🧠领券文案干扰信息-逻辑
CONCATENATE("(?:楼上|如图)")
=====
🧠优惠券-逻辑
CONCATENATE("(?:(?:领|叠)"&[🧠领券文案干扰信息-逻辑]&"?(\d+\-\d+(?:/\d+\-\d+)?|\d+折))")
=====
🧠优惠券类型-逻辑
CONCATENATE("(?:(?:消费|宠物|惊喜)*券)")
=====
🧠保质期-逻辑
CONCATENATE("((?:猪肉|新鲜)?(?:保质期|(?:有|好|新)?效期|到期|临期).*)")
=====
🧠价格优势-逻辑
CONCATENATE("(?:(?:有|旗下|超级|稀[少有]|自有|直营|自营|旗舰店|品牌|试吃)?(?:近期[最超新]低|近期[好低][价车]|历史[好低][价车]|[史最新]低|低价|白菜价|豪车|好价|好车|羊毛|[豪好]🚗|好价和豪车(?:之间|边缘)))")
=====
🧠商品介绍-逻辑
CONCATENATE("(\n(?:.*?)\n\s*🔗)|(.*?\d+(?:\.\d+)?\+?(?:%|％).*)")
.REGEXREPLACE("(?m)^\s*$\r?\n?","")
=====
🧠拍凑份数-逻辑
CONCATENATE("([拍买]\d+(?:"&[🧠单位合集]&")?[赠送发](?:\d+|同款|试吃).*)|((?:.*?(?:任?[选买拍]|加购物车|加[购车]?|凑单?)各?\s*\d+.*)(?:\r?\n)?(?:.*?(?:共|到手)\d+(?:"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&"｜"&[🧠终极单位]&").*)?)")
.REGEXREPLACE("(?m)^\s*$\r?\n?","")
=====
🧠下单口令/链接-逻辑
CONCATENATE("(?:(?:[Hh]ttps?://\S+)|♎️
|(?:[^\u4E00-\u9FFF\n]*?[a-zA-Z0-9\/ 《()]{11,99}[^\u4E00-\u9FFF\n]*)|♎️
|(?:[^\u4E00-\u9FFF\n]*?[0-9a-zA-Z]@[.0-9a-zA-Z]{4,99}[^\u4E00-\u9FFF\n]*))")
=====
🧠下单文案-逻辑
CONCATENATE("(?:.*?(?:(?:直播间|会场|收藏|免单|抽奖|返京豆)|(?:小程序|[Aa][Pp][Pp])|(?:(?:合并|一起|用).*?付)|(?:入会[先再]拍)|(?:收货.*?返)|(?:需要新入会员)|(?:搜索)|(?:限购)|(?:凑单(?:参考|推荐|链接))|(?:以上任选)|(?:选项)|(?:[任各][意选拍])|(?:晒图活动.*客服)|(?:退)).*)")
=====
🧠折算价格-逻辑
CONCATENATE("

(?:(㊙️㊙️㊙️处理：一包约15g*20枚  这种情况)|♎️

|(\d+(?:"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&"|"&[🧠终极单位]&")(?:折|约?等于|约|换算成?|相当于|划)(?:\d+(?:\.\d+)?(?:"&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")\s*(?:\*\s*\d+(?:"&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")?)?))|♎️


|(㊙️㊙️㊙️处理：3kg折💰100;每斤相当于💰129  这种情况)|♎️

|((?:算上赠品|排除赠品|充值购物金)?(?:\d+(?:\.\d+)?折)?(?:猫粮|罐头|狗粮|餐包|犬粮|零食|罐罐|猫砂|冻干桶?|猫条|礼包)?\s*💰?\s*(?:每|单|一|(\d+(?:\.\d+)?))(?:"&[🧠单位合集]&")(?:折|等于|换算成?|相当于|划)\s*💰\s*\d+(?:\.\d+)?/?.*)|♎️

|(㊙️㊙️㊙️处理：折3kg💰100;相当于每斤💰129 这种情况)|♎️

|((?:算上赠品|排除赠品|充值购物金)?(?:\d+(?:\.\d+)?折)?(?:猫粮|罐头|狗粮|餐包|犬粮|零食|罐罐|猫砂|冻干桶?|猫条|礼包)?(?:折|等于|换算成?|相当于|划)\s*💰?\s*(?:每|单|一|\d+(?:\.\d+)?)?(?:"&[🧠单位合集]&")?.*?💰\s*\d+(?:\.\d+)?/?.*)|♎️

|(㊙️㊙️㊙️处理：3kg猫砂仅划💰100/袋 这种情况)|♎️
|((?:算上赠品|排除赠品|充值购物金)?(?:\d+(?:\.\d+)?折)?\d+(?:\.\d+)?(?:"&[🧠基础单位]&")(?:猫粮|罐头|狗粮|餐包|犬粮|零食|罐罐|猫砂|冻干桶?|猫条|礼包)?[才仅需划折约]{0,2}(?:折|等于|换算成?|划)?\s*💰\s*\d+(?:\.\d+)?/(?:"&[🧠单位合集]&"))|♎️

|(㊙️㊙️㊙️处理：猫砂才仅需💰1.6/斤 这种情况)|♎️
|((?:算上赠品|排除赠品|充值购物金)?(?:\d+(?:\.\d+)?折)?(?:猫粮|罐头|狗粮|餐包|犬粮|零食|罐罐|猫砂|冻干桶?|猫条|礼包)(?:折|等于|换算成?|相当于|到手|等于|约)?[才仅需划折约]{0,2}\s*💰\s*\d+(?:\.\d+)?/(?:\d+(?:\.\d+)?)?(?:"&[🧠单位合集]&"))|♎️

|(㊙️㊙️㊙️处理：折💰100/3盒 这种情况)|♎️
|((?:算上赠品|排除赠品|充值购物金)?(?:\d+(?:\.\d+)?折)?(?:猫粮|罐头|狗粮|餐包|犬粮|零食|罐罐|猫砂|冻干桶?|猫条|礼包)?(?:折|等于|换算成?|相当于|到手|等于|约)\s*💰?\s*\d+(?:\.\d+)?\/(?:\d+(?:\.\d+)?)?(?:"&[🧠单位合集]&"))|♎️


|(㊙️㊙️㊙️处理：相当于2kg猫粮💰20; 折猫粮💰20 这种情况)|♎️
|((?:算上赠品|排除赠品|充值购物金)?(?:\d+(?:\.\d+)?折)?(?:折|换算成?|相当于|等于)\s*(?:💰?\s*(\d+(?:\.\d+)?)(?:"&[🧠单位合集]&"))?(?:猫粮|罐头|狗粮|餐包|犬粮|零食|罐罐|猫砂|冻干桶?|猫条|礼包)?[才仅需划折约]{0,2}\s*💰\s*(?:\d+(?:\.\d+)))|♎️

|(㊙️㊙️㊙️处理：到手共💰20kg 这种情况)|♎️

|((?:算上赠品|排除赠品|充值购物金)?(?:\d+(?:\.\d+)?折)?(?:猫粮|罐头|狗粮|餐包|犬粮|零食|罐罐|猫砂|冻干桶?|猫条|礼包)?到手[一?共]?\s*💰\s*\d+(?:\.\d+)?(?:"&[🧠单位合集]&")))|♎️

|(㊙️㊙️㊙️处理：折💰1.6/斤 这种情况，适合前面折扣前面什么限定信息都没有的情况)|♎️
|((?:折|等于|换算成?|相当于|到手|等于|约)?[才仅需划折约]{0,2}\s*💰\s*\d+(?:\.\d+)?/(?:\d+(?:\.\d+)?)?(?:"&[🧠单位合集]&"))


")
.REGEXREPLACE("(?m)^\s*$\r?\n?","")
=====
🧠需转义符号-逻辑
CONCATENATE("(?:\\|\/|\(|\)|\[|\]|\$|\+|\*|\{|\}|\?|\^)")
=====
🧠规格提取-逻辑
CONCATENATE("(\d+(?:\.\d+)?\s*(?:"&[🧠单位合集]&")(?:选项)?.*?(?:(?:"&[🧠乘法符号]&"|拍|下单)\d+(?:\.\d+)?(?:"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&"|"&[🧠终极单位]&")?)*)")
=====
🧠基础单位
CONCATENATE("(?:[Kk]?[Gg]|[Mm][Gg]|[Ll][Bb]|[Mm]?[Ll]|[Cc]?[Mm]|磅|千?克|公?斤)")
=====
🧠初级单位
CONCATENATE("(?:[个枚支只片颗粒块饼根])")
=====
🧠进阶单位
CONCATENATE("(?:[板条杯瓶罐袋卷包])")
=====
🧠高阶单位
CONCATENATE("(?:[桶盒箱])")
=====
🧠终极单位
CONCATENATE("(?:[份件单期])")
=====
🧠单位合集
CONCATENATE("(?:"&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&"|"&[🧠终极单位]&")")
=====
🧠乘法符号
CONCATENATE("(?:[×✕🞨✗✖️✘Xx*🞱🞲🞳🞴*️✲✱✳︎🞵🞶🞷🞸🞻🞼✴︎※❋])")
=====
🧠所拍规格-逻辑
CONCATENATE("(\d+(?:"&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")(?:选项)?)(拍\d+(?:"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&"|"&[🧠终极单位]&")?)|♎️

|(拍?\d+(?:"&[🧠单位合集]&")选项)")
=====

"""
依赖管理系统演示
展示如何处理表格间复杂的字段依赖关系
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from A_main.core_engine import CoreEngine
from A_main.dependency_manager import DependencyManager, FieldStatus, FieldType
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

def demo_dependency_system():
    """演示依赖管理系统"""
    print("=" * 60)
    print("🚀 依赖管理系统演示")
    print("=" * 60)
    
    # 创建依赖管理器
    dep_manager = DependencyManager()
    
    print("\n📋 1. 查看依赖关系")
    print("-" * 40)
    
    # 查看几个关键字段的依赖
    test_fields = [
        ("📁 信息获取（基建）", "Ⓜ️🤍‼️品牌（标准格式）"),
        ("📁 信息获取（基建）", "💰到手价格（终版）"),
        ("🤖 中转表（工具）", "🤖🏷️品牌（标准格式）")
    ]
    
    for table, field in test_fields:
        deps = dep_manager.get_field_dependencies(table, field)
        print(f"📊 {table}.{field}")
        if deps:
            for dep in deps:
                print(f"   └─ 依赖: {dep.source_table}.{dep.source_field} ({dep.field_type.value})")
        else:
            print("   └─ 无依赖")
        print()
    
    print("\n🔄 2. 模拟字段计算过程")
    print("-" * 40)
    
    # 模拟一些字段的状态变化
    record_id = "test_001"
    
    # 第一轮：处理源信息字段
    print("第一轮：处理源信息和第一原生字段")
    source_fields = [
        ("📁 信息获取（基建）", "🍬商品信息（原始版）"),
        ("📁 信息获取（基建）", "清洗后商品名称"),
        ("🔪 SKU解构（基建）", "商品名称解析")
    ]
    
    for table, field in source_fields:
        dep_manager.set_field_status(table, field, FieldStatus.COMPLETED, record_id, f"计算结果_{field}")
        print(f"✅ 完成: {table}.{field}")
    
    # 第二轮：检查关联字段是否可以计算
    print("\n第二轮：检查关联字段")
    associated_fields = [
        ("📁 信息获取（基建）", "Ⓜ️🤍‼️品牌（标准格式）"),
        ("🔪 SKU解构（基建）", "🍬商品信息（原始版）")
    ]
    
    for table, field in associated_fields:
        can_calc, missing = dep_manager.can_calculate_field(table, field, record_id)
        status = "✅ 可计算" if can_calc else f"⏳ 等待依赖: {missing}"
        print(f"{status}: {table}.{field}")
        
        if can_calc:
            dep_manager.set_field_status(table, field, FieldStatus.COMPLETED, record_id, f"关联结果_{field}")
    
    print("\n📊 3. 查看处理状态")
    print("-" * 40)
    
    status_summary = dep_manager.get_status_summary()
    print(f"总字段数: {status_summary['total_fields']}")
    print(f"已完成: {status_summary['completed']}")
    print(f"处理中: {status_summary['in_progress']}")
    print(f"等待依赖: {status_summary['waiting']}")
    print(f"失败: {status_summary['failed']}")
    
    print("\n按表格统计:")
    for table_name, table_stats in status_summary['by_table'].items():
        print(f"  {table_name}: {table_stats['completed']}/{table_stats['total']} 完成")
    
    print("\n🎯 4. 获取处理计划")
    print("-" * 40)
    
    processing_plan = dep_manager.get_processing_plan()
    for round_num, fields in processing_plan.items():
        if fields:
            print(f"第 {round_num} 轮 ({len(fields)} 个字段):")
            for table, field in fields[:3]:  # 只显示前3个
                print(f"  - {table}.{field}")
            if len(fields) > 3:
                print(f"  ... 还有 {len(fields) - 3} 个字段")
        else:
            print(f"第 {round_num} 轮: 无字段")

def demo_core_engine():
    """演示核心引擎的多轮处理"""
    print("\n" + "=" * 60)
    print("🔧 核心引擎多轮处理演示")
    print("=" * 60)
    
    # 创建核心引擎
    engine = CoreEngine()
    
    print("\n📋 引擎状态:")
    status = engine.get_processing_status()
    print(f"已加载处理器: {len(status['loaded_processors'])}")
    print(f"处理顺序: {len(status['processing_order'])} 个表格")
    print(f"缓存大小: {status['cache_size']}")
    
    # 测试数据
    test_data = {
        "原始商品名称": "爱肯拿 农场盛宴 全猫粮 1.8kg 💰128.5元",
        "商品信息ID": "test_001",
        "信息来源": "测试数据"
    }
    
    print(f"\n📥 测试数据: {test_data}")
    
    try:
        print("\n🔄 开始多轮处理...")
        # 注意：这里可能会因为MongoDB连接失败而报错，这是正常的演示行为
        results = engine.process_data_flow(test_data)
        print(f"✅ 处理完成: {len(results)} 个表格")
        
        for table_name, result in results.items():
            print(f"  {table_name}: {result.get('status', '未知状态')}")
            
    except Exception as e:
        print(f"⚠️ 处理过程中遇到预期错误: {e}")
        print("这通常是因为MongoDB连接或某些依赖未就绪，这在演示中是正常的")

def show_solution_summary():
    """展示解决方案总结"""
    print("\n" + "=" * 60)
    print("💡 解决方案总结")
    print("=" * 60)
    
    print("""
🎯 针对您提出的复杂依赖关系问题，我设计了以下解决方案：

1️⃣ 多轮迭代处理策略
   - 第一轮：处理所有表的"源信息"和"第一原生字段"
   - 第二轮：处理所有表的"关联字段"
   - 第三轮：处理所有表的"第二原生字段"
   - 第四轮：处理复杂的交叉依赖字段

2️⃣ 依赖检查机制
   - 每个字段计算前，先检查所有依赖是否就绪
   - 未就绪的字段标记为"等待依赖"，下一轮再尝试
   - 避免了循环依赖和计算顺序问题

3️⃣ 状态跟踪系统
   - 实时跟踪每个字段的计算状态
   - 支持错误处理和重试机制
   - 提供详细的进度报告

4️⃣ 灵活的扩展性
   - 可以轻松添加新的表格和字段依赖
   - 支持复杂的计算逻辑
   - 模块化设计，便于维护

🚀 这样，您就可以：
   ✅ 同时实现所有12个表格的字段逻辑
   ✅ 自动处理复杂的依赖关系
   ✅ 确保计算顺序的正确性
   ✅ 获得完整的处理状态反馈

📝 下一步建议：
   1. 完善各表格处理器的具体字段计算逻辑
   2. 添加更多的依赖关系定义
   3. 集成MongoDB数据同步功能
   4. 添加更多的错误处理和重试机制
    """)

if __name__ == "__main__":
    # 运行演示
    demo_dependency_system()
    demo_core_engine()
    show_solution_summary()
    
    print("\n" + "=" * 60)
    print("🎉 演示完成！")
    print("=" * 60)

from A_连接MongoDB.MongoDB_connector import MongoDBConnector
from A_config.settings import FIELD_MARKERS, ICON_TO_COLLECTION
from datetime import datetime

class LogicTableModel:
    def __init__(self):
        # 数据库连接
        self.connector = MongoDBConnector()
        self.connected = self.connector.connect()
        self.table_name = "逻辑表（底库）"
        self.icon = FIELD_MARKERS[self.table_name]
        self.collection = self.connector.get_collection(ICON_TO_COLLECTION[self.icon])
        
        # 字段定义
        self.fields = {
            "规则ID": f"{self.icon}规则ID",
            "字段类型": f"{self.icon}字段类型",
            "字段名称": f"{self.icon}字段名称",
            "计算公式": f"{self.icon}计算公式",
            "依赖字段": f"{self.icon}依赖字段",
            "适用表名": f"{self.icon}适用表名"
        }

    def add_rule(self, field_type, field_name, formula, depend_fields, apply_table):
        """添加字段规则"""
        if not self.connected:
            print("❌ 数据库未连接")
            return None
            
        rule = {
            self.fields["规则ID"]: f"{self.icon}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
            self.fields["字段类型"]: field_type,
            self.fields["字段名称"]: field_name,
            self.fields["计算公式"]: formula,
            self.fields["依赖字段"]: depend_fields,
            self.fields["适用表名"]: apply_table
        }
        
        try:
            return self.collection.insert_one(rule).inserted_id
        except Exception as e:
            print(f"❌ 添加规则失败: {e}")
            return None

    def get_rules(self, table_name, field_type=None):
        """获取指定表的规则"""
        if not self.connected:
            return []
            
        query = {self.fields["适用表名"]: table_name}
        if field_type:
            query[self.fields["字段类型"]] = field_type
            
        return list(self.collection.find(query, {"_id": 0}))

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
养宠好物折扣券分享业务 - 智能启动程序
自动处理NAS连接、本地缓存、emoji路径等问题
"""

import sys
import os
from pathlib import Path
import logging

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('startup.log', encoding='utf-8')
        ]
    )
    return logging.getLogger('启动程序')

def setup_environment():
    """设置运行环境"""
    logger = logging.getLogger('启动程序')
    
    # 获取项目根目录
    project_root = Path(__file__).parent
    os.chdir(project_root)
    
    # 添加Python路径
    python_paths = [
        str(project_root),
        str(project_root / "A_main"),
        str(project_root / "A_连接MongoDB"),
        str(project_root / "A_config"),
        str(project_root / "C_公式代码")
    ]
    
    for path in python_paths:
        if path not in sys.path:
            sys.path.insert(0, path)
    
    logger.info(f"✅ 环境设置完成，项目根目录: {project_root}")
    return project_root

def check_dependencies():
    """检查依赖"""
    logger = logging.getLogger('启动程序')
    
    try:
        # 检查核心模块
        from A_main.本地缓存管理器 import 缓存管理器
        from A_main.架构适配器 import 架构适配器实例
        from A_main.multi_round_scheduler import MultiRoundScheduler
        
        logger.info("✅ 核心模块导入成功")
        return True
        
    except ImportError as e:
        logger.error(f"❌ 模块导入失败: {e}")
        return False

def handle_nas_and_cache():
    """处理NAS连接和本地缓存"""
    logger = logging.getLogger('启动程序')
    
    try:
        from A_main.本地缓存管理器 import 缓存管理器
        
        # 检查NAS状态
        nas_available = 缓存管理器.is_nas_available()
        logger.info(f"NAS状态: {'可用' if nas_available else '不可用'}")
        
        if nas_available:
            # NAS可用，同步到本地缓存
            logger.info("🔄 同步NAS数据到本地缓存...")
            success = 缓存管理器.sync_to_local()
            if success:
                logger.info("✅ 同步完成")
            else:
                logger.warning("⚠️ 同步失败，但可以继续使用")
        else:
            # NAS不可用，检查本地缓存
            cache_status = 缓存管理器.get_cache_status()
            if cache_status["cached_files"] > 0:
                logger.info(f"📁 使用本地缓存 ({cache_status['cached_files']} 个文件)")
            else:
                logger.warning("⚠️ NAS不可用且无本地缓存，功能可能受限")
        
        # 设置工作环境
        work_dir = 缓存管理器.setup_offline_environment()
        logger.info(f"🏠 工作目录: {work_dir}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 缓存管理失败: {e}")
        return False

def show_startup_menu():
    """显示启动菜单"""
    print("=" * 80)
    print("🚀 养宠好物折扣券分享业务 - 文本解构重组系统")
    print("=" * 80)
    print()
    print("请选择要执行的功能:")
    print("1. 🎯 完整系统演示 (推荐)")
    print("2. 🔧 多轮处理器测试")
    print("3. 🧪 架构适配器测试")
    print("4. 📊 系统状态检查")
    print("5. 🗄️ 缓存管理")
    print("6. 📦 创建便携包")
    print("7. 🔍 单个表格测试")
    print("0. 退出")
    print()
    
    while True:
        try:
            choice = input("请输入选项 (0-7): ").strip()
            if choice in ['0', '1', '2', '3', '4', '5', '6', '7']:
                return choice
            else:
                print("❌ 无效选项，请重新输入")
        except KeyboardInterrupt:
            print("\n👋 再见！")
            return '0'

def execute_choice(choice: str):
    """执行用户选择"""
    logger = logging.getLogger('启动程序')
    
    try:
        if choice == '0':
            print("👋 再见！")
            return
        
        elif choice == '1':
            print("\n🎯 启动完整系统演示...")
            from A_main.final_demo import demo_complete_system
            demo_complete_system()
        
        elif choice == '2':
            print("\n🔧 启动多轮处理器测试...")
            from A_main.multi_round_scheduler import MultiRoundScheduler
            
            scheduler = MultiRoundScheduler()
            test_data = {
                "🍬商品信息（原始版）": "爱肯拿 农场盛宴 全猫粮 1.8kg 💰128.5元 券后💰98元",
                "🍬商品信息ID": "startup_test_001",
                "🍬信息来源 / 发车人": "启动程序测试"
            }
            
            results = scheduler.process_complete_workflow(test_data)
            print(f"✅ 处理完成，共处理 {len(results)} 个表格")
        
        elif choice == '3':
            print("\n🧪 启动架构适配器测试...")
            from A_main.架构适配器 import 架构适配器实例
            
            # 测试表格映射
            test_tables = ["🧪 规格解构（基建）", "Ⓜ️ 品牌识别（基建）"]
            for table in test_tables:
                safe_name = 架构适配器实例.get_safe_table_name(table)
                folder_path = 架构适配器实例.get_table_folder_path(table)
                print(f"表格: {table}")
                print(f"安全名称: {safe_name}")
                print(f"文件夹: {folder_path}")
                print()
        
        elif choice == '4':
            print("\n📊 系统状态检查...")
            
            # NAS状态
            from A_main.本地缓存管理器 import 缓存管理器
            cache_status = 缓存管理器.get_cache_status()
            
            print(f"NAS状态: {'✅ 可用' if cache_status['nas_available'] else '❌ 不可用'}")
            print(f"本地缓存: {cache_status['cached_files']} 个文件")
            print(f"缓存大小: {cache_status['cache_size']/1024/1024:.2f}MB")
            print(f"最后同步: {cache_status['last_sync'] or '从未同步'}")
            
            # 架构状态
            from A_main.架构适配器 import 架构适配器实例
            架构适配器实例.create_safe_file_structure()
        
        elif choice == '5':
            print("\n🗄️ 缓存管理...")
            from A_main.本地缓存管理器 import 缓存管理器
            
            print("缓存管理选项:")
            print("1. 强制同步到本地")
            print("2. 回写到NAS")
            print("3. 清理本地缓存")
            
            sub_choice = input("请选择 (1-3): ").strip()
            
            if sub_choice == '1':
                success = 缓存管理器.sync_to_local(force=True)
                print(f"同步结果: {'✅ 成功' if success else '❌ 失败'}")
            elif sub_choice == '2':
                success = 缓存管理器.sync_from_local()
                print(f"回写结果: {'✅ 成功' if success else '❌ 失败'}")
            elif sub_choice == '3':
                import shutil
                if 缓存管理器.local_cache_root.exists():
                    shutil.rmtree(缓存管理器.local_cache_root)
                    print("✅ 本地缓存已清理")
                else:
                    print("ℹ️ 本地缓存不存在")
        
        elif choice == '6':
            print("\n📦 创建便携包...")
            from A_main.本地缓存管理器 import 缓存管理器
            
            output_path = 缓存管理器.create_portable_package()
            if output_path:
                print(f"✅ 便携包已创建: {output_path}")
            else:
                print("❌ 便携包创建失败")
        
        elif choice == '7':
            print("\n🔍 单个表格测试...")
            from A_main.架构适配器 import 架构适配器实例
            
            tables = 架构适配器实例.get_all_table_names()
            print("可用表格:")
            for i, table in enumerate(tables, 1):
                print(f"{i:2d}. {table}")
            
            try:
                table_choice = int(input("请选择表格编号: ")) - 1
                if 0 <= table_choice < len(tables):
                    selected_table = tables[table_choice]
                    processor = 架构适配器实例.safe_import_processor(selected_table)
                    
                    if processor:
                        print(f"✅ 成功加载处理器: {selected_table}")
                        # 这里可以添加具体的测试逻辑
                    else:
                        print(f"❌ 无法加载处理器: {selected_table}")
                else:
                    print("❌ 无效的表格编号")
            except ValueError:
                print("❌ 请输入有效的数字")
        
    except Exception as e:
        logger.error(f"❌ 执行失败: {e}")
        print(f"❌ 执行失败: {e}")

def main():
    """主函数"""
    logger = setup_logging()
    
    print("🚀 正在启动系统...")
    
    # 1. 设置环境
    project_root = setup_environment()
    
    # 2. 检查依赖
    if not check_dependencies():
        print("❌ 依赖检查失败，请检查安装")
        return
    
    # 3. 处理NAS和缓存
    if not handle_nas_and_cache():
        print("⚠️ 缓存管理有问题，但系统可以继续运行")
    
    # 4. 显示菜单并执行
    while True:
        choice = show_startup_menu()
        if choice == '0':
            break
        
        execute_choice(choice)
        
        if choice != '0':
            input("\n按回车键继续...")

if __name__ == "__main__":
    main()

{
    "python.defaultInterpreterPath": "./A_conda虚拟环境/bin/python",
    "python.terminal.activateEnvironment": true,
    
    // 文件编码设置
    "files.encoding": "utf8",
    "files.autoGuessEncoding": true,
    
    // 文件搜索设置
    "search.useIgnoreFiles": false,
    "search.followSymlinks": false,
    "search.exclude": {
        "**/node_modules": true,
        "**/bower_components": true,
        "**/*.code-search": true,
        "**/__pycache__": true,
        "**/*.pyc": true
    },
    
    // Python路径设置
    "python.analysis.extraPaths": [
        "./A_main",
        "./A_连接MongoDB",
        "./C_公式代码",
        "./C_公式代码/01_🧠 逻辑表（底库）",
        "./C_公式代码/02_📁｜✍️ 信息获取(基建）",
        "./C_公式代码/03_🎫 优惠券管理（底库）",
        "./C_公式代码/04_Ⓜ️｜✍️ 品牌识别（基建）",
        "./C_公式代码/05_🛍️｜✍️ 品类｜口味｜制作工艺 ｜ 使用对象 ｜ 成长期（基建）",
        "./C_公式代码/06_🔪｜✍️ sku解构(基建)",
        "./C_公式代码/07_🐤 sku重组-网站专用（基建）",
        "./C_公式代码/08_🐽 sku重组-社群专用 (基建)",
        "./C_公式代码/09_🧪 规格解构（基建）",
        "./C_公式代码/10_💰 价格解构（基建）",
        "./C_公式代码/11_💻 单价计算（基建）",
        "./C_公式代码/12_🔥 价格热度计算（基建）",
        "./C_公式代码/13_🤖｜✍️ 中转表（工具）"
    ],
    
    // 文件关联设置
    "files.associations": {
        "*.py": "python"
    },
    
    // 终端设置
    "terminal.integrated.cwd": "${workspaceFolder}",
    "terminal.integrated.env.osx": {
        "PYTHONPATH": "${workspaceFolder}:${workspaceFolder}/A_main:${workspaceFolder}/A_连接MongoDB:${workspaceFolder}/C_公式代码"
    },
    "terminal.integrated.env.linux": {
        "PYTHONPATH": "${workspaceFolder}:${workspaceFolder}/A_main:${workspaceFolder}/A_连接MongoDB:${workspaceFolder}/C_公式代码"
    },
    "terminal.integrated.env.windows": {
        "PYTHONPATH": "${workspaceFolder};${workspaceFolder}/A_main;${workspaceFolder}/A_连接MongoDB;${workspaceFolder}/C_公式代码"
    },
    
    // 代码格式化设置
    "python.formatting.provider": "black",
    "python.linting.enabled": true,
    "python.linting.pylintEnabled": false,
    "python.linting.flake8Enabled": true,
    
    // 智能提示设置
    "python.analysis.autoImportCompletions": true,
    "python.analysis.completeFunctionParens": true,
    
    // 文件监视设置
    "files.watcherExclude": {
        "**/.git/objects/**": true,
        "**/.git/subtree-cache/**": true,
        "**/node_modules/*/**": true,
        "**/__pycache__/**": true,
        "**/A_conda虚拟环境/**": true
    },
    
    // 工作区设置
    "explorer.sortOrder": "type",
    "explorer.fileNesting.enabled": true,
    "explorer.fileNesting.patterns": {
        "*.py": "${capture}_test.py,${capture}_model.py,${capture}_processor.py"
    }
}

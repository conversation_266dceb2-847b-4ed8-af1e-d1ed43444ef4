2025-08-04 22:58:04,665 - 启动程序 - INFO - ✅ 环境设置完成，项目根目录: /Volumes/阿方方备份/Nars-个人wiki/Nars-代码管理
2025-08-04 22:58:05,123 - 启动程序 - INFO - ✅ 核心模块导入成功
2025-08-04 22:58:05,124 - 启动程序 - INFO - NAS状态: 可用
2025-08-04 22:58:05,124 - 启动程序 - INFO - 🔄 同步NAS数据到本地缓存...
2025-08-04 22:58:05,124 - 本地缓存管理器 - INFO - 开始同步NAS数据到本地缓存...
2025-08-04 22:58:07,671 - 本地缓存管理器 - INFO - 同步完成: 85 个文件, 总大小: 1.77MB
2025-08-04 22:58:07,671 - 启动程序 - INFO - ✅ 同步完成
2025-08-04 22:58:07,671 - 本地缓存管理器 - INFO - 使用NAS目录
2025-08-04 22:58:07,672 - 本地缓存管理器 - INFO - 离线环境已设置，工作目录: /Volumes/阿方方备份/Nars-个人wiki/Nars-代码管理
2025-08-04 22:58:07,672 - 启动程序 - INFO - 🏠 工作目录: /Volumes/阿方方备份/Nars-个人wiki/Nars-代码管理
2025-08-04 22:58:31,529 - 架构适配器 - INFO - 检查文件结构...
2025-08-04 22:58:31,531 - 架构适配器 - INFO - 缺失的文件:
2025-08-04 22:58:31,531 - 架构适配器 - INFO -   - 处理器: /Volumes/阿方方备份/Nars-个人wiki/Nars-代码管理/C_公式代码/04_Ⓜ️｜✍️ 品牌识别（基建）/brand_processor.py
2025-08-04 22:58:31,531 - 架构适配器 - INFO -   - 处理器: /Volumes/阿方方备份/Nars-个人wiki/Nars-代码管理/C_公式代码/06_🔪｜✍️ sku解构(基建)/sku_processor.py
2025-08-04 22:58:31,531 - 架构适配器 - INFO -   - 模型: /Volumes/阿方方备份/Nars-个人wiki/Nars-代码管理/C_公式代码/06_🔪｜✍️ sku解构(基建)/sku_table_model.py
2025-08-04 22:58:31,531 - 架构适配器 - INFO -   - 处理器: /Volumes/阿方方备份/Nars-个人wiki/Nars-代码管理/C_公式代码/07_🐤 sku重组-网站专用（基建）/web_reorg_processor.py
2025-08-04 22:58:31,531 - 架构适配器 - INFO -   - 模型: /Volumes/阿方方备份/Nars-个人wiki/Nars-代码管理/C_公式代码/07_🐤 sku重组-网站专用（基建）/web_reorg_table_model.py
2025-08-04 22:58:31,542 - 架构适配器 - INFO -   - 处理器: /Volumes/阿方方备份/Nars-个人wiki/Nars-代码管理/C_公式代码/08_🐽 sku重组-社群专用 (基建)/social_reorg_processor.py
2025-08-04 22:58:31,542 - 架构适配器 - INFO -   - 模型: /Volumes/阿方方备份/Nars-个人wiki/Nars-代码管理/C_公式代码/08_🐽 sku重组-社群专用 (基建)/social_reorg_table_model.py
2025-08-04 22:58:31,542 - 架构适配器 - INFO -   - 处理器: /Volumes/阿方方备份/Nars-个人wiki/Nars-代码管理/C_公式代码/09_🧪 规格解构（基建）/spec_processor.py
2025-08-04 22:58:31,542 - 架构适配器 - INFO -   - 模型: /Volumes/阿方方备份/Nars-个人wiki/Nars-代码管理/C_公式代码/09_🧪 规格解构（基建）/spec_table_model.py
2025-08-04 22:58:31,542 - 架构适配器 - INFO -   - 处理器: /Volumes/阿方方备份/Nars-个人wiki/Nars-代码管理/C_公式代码/10_💰 价格解构（基建）/price_processor.py
2025-08-04 22:58:31,542 - 架构适配器 - INFO -   - 模型: /Volumes/阿方方备份/Nars-个人wiki/Nars-代码管理/C_公式代码/10_💰 价格解构（基建）/price_table_model.py
2025-08-04 22:58:31,542 - 架构适配器 - INFO -   - 处理器: /Volumes/阿方方备份/Nars-个人wiki/Nars-代码管理/C_公式代码/11_💻 单价计算（基建）/unit_price_processor.py
2025-08-04 22:58:31,543 - 架构适配器 - INFO -   - 处理器: /Volumes/阿方方备份/Nars-个人wiki/Nars-代码管理/C_公式代码/12_🔥 价格热度计算（基建）/price_trend_processor.py
2025-08-04 22:58:31,543 - 架构适配器 - INFO -   - 模型: /Volumes/阿方方备份/Nars-个人wiki/Nars-代码管理/C_公式代码/12_🔥 价格热度计算（基建）/price_trend_table_model.py
2025-08-04 23:01:30,633 - 启动程序 - INFO - ✅ 环境设置完成，项目根目录: /Volumes/阿方方备份/Nars-个人wiki/Nars-代码管理
2025-08-04 23:01:31,429 - 启动程序 - INFO - ✅ 核心模块导入成功
2025-08-04 23:01:31,429 - 启动程序 - INFO - NAS状态: 可用
2025-08-04 23:01:31,429 - 启动程序 - INFO - 🔄 同步NAS数据到本地缓存...
2025-08-04 23:01:31,429 - 本地缓存管理器 - INFO - 开始同步NAS数据到本地缓存...
2025-08-04 23:01:33,725 - 本地缓存管理器 - INFO - 同步完成: 8 个文件, 总大小: 0.14MB
2025-08-04 23:01:33,725 - 启动程序 - INFO - ✅ 同步完成
2025-08-04 23:01:33,726 - 本地缓存管理器 - INFO - 使用NAS目录
2025-08-04 23:01:33,726 - 本地缓存管理器 - INFO - 离线环境已设置，工作目录: /Volumes/阿方方备份/Nars-个人wiki/Nars-代码管理
2025-08-04 23:01:33,726 - 启动程序 - INFO - 🏠 工作目录: /Volumes/阿方方备份/Nars-个人wiki/Nars-代码管理
2025-08-04 23:01:45,276 - DependencyManager - INFO - ✅ 已初始化 7 个依赖关系
2025-08-04 23:01:45,277 - MultiRoundScheduler - INFO - 🚀 开始多轮迭代处理工作流
2025-08-04 23:01:45,277 - MultiRoundScheduler - INFO - ============================================================
2025-08-04 23:01:45,277 - MultiRoundScheduler - INFO - 🔵 第一轮：所有表的源信息和第一原生字段
2025-08-04 23:01:45,277 - MultiRoundScheduler - INFO - ============================================================
2025-08-04 23:01:45,277 - Round1Processor - INFO - 🔵 开始第一轮处理：源信息和第一原生字段
2025-08-04 23:01:45,277 - Round1Processor - INFO - 📊 处理表格: 🧠 逻辑表（底库）
2025-08-04 23:01:45,277 - Round1Processor - INFO - ✅ 完成表格: 🧠 逻辑表（底库）
2025-08-04 23:01:45,277 - Round1Processor - INFO - 📊 处理表格: 📁 信息获取（基建）
2025-08-04 23:01:45,291 - Round1Processor - INFO - ✅ 完成表格: 📁 信息获取（基建）
2025-08-04 23:01:45,291 - Round1Processor - INFO - 📊 处理表格: 🎫 优惠券管理（底库）
2025-08-04 23:01:45,291 - Round1Processor - INFO - ✅ 完成表格: 🎫 优惠券管理（底库）
2025-08-04 23:01:45,291 - Round1Processor - INFO - 📊 处理表格: Ⓜ️ 品牌识别（基建）
2025-08-04 23:01:45,292 - Round1Processor - INFO - ✅ 完成表格: Ⓜ️ 品牌识别（基建）
2025-08-04 23:01:45,292 - Round1Processor - INFO - 📊 处理表格: 🛍️ 品类管理（基建）
2025-08-04 23:01:45,292 - Round1Processor - INFO - ✅ 完成表格: 🛍️ 品类管理（基建）
2025-08-04 23:01:45,292 - Round1Processor - INFO - 📊 处理表格: 🔪 SKU解构（基建）
2025-08-04 23:01:45,293 - Round1Processor - INFO - ✅ 完成表格: 🔪 SKU解构（基建）
2025-08-04 23:01:45,293 - Round1Processor - INFO - 📊 处理表格: 🧪 规格解构（基建）
2025-08-04 23:01:45,293 - Round1Processor - INFO - ✅ 完成表格: 🧪 规格解构（基建）
2025-08-04 23:01:45,293 - Round1Processor - INFO - 📊 处理表格: 💰 价格解构（基建）
2025-08-04 23:01:45,294 - Round1Processor - INFO - ✅ 完成表格: 💰 价格解构（基建）
2025-08-04 23:01:45,294 - Round1Processor - INFO - 📊 处理表格: 💻 单价计算（基建）
2025-08-04 23:01:45,294 - Round1Processor - INFO - ✅ 完成表格: 💻 单价计算（基建）
2025-08-04 23:01:45,294 - Round1Processor - INFO - 📊 处理表格: 🔥 价格热度计算（基建）
2025-08-04 23:01:45,294 - Round1Processor - INFO - ✅ 完成表格: 🔥 价格热度计算（基建）
2025-08-04 23:01:45,294 - Round1Processor - INFO - 📊 处理表格: 🐤 SKU重组-网站专用（基建）
2025-08-04 23:01:45,294 - Round1Processor - INFO - ✅ 完成表格: 🐤 SKU重组-网站专用（基建）
2025-08-04 23:01:45,294 - Round1Processor - INFO - 📊 处理表格: 🐽 SKU重组-社群专用（基建）
2025-08-04 23:01:45,294 - Round1Processor - INFO - ✅ 完成表格: 🐽 SKU重组-社群专用（基建）
2025-08-04 23:01:45,294 - Round1Processor - INFO - 📊 处理表格: 🤖 中转表（工具）
2025-08-04 23:01:45,294 - Round1Processor - INFO - ✅ 完成表格: 🤖 中转表（工具）
2025-08-04 23:01:45,294 - Round1Processor - INFO - 🎉 第一轮处理完成
2025-08-04 23:01:45,294 - MultiRoundScheduler - INFO - 📊 第一轮处理摘要:
2025-08-04 23:01:45,294 - MultiRoundScheduler - INFO -    总表格数: 13
2025-08-04 23:01:45,294 - MultiRoundScheduler - INFO -    完成表格: 13
2025-08-04 23:01:45,294 - MultiRoundScheduler - INFO -    失败表格: 0
2025-08-04 23:01:45,294 - MultiRoundScheduler - INFO -    成功率: 100.0%
2025-08-04 23:01:45,294 - MultiRoundScheduler - INFO - 
============================================================
2025-08-04 23:01:45,295 - MultiRoundScheduler - INFO - 🟡 第二轮：所有表的关联字段
2025-08-04 23:01:45,295 - MultiRoundScheduler - INFO - ============================================================
2025-08-04 23:01:45,295 - Round2Processor - INFO - 🟡 开始第二轮处理：关联字段
2025-08-04 23:01:45,295 - Round2Processor - INFO - 📊 处理表格关联字段: 🧠 逻辑表（底库）
2025-08-04 23:01:45,295 - Round2Processor - INFO - ✅ 完成表格关联字段: 🧠 逻辑表（底库）
2025-08-04 23:01:45,295 - Round2Processor - INFO - 📊 处理表格关联字段: 📁 信息获取（基建）
2025-08-04 23:01:45,295 - Round2Processor - INFO - ✅ 完成表格关联字段: 📁 信息获取（基建）
2025-08-04 23:01:45,295 - Round2Processor - INFO - 📊 处理表格关联字段: 🎫 优惠券管理（底库）
2025-08-04 23:01:45,295 - Round2Processor - INFO - ✅ 完成表格关联字段: 🎫 优惠券管理（底库）
2025-08-04 23:01:45,295 - Round2Processor - INFO - 📊 处理表格关联字段: Ⓜ️ 品牌识别（基建）
2025-08-04 23:01:45,295 - Round2Processor - INFO - ✅ 完成表格关联字段: Ⓜ️ 品牌识别（基建）
2025-08-04 23:01:45,295 - Round2Processor - INFO - 📊 处理表格关联字段: 🛍️ 品类管理（基建）
2025-08-04 23:01:45,295 - Round2Processor - INFO - ✅ 完成表格关联字段: 🛍️ 品类管理（基建）
2025-08-04 23:01:45,295 - Round2Processor - INFO - 📊 处理表格关联字段: 🔪 SKU解构（基建）
2025-08-04 23:01:45,297 - Round2Processor - INFO - ✅ 完成表格关联字段: 🔪 SKU解构（基建）
2025-08-04 23:01:45,297 - Round2Processor - INFO - 📊 处理表格关联字段: 🧪 规格解构（基建）
2025-08-04 23:01:45,297 - Round2Processor - INFO - ✅ 完成表格关联字段: 🧪 规格解构（基建）
2025-08-04 23:01:45,297 - Round2Processor - INFO - 📊 处理表格关联字段: 💰 价格解构（基建）
2025-08-04 23:01:45,297 - Round2Processor - INFO - ✅ 完成表格关联字段: 💰 价格解构（基建）
2025-08-04 23:01:45,297 - Round2Processor - INFO - 📊 处理表格关联字段: 💻 单价计算（基建）
2025-08-04 23:01:45,297 - Round2Processor - INFO - ✅ 完成表格关联字段: 💻 单价计算（基建）
2025-08-04 23:01:45,297 - Round2Processor - INFO - 📊 处理表格关联字段: 🔥 价格热度计算（基建）
2025-08-04 23:01:45,297 - Round2Processor - INFO - ✅ 完成表格关联字段: 🔥 价格热度计算（基建）
2025-08-04 23:01:45,297 - Round2Processor - INFO - 📊 处理表格关联字段: 🐤 SKU重组-网站专用（基建）
2025-08-04 23:01:45,297 - Round2Processor - INFO - ✅ 完成表格关联字段: 🐤 SKU重组-网站专用（基建）
2025-08-04 23:01:45,298 - Round2Processor - INFO - 📊 处理表格关联字段: 🐽 SKU重组-社群专用（基建）
2025-08-04 23:01:45,298 - Round2Processor - INFO - ✅ 完成表格关联字段: 🐽 SKU重组-社群专用（基建）
2025-08-04 23:01:45,298 - Round2Processor - INFO - 📊 处理表格关联字段: 🤖 中转表（工具）
2025-08-04 23:01:45,298 - Round2Processor - INFO - ✅ 完成表格关联字段: 🤖 中转表（工具）
2025-08-04 23:01:45,298 - Round2Processor - INFO - 🎉 第二轮处理完成
2025-08-04 23:01:45,298 - MultiRoundScheduler - INFO - 📊 第二轮处理摘要:
2025-08-04 23:01:45,298 - MultiRoundScheduler - INFO -    总表格数: 13
2025-08-04 23:01:45,298 - MultiRoundScheduler - INFO -    完成表格: 13
2025-08-04 23:01:45,299 - MultiRoundScheduler - INFO -    失败表格: 0
2025-08-04 23:01:45,299 - MultiRoundScheduler - INFO -    成功率: 100.0%
2025-08-04 23:01:45,299 - MultiRoundScheduler - INFO - 
============================================================
2025-08-04 23:01:45,299 - MultiRoundScheduler - INFO - 🟢 第三轮：所有表的第二原生字段
2025-08-04 23:01:45,299 - MultiRoundScheduler - INFO - ============================================================
2025-08-04 23:01:45,299 - MultiRoundScheduler - INFO - 🟢 开始第三轮处理：第二原生字段
2025-08-04 23:01:45,299 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 🧠 逻辑表（底库）
2025-08-04 23:01:45,299 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 🧠 逻辑表（底库）
2025-08-04 23:01:45,299 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 📁 信息获取（基建）
2025-08-04 23:01:45,299 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 📁 信息获取（基建）
2025-08-04 23:01:45,299 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 🎫 优惠券管理（底库）
2025-08-04 23:01:45,299 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 🎫 优惠券管理（底库）
2025-08-04 23:01:45,299 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: Ⓜ️ 品牌识别（基建）
2025-08-04 23:01:45,299 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: Ⓜ️ 品牌识别（基建）
2025-08-04 23:01:45,300 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 🛍️ 品类管理（基建）
2025-08-04 23:01:45,300 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 🛍️ 品类管理（基建）
2025-08-04 23:01:45,300 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 🔪 SKU解构（基建）
2025-08-04 23:01:45,300 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 🔪 SKU解构（基建）
2025-08-04 23:01:45,300 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 🧪 规格解构（基建）
2025-08-04 23:01:45,300 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 🧪 规格解构（基建）
2025-08-04 23:01:45,300 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 💰 价格解构（基建）
2025-08-04 23:01:45,300 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 💰 价格解构（基建）
2025-08-04 23:01:45,300 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 💻 单价计算（基建）
2025-08-04 23:01:45,300 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 💻 单价计算（基建）
2025-08-04 23:01:45,300 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 🔥 价格热度计算（基建）
2025-08-04 23:01:45,300 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 🔥 价格热度计算（基建）
2025-08-04 23:01:45,300 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 🐤 SKU重组-网站专用（基建）
2025-08-04 23:01:45,300 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 🐤 SKU重组-网站专用（基建）
2025-08-04 23:01:45,300 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 🐽 SKU重组-社群专用（基建）
2025-08-04 23:01:45,300 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 🐽 SKU重组-社群专用（基建）
2025-08-04 23:01:45,300 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 🤖 中转表（工具）
2025-08-04 23:01:45,300 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 🤖 中转表（工具）
2025-08-04 23:01:45,300 - MultiRoundScheduler - INFO - 📊 第三轮处理摘要:
2025-08-04 23:01:45,300 - MultiRoundScheduler - INFO -    总表格数: 13
2025-08-04 23:01:45,300 - MultiRoundScheduler - INFO -    完成表格: 13
2025-08-04 23:01:45,301 - MultiRoundScheduler - INFO -    失败表格: 0
2025-08-04 23:01:45,301 - MultiRoundScheduler - INFO -    成功率: 100.0%
2025-08-04 23:01:45,301 - MultiRoundScheduler - INFO - 
============================================================
2025-08-04 23:01:45,301 - MultiRoundScheduler - INFO - 🟣 第四轮：复杂交叉依赖字段
2025-08-04 23:01:45,301 - MultiRoundScheduler - INFO - ============================================================
2025-08-04 23:01:45,301 - MultiRoundScheduler - INFO - 🟣 开始第四轮处理：复杂交叉依赖
2025-08-04 23:01:45,301 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 🧠 逻辑表（底库）
2025-08-04 23:01:45,301 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 🧠 逻辑表（底库）
2025-08-04 23:01:45,301 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 📁 信息获取（基建）
2025-08-04 23:01:45,301 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 📁 信息获取（基建）
2025-08-04 23:01:45,301 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 🎫 优惠券管理（底库）
2025-08-04 23:01:45,301 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 🎫 优惠券管理（底库）
2025-08-04 23:01:45,301 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: Ⓜ️ 品牌识别（基建）
2025-08-04 23:01:45,301 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: Ⓜ️ 品牌识别（基建）
2025-08-04 23:01:45,301 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 🛍️ 品类管理（基建）
2025-08-04 23:01:45,301 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 🛍️ 品类管理（基建）
2025-08-04 23:01:45,301 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 🔪 SKU解构（基建）
2025-08-04 23:01:45,301 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 🔪 SKU解构（基建）
2025-08-04 23:01:45,301 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 🧪 规格解构（基建）
2025-08-04 23:01:45,301 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 🧪 规格解构（基建）
2025-08-04 23:01:45,302 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 💰 价格解构（基建）
2025-08-04 23:01:45,302 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 💰 价格解构（基建）
2025-08-04 23:01:45,302 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 💻 单价计算（基建）
2025-08-04 23:01:45,302 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 💻 单价计算（基建）
2025-08-04 23:01:45,302 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 🔥 价格热度计算（基建）
2025-08-04 23:01:45,302 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 🔥 价格热度计算（基建）
2025-08-04 23:01:45,302 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 🐤 SKU重组-网站专用（基建）
2025-08-04 23:01:45,302 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 🐤 SKU重组-网站专用（基建）
2025-08-04 23:01:45,302 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 🐽 SKU重组-社群专用（基建）
2025-08-04 23:01:45,302 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 🐽 SKU重组-社群专用（基建）
2025-08-04 23:01:45,302 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 🤖 中转表（工具）
2025-08-04 23:01:45,302 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 🤖 中转表（工具）
2025-08-04 23:01:45,302 - MultiRoundScheduler - INFO - 📊 第四轮处理摘要:
2025-08-04 23:01:45,302 - MultiRoundScheduler - INFO -    总表格数: 13
2025-08-04 23:01:45,302 - MultiRoundScheduler - INFO -    完成表格: 13
2025-08-04 23:01:45,302 - MultiRoundScheduler - INFO -    失败表格: 0
2025-08-04 23:01:45,302 - MultiRoundScheduler - INFO -    成功率: 100.0%
2025-08-04 23:01:45,303 - MultiRoundScheduler - INFO - 
============================================================
2025-08-04 23:01:45,303 - MultiRoundScheduler - INFO - 🎉 多轮处理工作流完成
2025-08-04 23:01:45,303 - MultiRoundScheduler - INFO - ============================================================
2025-08-04 23:01:45,303 - MultiRoundScheduler - INFO - ⏱️  处理时长: 0.03秒
2025-08-04 23:01:45,303 - MultiRoundScheduler - INFO - 🔄 完成轮次: 4/4
2025-08-04 23:01:45,303 - MultiRoundScheduler - INFO - 📊 处理表格: 13
2025-08-04 23:01:45,303 - MultiRoundScheduler - INFO - 📈 最终状态统计:
2025-08-04 23:01:45,303 - MultiRoundScheduler - INFO -    全部完成✅: 13个表格
2025-08-04 23:01:45,303 - MultiRoundScheduler - INFO - 🚀 开始多轮迭代处理工作流
2025-08-04 23:01:45,303 - MultiRoundScheduler - INFO - ============================================================
2025-08-04 23:01:45,303 - MultiRoundScheduler - INFO - 🔵 第一轮：所有表的源信息和第一原生字段
2025-08-04 23:01:45,304 - MultiRoundScheduler - INFO - ============================================================
2025-08-04 23:01:45,304 - Round1Processor - INFO - 🔵 开始第一轮处理：源信息和第一原生字段
2025-08-04 23:01:45,304 - Round1Processor - INFO - 📊 处理表格: 🧠 逻辑表（底库）
2025-08-04 23:01:45,304 - Round1Processor - INFO - ✅ 完成表格: 🧠 逻辑表（底库）
2025-08-04 23:01:45,304 - Round1Processor - INFO - 📊 处理表格: 📁 信息获取（基建）
2025-08-04 23:01:45,304 - Round1Processor - INFO - ✅ 完成表格: 📁 信息获取（基建）
2025-08-04 23:01:45,304 - Round1Processor - INFO - 📊 处理表格: 🎫 优惠券管理（底库）
2025-08-04 23:01:45,304 - Round1Processor - INFO - ✅ 完成表格: 🎫 优惠券管理（底库）
2025-08-04 23:01:45,304 - Round1Processor - INFO - 📊 处理表格: Ⓜ️ 品牌识别（基建）
2025-08-04 23:01:45,304 - Round1Processor - INFO - ✅ 完成表格: Ⓜ️ 品牌识别（基建）
2025-08-04 23:01:45,304 - Round1Processor - INFO - 📊 处理表格: 🛍️ 品类管理（基建）
2025-08-04 23:01:45,304 - Round1Processor - INFO - ✅ 完成表格: 🛍️ 品类管理（基建）
2025-08-04 23:01:45,305 - Round1Processor - INFO - 📊 处理表格: 🔪 SKU解构（基建）
2025-08-04 23:01:45,305 - Round1Processor - INFO - ✅ 完成表格: 🔪 SKU解构（基建）
2025-08-04 23:01:45,305 - Round1Processor - INFO - 📊 处理表格: 🧪 规格解构（基建）
2025-08-04 23:01:45,305 - Round1Processor - INFO - ✅ 完成表格: 🧪 规格解构（基建）
2025-08-04 23:01:45,305 - Round1Processor - INFO - 📊 处理表格: 💰 价格解构（基建）
2025-08-04 23:01:45,305 - Round1Processor - INFO - ✅ 完成表格: 💰 价格解构（基建）
2025-08-04 23:01:45,305 - Round1Processor - INFO - 📊 处理表格: 💻 单价计算（基建）
2025-08-04 23:01:45,305 - Round1Processor - INFO - ✅ 完成表格: 💻 单价计算（基建）
2025-08-04 23:01:45,305 - Round1Processor - INFO - 📊 处理表格: 🔥 价格热度计算（基建）
2025-08-04 23:01:45,305 - Round1Processor - INFO - ✅ 完成表格: 🔥 价格热度计算（基建）
2025-08-04 23:01:45,305 - Round1Processor - INFO - 📊 处理表格: 🐤 SKU重组-网站专用（基建）
2025-08-04 23:01:45,306 - Round1Processor - INFO - ✅ 完成表格: 🐤 SKU重组-网站专用（基建）
2025-08-04 23:01:45,306 - Round1Processor - INFO - 📊 处理表格: 🐽 SKU重组-社群专用（基建）
2025-08-04 23:01:45,306 - Round1Processor - INFO - ✅ 完成表格: 🐽 SKU重组-社群专用（基建）
2025-08-04 23:01:45,306 - Round1Processor - INFO - 📊 处理表格: 🤖 中转表（工具）
2025-08-04 23:01:45,306 - Round1Processor - INFO - ✅ 完成表格: 🤖 中转表（工具）
2025-08-04 23:01:45,306 - Round1Processor - INFO - 🎉 第一轮处理完成
2025-08-04 23:01:45,306 - MultiRoundScheduler - INFO - 📊 第一轮处理摘要:
2025-08-04 23:01:45,306 - MultiRoundScheduler - INFO -    总表格数: 13
2025-08-04 23:01:45,306 - MultiRoundScheduler - INFO -    完成表格: 13
2025-08-04 23:01:45,306 - MultiRoundScheduler - INFO -    失败表格: 0
2025-08-04 23:01:45,306 - MultiRoundScheduler - INFO -    成功率: 100.0%
2025-08-04 23:01:45,306 - MultiRoundScheduler - INFO - 
============================================================
2025-08-04 23:01:45,306 - MultiRoundScheduler - INFO - 🟡 第二轮：所有表的关联字段
2025-08-04 23:01:45,306 - MultiRoundScheduler - INFO - ============================================================
2025-08-04 23:01:45,306 - Round2Processor - INFO - 🟡 开始第二轮处理：关联字段
2025-08-04 23:01:45,306 - Round2Processor - INFO - 📊 处理表格关联字段: 🧠 逻辑表（底库）
2025-08-04 23:01:45,306 - Round2Processor - INFO - ✅ 完成表格关联字段: 🧠 逻辑表（底库）
2025-08-04 23:01:45,306 - Round2Processor - INFO - 📊 处理表格关联字段: 📁 信息获取（基建）
2025-08-04 23:01:45,306 - Round2Processor - INFO - ✅ 完成表格关联字段: 📁 信息获取（基建）
2025-08-04 23:01:45,306 - Round2Processor - INFO - 📊 处理表格关联字段: 🎫 优惠券管理（底库）
2025-08-04 23:01:45,306 - Round2Processor - INFO - ✅ 完成表格关联字段: 🎫 优惠券管理（底库）
2025-08-04 23:01:45,306 - Round2Processor - INFO - 📊 处理表格关联字段: Ⓜ️ 品牌识别（基建）
2025-08-04 23:01:45,306 - Round2Processor - INFO - ✅ 完成表格关联字段: Ⓜ️ 品牌识别（基建）
2025-08-04 23:01:45,306 - Round2Processor - INFO - 📊 处理表格关联字段: 🛍️ 品类管理（基建）
2025-08-04 23:01:45,306 - Round2Processor - INFO - ✅ 完成表格关联字段: 🛍️ 品类管理（基建）
2025-08-04 23:01:45,307 - Round2Processor - INFO - 📊 处理表格关联字段: 🔪 SKU解构（基建）
2025-08-04 23:01:45,307 - Round2Processor - INFO - ✅ 完成表格关联字段: 🔪 SKU解构（基建）
2025-08-04 23:01:45,307 - Round2Processor - INFO - 📊 处理表格关联字段: 🧪 规格解构（基建）
2025-08-04 23:01:45,307 - Round2Processor - INFO - ✅ 完成表格关联字段: 🧪 规格解构（基建）
2025-08-04 23:01:45,307 - Round2Processor - INFO - 📊 处理表格关联字段: 💰 价格解构（基建）
2025-08-04 23:01:45,307 - Round2Processor - INFO - ✅ 完成表格关联字段: 💰 价格解构（基建）
2025-08-04 23:01:45,307 - Round2Processor - INFO - 📊 处理表格关联字段: 💻 单价计算（基建）
2025-08-04 23:01:45,307 - Round2Processor - INFO - ✅ 完成表格关联字段: 💻 单价计算（基建）
2025-08-04 23:01:45,307 - Round2Processor - INFO - 📊 处理表格关联字段: 🔥 价格热度计算（基建）
2025-08-04 23:01:45,307 - Round2Processor - INFO - ✅ 完成表格关联字段: 🔥 价格热度计算（基建）
2025-08-04 23:01:45,307 - Round2Processor - INFO - 📊 处理表格关联字段: 🐤 SKU重组-网站专用（基建）
2025-08-04 23:01:45,307 - Round2Processor - INFO - ✅ 完成表格关联字段: 🐤 SKU重组-网站专用（基建）
2025-08-04 23:01:45,307 - Round2Processor - INFO - 📊 处理表格关联字段: 🐽 SKU重组-社群专用（基建）
2025-08-04 23:01:45,307 - Round2Processor - INFO - ✅ 完成表格关联字段: 🐽 SKU重组-社群专用（基建）
2025-08-04 23:01:45,307 - Round2Processor - INFO - 📊 处理表格关联字段: 🤖 中转表（工具）
2025-08-04 23:01:45,307 - Round2Processor - INFO - ✅ 完成表格关联字段: 🤖 中转表（工具）
2025-08-04 23:01:45,307 - Round2Processor - INFO - 🎉 第二轮处理完成
2025-08-04 23:01:45,307 - MultiRoundScheduler - INFO - 📊 第二轮处理摘要:
2025-08-04 23:01:45,307 - MultiRoundScheduler - INFO -    总表格数: 13
2025-08-04 23:01:45,307 - MultiRoundScheduler - INFO -    完成表格: 13
2025-08-04 23:01:45,307 - MultiRoundScheduler - INFO -    失败表格: 0
2025-08-04 23:01:45,307 - MultiRoundScheduler - INFO -    成功率: 100.0%
2025-08-04 23:01:45,307 - MultiRoundScheduler - INFO - 
============================================================
2025-08-04 23:01:45,308 - MultiRoundScheduler - INFO - 🟢 第三轮：所有表的第二原生字段
2025-08-04 23:01:45,308 - MultiRoundScheduler - INFO - ============================================================
2025-08-04 23:01:45,308 - MultiRoundScheduler - INFO - 🟢 开始第三轮处理：第二原生字段
2025-08-04 23:01:45,308 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 🧠 逻辑表（底库）
2025-08-04 23:01:45,308 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 🧠 逻辑表（底库）
2025-08-04 23:01:45,308 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 📁 信息获取（基建）
2025-08-04 23:01:45,308 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 📁 信息获取（基建）
2025-08-04 23:01:45,308 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 🎫 优惠券管理（底库）
2025-08-04 23:01:45,308 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 🎫 优惠券管理（底库）
2025-08-04 23:01:45,308 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: Ⓜ️ 品牌识别（基建）
2025-08-04 23:01:45,308 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: Ⓜ️ 品牌识别（基建）
2025-08-04 23:01:45,308 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 🛍️ 品类管理（基建）
2025-08-04 23:01:45,309 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 🛍️ 品类管理（基建）
2025-08-04 23:01:45,309 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 🔪 SKU解构（基建）
2025-08-04 23:01:45,309 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 🔪 SKU解构（基建）
2025-08-04 23:01:45,309 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 🧪 规格解构（基建）
2025-08-04 23:01:45,309 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 🧪 规格解构（基建）
2025-08-04 23:01:45,309 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 💰 价格解构（基建）
2025-08-04 23:01:45,309 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 💰 价格解构（基建）
2025-08-04 23:01:45,309 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 💻 单价计算（基建）
2025-08-04 23:01:45,309 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 💻 单价计算（基建）
2025-08-04 23:01:45,309 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 🔥 价格热度计算（基建）
2025-08-04 23:01:45,309 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 🔥 价格热度计算（基建）
2025-08-04 23:01:45,309 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 🐤 SKU重组-网站专用（基建）
2025-08-04 23:01:45,309 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 🐤 SKU重组-网站专用（基建）
2025-08-04 23:01:45,309 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 🐽 SKU重组-社群专用（基建）
2025-08-04 23:01:45,309 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 🐽 SKU重组-社群专用（基建）
2025-08-04 23:01:45,309 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 🤖 中转表（工具）
2025-08-04 23:01:45,309 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 🤖 中转表（工具）
2025-08-04 23:01:45,309 - MultiRoundScheduler - INFO - 📊 第三轮处理摘要:
2025-08-04 23:01:45,309 - MultiRoundScheduler - INFO -    总表格数: 13
2025-08-04 23:01:45,309 - MultiRoundScheduler - INFO -    完成表格: 13
2025-08-04 23:01:45,309 - MultiRoundScheduler - INFO -    失败表格: 0
2025-08-04 23:01:45,309 - MultiRoundScheduler - INFO -    成功率: 100.0%
2025-08-04 23:01:45,309 - MultiRoundScheduler - INFO - 
============================================================
2025-08-04 23:01:45,309 - MultiRoundScheduler - INFO - 🟣 第四轮：复杂交叉依赖字段
2025-08-04 23:01:45,309 - MultiRoundScheduler - INFO - ============================================================
2025-08-04 23:01:45,309 - MultiRoundScheduler - INFO - 🟣 开始第四轮处理：复杂交叉依赖
2025-08-04 23:01:45,309 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 🧠 逻辑表（底库）
2025-08-04 23:01:45,309 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 🧠 逻辑表（底库）
2025-08-04 23:01:45,309 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 📁 信息获取（基建）
2025-08-04 23:01:45,310 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 📁 信息获取（基建）
2025-08-04 23:01:45,310 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 🎫 优惠券管理（底库）
2025-08-04 23:01:45,310 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 🎫 优惠券管理（底库）
2025-08-04 23:01:45,310 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: Ⓜ️ 品牌识别（基建）
2025-08-04 23:01:45,310 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: Ⓜ️ 品牌识别（基建）
2025-08-04 23:01:45,310 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 🛍️ 品类管理（基建）
2025-08-04 23:01:45,310 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 🛍️ 品类管理（基建）
2025-08-04 23:01:45,310 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 🔪 SKU解构（基建）
2025-08-04 23:01:45,310 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 🔪 SKU解构（基建）
2025-08-04 23:01:45,310 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 🧪 规格解构（基建）
2025-08-04 23:01:45,310 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 🧪 规格解构（基建）
2025-08-04 23:01:45,310 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 💰 价格解构（基建）
2025-08-04 23:01:45,310 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 💰 价格解构（基建）
2025-08-04 23:01:45,310 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 💻 单价计算（基建）
2025-08-04 23:01:45,310 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 💻 单价计算（基建）
2025-08-04 23:01:45,310 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 🔥 价格热度计算（基建）
2025-08-04 23:01:45,310 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 🔥 价格热度计算（基建）
2025-08-04 23:01:45,310 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 🐤 SKU重组-网站专用（基建）
2025-08-04 23:01:45,310 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 🐤 SKU重组-网站专用（基建）
2025-08-04 23:01:45,310 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 🐽 SKU重组-社群专用（基建）
2025-08-04 23:01:45,310 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 🐽 SKU重组-社群专用（基建）
2025-08-04 23:01:45,310 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 🤖 中转表（工具）
2025-08-04 23:01:45,310 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 🤖 中转表（工具）
2025-08-04 23:01:45,310 - MultiRoundScheduler - INFO - 📊 第四轮处理摘要:
2025-08-04 23:01:45,310 - MultiRoundScheduler - INFO -    总表格数: 13
2025-08-04 23:01:45,310 - MultiRoundScheduler - INFO -    完成表格: 13
2025-08-04 23:01:45,310 - MultiRoundScheduler - INFO -    失败表格: 0
2025-08-04 23:01:45,311 - MultiRoundScheduler - INFO -    成功率: 100.0%
2025-08-04 23:01:45,311 - MultiRoundScheduler - INFO - 
============================================================
2025-08-04 23:01:45,311 - MultiRoundScheduler - INFO - 🎉 多轮处理工作流完成
2025-08-04 23:01:45,311 - MultiRoundScheduler - INFO - ============================================================
2025-08-04 23:01:45,311 - MultiRoundScheduler - INFO - ⏱️  处理时长: 0.01秒
2025-08-04 23:01:45,311 - MultiRoundScheduler - INFO - 🔄 完成轮次: 4/4
2025-08-04 23:01:45,311 - MultiRoundScheduler - INFO - 📊 处理表格: 13
2025-08-04 23:01:45,311 - MultiRoundScheduler - INFO - 📈 最终状态统计:
2025-08-04 23:01:45,311 - MultiRoundScheduler - INFO -    全部完成✅: 13个表格
2025-08-04 23:01:45,311 - MultiRoundScheduler - INFO - 🚀 开始多轮迭代处理工作流
2025-08-04 23:01:45,311 - MultiRoundScheduler - INFO - ============================================================
2025-08-04 23:01:45,311 - MultiRoundScheduler - INFO - 🔵 第一轮：所有表的源信息和第一原生字段
2025-08-04 23:01:45,311 - MultiRoundScheduler - INFO - ============================================================
2025-08-04 23:01:45,311 - Round1Processor - INFO - 🔵 开始第一轮处理：源信息和第一原生字段
2025-08-04 23:01:45,311 - Round1Processor - INFO - 📊 处理表格: 🧠 逻辑表（底库）
2025-08-04 23:01:45,312 - Round1Processor - INFO - ✅ 完成表格: 🧠 逻辑表（底库）
2025-08-04 23:01:45,312 - Round1Processor - INFO - 📊 处理表格: 📁 信息获取（基建）
2025-08-04 23:01:45,312 - Round1Processor - INFO - ✅ 完成表格: 📁 信息获取（基建）
2025-08-04 23:01:45,312 - Round1Processor - INFO - 📊 处理表格: 🎫 优惠券管理（底库）
2025-08-04 23:01:45,312 - Round1Processor - INFO - ✅ 完成表格: 🎫 优惠券管理（底库）
2025-08-04 23:01:45,312 - Round1Processor - INFO - 📊 处理表格: Ⓜ️ 品牌识别（基建）
2025-08-04 23:01:45,312 - Round1Processor - INFO - ✅ 完成表格: Ⓜ️ 品牌识别（基建）
2025-08-04 23:01:45,312 - Round1Processor - INFO - 📊 处理表格: 🛍️ 品类管理（基建）
2025-08-04 23:01:45,312 - Round1Processor - INFO - ✅ 完成表格: 🛍️ 品类管理（基建）
2025-08-04 23:01:45,312 - Round1Processor - INFO - 📊 处理表格: 🔪 SKU解构（基建）
2025-08-04 23:01:45,312 - Round1Processor - INFO - ✅ 完成表格: 🔪 SKU解构（基建）
2025-08-04 23:01:45,312 - Round1Processor - INFO - 📊 处理表格: 🧪 规格解构（基建）
2025-08-04 23:01:45,313 - Round1Processor - INFO - ✅ 完成表格: 🧪 规格解构（基建）
2025-08-04 23:01:45,313 - Round1Processor - INFO - 📊 处理表格: 💰 价格解构（基建）
2025-08-04 23:01:45,313 - Round1Processor - INFO - ✅ 完成表格: 💰 价格解构（基建）
2025-08-04 23:01:45,313 - Round1Processor - INFO - 📊 处理表格: 💻 单价计算（基建）
2025-08-04 23:01:45,313 - Round1Processor - INFO - ✅ 完成表格: 💻 单价计算（基建）
2025-08-04 23:01:45,313 - Round1Processor - INFO - 📊 处理表格: 🔥 价格热度计算（基建）
2025-08-04 23:01:45,313 - Round1Processor - INFO - ✅ 完成表格: 🔥 价格热度计算（基建）
2025-08-04 23:01:45,313 - Round1Processor - INFO - 📊 处理表格: 🐤 SKU重组-网站专用（基建）
2025-08-04 23:01:45,313 - Round1Processor - INFO - ✅ 完成表格: 🐤 SKU重组-网站专用（基建）
2025-08-04 23:01:45,313 - Round1Processor - INFO - 📊 处理表格: 🐽 SKU重组-社群专用（基建）
2025-08-04 23:01:45,313 - Round1Processor - INFO - ✅ 完成表格: 🐽 SKU重组-社群专用（基建）
2025-08-04 23:01:45,313 - Round1Processor - INFO - 📊 处理表格: 🤖 中转表（工具）
2025-08-04 23:01:45,313 - Round1Processor - INFO - ✅ 完成表格: 🤖 中转表（工具）
2025-08-04 23:01:45,313 - Round1Processor - INFO - 🎉 第一轮处理完成
2025-08-04 23:01:45,313 - MultiRoundScheduler - INFO - 📊 第一轮处理摘要:
2025-08-04 23:01:45,313 - MultiRoundScheduler - INFO -    总表格数: 13
2025-08-04 23:01:45,313 - MultiRoundScheduler - INFO -    完成表格: 13
2025-08-04 23:01:45,313 - MultiRoundScheduler - INFO -    失败表格: 0
2025-08-04 23:01:45,313 - MultiRoundScheduler - INFO -    成功率: 100.0%
2025-08-04 23:01:45,313 - MultiRoundScheduler - INFO - 
============================================================
2025-08-04 23:01:45,314 - MultiRoundScheduler - INFO - 🟡 第二轮：所有表的关联字段
2025-08-04 23:01:45,314 - MultiRoundScheduler - INFO - ============================================================
2025-08-04 23:01:45,314 - Round2Processor - INFO - 🟡 开始第二轮处理：关联字段
2025-08-04 23:01:45,314 - Round2Processor - INFO - 📊 处理表格关联字段: 🧠 逻辑表（底库）
2025-08-04 23:01:45,314 - Round2Processor - INFO - ✅ 完成表格关联字段: 🧠 逻辑表（底库）
2025-08-04 23:01:45,314 - Round2Processor - INFO - 📊 处理表格关联字段: 📁 信息获取（基建）
2025-08-04 23:01:45,314 - Round2Processor - INFO - ✅ 完成表格关联字段: 📁 信息获取（基建）
2025-08-04 23:01:45,314 - Round2Processor - INFO - 📊 处理表格关联字段: 🎫 优惠券管理（底库）
2025-08-04 23:01:45,314 - Round2Processor - INFO - ✅ 完成表格关联字段: 🎫 优惠券管理（底库）
2025-08-04 23:01:45,314 - Round2Processor - INFO - 📊 处理表格关联字段: Ⓜ️ 品牌识别（基建）
2025-08-04 23:01:45,314 - Round2Processor - INFO - ✅ 完成表格关联字段: Ⓜ️ 品牌识别（基建）
2025-08-04 23:01:45,314 - Round2Processor - INFO - 📊 处理表格关联字段: 🛍️ 品类管理（基建）
2025-08-04 23:01:45,314 - Round2Processor - INFO - ✅ 完成表格关联字段: 🛍️ 品类管理（基建）
2025-08-04 23:01:45,314 - Round2Processor - INFO - 📊 处理表格关联字段: 🔪 SKU解构（基建）
2025-08-04 23:01:45,314 - Round2Processor - INFO - ✅ 完成表格关联字段: 🔪 SKU解构（基建）
2025-08-04 23:01:45,314 - Round2Processor - INFO - 📊 处理表格关联字段: 🧪 规格解构（基建）
2025-08-04 23:01:45,314 - Round2Processor - INFO - ✅ 完成表格关联字段: 🧪 规格解构（基建）
2025-08-04 23:01:45,314 - Round2Processor - INFO - 📊 处理表格关联字段: 💰 价格解构（基建）
2025-08-04 23:01:45,314 - Round2Processor - INFO - ✅ 完成表格关联字段: 💰 价格解构（基建）
2025-08-04 23:01:45,315 - Round2Processor - INFO - 📊 处理表格关联字段: 💻 单价计算（基建）
2025-08-04 23:01:45,315 - Round2Processor - INFO - ✅ 完成表格关联字段: 💻 单价计算（基建）
2025-08-04 23:01:45,315 - Round2Processor - INFO - 📊 处理表格关联字段: 🔥 价格热度计算（基建）
2025-08-04 23:01:45,315 - Round2Processor - INFO - ✅ 完成表格关联字段: 🔥 价格热度计算（基建）
2025-08-04 23:01:45,315 - Round2Processor - INFO - 📊 处理表格关联字段: 🐤 SKU重组-网站专用（基建）
2025-08-04 23:01:45,315 - Round2Processor - INFO - ✅ 完成表格关联字段: 🐤 SKU重组-网站专用（基建）
2025-08-04 23:01:45,315 - Round2Processor - INFO - 📊 处理表格关联字段: 🐽 SKU重组-社群专用（基建）
2025-08-04 23:01:45,315 - Round2Processor - INFO - ✅ 完成表格关联字段: 🐽 SKU重组-社群专用（基建）
2025-08-04 23:01:45,315 - Round2Processor - INFO - 📊 处理表格关联字段: 🤖 中转表（工具）
2025-08-04 23:01:45,315 - Round2Processor - INFO - ✅ 完成表格关联字段: 🤖 中转表（工具）
2025-08-04 23:01:45,315 - Round2Processor - INFO - 🎉 第二轮处理完成
2025-08-04 23:01:45,315 - MultiRoundScheduler - INFO - 📊 第二轮处理摘要:
2025-08-04 23:01:45,315 - MultiRoundScheduler - INFO -    总表格数: 13
2025-08-04 23:01:45,315 - MultiRoundScheduler - INFO -    完成表格: 13
2025-08-04 23:01:45,315 - MultiRoundScheduler - INFO -    失败表格: 0
2025-08-04 23:01:45,315 - MultiRoundScheduler - INFO -    成功率: 100.0%
2025-08-04 23:01:45,315 - MultiRoundScheduler - INFO - 
============================================================
2025-08-04 23:01:45,315 - MultiRoundScheduler - INFO - 🟢 第三轮：所有表的第二原生字段
2025-08-04 23:01:45,316 - MultiRoundScheduler - INFO - ============================================================
2025-08-04 23:01:45,316 - MultiRoundScheduler - INFO - 🟢 开始第三轮处理：第二原生字段
2025-08-04 23:01:45,316 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 🧠 逻辑表（底库）
2025-08-04 23:01:45,316 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 🧠 逻辑表（底库）
2025-08-04 23:01:45,316 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 📁 信息获取（基建）
2025-08-04 23:01:45,316 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 📁 信息获取（基建）
2025-08-04 23:01:45,316 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 🎫 优惠券管理（底库）
2025-08-04 23:01:45,316 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 🎫 优惠券管理（底库）
2025-08-04 23:01:45,316 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: Ⓜ️ 品牌识别（基建）
2025-08-04 23:01:45,316 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: Ⓜ️ 品牌识别（基建）
2025-08-04 23:01:45,316 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 🛍️ 品类管理（基建）
2025-08-04 23:01:45,316 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 🛍️ 品类管理（基建）
2025-08-04 23:01:45,316 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 🔪 SKU解构（基建）
2025-08-04 23:01:45,316 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 🔪 SKU解构（基建）
2025-08-04 23:01:45,316 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 🧪 规格解构（基建）
2025-08-04 23:01:45,316 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 🧪 规格解构（基建）
2025-08-04 23:01:45,317 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 💰 价格解构（基建）
2025-08-04 23:01:45,317 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 💰 价格解构（基建）
2025-08-04 23:01:45,317 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 💻 单价计算（基建）
2025-08-04 23:01:45,317 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 💻 单价计算（基建）
2025-08-04 23:01:45,317 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 🔥 价格热度计算（基建）
2025-08-04 23:01:45,317 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 🔥 价格热度计算（基建）
2025-08-04 23:01:45,317 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 🐤 SKU重组-网站专用（基建）
2025-08-04 23:01:45,317 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 🐤 SKU重组-网站专用（基建）
2025-08-04 23:01:45,317 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 🐽 SKU重组-社群专用（基建）
2025-08-04 23:01:45,317 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 🐽 SKU重组-社群专用（基建）
2025-08-04 23:01:45,317 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 🤖 中转表（工具）
2025-08-04 23:01:45,317 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 🤖 中转表（工具）
2025-08-04 23:01:45,317 - MultiRoundScheduler - INFO - 📊 第三轮处理摘要:
2025-08-04 23:01:45,317 - MultiRoundScheduler - INFO -    总表格数: 13
2025-08-04 23:01:45,317 - MultiRoundScheduler - INFO -    完成表格: 13
2025-08-04 23:01:45,317 - MultiRoundScheduler - INFO -    失败表格: 0
2025-08-04 23:01:45,317 - MultiRoundScheduler - INFO -    成功率: 100.0%
2025-08-04 23:01:45,317 - MultiRoundScheduler - INFO - 
============================================================
2025-08-04 23:01:45,318 - MultiRoundScheduler - INFO - 🟣 第四轮：复杂交叉依赖字段
2025-08-04 23:01:45,318 - MultiRoundScheduler - INFO - ============================================================
2025-08-04 23:01:45,318 - MultiRoundScheduler - INFO - 🟣 开始第四轮处理：复杂交叉依赖
2025-08-04 23:01:45,318 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 🧠 逻辑表（底库）
2025-08-04 23:01:45,318 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 🧠 逻辑表（底库）
2025-08-04 23:01:45,318 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 📁 信息获取（基建）
2025-08-04 23:01:45,318 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 📁 信息获取（基建）
2025-08-04 23:01:45,318 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 🎫 优惠券管理（底库）
2025-08-04 23:01:45,318 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 🎫 优惠券管理（底库）
2025-08-04 23:01:45,318 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: Ⓜ️ 品牌识别（基建）
2025-08-04 23:01:45,318 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: Ⓜ️ 品牌识别（基建）
2025-08-04 23:01:45,318 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 🛍️ 品类管理（基建）
2025-08-04 23:01:45,318 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 🛍️ 品类管理（基建）
2025-08-04 23:01:45,318 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 🔪 SKU解构（基建）
2025-08-04 23:01:45,318 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 🔪 SKU解构（基建）
2025-08-04 23:01:45,318 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 🧪 规格解构（基建）
2025-08-04 23:01:45,318 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 🧪 规格解构（基建）
2025-08-04 23:01:45,318 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 💰 价格解构（基建）
2025-08-04 23:01:45,318 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 💰 价格解构（基建）
2025-08-04 23:01:45,318 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 💻 单价计算（基建）
2025-08-04 23:01:45,318 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 💻 单价计算（基建）
2025-08-04 23:01:45,318 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 🔥 价格热度计算（基建）
2025-08-04 23:01:45,318 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 🔥 价格热度计算（基建）
2025-08-04 23:01:45,318 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 🐤 SKU重组-网站专用（基建）
2025-08-04 23:01:45,318 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 🐤 SKU重组-网站专用（基建）
2025-08-04 23:01:45,318 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 🐽 SKU重组-社群专用（基建）
2025-08-04 23:01:45,318 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 🐽 SKU重组-社群专用（基建）
2025-08-04 23:01:45,318 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 🤖 中转表（工具）
2025-08-04 23:01:45,318 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 🤖 中转表（工具）
2025-08-04 23:01:45,318 - MultiRoundScheduler - INFO - 📊 第四轮处理摘要:
2025-08-04 23:01:45,318 - MultiRoundScheduler - INFO -    总表格数: 13
2025-08-04 23:01:45,318 - MultiRoundScheduler - INFO -    完成表格: 13
2025-08-04 23:01:45,318 - MultiRoundScheduler - INFO -    失败表格: 0
2025-08-04 23:01:45,318 - MultiRoundScheduler - INFO -    成功率: 100.0%
2025-08-04 23:01:45,318 - MultiRoundScheduler - INFO - 
============================================================
2025-08-04 23:01:45,318 - MultiRoundScheduler - INFO - 🎉 多轮处理工作流完成
2025-08-04 23:01:45,319 - MultiRoundScheduler - INFO - ============================================================
2025-08-04 23:01:45,319 - MultiRoundScheduler - INFO - ⏱️  处理时长: 0.01秒
2025-08-04 23:01:45,319 - MultiRoundScheduler - INFO - 🔄 完成轮次: 4/4
2025-08-04 23:01:45,319 - MultiRoundScheduler - INFO - 📊 处理表格: 13
2025-08-04 23:01:45,319 - MultiRoundScheduler - INFO - 📈 最终状态统计:
2025-08-04 23:01:45,319 - MultiRoundScheduler - INFO -    全部完成✅: 13个表格
2025-08-04 23:01:49,722 - DependencyManager - INFO - ✅ 已初始化 7 个依赖关系
2025-08-04 23:01:49,722 - MultiRoundScheduler - INFO - 🚀 开始多轮迭代处理工作流
2025-08-04 23:01:49,723 - MultiRoundScheduler - INFO - ============================================================
2025-08-04 23:01:49,723 - MultiRoundScheduler - INFO - 🔵 第一轮：所有表的源信息和第一原生字段
2025-08-04 23:01:49,723 - MultiRoundScheduler - INFO - ============================================================
2025-08-04 23:01:49,723 - Round1Processor - INFO - 🔵 开始第一轮处理：源信息和第一原生字段
2025-08-04 23:01:49,723 - Round1Processor - INFO - 📊 处理表格: 🧠 逻辑表（底库）
2025-08-04 23:01:49,723 - Round1Processor - INFO - ✅ 完成表格: 🧠 逻辑表（底库）
2025-08-04 23:01:49,723 - Round1Processor - INFO - 📊 处理表格: 📁 信息获取（基建）
2025-08-04 23:01:49,723 - Round1Processor - INFO - ✅ 完成表格: 📁 信息获取（基建）
2025-08-04 23:01:49,723 - Round1Processor - INFO - 📊 处理表格: 🎫 优惠券管理（底库）
2025-08-04 23:01:49,724 - Round1Processor - INFO - ✅ 完成表格: 🎫 优惠券管理（底库）
2025-08-04 23:01:49,724 - Round1Processor - INFO - 📊 处理表格: Ⓜ️ 品牌识别（基建）
2025-08-04 23:01:49,724 - Round1Processor - INFO - ✅ 完成表格: Ⓜ️ 品牌识别（基建）
2025-08-04 23:01:49,724 - Round1Processor - INFO - 📊 处理表格: 🛍️ 品类管理（基建）
2025-08-04 23:01:49,724 - Round1Processor - INFO - ✅ 完成表格: 🛍️ 品类管理（基建）
2025-08-04 23:01:49,724 - Round1Processor - INFO - 📊 处理表格: 🔪 SKU解构（基建）
2025-08-04 23:01:49,724 - Round1Processor - INFO - ✅ 完成表格: 🔪 SKU解构（基建）
2025-08-04 23:01:49,724 - Round1Processor - INFO - 📊 处理表格: 🧪 规格解构（基建）
2025-08-04 23:01:49,725 - Round1Processor - INFO - ✅ 完成表格: 🧪 规格解构（基建）
2025-08-04 23:01:49,725 - Round1Processor - INFO - 📊 处理表格: 💰 价格解构（基建）
2025-08-04 23:01:49,725 - Round1Processor - INFO - ✅ 完成表格: 💰 价格解构（基建）
2025-08-04 23:01:49,725 - Round1Processor - INFO - 📊 处理表格: 💻 单价计算（基建）
2025-08-04 23:01:49,725 - Round1Processor - INFO - ✅ 完成表格: 💻 单价计算（基建）
2025-08-04 23:01:49,725 - Round1Processor - INFO - 📊 处理表格: 🔥 价格热度计算（基建）
2025-08-04 23:01:49,725 - Round1Processor - INFO - ✅ 完成表格: 🔥 价格热度计算（基建）
2025-08-04 23:01:49,725 - Round1Processor - INFO - 📊 处理表格: 🐤 SKU重组-网站专用（基建）
2025-08-04 23:01:49,725 - Round1Processor - INFO - ✅ 完成表格: 🐤 SKU重组-网站专用（基建）
2025-08-04 23:01:49,725 - Round1Processor - INFO - 📊 处理表格: 🐽 SKU重组-社群专用（基建）
2025-08-04 23:01:49,725 - Round1Processor - INFO - ✅ 完成表格: 🐽 SKU重组-社群专用（基建）
2025-08-04 23:01:49,725 - Round1Processor - INFO - 📊 处理表格: 🤖 中转表（工具）
2025-08-04 23:01:49,725 - Round1Processor - INFO - ✅ 完成表格: 🤖 中转表（工具）
2025-08-04 23:01:49,725 - Round1Processor - INFO - 🎉 第一轮处理完成
2025-08-04 23:01:49,725 - MultiRoundScheduler - INFO - 📊 第一轮处理摘要:
2025-08-04 23:01:49,725 - MultiRoundScheduler - INFO -    总表格数: 13
2025-08-04 23:01:49,726 - MultiRoundScheduler - INFO -    完成表格: 13
2025-08-04 23:01:49,726 - MultiRoundScheduler - INFO -    失败表格: 0
2025-08-04 23:01:49,726 - MultiRoundScheduler - INFO -    成功率: 100.0%
2025-08-04 23:01:49,726 - MultiRoundScheduler - INFO - 
============================================================
2025-08-04 23:01:49,726 - MultiRoundScheduler - INFO - 🟡 第二轮：所有表的关联字段
2025-08-04 23:01:49,726 - MultiRoundScheduler - INFO - ============================================================
2025-08-04 23:01:49,726 - Round2Processor - INFO - 🟡 开始第二轮处理：关联字段
2025-08-04 23:01:49,726 - Round2Processor - INFO - 📊 处理表格关联字段: 🧠 逻辑表（底库）
2025-08-04 23:01:49,726 - Round2Processor - INFO - ✅ 完成表格关联字段: 🧠 逻辑表（底库）
2025-08-04 23:01:49,726 - Round2Processor - INFO - 📊 处理表格关联字段: 📁 信息获取（基建）
2025-08-04 23:01:49,727 - Round2Processor - INFO - ✅ 完成表格关联字段: 📁 信息获取（基建）
2025-08-04 23:01:49,727 - Round2Processor - INFO - 📊 处理表格关联字段: 🎫 优惠券管理（底库）
2025-08-04 23:01:49,727 - Round2Processor - INFO - ✅ 完成表格关联字段: 🎫 优惠券管理（底库）
2025-08-04 23:01:49,727 - Round2Processor - INFO - 📊 处理表格关联字段: Ⓜ️ 品牌识别（基建）
2025-08-04 23:01:49,727 - Round2Processor - INFO - ✅ 完成表格关联字段: Ⓜ️ 品牌识别（基建）
2025-08-04 23:01:49,727 - Round2Processor - INFO - 📊 处理表格关联字段: 🛍️ 品类管理（基建）
2025-08-04 23:01:49,727 - Round2Processor - INFO - ✅ 完成表格关联字段: 🛍️ 品类管理（基建）
2025-08-04 23:01:49,727 - Round2Processor - INFO - 📊 处理表格关联字段: 🔪 SKU解构（基建）
2025-08-04 23:01:49,727 - Round2Processor - INFO - ✅ 完成表格关联字段: 🔪 SKU解构（基建）
2025-08-04 23:01:49,727 - Round2Processor - INFO - 📊 处理表格关联字段: 🧪 规格解构（基建）
2025-08-04 23:01:49,727 - Round2Processor - INFO - ✅ 完成表格关联字段: 🧪 规格解构（基建）
2025-08-04 23:01:49,727 - Round2Processor - INFO - 📊 处理表格关联字段: 💰 价格解构（基建）
2025-08-04 23:01:49,727 - Round2Processor - INFO - ✅ 完成表格关联字段: 💰 价格解构（基建）
2025-08-04 23:01:49,727 - Round2Processor - INFO - 📊 处理表格关联字段: 💻 单价计算（基建）
2025-08-04 23:01:49,727 - Round2Processor - INFO - ✅ 完成表格关联字段: 💻 单价计算（基建）
2025-08-04 23:01:49,727 - Round2Processor - INFO - 📊 处理表格关联字段: 🔥 价格热度计算（基建）
2025-08-04 23:01:49,728 - Round2Processor - INFO - ✅ 完成表格关联字段: 🔥 价格热度计算（基建）
2025-08-04 23:01:49,728 - Round2Processor - INFO - 📊 处理表格关联字段: 🐤 SKU重组-网站专用（基建）
2025-08-04 23:01:49,728 - Round2Processor - INFO - ✅ 完成表格关联字段: 🐤 SKU重组-网站专用（基建）
2025-08-04 23:01:49,728 - Round2Processor - INFO - 📊 处理表格关联字段: 🐽 SKU重组-社群专用（基建）
2025-08-04 23:01:49,728 - Round2Processor - INFO - ✅ 完成表格关联字段: 🐽 SKU重组-社群专用（基建）
2025-08-04 23:01:49,728 - Round2Processor - INFO - 📊 处理表格关联字段: 🤖 中转表（工具）
2025-08-04 23:01:49,728 - Round2Processor - INFO - ✅ 完成表格关联字段: 🤖 中转表（工具）
2025-08-04 23:01:49,728 - Round2Processor - INFO - 🎉 第二轮处理完成
2025-08-04 23:01:49,728 - MultiRoundScheduler - INFO - 📊 第二轮处理摘要:
2025-08-04 23:01:49,728 - MultiRoundScheduler - INFO -    总表格数: 13
2025-08-04 23:01:49,728 - MultiRoundScheduler - INFO -    完成表格: 13
2025-08-04 23:01:49,728 - MultiRoundScheduler - INFO -    失败表格: 0
2025-08-04 23:01:49,728 - MultiRoundScheduler - INFO -    成功率: 100.0%
2025-08-04 23:01:49,729 - MultiRoundScheduler - INFO - 
============================================================
2025-08-04 23:01:49,729 - MultiRoundScheduler - INFO - 🟢 第三轮：所有表的第二原生字段
2025-08-04 23:01:49,730 - MultiRoundScheduler - INFO - ============================================================
2025-08-04 23:01:49,730 - MultiRoundScheduler - INFO - 🟢 开始第三轮处理：第二原生字段
2025-08-04 23:01:49,730 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 🧠 逻辑表（底库）
2025-08-04 23:01:49,730 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 🧠 逻辑表（底库）
2025-08-04 23:01:49,730 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 📁 信息获取（基建）
2025-08-04 23:01:49,730 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 📁 信息获取（基建）
2025-08-04 23:01:49,730 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 🎫 优惠券管理（底库）
2025-08-04 23:01:49,730 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 🎫 优惠券管理（底库）
2025-08-04 23:01:49,730 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: Ⓜ️ 品牌识别（基建）
2025-08-04 23:01:49,730 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: Ⓜ️ 品牌识别（基建）
2025-08-04 23:01:49,730 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 🛍️ 品类管理（基建）
2025-08-04 23:01:49,730 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 🛍️ 品类管理（基建）
2025-08-04 23:01:49,730 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 🔪 SKU解构（基建）
2025-08-04 23:01:49,731 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 🔪 SKU解构（基建）
2025-08-04 23:01:49,731 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 🧪 规格解构（基建）
2025-08-04 23:01:49,731 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 🧪 规格解构（基建）
2025-08-04 23:01:49,731 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 💰 价格解构（基建）
2025-08-04 23:01:49,731 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 💰 价格解构（基建）
2025-08-04 23:01:49,731 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 💻 单价计算（基建）
2025-08-04 23:01:49,731 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 💻 单价计算（基建）
2025-08-04 23:01:49,731 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 🔥 价格热度计算（基建）
2025-08-04 23:01:49,731 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 🔥 价格热度计算（基建）
2025-08-04 23:01:49,731 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 🐤 SKU重组-网站专用（基建）
2025-08-04 23:01:49,731 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 🐤 SKU重组-网站专用（基建）
2025-08-04 23:01:49,731 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 🐽 SKU重组-社群专用（基建）
2025-08-04 23:01:49,731 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 🐽 SKU重组-社群专用（基建）
2025-08-04 23:01:49,731 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 🤖 中转表（工具）
2025-08-04 23:01:49,732 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 🤖 中转表（工具）
2025-08-04 23:01:49,732 - MultiRoundScheduler - INFO - 📊 第三轮处理摘要:
2025-08-04 23:01:49,732 - MultiRoundScheduler - INFO -    总表格数: 13
2025-08-04 23:01:49,732 - MultiRoundScheduler - INFO -    完成表格: 13
2025-08-04 23:01:49,732 - MultiRoundScheduler - INFO -    失败表格: 0
2025-08-04 23:01:49,732 - MultiRoundScheduler - INFO -    成功率: 100.0%
2025-08-04 23:01:49,732 - MultiRoundScheduler - INFO - 
============================================================
2025-08-04 23:01:49,732 - MultiRoundScheduler - INFO - 🟣 第四轮：复杂交叉依赖字段
2025-08-04 23:01:49,732 - MultiRoundScheduler - INFO - ============================================================
2025-08-04 23:01:49,732 - MultiRoundScheduler - INFO - 🟣 开始第四轮处理：复杂交叉依赖
2025-08-04 23:01:49,733 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 🧠 逻辑表（底库）
2025-08-04 23:01:49,733 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 🧠 逻辑表（底库）
2025-08-04 23:01:49,733 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 📁 信息获取（基建）
2025-08-04 23:01:49,733 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 📁 信息获取（基建）
2025-08-04 23:01:49,734 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 🎫 优惠券管理（底库）
2025-08-04 23:01:49,734 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 🎫 优惠券管理（底库）
2025-08-04 23:01:49,734 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: Ⓜ️ 品牌识别（基建）
2025-08-04 23:01:49,734 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: Ⓜ️ 品牌识别（基建）
2025-08-04 23:01:49,734 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 🛍️ 品类管理（基建）
2025-08-04 23:01:49,734 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 🛍️ 品类管理（基建）
2025-08-04 23:01:49,734 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 🔪 SKU解构（基建）
2025-08-04 23:01:49,734 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 🔪 SKU解构（基建）
2025-08-04 23:01:49,734 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 🧪 规格解构（基建）
2025-08-04 23:01:49,734 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 🧪 规格解构（基建）
2025-08-04 23:01:49,734 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 💰 价格解构（基建）
2025-08-04 23:01:49,734 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 💰 价格解构（基建）
2025-08-04 23:01:49,734 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 💻 单价计算（基建）
2025-08-04 23:01:49,734 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 💻 单价计算（基建）
2025-08-04 23:01:49,734 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 🔥 价格热度计算（基建）
2025-08-04 23:01:49,734 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 🔥 价格热度计算（基建）
2025-08-04 23:01:49,734 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 🐤 SKU重组-网站专用（基建）
2025-08-04 23:01:49,734 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 🐤 SKU重组-网站专用（基建）
2025-08-04 23:01:49,734 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 🐽 SKU重组-社群专用（基建）
2025-08-04 23:01:49,734 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 🐽 SKU重组-社群专用（基建）
2025-08-04 23:01:49,735 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 🤖 中转表（工具）
2025-08-04 23:01:49,735 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 🤖 中转表（工具）
2025-08-04 23:01:49,735 - MultiRoundScheduler - INFO - 📊 第四轮处理摘要:
2025-08-04 23:01:49,735 - MultiRoundScheduler - INFO -    总表格数: 13
2025-08-04 23:01:49,736 - MultiRoundScheduler - INFO -    完成表格: 13
2025-08-04 23:01:49,736 - MultiRoundScheduler - INFO -    失败表格: 0
2025-08-04 23:01:49,736 - MultiRoundScheduler - INFO -    成功率: 100.0%
2025-08-04 23:01:49,736 - MultiRoundScheduler - INFO - 
============================================================
2025-08-04 23:01:49,736 - MultiRoundScheduler - INFO - 🎉 多轮处理工作流完成
2025-08-04 23:01:49,737 - MultiRoundScheduler - INFO - ============================================================
2025-08-04 23:01:49,737 - MultiRoundScheduler - INFO - ⏱️  处理时长: 0.01秒
2025-08-04 23:01:49,737 - MultiRoundScheduler - INFO - 🔄 完成轮次: 4/4
2025-08-04 23:01:49,737 - MultiRoundScheduler - INFO - 📊 处理表格: 13
2025-08-04 23:01:49,737 - MultiRoundScheduler - INFO - 📈 最终状态统计:
2025-08-04 23:01:49,737 - MultiRoundScheduler - INFO -    全部完成✅: 13个表格
2025-08-04 23:01:49,737 - MultiRoundScheduler - INFO - 🚀 开始多轮迭代处理工作流
2025-08-04 23:01:49,737 - MultiRoundScheduler - INFO - ============================================================
2025-08-04 23:01:49,738 - MultiRoundScheduler - INFO - 🔵 第一轮：所有表的源信息和第一原生字段
2025-08-04 23:01:49,738 - MultiRoundScheduler - INFO - ============================================================
2025-08-04 23:01:49,738 - Round1Processor - INFO - 🔵 开始第一轮处理：源信息和第一原生字段
2025-08-04 23:01:49,738 - Round1Processor - INFO - 📊 处理表格: 🧠 逻辑表（底库）
2025-08-04 23:01:49,738 - Round1Processor - INFO - ✅ 完成表格: 🧠 逻辑表（底库）
2025-08-04 23:01:49,738 - Round1Processor - INFO - 📊 处理表格: 📁 信息获取（基建）
2025-08-04 23:01:49,738 - Round1Processor - INFO - ✅ 完成表格: 📁 信息获取（基建）
2025-08-04 23:01:49,738 - Round1Processor - INFO - 📊 处理表格: 🎫 优惠券管理（底库）
2025-08-04 23:01:49,738 - Round1Processor - INFO - ✅ 完成表格: 🎫 优惠券管理（底库）
2025-08-04 23:01:49,738 - Round1Processor - INFO - 📊 处理表格: Ⓜ️ 品牌识别（基建）
2025-08-04 23:01:49,738 - Round1Processor - INFO - ✅ 完成表格: Ⓜ️ 品牌识别（基建）
2025-08-04 23:01:49,738 - Round1Processor - INFO - 📊 处理表格: 🛍️ 品类管理（基建）
2025-08-04 23:01:49,738 - Round1Processor - INFO - ✅ 完成表格: 🛍️ 品类管理（基建）
2025-08-04 23:01:49,738 - Round1Processor - INFO - 📊 处理表格: 🔪 SKU解构（基建）
2025-08-04 23:01:49,738 - Round1Processor - INFO - ✅ 完成表格: 🔪 SKU解构（基建）
2025-08-04 23:01:49,738 - Round1Processor - INFO - 📊 处理表格: 🧪 规格解构（基建）
2025-08-04 23:01:49,738 - Round1Processor - INFO - ✅ 完成表格: 🧪 规格解构（基建）
2025-08-04 23:01:49,738 - Round1Processor - INFO - 📊 处理表格: 💰 价格解构（基建）
2025-08-04 23:01:49,739 - Round1Processor - INFO - ✅ 完成表格: 💰 价格解构（基建）
2025-08-04 23:01:49,739 - Round1Processor - INFO - 📊 处理表格: 💻 单价计算（基建）
2025-08-04 23:01:49,739 - Round1Processor - INFO - ✅ 完成表格: 💻 单价计算（基建）
2025-08-04 23:01:49,739 - Round1Processor - INFO - 📊 处理表格: 🔥 价格热度计算（基建）
2025-08-04 23:01:49,739 - Round1Processor - INFO - ✅ 完成表格: 🔥 价格热度计算（基建）
2025-08-04 23:01:49,739 - Round1Processor - INFO - 📊 处理表格: 🐤 SKU重组-网站专用（基建）
2025-08-04 23:01:49,739 - Round1Processor - INFO - ✅ 完成表格: 🐤 SKU重组-网站专用（基建）
2025-08-04 23:01:49,739 - Round1Processor - INFO - 📊 处理表格: 🐽 SKU重组-社群专用（基建）
2025-08-04 23:01:49,739 - Round1Processor - INFO - ✅ 完成表格: 🐽 SKU重组-社群专用（基建）
2025-08-04 23:01:49,739 - Round1Processor - INFO - 📊 处理表格: 🤖 中转表（工具）
2025-08-04 23:01:49,739 - Round1Processor - INFO - ✅ 完成表格: 🤖 中转表（工具）
2025-08-04 23:01:49,739 - Round1Processor - INFO - 🎉 第一轮处理完成
2025-08-04 23:01:49,739 - MultiRoundScheduler - INFO - 📊 第一轮处理摘要:
2025-08-04 23:01:49,739 - MultiRoundScheduler - INFO -    总表格数: 13
2025-08-04 23:01:49,739 - MultiRoundScheduler - INFO -    完成表格: 13
2025-08-04 23:01:49,739 - MultiRoundScheduler - INFO -    失败表格: 0
2025-08-04 23:01:49,739 - MultiRoundScheduler - INFO -    成功率: 100.0%
2025-08-04 23:01:49,739 - MultiRoundScheduler - INFO - 
============================================================
2025-08-04 23:01:49,739 - MultiRoundScheduler - INFO - 🟡 第二轮：所有表的关联字段
2025-08-04 23:01:49,739 - MultiRoundScheduler - INFO - ============================================================
2025-08-04 23:01:49,739 - Round2Processor - INFO - 🟡 开始第二轮处理：关联字段
2025-08-04 23:01:49,739 - Round2Processor - INFO - 📊 处理表格关联字段: 🧠 逻辑表（底库）
2025-08-04 23:01:49,740 - Round2Processor - INFO - ✅ 完成表格关联字段: 🧠 逻辑表（底库）
2025-08-04 23:01:49,740 - Round2Processor - INFO - 📊 处理表格关联字段: 📁 信息获取（基建）
2025-08-04 23:01:49,740 - Round2Processor - INFO - ✅ 完成表格关联字段: 📁 信息获取（基建）
2025-08-04 23:01:49,740 - Round2Processor - INFO - 📊 处理表格关联字段: 🎫 优惠券管理（底库）
2025-08-04 23:01:49,740 - Round2Processor - INFO - ✅ 完成表格关联字段: 🎫 优惠券管理（底库）
2025-08-04 23:01:49,740 - Round2Processor - INFO - 📊 处理表格关联字段: Ⓜ️ 品牌识别（基建）
2025-08-04 23:01:49,740 - Round2Processor - INFO - ✅ 完成表格关联字段: Ⓜ️ 品牌识别（基建）
2025-08-04 23:01:49,740 - Round2Processor - INFO - 📊 处理表格关联字段: 🛍️ 品类管理（基建）
2025-08-04 23:01:49,740 - Round2Processor - INFO - ✅ 完成表格关联字段: 🛍️ 品类管理（基建）
2025-08-04 23:01:49,740 - Round2Processor - INFO - 📊 处理表格关联字段: 🔪 SKU解构（基建）
2025-08-04 23:01:49,740 - Round2Processor - INFO - ✅ 完成表格关联字段: 🔪 SKU解构（基建）
2025-08-04 23:01:49,740 - Round2Processor - INFO - 📊 处理表格关联字段: 🧪 规格解构（基建）
2025-08-04 23:01:49,740 - Round2Processor - INFO - ✅ 完成表格关联字段: 🧪 规格解构（基建）
2025-08-04 23:01:49,740 - Round2Processor - INFO - 📊 处理表格关联字段: 💰 价格解构（基建）
2025-08-04 23:01:49,740 - Round2Processor - INFO - ✅ 完成表格关联字段: 💰 价格解构（基建）
2025-08-04 23:01:49,740 - Round2Processor - INFO - 📊 处理表格关联字段: 💻 单价计算（基建）
2025-08-04 23:01:49,740 - Round2Processor - INFO - ✅ 完成表格关联字段: 💻 单价计算（基建）
2025-08-04 23:01:49,740 - Round2Processor - INFO - 📊 处理表格关联字段: 🔥 价格热度计算（基建）
2025-08-04 23:01:49,740 - Round2Processor - INFO - ✅ 完成表格关联字段: 🔥 价格热度计算（基建）
2025-08-04 23:01:49,740 - Round2Processor - INFO - 📊 处理表格关联字段: 🐤 SKU重组-网站专用（基建）
2025-08-04 23:01:49,740 - Round2Processor - INFO - ✅ 完成表格关联字段: 🐤 SKU重组-网站专用（基建）
2025-08-04 23:01:49,740 - Round2Processor - INFO - 📊 处理表格关联字段: 🐽 SKU重组-社群专用（基建）
2025-08-04 23:01:49,740 - Round2Processor - INFO - ✅ 完成表格关联字段: 🐽 SKU重组-社群专用（基建）
2025-08-04 23:01:49,740 - Round2Processor - INFO - 📊 处理表格关联字段: 🤖 中转表（工具）
2025-08-04 23:01:49,740 - Round2Processor - INFO - ✅ 完成表格关联字段: 🤖 中转表（工具）
2025-08-04 23:01:49,740 - Round2Processor - INFO - 🎉 第二轮处理完成
2025-08-04 23:01:49,740 - MultiRoundScheduler - INFO - 📊 第二轮处理摘要:
2025-08-04 23:01:49,740 - MultiRoundScheduler - INFO -    总表格数: 13
2025-08-04 23:01:49,740 - MultiRoundScheduler - INFO -    完成表格: 13
2025-08-04 23:01:49,740 - MultiRoundScheduler - INFO -    失败表格: 0
2025-08-04 23:01:49,740 - MultiRoundScheduler - INFO -    成功率: 100.0%
2025-08-04 23:01:49,741 - MultiRoundScheduler - INFO - 
============================================================
2025-08-04 23:01:49,741 - MultiRoundScheduler - INFO - 🟢 第三轮：所有表的第二原生字段
2025-08-04 23:01:49,741 - MultiRoundScheduler - INFO - ============================================================
2025-08-04 23:01:49,741 - MultiRoundScheduler - INFO - 🟢 开始第三轮处理：第二原生字段
2025-08-04 23:01:49,741 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 🧠 逻辑表（底库）
2025-08-04 23:01:49,741 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 🧠 逻辑表（底库）
2025-08-04 23:01:49,741 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 📁 信息获取（基建）
2025-08-04 23:01:49,741 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 📁 信息获取（基建）
2025-08-04 23:01:49,741 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 🎫 优惠券管理（底库）
2025-08-04 23:01:49,741 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 🎫 优惠券管理（底库）
2025-08-04 23:01:49,741 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: Ⓜ️ 品牌识别（基建）
2025-08-04 23:01:49,741 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: Ⓜ️ 品牌识别（基建）
2025-08-04 23:01:49,741 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 🛍️ 品类管理（基建）
2025-08-04 23:01:49,741 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 🛍️ 品类管理（基建）
2025-08-04 23:01:49,741 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 🔪 SKU解构（基建）
2025-08-04 23:01:49,741 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 🔪 SKU解构（基建）
2025-08-04 23:01:49,741 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 🧪 规格解构（基建）
2025-08-04 23:01:49,741 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 🧪 规格解构（基建）
2025-08-04 23:01:49,741 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 💰 价格解构（基建）
2025-08-04 23:01:49,741 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 💰 价格解构（基建）
2025-08-04 23:01:49,741 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 💻 单价计算（基建）
2025-08-04 23:01:49,741 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 💻 单价计算（基建）
2025-08-04 23:01:49,742 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 🔥 价格热度计算（基建）
2025-08-04 23:01:49,742 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 🔥 价格热度计算（基建）
2025-08-04 23:01:49,742 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 🐤 SKU重组-网站专用（基建）
2025-08-04 23:01:49,742 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 🐤 SKU重组-网站专用（基建）
2025-08-04 23:01:49,742 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 🐽 SKU重组-社群专用（基建）
2025-08-04 23:01:49,742 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 🐽 SKU重组-社群专用（基建）
2025-08-04 23:01:49,742 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 🤖 中转表（工具）
2025-08-04 23:01:49,742 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 🤖 中转表（工具）
2025-08-04 23:01:49,742 - MultiRoundScheduler - INFO - 📊 第三轮处理摘要:
2025-08-04 23:01:49,742 - MultiRoundScheduler - INFO -    总表格数: 13
2025-08-04 23:01:49,742 - MultiRoundScheduler - INFO -    完成表格: 13
2025-08-04 23:01:49,742 - MultiRoundScheduler - INFO -    失败表格: 0
2025-08-04 23:01:49,742 - MultiRoundScheduler - INFO -    成功率: 100.0%
2025-08-04 23:01:49,742 - MultiRoundScheduler - INFO - 
============================================================
2025-08-04 23:01:49,742 - MultiRoundScheduler - INFO - 🟣 第四轮：复杂交叉依赖字段
2025-08-04 23:01:49,742 - MultiRoundScheduler - INFO - ============================================================
2025-08-04 23:01:49,742 - MultiRoundScheduler - INFO - 🟣 开始第四轮处理：复杂交叉依赖
2025-08-04 23:01:49,742 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 🧠 逻辑表（底库）
2025-08-04 23:01:49,743 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 🧠 逻辑表（底库）
2025-08-04 23:01:49,743 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 📁 信息获取（基建）
2025-08-04 23:01:49,743 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 📁 信息获取（基建）
2025-08-04 23:01:49,743 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 🎫 优惠券管理（底库）
2025-08-04 23:01:49,743 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 🎫 优惠券管理（底库）
2025-08-04 23:01:49,743 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: Ⓜ️ 品牌识别（基建）
2025-08-04 23:01:49,743 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: Ⓜ️ 品牌识别（基建）
2025-08-04 23:01:49,743 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 🛍️ 品类管理（基建）
2025-08-04 23:01:49,743 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 🛍️ 品类管理（基建）
2025-08-04 23:01:49,743 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 🔪 SKU解构（基建）
2025-08-04 23:01:49,743 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 🔪 SKU解构（基建）
2025-08-04 23:01:49,743 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 🧪 规格解构（基建）
2025-08-04 23:01:49,743 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 🧪 规格解构（基建）
2025-08-04 23:01:49,743 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 💰 价格解构（基建）
2025-08-04 23:01:49,743 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 💰 价格解构（基建）
2025-08-04 23:01:49,743 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 💻 单价计算（基建）
2025-08-04 23:01:49,743 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 💻 单价计算（基建）
2025-08-04 23:01:49,743 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 🔥 价格热度计算（基建）
2025-08-04 23:01:49,743 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 🔥 价格热度计算（基建）
2025-08-04 23:01:49,743 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 🐤 SKU重组-网站专用（基建）
2025-08-04 23:01:49,743 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 🐤 SKU重组-网站专用（基建）
2025-08-04 23:01:49,743 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 🐽 SKU重组-社群专用（基建）
2025-08-04 23:01:49,743 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 🐽 SKU重组-社群专用（基建）
2025-08-04 23:01:49,743 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 🤖 中转表（工具）
2025-08-04 23:01:49,744 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 🤖 中转表（工具）
2025-08-04 23:01:49,744 - MultiRoundScheduler - INFO - 📊 第四轮处理摘要:
2025-08-04 23:01:49,744 - MultiRoundScheduler - INFO -    总表格数: 13
2025-08-04 23:01:49,744 - MultiRoundScheduler - INFO -    完成表格: 13
2025-08-04 23:01:49,744 - MultiRoundScheduler - INFO -    失败表格: 0
2025-08-04 23:01:49,744 - MultiRoundScheduler - INFO -    成功率: 100.0%
2025-08-04 23:01:49,744 - MultiRoundScheduler - INFO - 
============================================================
2025-08-04 23:01:49,744 - MultiRoundScheduler - INFO - 🎉 多轮处理工作流完成
2025-08-04 23:01:49,744 - MultiRoundScheduler - INFO - ============================================================
2025-08-04 23:01:49,744 - MultiRoundScheduler - INFO - ⏱️  处理时长: 0.01秒
2025-08-04 23:01:49,744 - MultiRoundScheduler - INFO - 🔄 完成轮次: 4/4
2025-08-04 23:01:49,744 - MultiRoundScheduler - INFO - 📊 处理表格: 13
2025-08-04 23:01:49,744 - MultiRoundScheduler - INFO - 📈 最终状态统计:
2025-08-04 23:01:49,744 - MultiRoundScheduler - INFO -    全部完成✅: 13个表格
2025-08-04 23:01:49,744 - MultiRoundScheduler - INFO - 🚀 开始多轮迭代处理工作流
2025-08-04 23:01:49,744 - MultiRoundScheduler - INFO - ============================================================
2025-08-04 23:01:49,744 - MultiRoundScheduler - INFO - 🔵 第一轮：所有表的源信息和第一原生字段
2025-08-04 23:01:49,744 - MultiRoundScheduler - INFO - ============================================================
2025-08-04 23:01:49,744 - Round1Processor - INFO - 🔵 开始第一轮处理：源信息和第一原生字段
2025-08-04 23:01:49,744 - Round1Processor - INFO - 📊 处理表格: 🧠 逻辑表（底库）
2025-08-04 23:01:49,744 - Round1Processor - INFO - ✅ 完成表格: 🧠 逻辑表（底库）
2025-08-04 23:01:49,744 - Round1Processor - INFO - 📊 处理表格: 📁 信息获取（基建）
2025-08-04 23:01:49,744 - Round1Processor - INFO - ✅ 完成表格: 📁 信息获取（基建）
2025-08-04 23:01:49,744 - Round1Processor - INFO - 📊 处理表格: 🎫 优惠券管理（底库）
2025-08-04 23:01:49,744 - Round1Processor - INFO - ✅ 完成表格: 🎫 优惠券管理（底库）
2025-08-04 23:01:49,744 - Round1Processor - INFO - 📊 处理表格: Ⓜ️ 品牌识别（基建）
2025-08-04 23:01:49,745 - Round1Processor - INFO - ✅ 完成表格: Ⓜ️ 品牌识别（基建）
2025-08-04 23:01:49,745 - Round1Processor - INFO - 📊 处理表格: 🛍️ 品类管理（基建）
2025-08-04 23:01:49,745 - Round1Processor - INFO - ✅ 完成表格: 🛍️ 品类管理（基建）
2025-08-04 23:01:49,745 - Round1Processor - INFO - 📊 处理表格: 🔪 SKU解构（基建）
2025-08-04 23:01:49,745 - Round1Processor - INFO - ✅ 完成表格: 🔪 SKU解构（基建）
2025-08-04 23:01:49,745 - Round1Processor - INFO - 📊 处理表格: 🧪 规格解构（基建）
2025-08-04 23:01:49,745 - Round1Processor - INFO - ✅ 完成表格: 🧪 规格解构（基建）
2025-08-04 23:01:49,745 - Round1Processor - INFO - 📊 处理表格: 💰 价格解构（基建）
2025-08-04 23:01:49,745 - Round1Processor - INFO - ✅ 完成表格: 💰 价格解构（基建）
2025-08-04 23:01:49,745 - Round1Processor - INFO - 📊 处理表格: 💻 单价计算（基建）
2025-08-04 23:01:49,745 - Round1Processor - INFO - ✅ 完成表格: 💻 单价计算（基建）
2025-08-04 23:01:49,745 - Round1Processor - INFO - 📊 处理表格: 🔥 价格热度计算（基建）
2025-08-04 23:01:49,745 - Round1Processor - INFO - ✅ 完成表格: 🔥 价格热度计算（基建）
2025-08-04 23:01:49,745 - Round1Processor - INFO - 📊 处理表格: 🐤 SKU重组-网站专用（基建）
2025-08-04 23:01:49,745 - Round1Processor - INFO - ✅ 完成表格: 🐤 SKU重组-网站专用（基建）
2025-08-04 23:01:49,745 - Round1Processor - INFO - 📊 处理表格: 🐽 SKU重组-社群专用（基建）
2025-08-04 23:01:49,745 - Round1Processor - INFO - ✅ 完成表格: 🐽 SKU重组-社群专用（基建）
2025-08-04 23:01:49,745 - Round1Processor - INFO - 📊 处理表格: 🤖 中转表（工具）
2025-08-04 23:01:49,745 - Round1Processor - INFO - ✅ 完成表格: 🤖 中转表（工具）
2025-08-04 23:01:49,745 - Round1Processor - INFO - 🎉 第一轮处理完成
2025-08-04 23:01:49,745 - MultiRoundScheduler - INFO - 📊 第一轮处理摘要:
2025-08-04 23:01:49,745 - MultiRoundScheduler - INFO -    总表格数: 13
2025-08-04 23:01:49,745 - MultiRoundScheduler - INFO -    完成表格: 13
2025-08-04 23:01:49,745 - MultiRoundScheduler - INFO -    失败表格: 0
2025-08-04 23:01:49,746 - MultiRoundScheduler - INFO -    成功率: 100.0%
2025-08-04 23:01:49,746 - MultiRoundScheduler - INFO - 
============================================================
2025-08-04 23:01:49,746 - MultiRoundScheduler - INFO - 🟡 第二轮：所有表的关联字段
2025-08-04 23:01:49,746 - MultiRoundScheduler - INFO - ============================================================
2025-08-04 23:01:49,746 - Round2Processor - INFO - 🟡 开始第二轮处理：关联字段
2025-08-04 23:01:49,746 - Round2Processor - INFO - 📊 处理表格关联字段: 🧠 逻辑表（底库）
2025-08-04 23:01:49,746 - Round2Processor - INFO - ✅ 完成表格关联字段: 🧠 逻辑表（底库）
2025-08-04 23:01:49,746 - Round2Processor - INFO - 📊 处理表格关联字段: 📁 信息获取（基建）
2025-08-04 23:01:49,746 - Round2Processor - INFO - ✅ 完成表格关联字段: 📁 信息获取（基建）
2025-08-04 23:01:49,746 - Round2Processor - INFO - 📊 处理表格关联字段: 🎫 优惠券管理（底库）
2025-08-04 23:01:49,747 - Round2Processor - INFO - ✅ 完成表格关联字段: 🎫 优惠券管理（底库）
2025-08-04 23:01:49,747 - Round2Processor - INFO - 📊 处理表格关联字段: Ⓜ️ 品牌识别（基建）
2025-08-04 23:01:49,747 - Round2Processor - INFO - ✅ 完成表格关联字段: Ⓜ️ 品牌识别（基建）
2025-08-04 23:01:49,747 - Round2Processor - INFO - 📊 处理表格关联字段: 🛍️ 品类管理（基建）
2025-08-04 23:01:49,747 - Round2Processor - INFO - ✅ 完成表格关联字段: 🛍️ 品类管理（基建）
2025-08-04 23:01:49,747 - Round2Processor - INFO - 📊 处理表格关联字段: 🔪 SKU解构（基建）
2025-08-04 23:01:49,747 - Round2Processor - INFO - ✅ 完成表格关联字段: 🔪 SKU解构（基建）
2025-08-04 23:01:49,748 - Round2Processor - INFO - 📊 处理表格关联字段: 🧪 规格解构（基建）
2025-08-04 23:01:49,748 - Round2Processor - INFO - ✅ 完成表格关联字段: 🧪 规格解构（基建）
2025-08-04 23:01:49,748 - Round2Processor - INFO - 📊 处理表格关联字段: 💰 价格解构（基建）
2025-08-04 23:01:49,748 - Round2Processor - INFO - ✅ 完成表格关联字段: 💰 价格解构（基建）
2025-08-04 23:01:49,748 - Round2Processor - INFO - 📊 处理表格关联字段: 💻 单价计算（基建）
2025-08-04 23:01:49,748 - Round2Processor - INFO - ✅ 完成表格关联字段: 💻 单价计算（基建）
2025-08-04 23:01:49,748 - Round2Processor - INFO - 📊 处理表格关联字段: 🔥 价格热度计算（基建）
2025-08-04 23:01:49,748 - Round2Processor - INFO - ✅ 完成表格关联字段: 🔥 价格热度计算（基建）
2025-08-04 23:01:49,748 - Round2Processor - INFO - 📊 处理表格关联字段: 🐤 SKU重组-网站专用（基建）
2025-08-04 23:01:49,748 - Round2Processor - INFO - ✅ 完成表格关联字段: 🐤 SKU重组-网站专用（基建）
2025-08-04 23:01:49,748 - Round2Processor - INFO - 📊 处理表格关联字段: 🐽 SKU重组-社群专用（基建）
2025-08-04 23:01:49,748 - Round2Processor - INFO - ✅ 完成表格关联字段: 🐽 SKU重组-社群专用（基建）
2025-08-04 23:01:49,749 - Round2Processor - INFO - 📊 处理表格关联字段: 🤖 中转表（工具）
2025-08-04 23:01:49,749 - Round2Processor - INFO - ✅ 完成表格关联字段: 🤖 中转表（工具）
2025-08-04 23:01:49,749 - Round2Processor - INFO - 🎉 第二轮处理完成
2025-08-04 23:01:49,749 - MultiRoundScheduler - INFO - 📊 第二轮处理摘要:
2025-08-04 23:01:49,749 - MultiRoundScheduler - INFO -    总表格数: 13
2025-08-04 23:01:49,749 - MultiRoundScheduler - INFO -    完成表格: 13
2025-08-04 23:01:49,749 - MultiRoundScheduler - INFO -    失败表格: 0
2025-08-04 23:01:49,749 - MultiRoundScheduler - INFO -    成功率: 100.0%
2025-08-04 23:01:49,749 - MultiRoundScheduler - INFO - 
============================================================
2025-08-04 23:01:49,749 - MultiRoundScheduler - INFO - 🟢 第三轮：所有表的第二原生字段
2025-08-04 23:01:49,749 - MultiRoundScheduler - INFO - ============================================================
2025-08-04 23:01:49,749 - MultiRoundScheduler - INFO - 🟢 开始第三轮处理：第二原生字段
2025-08-04 23:01:49,749 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 🧠 逻辑表（底库）
2025-08-04 23:01:49,749 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 🧠 逻辑表（底库）
2025-08-04 23:01:49,749 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 📁 信息获取（基建）
2025-08-04 23:01:49,749 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 📁 信息获取（基建）
2025-08-04 23:01:49,749 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 🎫 优惠券管理（底库）
2025-08-04 23:01:49,749 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 🎫 优惠券管理（底库）
2025-08-04 23:01:49,749 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: Ⓜ️ 品牌识别（基建）
2025-08-04 23:01:49,749 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: Ⓜ️ 品牌识别（基建）
2025-08-04 23:01:49,749 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 🛍️ 品类管理（基建）
2025-08-04 23:01:49,749 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 🛍️ 品类管理（基建）
2025-08-04 23:01:49,750 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 🔪 SKU解构（基建）
2025-08-04 23:01:49,750 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 🔪 SKU解构（基建）
2025-08-04 23:01:49,750 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 🧪 规格解构（基建）
2025-08-04 23:01:49,750 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 🧪 规格解构（基建）
2025-08-04 23:01:49,750 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 💰 价格解构（基建）
2025-08-04 23:01:49,750 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 💰 价格解构（基建）
2025-08-04 23:01:49,750 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 💻 单价计算（基建）
2025-08-04 23:01:49,750 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 💻 单价计算（基建）
2025-08-04 23:01:49,750 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 🔥 价格热度计算（基建）
2025-08-04 23:01:49,750 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 🔥 价格热度计算（基建）
2025-08-04 23:01:49,750 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 🐤 SKU重组-网站专用（基建）
2025-08-04 23:01:49,750 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 🐤 SKU重组-网站专用（基建）
2025-08-04 23:01:49,750 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 🐽 SKU重组-社群专用（基建）
2025-08-04 23:01:49,750 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 🐽 SKU重组-社群专用（基建）
2025-08-04 23:01:49,750 - MultiRoundScheduler - INFO - 📊 处理表格第二原生字段: 🤖 中转表（工具）
2025-08-04 23:01:49,750 - MultiRoundScheduler - INFO - ✅ 完成表格第二原生字段: 🤖 中转表（工具）
2025-08-04 23:01:49,750 - MultiRoundScheduler - INFO - 📊 第三轮处理摘要:
2025-08-04 23:01:49,750 - MultiRoundScheduler - INFO -    总表格数: 13
2025-08-04 23:01:49,750 - MultiRoundScheduler - INFO -    完成表格: 13
2025-08-04 23:01:49,750 - MultiRoundScheduler - INFO -    失败表格: 0
2025-08-04 23:01:49,751 - MultiRoundScheduler - INFO -    成功率: 100.0%
2025-08-04 23:01:49,751 - MultiRoundScheduler - INFO - 
============================================================
2025-08-04 23:01:49,751 - MultiRoundScheduler - INFO - 🟣 第四轮：复杂交叉依赖字段
2025-08-04 23:01:49,751 - MultiRoundScheduler - INFO - ============================================================
2025-08-04 23:01:49,751 - MultiRoundScheduler - INFO - 🟣 开始第四轮处理：复杂交叉依赖
2025-08-04 23:01:49,751 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 🧠 逻辑表（底库）
2025-08-04 23:01:49,751 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 🧠 逻辑表（底库）
2025-08-04 23:01:49,751 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 📁 信息获取（基建）
2025-08-04 23:01:49,751 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 📁 信息获取（基建）
2025-08-04 23:01:49,751 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 🎫 优惠券管理（底库）
2025-08-04 23:01:49,751 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 🎫 优惠券管理（底库）
2025-08-04 23:01:49,751 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: Ⓜ️ 品牌识别（基建）
2025-08-04 23:01:49,751 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: Ⓜ️ 品牌识别（基建）
2025-08-04 23:01:49,751 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 🛍️ 品类管理（基建）
2025-08-04 23:01:49,751 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 🛍️ 品类管理（基建）
2025-08-04 23:01:49,751 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 🔪 SKU解构（基建）
2025-08-04 23:01:49,751 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 🔪 SKU解构（基建）
2025-08-04 23:01:49,751 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 🧪 规格解构（基建）
2025-08-04 23:01:49,751 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 🧪 规格解构（基建）
2025-08-04 23:01:49,751 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 💰 价格解构（基建）
2025-08-04 23:01:49,752 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 💰 价格解构（基建）
2025-08-04 23:01:49,752 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 💻 单价计算（基建）
2025-08-04 23:01:49,752 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 💻 单价计算（基建）
2025-08-04 23:01:49,752 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 🔥 价格热度计算（基建）
2025-08-04 23:01:49,752 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 🔥 价格热度计算（基建）
2025-08-04 23:01:49,752 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 🐤 SKU重组-网站专用（基建）
2025-08-04 23:01:49,752 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 🐤 SKU重组-网站专用（基建）
2025-08-04 23:01:49,752 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 🐽 SKU重组-社群专用（基建）
2025-08-04 23:01:49,752 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 🐽 SKU重组-社群专用（基建）
2025-08-04 23:01:49,752 - MultiRoundScheduler - INFO - 📊 处理表格复杂依赖: 🤖 中转表（工具）
2025-08-04 23:01:49,752 - MultiRoundScheduler - INFO - ✅ 完成表格复杂依赖: 🤖 中转表（工具）
2025-08-04 23:01:49,752 - MultiRoundScheduler - INFO - 📊 第四轮处理摘要:
2025-08-04 23:01:49,752 - MultiRoundScheduler - INFO -    总表格数: 13
2025-08-04 23:01:49,752 - MultiRoundScheduler - INFO -    完成表格: 13
2025-08-04 23:01:49,752 - MultiRoundScheduler - INFO -    失败表格: 0
2025-08-04 23:01:49,752 - MultiRoundScheduler - INFO -    成功率: 100.0%
2025-08-04 23:01:49,752 - MultiRoundScheduler - INFO - 
============================================================
2025-08-04 23:01:49,753 - MultiRoundScheduler - INFO - 🎉 多轮处理工作流完成
2025-08-04 23:01:49,753 - MultiRoundScheduler - INFO - ============================================================
2025-08-04 23:01:49,753 - MultiRoundScheduler - INFO - ⏱️  处理时长: 0.01秒
2025-08-04 23:01:49,753 - MultiRoundScheduler - INFO - 🔄 完成轮次: 4/4
2025-08-04 23:01:49,753 - MultiRoundScheduler - INFO - 📊 处理表格: 13
2025-08-04 23:01:49,753 - MultiRoundScheduler - INFO - 📈 最终状态统计:
2025-08-04 23:01:49,753 - MultiRoundScheduler - INFO -    全部完成✅: 13个表格

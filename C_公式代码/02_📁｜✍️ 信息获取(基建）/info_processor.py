from info_table_model import InfoTableModel
import importlib
import sys
import re
from pathlib import Path

# 定位项目根目录（确保能找到所有文件夹）
current_file = Path(__file__).resolve()
project_root = current_file.parent.parent.parent  # 退到 Nars-代码管理 文件夹
sys.path.append(str(project_root))

# 动态导入 LogicProcessor（保留带图标的文件夹名）
logic_module = importlib.import_module("C_公式代码.01_🧠 逻辑表（底库）.logic_processor")
LogicProcessor = logic_module.LogicProcessor

# 导入新的处理工具
from A_main.text_processor import TextProcessor
from A_main.formula_converter import FormulaConverter
    

class InfoProcessor:
    def __init__(self):
        self.model = InfoTableModel()
        self.logic = LogicProcessor()
        self.table_name = "信息获取（基建）"

        # 初始化新的处理工具
        self.text_processor = TextProcessor()
        self.formula_converter = FormulaConverter()

    def process_basic_fields(self, raw_data):
        """处理源信息和第一原生字段"""
        # 源信息
        original_name = raw_data.get("原始商品名称", raw_data.get("name", ""))
        source = {self.model.fields["原始商品名称"]: original_name}

        # 第一原生字段 - 使用新的文本处理器
        cleaned_name = self.text_processor.clean_text(original_name)
        first_native = {
            self.model.fields["清洗后商品名称"]: cleaned_name
        }

        # 添加更多基础字段处理
        processed_fields = self._process_additional_basic_fields(raw_data, cleaned_name)

        # 数据状态
        status = {
            "源信息": "已完成",
            "第一原生字段": "已完成",
            "关联字段": "未就绪",
            "第二原生字段": "未就绪"
        }

        return {**source, **first_native, **processed_fields, self.model.fields["数据状态"]: status}

    def _process_additional_basic_fields(self, raw_data, cleaned_name):
        """处理额外的基础字段"""
        processed = {}

        # 提取平台信息
        original_info = raw_data.get("原始商品名称", "")
        platform = self._detect_platform(original_info)
        processed["所属平台"] = platform

        # 提取价格信息
        prices = self.text_processor.extract_prices(cleaned_name)
        if prices:
            processed["到手价格"] = max(prices)  # 取最高价格作为到手价格

        # 提取品牌信息
        brands = self.text_processor.extract_brands(cleaned_name)
        if brands:
            processed["检测到的品牌"] = brands[0]  # 取第一个品牌

        # 提取规格信息
        specs = self.text_processor.extract_specifications(cleaned_name)
        processed["规格信息"] = specs

        # 信息类型分类
        info_type = self.text_processor.classify_info_type(cleaned_name)
        processed["信息类型"] = info_type

        return processed

    def _detect_platform(self, text):
        """检测所属平台"""
        if not text:
            return ""

        if "u.jd.com" in text:
            return "🔴京东"
        elif "pinduoduo.com" in text:
            return "🟡拼多多"
        elif re.search(r".*?[0-9a-zA-z]@[.0-9a-zA-z]{4,99}.*|◤◥", text):
            return "🟣抖音"
        else:
            return "🍑淘宝"

    def process_associated_fields(self, data_id):
        """处理关联字段"""
        ready, msg = self.logic.check_deps(self.table_name, "品牌ID")
        if not ready:
            print(f"⚠️ 关联字段处理暂停: {msg}")
            return False
            
        # 模拟获取品牌ID
        self.model.update(data_id, self.model.fields["品牌ID"], "BRAND_001")
        self._update_status(data_id, "关联字段", "已完成")
        print("✅ 关联字段处理完成")
        return True

    def process_second_fields(self, data_id):
        """处理第二原生字段"""
        data = self.model.get(data_id)
        if data[self.model.fields["数据状态"]]["关联字段"] != "已完成":
            print("⚠️ 第二原生字段依赖未就绪")
            return False
            
        # 模拟计算品牌评分
        self.model.update(data_id, self.model.fields["品牌评分"], 4.8)
        self._update_status(data_id, "第二原生字段", "已完成")
        print("✅ 第二原生字段处理完成")
        return True

    def _update_status(self, data_id, field_type, status):
        """更新数据状态"""
        data = self.model.get(data_id)
        current_status = data[self.model.fields["数据状态"]]
        current_status[field_type] = status
        self.model.update(data_id, self.model.fields["数据状态"], current_status)

    def full_process(self, raw_data):
        """全流程处理"""
        # 处理基础字段
        basic_data = self.process_basic_fields(raw_data)
        data_id = self.model.insert(basic_data)
        if not data_id:
            return
            
        print(f"基础字段处理完成，数据ID: {data_id}")
        
        # 尝试处理关联字段
        self.process_associated_fields(data_id)
        
        # 尝试处理第二原生字段
        self.process_second_fields(data_id)
        
        # 显示最终状态
        final_data = self.model.get(data_id)
        print(f"\n最终状态: {final_data[self.model.fields['数据状态']]}")

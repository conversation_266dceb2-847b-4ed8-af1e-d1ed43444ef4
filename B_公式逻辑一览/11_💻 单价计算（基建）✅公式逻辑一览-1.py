💻编号（最终）
TEXT([💻 单价计算（基建）]
.COUNTIF(CurrentValue.[💻编号（参考勿删！）]<[💻编号（参考勿删！）])+1

，"👀000000000"）
=====
💻编号（参考勿删！）
自增数字
=====
🍬商品信息（原始版）
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[💻编号（最终）]).[🍬商品信息（原始版）].LISTCOMBINE()
=====
🍬商品信息ID
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[💻编号（最终）]).[🍬商品信息ID].LISTCOMBINE()
=====
🧠基础单位
IF([🍬商品信息（原始版）].ISBLANK(),""，
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠基础单位]!="").[🧠基础单位].LISTCOMBINE().UNIQUE())
=====
🧠初级单位
IF([🍬商品信息（原始版）].ISBLANK(),""，
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠初级单位]!="").[🧠初级单位].LISTCOMBINE().UNIQUE())
=====
🧠进阶单位
IF([🍬商品信息（原始版）].ISBLANK(),""，
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠进阶单位]!="").[🧠进阶单位].LISTCOMBINE().UNIQUE())
=====
🧠高阶单位
IF([🍬商品信息（原始版）].ISBLANK(),""，
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠高阶单位]!="").[🧠高阶单位].LISTCOMBINE().UNIQUE())
=====
🧠终极单位
IF([🍬商品信息（原始版）].ISBLANK(),""，
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠终极单位]!="").[🧠终极单位].LISTCOMBINE().UNIQUE())
=====
🧠单位合集
IF([🍬商品信息（原始版）].ISBLANK(),""，
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠单位合集]!="").[🧠单位合集].LISTCOMBINE().UNIQUE())
=====
🧠乘法符号
IF([🍬商品信息（原始版）].ISBLANK(),""，
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠乘法符号]!="").[🧠乘法符号].LISTCOMBINE().UNIQUE())
=====
🧠品牌-逻辑
IF([🍬商品信息（原始版）].ISBLANK(),""，
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠品牌-逻辑]!="").[🧠品牌-逻辑].LISTCOMBINE().UNIQUE())
=====
📁💜商品信息（终版分割版）
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[💻编号（最终）]).[📁💜商品信息（终版分割版）].LISTCOMBINE()
=====
📁🤍😀商品标题（纯净分割版）
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[💻编号（最终）]).[📁🤍😀商品信息（纯净分割版） -商品名称&价格专享版].LISTCOMBINE()
=====
📁🤍商品信息（纯净分割版）
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[💻编号（最终）]).[📁🤍商品信息（纯净分割版）].LISTCOMBINE()
=====
📁选品定位符
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[💻编号（最终）]).[📁选品定位符].LISTCOMBINE()
=====
📁源信息发布时间(最终)
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[💻编号（最终）]).[📁源信息发布时间(最终)]
=====
🛍️🤍品类-一级品类（原始值）
[🛍️ ｜✍️ 品类｜口味｜制作工艺 ｜ 使用对象 ｜ 成长期（基建）].FILTER(CurrentValue.[🛍️编号（最终）]=[💻编号（最终）]).[🛍️🤍😀品类-一级品类（原始值）]
=====
🛍️🤍品类-二级分类（原始值）-优化版
[🛍️ ｜✍️ 品类｜口味｜制作工艺 ｜ 使用对象 ｜ 成长期（基建）].FILTER(CurrentValue.[🛍️编号（最终）]=[💻编号（最终）]).[🛍️🤍😀品类-二级分类（原始值）-优化版].LISTCOMBINE()
=====
🔪💚商品价格（信息重组专用版）
[🔪 ｜ ✍️ sku解构(基建)]
.FILTER(CurrentValue.[🔪编号（最终）]=[💻编号（最终）])
.[🔪💚商品价格（信息重组专用版）]
=====
🔪💚商品信息（发车重组版）
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[💻编号（最终）]).[🐽💚商品信息（重组版）].LISTCOMBINE()
=====
💰到手价格（终版）
[💰 价格解构（基建）]
.FILTER(CurrentValue.[💰编号（最终）]=[💻编号（最终）]).[💰🤎到手价格（终版）]
=====
💰付款形式
[💰 价格解构（基建）].FILTER(CurrentValue.[💰编号（最终）]=[💻编号（最终）]).[💰🤍😀付款类型]
=====
🧪🔥🤍规格✖️数量提取(单位回溯版)
[🧪 规格解构（基建）]
.FILTER(CurrentValue.[🧪编号（最终）]=[💻编号（最终）]).[🧪🔥🤍规格✖️数量提取(单位回溯版)]
=====
🧪🔥🤍规格✖️数量提取（终版）
[🧪 规格解构（基建）]
.FILTER(CurrentValue.[🧪编号（最终）]=[💻编号（最终）]).[🧪🔥🤍规格✖️数量提取（终版）]
=====
🧪🔥🤍规格✖️数量✖️起拍数量
[🧪 规格解构（基建）]
.FILTER(CurrentValue.[🧪编号（最终）]=[💻编号（最终）]).[🧪🔥🤍规格✖️数量✖️起拍数量]
=====
🧪🤍规格类型（原始值）
[🧪 规格解构（基建）]
.FILTER(CurrentValue.[🧪编号（最终）]=[💻编号（最终）])
.[🧪🤍规格类型（原始值）]
=====
🧪规格类型（唯一值）
[🧪 规格解构（基建）]
.FILTER(CurrentValue.[🧪编号（最终）]=[💻编号（最终）])
.[🧪规格类型（唯一值）]
=====
🧪🤍基础规格（原始版）
[🧪 规格解构（基建）].FILTER(CurrentValue.[🧪编号（最终）]=[💻编号（最终）]).[🧪🤍基础规格（原始版）]
=====
🧪🤍基础规格-单位提取
[🧪 规格解构（基建）].FILTER(CurrentValue.[🧪编号（最终）]=[💻编号（最终）]).[🧪🤍基础规格-单位提取]
=====
🧪🤍基础规格-数字提取
[🧪 规格解构（基建）].FILTER(CurrentValue.[🧪编号（最终）]=[💻编号（最终）]).[🧪🤍基础规格-数字提取]
=====
🧪🤍基础规格（清洗版+终版）
[🧪 规格解构（基建）].FILTER(CurrentValue.[🧪编号（最终）]=[💻编号（最终）]).[🧪🤍基础规格（清洗版+终版）]
=====
🧪🤍初级数量（原始版）
[🧪 规格解构（基建）].FILTER(CurrentValue.[🧪编号（最终）]=[💻编号（最终）]).[🧪🤍初级数量（原始版）]
=====
🧪🤍初级数量-数字提取（终版）
[🧪 规格解构（基建）].FILTER(CurrentValue.[🧪编号（最终）]=[💻编号（最终）]).[🧪🤍初级数量-数字提取（终版）]
=====
🧪🤍初级数量-单位提取（终版）
[🧪 规格解构（基建）].FILTER(CurrentValue.[🧪编号（最终）]=[💻编号（最终）]).[🧪🤍初级数量-单位提取（终版）]
=====
🧪🤍初级数量（清洗版+终版）
[🧪 规格解构（基建）].FILTER(CurrentValue.[🧪编号（最终）]=[💻编号（最终）]).[🧪🤍初级数量（清洗版+终版）]
=====
🧪🤍进阶数量（原始版）
[🧪 规格解构（基建）].FILTER(CurrentValue.[🧪编号（最终）]=[💻编号（最终）]).[🧪🤍进阶数量（原始版）]
=====
🧪🤍进阶数量-数字提取（终版）
[🧪 规格解构（基建）].FILTER(CurrentValue.[🧪编号（最终）]=[💻编号（最终）]).[🧪🤍进阶数量-数字提取（终版）]
=====
🧪🤍进阶数量-单位提取（终版）
[🧪 规格解构（基建）].FILTER(CurrentValue.[🧪编号（最终）]=[💻编号（最终）]).[🧪🤍进阶数量-单位提取（终版）]
=====
🧪🤍进阶数量（清洗版+终版）
[🧪 规格解构（基建）].FILTER(CurrentValue.[🧪编号（最终）]=[💻编号（最终）]).[🧪🤍进阶数量（清洗版+终版）]
=====
🧪🤍高阶数量（原始版）
[🧪 规格解构（基建）].FILTER(CurrentValue.[🧪编号（最终）]=[💻编号（最终）]).[🧪🤍高阶数量（原始版）]
=====
🧪🤍高阶数量-数字提取（终版）
[🧪 规格解构（基建）].FILTER(CurrentValue.[🧪编号（最终）]=[💻编号（最终）]).[🧪🤍高阶数量-数字提取（终版）]
=====
🧪🤍高阶数量-单位提取（终版）
[🧪 规格解构（基建）].FILTER(CurrentValue.[🧪编号（最终）]=[💻编号（最终）]).[🧪🤍高阶数量-单位提取（终版）]
=====
🧪🤍高阶数量（清洗版+终版）
[🧪 规格解构（基建）].FILTER(CurrentValue.[🧪编号（最终）]=[💻编号（最终）]).[🧪🤍高阶数量（清洗版+终版）]
=====
🧪🤍😀终极数量
[🧪 规格解构（基建）].FILTER(CurrentValue.[🧪编号（最终）]=[💻编号（最终）]).[🧪🤍😀终极数量].LISTCOMBINE()
=====
💻🤍到手价格-数字提取 （完整版）勿删！
IF(ISBLANK([🍬商品信息（原始版）])，""，

CONCATENATE(
IF(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(1)
.ISBLANK()

,"❶0❶🔞；❶0❶🔞；❶0❶🔞；❶0❶🔞；❶0❶🔞；❶0❶🔞
"
,
CONCATENATE(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(1)

,"；"，

IF(ISBLANK(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(2))
，
[💰到手价格（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(1)
，

[💰到手价格（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(2)
)

,"；"，

IF(ISBLANK(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(3))

，
[💰到手价格（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(1)
，

[💰到手价格（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(3)
)
,"；"，

IF(ISBLANK(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(4))

，
[💰到手价格（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(1)
，

[💰到手价格（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(4)
)

,"；"，

IF(ISBLANK(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(5))

，
[💰到手价格（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(1)
，

[💰到手价格（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(5)
)

,"；"，

IF(ISBLANK(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(6))
，
[💰到手价格（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(1)
，

[💰到手价格（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(6)
)))




,CHAR(10),



IF(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(1)
.ISBLANK()

,"❷0❷🔞；❷0❷🔞；❷0❷🔞；❷0❷🔞；❷0❷🔞；❷0❷🔞
"
,
CONCATENATE(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(1)
, "；",

IF(ISBLANK(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(2))
,
[💰到手价格（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(1)
,

[💰到手价格（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(2)
)

, "；",

IF(ISBLANK(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(3))
,
[💰到手价格（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(1)
,

[💰到手价格（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(3)
)
, "；",

IF(ISBLANK(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(4))
,
[💰到手价格（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(1)
,

[💰到手价格（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(4)
)

, "；",

IF(ISBLANK(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(5))
,
[💰到手价格（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(1)
,

[💰到手价格（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(5)
)

, "；",

IF(ISBLANK(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(6))
,
[💰到手价格（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(1)
,

[💰到手价格（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(6)
)))




,CHAR(10),



IF(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(1)
.ISBLANK()

,"❸0❸🔞；❸0❸🔞；❸0❸🔞；❸0❸🔞；❸0❸🔞；❸0❸🔞
"
,
CONCATENATE(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(1)

, "；",

IF(ISBLANK(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(2))
,
[💰到手价格（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(1)
,

[💰到手价格（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(2)
)

, "；",

IF(ISBLANK(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(3))
,
[💰到手价格（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(1)
,
[💰到手价格（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(3)
)
, "；",

IF(ISBLANK(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(4))
,
[💰到手价格（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(1)
,

[💰到手价格（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(4)
)

, "；",

IF(ISBLANK(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(5))
,
[💰到手价格（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(1)
,

[💰到手价格（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(5)
)

, "；",

IF(ISBLANK(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(6))
,
[💰到手价格（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(1)
,

[💰到手价格（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(6)
)))


,CHAR(10),



IF(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(1)
.ISBLANK()

,"❹0❹🔞；❹0❹🔞；❹0❹🔞；❹0❹🔞；❹0❹🔞；❹0❹🔞
"
,
CONCATENATE(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(1)

, "；",

IF(ISBLANK(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(2))
, 

[💰到手价格（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(1)
,

[💰到手价格（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(2)
)

, "；",

IF(ISBLANK(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(3))
, 

[💰到手价格（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(1)
,

[💰到手价格（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(3)
)
, "；",

IF(ISBLANK(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(4))
, 

[💰到手价格（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(1)
,

[💰到手价格（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(4)
)

, "；",

IF(ISBLANK(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(5))
, 

[💰到手价格（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(1)
,

[💰到手价格（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(5)
)

, "；",

IF(ISBLANK(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(6))
, 

[💰到手价格（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(1)
,

[💰到手价格（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(6)
)))

，"
"，

IF(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(1)
.ISBLANK()

,"❺0❺🔞；❺0❺🔞；❺0❺🔞；❺0❺🔞；❺0❺🔞；❺0❺🔞
"
,
CONCATENATE(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(1)

, "；",

IF(ISBLANK(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(2))
, 
[💰到手价格（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(1)
,

[💰到手价格（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(2)
)

, "；",

IF(ISBLANK(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(3))
, 
[💰到手价格（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(1)
,

[💰到手价格（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(3)
)
, "；",

IF(ISBLANK(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(4))
, 
[💰到手价格（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(1)
,

[💰到手价格（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(4)
)

, "；",

IF(ISBLANK(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(5))
, 
[💰到手价格（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(1)
,

[💰到手价格（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(5)
)

, "；",

IF(ISBLANK(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(6))
, 
[💰到手价格（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(1)
,

[💰到手价格（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(6)
)))





,CHAR(10),



IF(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(1)
.ISBLANK()

,"❻0❻🔞；❻0❻🔞；❻0❻🔞；❻0❻🔞；❻0❻🔞；❻0❻🔞
"
,
CONCATENATE(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(1)

, "；",

IF(ISBLANK(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(2))
,
 [💰到手价格（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(1)
,

[💰到手价格（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(2)
)

, "；",

IF(ISBLANK(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(3))
,
 [💰到手价格（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(1)
,

[💰到手价格（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(3)
)
, "；",

IF(ISBLANK(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(4))
,
 [💰到手价格（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(1)
,

[💰到手价格（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(4)
)

, "；",

IF(ISBLANK(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(5))
,
 [💰到手价格（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(1)
,

[💰到手价格（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(5)
)

, "；",

IF(ISBLANK(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(6))
,
 [💰到手价格（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(1)
,

[💰到手价格（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(6)
)))


,CHAR(10),

IF(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(1)
.ISBLANK()

,"❼0❼🔞；❼0❼🔞；❼0❼🔞；❼0❼🔞；❼0❼🔞；❼0❼🔞
"
,
CONCATENATE(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(1)

, "；",

IF(ISBLANK(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(2))
,
 [💰到手价格（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(1)
,

[💰到手价格（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(2)
)

, "；",

IF(ISBLANK(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(3))
,
 [💰到手价格（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(1)
,

[💰到手价格（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(3)
)
, "；",

IF(ISBLANK(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(4))
,
 [💰到手价格（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(1)
,

[💰到手价格（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(4)
)

, "；",

IF(ISBLANK(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(5))
,
 [💰到手价格（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(1)
,

[💰到手价格（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(5)
)

, "；",

IF(ISBLANK(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(6))
,
 [💰到手价格（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(1)
,

[💰到手价格（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(6)
)))


,CHAR(10),

IF(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(1)
.ISBLANK()

,"❽0❽🔞；❽0❽🔞；❽0❽🔞；❽0❽🔞；❽0❽🔞；❽0❽🔞
"
,
CONCATENATE(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(1)

, "；",

IF(ISBLANK(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(2))
,
 [💰到手价格（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(1)
,

[💰到手价格（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(2)
)

, "；",

IF(ISBLANK(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(3))
,
 [💰到手价格（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(1)
,

[💰到手价格（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(3)
)
, "；",

IF(ISBLANK(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(4))
,
 [💰到手价格（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(1)
,

[💰到手价格（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(4)
)

, "；",

IF(ISBLANK(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(5))
,
 [💰到手价格（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(1)
,

[💰到手价格（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(5)
)

, "；",

IF(ISBLANK(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(6))
,
 [💰到手价格（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(1)
,

[💰到手价格（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(6)
)))

,CHAR(10),

IF(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(1)
.ISBLANK()

,"❾0❾🔞；❾0❾🔞；❾0❾🔞；❾0❾🔞；❾0❾🔞；❾0❾🔞
"
,
CONCATENATE(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(1)

, "；",

IF(ISBLANK(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(2))
,
 [💰到手价格（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(1)
,

[💰到手价格（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(2)
)

, "；",

IF(ISBLANK(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(3))
,
 [💰到手价格（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(1)
,

[💰到手价格（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(3)
)
, "；",

IF(ISBLANK(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(4))
,
 [💰到手价格（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(1)
,

[💰到手价格（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(4)
)

, "；",

IF(ISBLANK(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(5))
,
 [💰到手价格（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(1)
,

[💰到手价格（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(5)
)

, "；",

IF(ISBLANK(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(6))
,
 [💰到手价格（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(1)
,

[💰到手价格（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(6)
)))



))
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
💻🤍基础规格-数字提取 （完整版）勿删！
IF(ISBLANK([🍬商品信息（原始版）]),""，

CONCATENATE(
IF(ISBLANK(
[🧪🤍基础规格（清洗版+终版）] .REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(1))
，"❶1❶🔞"，
[🧪🤍基础规格-数字提取]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(1)
)

,"；"，
IF(ISBLANK(
[🧪🤍基础规格（清洗版+终版）].REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(2))
，"❶1❶🔞"，
[🧪🤍基础规格-数字提取]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(2)
)

,"；"，
IF(ISBLANK(
[🧪🤍基础规格（清洗版+终版）].REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(3))
，"❶1❶🔞"，
[🧪🤍基础规格-数字提取]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(3)
)

,"；"，
IF(ISBLANK(
[🧪🤍基础规格（清洗版+终版）].REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(4))
，"❶1❶🔞"，
[🧪🤍基础规格-数字提取]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(4)
)

,"；"，
IF(ISBLANK(
[🧪🤍基础规格（清洗版+终版）].REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(5))
，"❶1❶🔞"，
[🧪🤍基础规格-数字提取]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(5)
)

,"；"，
IF(ISBLANK(
[🧪🤍基础规格（清洗版+终版）].REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(6))
，"❶1❶🔞"，
[🧪🤍基础规格-数字提取]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(6)
)



，CHAR(10)，



IF(ISBLANK(
[🧪🤍基础规格（清洗版+终版）].REGEXEXTRACTALL("❷.*?❷🔞").NTH(1))
,"❷1❷🔞",
[🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❷.*?❷🔞").NTH(1)
)
,"；",
IF(ISBLANK(
[🧪🤍基础规格（清洗版+终版）].REGEXEXTRACTALL("❷.*?❷🔞").NTH(2))
,"❷1❷🔞",
[🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❷.*?❷🔞").NTH(2)
)
,"；",
IF(ISBLANK(
[🧪🤍基础规格（清洗版+终版）].REGEXEXTRACTALL("❷.*?❷🔞").NTH(3))
,"❷1❷🔞",
[🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❷.*?❷🔞").NTH(3)
)
,"；",
IF(ISBLANK(
[🧪🤍基础规格（清洗版+终版）].REGEXEXTRACTALL("❷.*?❷🔞").NTH(4))
,"❷1❷🔞",
[🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❷.*?❷🔞").NTH(4)
)
,"；",
IF(ISBLANK(
[🧪🤍基础规格（清洗版+终版）].REGEXEXTRACTALL("❷.*?❷🔞").NTH(5))
,"❷1❷🔞",
[🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❷.*?❷🔞").NTH(5)
)
,"；",
IF(ISBLANK(
[🧪🤍基础规格（清洗版+终版）].REGEXEXTRACTALL("❷.*?❷🔞").NTH(6))
,"❷1❷🔞",
[🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❷.*?❷🔞").NTH(6)
)


，CHAR(10)，


IF(ISBLANK(
[🧪🤍基础规格（清洗版+终版）].REGEXEXTRACTALL("❸.*?❸🔞").NTH(1))
,"❸1❸🔞",
[🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❸.*?❸🔞").NTH(1)
)
,"；",
IF(ISBLANK(
[🧪🤍基础规格（清洗版+终版）].REGEXEXTRACTALL("❸.*?❸🔞").NTH(2))
,"❸1❸🔞",
[🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❸.*?❸🔞").NTH(2)
)
,"；",
IF(ISBLANK(
[🧪🤍基础规格（清洗版+终版）].REGEXEXTRACTALL("❸.*?❸🔞").NTH(3))
,"❸1❸🔞",
[🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❸.*?❸🔞").NTH(3)
)
,"；",
IF(ISBLANK(
[🧪🤍基础规格（清洗版+终版）].REGEXEXTRACTALL("❸.*?❸🔞").NTH(4))
,"❸1❸🔞",
[🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❸.*?❸🔞").NTH(4)
)
,"；",
IF(ISBLANK(
[🧪🤍基础规格（清洗版+终版）].REGEXEXTRACTALL("❸.*?❸🔞").NTH(5))
,"❸1❸🔞",
[🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❸.*?❸🔞").NTH(5)
)
,"；",
IF(ISBLANK(
[🧪🤍基础规格（清洗版+终版）].REGEXEXTRACTALL("❸.*?❸🔞").NTH(6))
,"❸1❸🔞",
[🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❸.*?❸🔞").NTH(6)
)



，"
"，



IF(ISBLANK(
[🧪🤍基础规格（清洗版+终版）].REGEXEXTRACTALL("❹.*?❹🔞").NTH(1))
,"❹1❹🔞",
[🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❹.*?❹🔞").NTH(1)
)
,"；",
IF(ISBLANK(
[🧪🤍基础规格（清洗版+终版）].REGEXEXTRACTALL("❹.*?❹🔞").NTH(2))
,"❹1❹🔞",
[🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❹.*?❹🔞").NTH(2)
)
,"；",
IF(ISBLANK(
[🧪🤍基础规格（清洗版+终版）].REGEXEXTRACTALL("❹.*?❹🔞").NTH(3))
,"❹1❹🔞",
[🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❹.*?❹🔞").NTH(3)
)
,"；",
IF(ISBLANK(
[🧪🤍基础规格（清洗版+终版）].REGEXEXTRACTALL("❹.*?❹🔞").NTH(4))
,"❹1❹🔞",
[🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❹.*?❹🔞").NTH(4)
)
,"；",
IF(ISBLANK(
[🧪🤍基础规格（清洗版+终版）].REGEXEXTRACTALL("❹.*?❹🔞").NTH(5))
,"❹1❹🔞",
[🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❹.*?❹🔞").NTH(5)
)
,"；",
IF(ISBLANK(
[🧪🤍基础规格（清洗版+终版）].REGEXEXTRACTALL("❹.*?❹🔞").NTH(6))
,"❹1❹🔞",
[🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❹.*?❹🔞").NTH(6)
)


，CHAR(10)，



IF(ISBLANK(
[🧪🤍基础规格（清洗版+终版）].REGEXEXTRACTALL("❺.*?❺🔞").NTH(1))
,"❺1❺🔞",
[🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❺.*?❺🔞").NTH(1)
)
,"；",
IF(ISBLANK(
[🧪🤍基础规格（清洗版+终版）].REGEXEXTRACTALL("❺.*?❺🔞").NTH(2))
,"❺1❺🔞",
[🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❺.*?❺🔞").NTH(2)
)
,"；",
IF(ISBLANK(
[🧪🤍基础规格（清洗版+终版）].REGEXEXTRACTALL("❺.*?❺🔞").NTH(3))
,"❺1❺🔞",
[🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❺.*?❺🔞").NTH(3)
)
,"；",
IF(ISBLANK(
[🧪🤍基础规格（清洗版+终版）].REGEXEXTRACTALL("❺.*?❺🔞").NTH(4))
,"❺1❺🔞",
[🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❺.*?❺🔞").NTH(4)
)
,"；",
IF(ISBLANK(
[🧪🤍基础规格（清洗版+终版）].REGEXEXTRACTALL("❺.*?❺🔞").NTH(5))
,"❺1❺🔞",
[🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❺.*?❺🔞").NTH(5)
)
,"；",
IF(ISBLANK(
[🧪🤍基础规格（清洗版+终版）].REGEXEXTRACTALL("❺.*?❺🔞").NTH(6))
,"❺1❺🔞",
[🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❺.*?❺🔞").NTH(6)
)




，CHAR(10)，

IF(ISBLANK(
[🧪🤍基础规格（清洗版+终版）].REGEXEXTRACTALL("❻.*?❻🔞").NTH(1))
,"❻1❻🔞",
[🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❻.*?❻🔞").NTH(1)
)
,"；",
IF(ISBLANK(
[🧪🤍基础规格（清洗版+终版）].REGEXEXTRACTALL("❻.*?❻🔞").NTH(2))
,"❻1❻🔞",
[🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❻.*?❻🔞").NTH(2)
)
,"；",
IF(ISBLANK(
[🧪🤍基础规格（清洗版+终版）].REGEXEXTRACTALL("❻.*?❻🔞").NTH(3))
,"❻1❻🔞",
[🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❻.*?❻🔞").NTH(3)
)
,"；",
IF(ISBLANK(
[🧪🤍基础规格（清洗版+终版）].REGEXEXTRACTALL("❻.*?❻🔞").NTH(4))
,"❻1❻🔞",
[🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❻.*?❻🔞").NTH(4)
)
,"；",
IF(ISBLANK(
[🧪🤍基础规格（清洗版+终版）].REGEXEXTRACTALL("❻.*?❻🔞").NTH(5))
,"❻1❻🔞",
[🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❻.*?❻🔞").NTH(5)
)
,"；",
IF(ISBLANK(
[🧪🤍基础规格（清洗版+终版）].REGEXEXTRACTALL("❻.*?❻🔞").NTH(6))
,"❻1❻🔞",
[🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❻.*?❻🔞").NTH(6)
)



，CHAR(10)，




CHAR(10),

IF(ISBLANK(
[🧪🤍基础规格（清洗版+终版）].REGEXEXTRACTALL("❼.*?❼🔞").NTH(1))
,"❼1❼🔞",
[🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❼.*?❼🔞").NTH(1)
)
,"；",
IF(ISBLANK(
[🧪🤍基础规格（清洗版+终版）].REGEXEXTRACTALL("❼.*?❼🔞").NTH(2))
,"❼1❼🔞",
[🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❼.*?❼🔞").NTH(2)
)
,"；",
IF(ISBLANK(
[🧪🤍基础规格（清洗版+终版）].REGEXEXTRACTALL("❼.*?❼🔞").NTH(3))
,"❼1❼🔞",
[🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❼.*?❼🔞").NTH(3)
)
,"；",
IF(ISBLANK(
[🧪🤍基础规格（清洗版+终版）].REGEXEXTRACTALL("❼.*?❼🔞").NTH(4))
,"❼1❼🔞",
[🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❼.*?❼🔞").NTH(4)
)
,"；",
IF(ISBLANK(
[🧪🤍基础规格（清洗版+终版）].REGEXEXTRACTALL("❼.*?❼🔞").NTH(5))
,"❼1❼🔞",
[🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❼.*?❼🔞").NTH(5)
)
,"；",
IF(ISBLANK(
[🧪🤍基础规格（清洗版+终版）].REGEXEXTRACTALL("❼.*?❼🔞").NTH(6))
,"❼1❼🔞",
[🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❼.*?❼🔞").NTH(6)
)






，CHAR(10)，





CHAR(10),

IF(ISBLANK(
[🧪🤍基础规格（清洗版+终版）].REGEXEXTRACTALL("❽.*?❽🔞").NTH(1))
,"❽1❽🔞",
[🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❽.*?❽🔞").NTH(1)
)
,"；",
IF(ISBLANK(
[🧪🤍基础规格（清洗版+终版）].REGEXEXTRACTALL("❽.*?❽🔞").NTH(2))
,"❽1❽🔞",
[🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❽.*?❽🔞").NTH(2)
)
,"；",
IF(ISBLANK(
[🧪🤍基础规格（清洗版+终版）].REGEXEXTRACTALL("❽.*?❽🔞").NTH(3))
,"❽1❽🔞",
[🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❽.*?❽🔞").NTH(3)
)
,"；",
IF(ISBLANK(
[🧪🤍基础规格（清洗版+终版）].REGEXEXTRACTALL("❽.*?❽🔞").NTH(4))
,"❽1❽🔞",
[🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❽.*?❽🔞").NTH(4)
)
,"；",
IF(ISBLANK(
[🧪🤍基础规格（清洗版+终版）].REGEXEXTRACTALL("❽.*?❽🔞").NTH(5))
,"❽1❽🔞",
[🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❽.*?❽🔞").NTH(5)
)
,"；",
IF(ISBLANK(
[🧪🤍基础规格（清洗版+终版）].REGEXEXTRACTALL("❽.*?❽🔞").NTH(6))
,"❽1❽🔞",
[🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❽.*?❽🔞").NTH(6)
)




，CHAR(10)，



CHAR(10),

IF(ISBLANK(
[🧪🤍基础规格（清洗版+终版）].REGEXEXTRACTALL("❾.*?❾🔞").NTH(1))
,"❾1❾🔞",
[🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❾.*?❾🔞").NTH(1)
)
,"；",
IF(ISBLANK(
[🧪🤍基础规格（清洗版+终版）].REGEXEXTRACTALL("❾.*?❾🔞").NTH(2))
,"❾1❾🔞",
[🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❾.*?❾🔞").NTH(2)
)
,"；",
IF(ISBLANK(
[🧪🤍基础规格（清洗版+终版）].REGEXEXTRACTALL("❾.*?❾🔞").NTH(3))
,"❾1❾🔞",
[🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❾.*?❾🔞").NTH(3)
)
,"；",
IF(ISBLANK(
[🧪🤍基础规格（清洗版+终版）].REGEXEXTRACTALL("❾.*?❾🔞").NTH(4))
,"❾1❾🔞",
[🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❾.*?❾🔞").NTH(4)
)
,"；",
IF(ISBLANK(
[🧪🤍基础规格（清洗版+终版）].REGEXEXTRACTALL("❾.*?❾🔞").NTH(5))
,"❾1❾🔞",
[🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❾.*?❾🔞").NTH(5)
)
,"；",
IF(ISBLANK(
[🧪🤍基础规格（清洗版+终版）].REGEXEXTRACTALL("❾.*?❾🔞").NTH(6))
,"❾1❾🔞",
[🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❾.*?❾🔞").NTH(6)
)



))
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
💻💚数量（最终）-【基础规格】颗粒度（1-5）
IF(ISBLANK([🍬商品信息（原始版）]),""，

CONCATENATE(

IF(
[🧪🤍规格类型（原始值）].REGEXEXTRACT("❶.*?❶🔞").REGEXMATCH("礼包"),
"",
CONCATENATE(
CONCATENATE(
"❶",
[💻🤍基础规格-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❶(.*?)❶🔞").NTH(1)
*
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❶(.*?)❶🔞").NTH(1)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❶(.*?)❶🔞").NTH(1)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❶(.*?)❶🔞").NTH(1)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❶(.*?)❶🔞").NTH(1),
"❶🔞"
),
"；",
CONCATENATE(
"❶",
[💻🤍基础规格-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❶(.*?)❶🔞").NTH(2)
*
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❶(.*?)❶🔞").NTH(2)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❶(.*?)❶🔞").NTH(2)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❶(.*?)❶🔞").NTH(2)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❶(.*?)❶🔞").NTH(2),
"❶🔞"
),
"；",
CONCATENATE(
"❶",
[💻🤍基础规格-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❶(.*?)❶🔞").NTH(3)
*
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❶(.*?)❶🔞").NTH(3)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❶(.*?)❶🔞").NTH(3)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❶(.*?)❶🔞").NTH(3)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❶(.*?)❶🔞").NTH(3),
"❶🔞"
),
"；",
CONCATENATE(
"❶",
[💻🤍基础规格-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❶(.*?)❶🔞").NTH(4)
*
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❶(.*?)❶🔞").NTH(4)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❶(.*?)❶🔞").NTH(4)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❶(.*?)❶🔞").NTH(4)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❶(.*?)❶🔞").NTH(4),
"❶🔞"
),
"；",
CONCATENATE(
"❶",
[💻🤍基础规格-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❶(.*?)❶🔞").NTH(5)
*
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❶(.*?)❶🔞").NTH(5)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❶(.*?)❶🔞").NTH(5)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❶(.*?)❶🔞").NTH(5)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❶(.*?)❶🔞").NTH(5),
"❶🔞"
),
"；",
CONCATENATE(
"❶",
[💻🤍基础规格-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❶(.*?)❶🔞").NTH(6)
*
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❶(.*?)❶🔞").NTH(6)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❶(.*?)❶🔞").NTH(6)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❶(.*?)❶🔞").NTH(6)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❶(.*?)❶🔞").NTH(6),
"❶🔞"
)
)
)


,CHAR(10),



IF(
[🧪🤍规格类型（原始值）].REGEXEXTRACT("❷.*?❷🔞").REGEXMATCH("礼包"),
"",
CONCATENATE(
CONCATENATE(
"❷",
[💻🤍基础规格-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(1)
*
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(1)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(1)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(1)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(1),
"❷🔞"
),
"；",
CONCATENATE(
"❷",
[💻🤍基础规格-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(2)
*
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(2)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(2)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(2)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(2),
"❷🔞"
),
"；",
CONCATENATE(
"❷",
[💻🤍基础规格-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(3)
*
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(3)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(3)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(3)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(3),
"❷🔞"
),
"；",
CONCATENATE(
"❷",
[💻🤍基础规格-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(4)
*
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(4)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(4)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(4)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(4),
"❷🔞"
),
"；",
CONCATENATE(
"❷",
[💻🤍基础规格-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(5)
*
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(5)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(5)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(5)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(5),
"❷🔞"
),
"；",
CONCATENATE(
"❷",
[💻🤍基础规格-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(6)
*
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(6)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(6)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(6)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(6),
"❷🔞"
)
)
)


,CHAR(10),



IF(
[🧪🤍规格类型（原始值）].REGEXEXTRACT("❸.*?❸🔞").REGEXMATCH("礼包"),
"",
CONCATENATE(
CONCATENATE(
"❸",
[💻🤍基础规格-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(1)
*
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(1)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(1)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(1)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(1),
"❸🔞"
),
"；",
CONCATENATE(
"❸",
[💻🤍基础规格-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(2)
*
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(2)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(2)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(2)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(2),
"❸🔞"
),
"；",
CONCATENATE(
"❸",
[💻🤍基础规格-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(3)
*
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(3)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(3)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(3)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(3),
"❸🔞"
),
"；",
CONCATENATE(
"❸",
[💻🤍基础规格-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(4)
*
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(4)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(4)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(4)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(4),
"❸🔞"
),
"；",
CONCATENATE(
"❸",
[💻🤍基础规格-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(5)
*
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(5)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(5)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(5)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(5),
"❸🔞"
),
"；",
CONCATENATE(
"❸",
[💻🤍基础规格-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(6)
*
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(6)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(6)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(6)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(6),
"❸🔞"
)
)
)



,CHAR(10),




IF(
[🧪🤍规格类型（原始值）].REGEXEXTRACT("❹.*?❹🔞").REGEXMATCH("礼包"),
"",
CONCATENATE(
CONCATENATE(
"❹",
[💻🤍基础规格-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❹(.*?)❹🔞").NTH(1)
*
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❹(.*?)❹🔞").NTH(1)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❹(.*?)❹🔞").NTH(1)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❹(.*?)❹🔞").NTH(1)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❹(.*?)❹🔞").NTH(1),
"❹🔞"
),
"；",
CONCATENATE(
"❹",
[💻🤍基础规格-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❹(.*?)❹🔞").NTH(2)
*
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❹(.*?)❹🔞").NTH(2)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❹(.*?)❹🔞").NTH(2)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❹(.*?)❹🔞").NTH(2)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❹(.*?)❹🔞").NTH(2),
"❹🔞"
),
"；",
CONCATENATE(
"❹",
[💻🤍基础规格-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❹(.*?)❹🔞").NTH(3)
*
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❹(.*?)❹🔞").NTH(3)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❹(.*?)❹🔞").NTH(3)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❹(.*?)❹🔞").NTH(3)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❹(.*?)❹🔞").NTH(3),
"❹🔞"
),
"；",
CONCATENATE(
"❹",
[💻🤍基础规格-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❹(.*?)❹🔞").NTH(4)
*
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❹(.*?)❹🔞").NTH(4)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❹(.*?)❹🔞").NTH(4)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❹(.*?)❹🔞").NTH(4)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❹(.*?)❹🔞").NTH(4),
"❹🔞"
),
"；",
CONCATENATE(
"❹",
[💻🤍基础规格-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❹(.*?)❹🔞").NTH(5)
*
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❹(.*?)❹🔞").NTH(5)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❹(.*?)❹🔞").NTH(5)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❹(.*?)❹🔞").NTH(5)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❹(.*?)❹🔞").NTH(5),
"❹🔞"
),

)
)



,CHAR(10),



IF(
[🧪🤍规格类型（原始值）].REGEXEXTRACT("❺.*?❺🔞").REGEXMATCH("礼包"),
"",
CONCATENATE(
CONCATENATE(
"❺",
[💻🤍基础规格-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❺(.*?)❺🔞").NTH(1)
*
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❺(.*?)❺🔞").NTH(1)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❺(.*?)❺🔞").NTH(1)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❺(.*?)❺🔞").NTH(1)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❺(.*?)❺🔞").NTH(1),
"❺🔞"
),
"；",
CONCATENATE(
"❺",
[💻🤍基础规格-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❺(.*?)❺🔞").NTH(2)
*
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❺(.*?)❺🔞").NTH(2)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❺(.*?)❺🔞").NTH(2)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❺(.*?)❺🔞").NTH(2)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❺(.*?)❺🔞").NTH(2),
"❺🔞"
),
"；",
CONCATENATE(
"❺",
[💻🤍基础规格-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❺(.*?)❺🔞").NTH(3)
*
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❺(.*?)❺🔞").NTH(3)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❺(.*?)❺🔞").NTH(3)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❺(.*?)❺🔞").NTH(3)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❺(.*?)❺🔞").NTH(3),
"❺🔞"
),
"；",
CONCATENATE(
"❺",
[💻🤍基础规格-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❺(.*?)❺🔞").NTH(4)
*
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❺(.*?)❺🔞").NTH(4)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❺(.*?)❺🔞").NTH(4)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❺(.*?)❺🔞").NTH(4)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❺(.*?)❺🔞").NTH(4),
"❺🔞"
),
"；",
CONCATENATE(
"❺",
[💻🤍基础规格-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❺(.*?)❺🔞").NTH(5)
*
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❺(.*?)❺🔞").NTH(5)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❺(.*?)❺🔞").NTH(5)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❺(.*?)❺🔞").NTH(5)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❺(.*?)❺🔞").NTH(5),
"❺🔞"
),

)
)

))
.REGEXREPLACE("[❶-❾]0[❶-❾]🔞；?","")

.REGEXREPLACE("[,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
💻💚数量（最终）-【基础规格】颗粒度（7-9）
IF(ISBLANK([🍬商品信息（原始版）]),"",

CONCATENATE(

IF(
[🧪🤍规格类型（原始值）].REGEXEXTRACT("❻.*?❻🔞").REGEXMATCH("礼包"),
"",
CONCATENATE(
CONCATENATE(
"❻",
[💻🤍基础规格-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(1)
*
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(1)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(1)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(1)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(1),
"❻🔞"
),
"；",
CONCATENATE(
"❻",
[💻🤍基础规格-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(2)
*
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(2)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(2)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(2)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(2),
"❻🔞"
),
"；",
CONCATENATE(
"❻",
[💻🤍基础规格-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(3)
*
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(3)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(3)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(3)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(3),
"❻🔞"
),
"；",
CONCATENATE(
"❻",
[💻🤍基础规格-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(4)
*
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(4)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(4)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(4)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(4),
"❻🔞"
),
"；",
CONCATENATE(
"❻",
[💻🤍基础规格-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(5)
*
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(5)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(5)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(5)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(5),
"❻🔞"
),
"；",
CONCATENATE(
"❻",
[💻🤍基础规格-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(6)
*
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(6)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(6)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(6)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(6),
"❻🔞"
)
)
)


,CHAR(10),



IF(
[🧪🤍规格类型（原始值）].REGEXEXTRACT("❼.*?❼🔞").REGEXMATCH("礼包"),
"",
CONCATENATE(
CONCATENATE(
"❼",
[💻🤍基础规格-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(1)
*
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(1)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(1)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(1)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(1),
"❼🔞"
),
"；",
CONCATENATE(
"❼",
[💻🤍基础规格-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(2)
*
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(2)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(2)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(2)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(2),
"❼🔞"
),
"；",
CONCATENATE(
"❼",
[💻🤍基础规格-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(3)
*
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(3)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(3)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(3)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(3),
"❼🔞"
),
"；",
CONCATENATE(
"❼",
[💻🤍基础规格-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(4)
*
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(4)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(4)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(4)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(4),
"❼🔞"
),
"；",
CONCATENATE(
"❼",
[💻🤍基础规格-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(5)
*
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(5)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(5)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(5)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(5),
"❼🔞"
),
"；",
CONCATENATE(
"❼",
[💻🤍基础规格-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(6)
*
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(6)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(6)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(6)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(6),
"❼🔞"
)
)
)


,CHAR(10),



IF(
[🧪🤍规格类型（原始值）].REGEXEXTRACT("❽.*?❽🔞").REGEXMATCH("礼包"),
"",
CONCATENATE(
CONCATENATE(
"❽",
[💻🤍基础规格-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(1)
*
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(1)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(1)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(1)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(1),
"❽🔞"
),
"；",
CONCATENATE(
"❽",
[💻🤍基础规格-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(2)
*
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(2)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(2)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(2)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(2),
"❽🔞"
),
"；",
CONCATENATE(
"❽",
[💻🤍基础规格-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(3)
*
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(3)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(3)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(3)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(3),
"❽🔞"
),
"；",
CONCATENATE(
"❽",
[💻🤍基础规格-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(4)
*
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(4)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(4)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(4)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(4),
"❽🔞"
),
"；",
CONCATENATE(
"❽",
[💻🤍基础规格-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(5)
*
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(5)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(5)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(5)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(5),
"❽🔞"
),
"；",
CONCATENATE(
"❽",
[💻🤍基础规格-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(6)
*
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(6)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(6)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(6)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(6),
"❽🔞"
)
)
)


,CHAR(10),



IF(
[🧪🤍规格类型（原始值）].REGEXEXTRACT("❾.*?❾🔞").REGEXMATCH("礼包"),
"",
CONCATENATE(
CONCATENATE(
"❾",
[💻🤍基础规格-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❾(.*?)❾🔞").NTH(1)
*
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❾(.*?)❾🔞").NTH(1)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❾(.*?)❾🔞").NTH(1)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❾(.*?)❾🔞").NTH(1)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❾(.*?)❾🔞").NTH(1),
"❾🔞"
),
"；",
CONCATENATE(
"❾",
[💻🤍基础规格-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❾(.*?)❾🔞").NTH(2)
*
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❾(.*?)❾🔞").NTH(2)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❾(.*?)❾🔞").NTH(2)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❾(.*?)❾🔞").NTH(2)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❾(.*?)❾🔞").NTH(2),
"❾🔞"
),
"；",
CONCATENATE(
"❾",
[💻🤍基础规格-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❾(.*?)❾🔞").NTH(3)
*
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❾(.*?)❾🔞").NTH(3)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❾(.*?)❾🔞").NTH(3)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❾(.*?)❾🔞").NTH(3)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❾(.*?)❾🔞").NTH(3),
"❾🔞"
),
"；",
CONCATENATE(
"❾",
[💻🤍基础规格-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❾(.*?)❾🔞").NTH(4)
*
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❾(.*?)❾🔞").NTH(4)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❾(.*?)❾🔞").NTH(4)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❾(.*?)❾🔞").NTH(4)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❾(.*?)❾🔞").NTH(4),
"❾🔞"
),
"；",
CONCATENATE(
"❾",
[💻🤍基础规格-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❾(.*?)❾🔞").NTH(5)
*
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❾(.*?)❾🔞").NTH(5)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❾(.*?)❾🔞").NTH(5)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❾(.*?)❾🔞").NTH(5)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❾(.*?)❾🔞").NTH(5),
"❾🔞"
)
)
)



))
.REGEXREPLACE("[❶-❾]0[❶-❾]🔞；?","")

.REGEXREPLACE("[,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
💻💚数量（最终）-【基础规格】颗粒度
CONCATENATE(
[💻💚数量（最终）-【基础规格】颗粒度（1-5）]
，CHAR(10)，
[💻💚数量（最终）-【基础规格】颗粒度（7-9）])
=====
💻💚基础单价（1-3）
CONCATENATE(

IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❶.*?❶🔞").REGEXMATCH("礼包")
，"",

IF(ISBLANK(
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(1)
)

，""，

CONCATENATE("❶"，
([💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(1)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(1)
.REGEXEXTRACT("\d+(?:\.\d+)?"))
.ROUNDDOWN(2)
，"/"，

[🧪🤍基础规格（清洗版+终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(1)
.REGEXEXTRACT("❶.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❶🔞")，"Ⓜ️❶🔞")



））
，"；"，

IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❶.*?❶🔞").REGEXMATCH("礼包")
，"",

IF(ISBLANK(
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(2)
)
，"",


CONCATENATE("❶"，
([💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(2)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(2)
.REGEXEXTRACT("\d+(?:\.\d+)?"))
.ROUNDDOWN(2)

，"/"，


[🧪🤍基础规格（清洗版+终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(2)
.REGEXEXTRACT("❶.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❶🔞")，"Ⓜ️❶🔞")



））

，"；"，

IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❶.*?❶🔞").REGEXMATCH("礼包")
，"",

IF(ISBLANK(
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(3)
)
，"",


CONCATENATE("❶"，
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(3)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(3)
.REGEXEXTRACT("\d+(?:\.\d+)?")
).ROUNDDOWN(2)

，"/"，


[🧪🤍基础规格（清洗版+终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(3)
.REGEXEXTRACT("❶.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❶🔞")，"Ⓜ️❶🔞")



））

，"；"，

IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❶.*?❶🔞").REGEXMATCH("礼包")
，"",

IF(ISBLANK(
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(4)
)
，"",


CONCATENATE("❶"，
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(4)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(4)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)

，"/"，


[🧪🤍基础规格（清洗版+终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(4)
.REGEXEXTRACT("❶.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❶🔞")，"Ⓜ️❶🔞")



））

，"；"，

IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❶.*?❶🔞").REGEXMATCH("礼包")
，"",

IF(ISBLANK(
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(5)
)
，"",


CONCATENATE("❶"，
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(5)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(5)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)

，"/"，


[🧪🤍基础规格（清洗版+终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(5)
.REGEXEXTRACT("❶.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❶🔞")，"Ⓜ️❶🔞")



））

，"；"，

IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❶.*?❶🔞").REGEXMATCH("礼包")
，"",

IF(ISBLANK(
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(6)
)
，"",


CONCATENATE("❶"，
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(6)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(6)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
，"/"，


[🧪🤍基础规格（清洗版+终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(6)
.REGEXEXTRACT("❶.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❶🔞")，"Ⓜ️❶🔞")


））


，CHAR(10)，




IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❷.*?❷🔞").REGEXMATCH("礼包")
, "",

IF(ISBLANK(
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(1)
)

, "",

CONCATENATE("❷",
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(1)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(1)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)

, "/",


[🧪🤍基础规格（清洗版+终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(1)
.REGEXEXTRACT("❷.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❷🔞")，"Ⓜ️❷🔞")



))
, "；",

IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❷.*?❷🔞").REGEXMATCH("礼包")
, "",

IF(ISBLANK(
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(2)
)
, "",


CONCATENATE("❷",
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(2)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(2)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)

, "/",

[🧪🤍基础规格（清洗版+终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(2)
.REGEXEXTRACT("❷.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❷🔞")，"Ⓜ️❷🔞")




))

, "；",

IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❷.*?❷🔞").REGEXMATCH("礼包")
, "",

IF(ISBLANK(
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(3)
)
, "",


CONCATENATE("❷",
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(3)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(3)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)

, "/",

[🧪🤍基础规格（清洗版+终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(3)
.REGEXEXTRACT("❷.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❷🔞")，"Ⓜ️❷🔞")



))

, "；",

IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❷.*?❷🔞").REGEXMATCH("礼包")
, "",

IF(ISBLANK(
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(4)
)
, "",


CONCATENATE("❷",
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(4)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(4)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)

, "/",


[🧪🤍基础规格（清洗版+终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(4)
.REGEXEXTRACT("❷.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❷🔞")，"Ⓜ️❷🔞")




))

, "；",

IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❷.*?❷🔞").REGEXMATCH("礼包")
, "",

IF(ISBLANK(
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(5)
)
, "",


CONCATENATE("❷",
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(5)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(5)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)

, "/",

[🧪🤍基础规格（清洗版+终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(5)
.REGEXEXTRACT("❷.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❷🔞")，"Ⓜ️❷🔞")




))

, "；",

IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❷.*?❷🔞").REGEXMATCH("礼包")
, "",

IF(ISBLANK(
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(6)
)
, "",


CONCATENATE("❷",
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(6)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(6)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
, "/",


[🧪🤍基础规格（清洗版+终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(6)
.REGEXEXTRACT("❷.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❷🔞")，"Ⓜ️❷🔞")

))




,CHAR(10),
IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❸.*?❸🔞").REGEXMATCH("礼包")
, "",
IF(ISBLANK(
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(1)
)
, "",
CONCATENATE("❸",
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(1)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(1)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
, "/",
[🧪🤍基础规格（清洗版+终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(1)
.REGEXEXTRACT("❸.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❸🔞"),"Ⓜ️❸🔞")
)
)
, "；",
IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❸.*?❸🔞").REGEXMATCH("礼包")
, "",
IF(ISBLANK(
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(2)
)
, "",
CONCATENATE("❸",
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(2)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(2)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
, "/",
[🧪🤍基础规格（清洗版+终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(2)
.REGEXEXTRACT("❸.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❸🔞"),"Ⓜ️❸🔞")
)
)
, "；",
IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❸.*?❸🔞").REGEXMATCH("礼包")
, "",
IF(ISBLANK(
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(3)
)
, "",
CONCATENATE("❸",
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(3)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(3)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
, "/",
[🧪🤍基础规格（清洗版+终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(3)
.REGEXEXTRACT("❸.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❸🔞"),"Ⓜ️❸🔞")
)
)
, "；",
IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❸.*?❸🔞").REGEXMATCH("礼包")
, "",
IF(ISBLANK(
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(4)
)
, "",
CONCATENATE("❸",
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(4)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(4)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
, "/",
[🧪🤍基础规格（清洗版+终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(4)
.REGEXEXTRACT("❸.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❸🔞"),"Ⓜ️❸🔞")
)
)
, "；",
IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❸.*?❸🔞").REGEXMATCH("礼包")
, "",
IF(ISBLANK(
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(5)
)
, "",
CONCATENATE("❸",
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(5)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(5)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
, "/",
[🧪🤍基础规格（清洗版+终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(5)
.REGEXEXTRACT("❸.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❸🔞"),"Ⓜ️❸🔞")
)
)
, "；",
IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❸.*?❸🔞").REGEXMATCH("礼包")
, "",
IF(ISBLANK(
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(6)
)
, "",
CONCATENATE("❸",
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(6)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(6)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
, "/",
[🧪🤍基础规格（清洗版+终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(6)
.REGEXEXTRACT("❸.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❸🔞"),"Ⓜ️❸🔞")
)
)


）
.REGEXREPLACE("\d+(?:\.\d+)?","¥$0")

.REGEXREPLACE("[❶-❾]¥0/?(?:"&[🧠单位合集]&")?Ⓜ️[❶-❾]🔞","")
.REGEXREPLACE("[❶-❾]¥\d+(?:\.\d+)?/Ⓜ️[❶-❾]🔞","")


.REGEXREPLACE("；+","；")
.REGEXREPLACE("^；","")
.REGEXREPLACE("
；","")


.REGEXREPLACE("[;；,，+➕～\\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
💻💚基础单价（4-6）
CONCATENATE(
IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❹.*?❹🔞").REGEXMATCH("礼包")
, "",
IF(ISBLANK(
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(1)
)
, "",
CONCATENATE("❹",
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(1)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(1)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
, "/",
[🧪🤍基础规格（清洗版+终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(1)
.REGEXEXTRACT("❹.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❹🔞"),"Ⓜ️❹🔞")
)
)
, "；",
IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❹.*?❹🔞").REGEXMATCH("礼包")
, "",
IF(ISBLANK(
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(2)
)
, "",
CONCATENATE("❹",
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(2)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(2)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
, "/",
[🧪🤍基础规格（清洗版+终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(2)
.REGEXEXTRACT("❹.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❹🔞"),"Ⓜ️❹🔞")
)
)
, "；",
IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❹.*?❹🔞").REGEXMATCH("礼包")
, "",
IF(ISBLANK(
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(3)
)
, "",
CONCATENATE("❹",
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(3)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(3)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
, "/",
[🧪🤍基础规格（清洗版+终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(3)
.REGEXEXTRACT("❹.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❹🔞"),"Ⓜ️❹🔞")
)
)
, "；",
IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❹.*?❹🔞").REGEXMATCH("礼包")
, "",
IF(ISBLANK(
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(4)
)
, "",
CONCATENATE("❹",
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(4)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(4)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
, "/",
[🧪🤍基础规格（清洗版+终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(4)
.REGEXEXTRACT("❹.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❹🔞"),"Ⓜ️❹🔞")
)
)
, "；",
IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❹.*?❹🔞").REGEXMATCH("礼包")
, "",
IF(ISBLANK(
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(5)
)
, "",
CONCATENATE("❹",
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(5)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(5)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
, "/",
[🧪🤍基础规格（清洗版+终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(5)
.REGEXEXTRACT("❹.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❹🔞"),"Ⓜ️❹🔞")
)
)
, "；",
IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❹.*?❹🔞").REGEXMATCH("礼包")
, "",
IF(ISBLANK(
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(6)
)
, "",
CONCATENATE("❹",
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(6)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(6)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
, "/",
[🧪🤍基础规格（清洗版+终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(6)
.REGEXEXTRACT("❹.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❹🔞"),"Ⓜ️❹🔞")
)
)







,CHAR(10),
IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❺.*?❺🔞").REGEXMATCH("礼包")
, "",
IF(ISBLANK(
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(1)
)
, "",
CONCATENATE("❺",
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(1)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(1)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
, "/",
[🧪🤍基础规格（清洗版+终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(1)
.REGEXEXTRACT("❺.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❺🔞"),"Ⓜ️❺🔞")
)
)
, "；",
IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❺.*?❺🔞").REGEXMATCH("礼包")
, "",
IF(ISBLANK(
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(2)
)
, "",
CONCATENATE("❺",
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(2)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(2)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
, "/",
[🧪🤍基础规格（清洗版+终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(2)
.REGEXEXTRACT("❺.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❺🔞"),"Ⓜ️❺🔞")
)
)
, "；",
IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❺.*?❺🔞").REGEXMATCH("礼包")
, "",
IF(ISBLANK(
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(3)
)
, "",
CONCATENATE("❺",
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(3)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(3)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
, "/",
[🧪🤍基础规格（清洗版+终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(3)
.REGEXEXTRACT("❺.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❺🔞"),"Ⓜ️❺🔞")
)
)
, "；",
IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❺.*?❺🔞").REGEXMATCH("礼包")
, "",
IF(ISBLANK(
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(4)
)
, "",
CONCATENATE("❺",
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(4)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(4)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
, "/",
[🧪🤍基础规格（清洗版+终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(4)
.REGEXEXTRACT("❺.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❺🔞"),"Ⓜ️❺🔞")
)
)
, "；",
IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❺.*?❺🔞").REGEXMATCH("礼包")
, "",
IF(ISBLANK(
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(5)
)
, "",
CONCATENATE("❺",
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(5)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(5)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
, "/",
[🧪🤍基础规格（清洗版+终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(5)
.REGEXEXTRACT("❺.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❺🔞"),"Ⓜ️❺🔞")
)
)
, "；",
IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❺.*?❺🔞").REGEXMATCH("礼包")
, "",
IF(ISBLANK(
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(6)
)
, "",
CONCATENATE("❺",
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(6)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(6)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
, "/",
[🧪🤍基础规格（清洗版+终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(6)
.REGEXEXTRACT("❺.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❺🔞"),"Ⓜ️❺🔞")
)
)





,CHAR(10),
IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❻.*?❻🔞").REGEXMATCH("礼包")
, "",
IF(ISBLANK(
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(1)
)
, "",
CONCATENATE("❻",
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(1)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(1)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
, "/",
[🧪🤍基础规格（清洗版+终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(1)
.REGEXEXTRACT("❻.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❻🔞"),"Ⓜ️❻🔞")
)
)
, "；",
IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❻.*?❻🔞").REGEXMATCH("礼包")
, "",
IF(ISBLANK(
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(2)
)
, "",
CONCATENATE("❻",
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(2)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(2)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
, "/",
[🧪🤍基础规格（清洗版+终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(2)
.REGEXEXTRACT("❻.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❻🔞"),"Ⓜ️❻🔞")
)
)
, "；",
IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❻.*?❻🔞").REGEXMATCH("礼包")
, "",
IF(ISBLANK(
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(3)
)
, "",
CONCATENATE("❻",
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(3)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(3)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
, "/",
[🧪🤍基础规格（清洗版+终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(3)
.REGEXEXTRACT("❻.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❻🔞"),"Ⓜ️❻🔞")
)
)
, "；",
IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❻.*?❻🔞").REGEXMATCH("礼包")
, "",
IF(ISBLANK(
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(4)
)
, "",
CONCATENATE("❻",
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(4)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(4)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
, "/",
[🧪🤍基础规格（清洗版+终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(4)
.REGEXEXTRACT("❻.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❻🔞"),"Ⓜ️❻🔞")
)
)
, "；",
IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❻.*?❻🔞").REGEXMATCH("礼包")
, "",
IF(ISBLANK(
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(5)
)
, "",
CONCATENATE("❻",
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(5)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(5)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
, "/",
[🧪🤍基础规格（清洗版+终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(5)
.REGEXEXTRACT("❻.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❻🔞"),"Ⓜ️❻🔞")
)
)
, "；",
IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❻.*?❻🔞").REGEXMATCH("礼包")
, "",
IF(ISBLANK(
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(6)
)
, "",
CONCATENATE("❻",
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(6)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(6)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
, "/",
[🧪🤍基础规格（清洗版+终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(6)
.REGEXEXTRACT("❻.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❻🔞"),"Ⓜ️❻🔞")
)
)

）
.REGEXREPLACE("\d+(?:\.\d+)?","¥$0")

.REGEXREPLACE("[❶-❾]¥0/?(?:"&[🧠单位合集]&")?Ⓜ️[❶-❾]🔞","")
.REGEXREPLACE("[❶-❾]¥\d+(?:\.\d+)?/Ⓜ️[❶-❾]🔞","")


.REGEXREPLACE("；+","；")
.REGEXREPLACE("^；","")
.REGEXREPLACE("
；","")


.REGEXREPLACE("[;；,，+➕～\\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
💻💚基础单价（7-9）
CONCATENATE(

IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❼.*?❼🔞").REGEXMATCH("礼包")
, "",
IF(ISBLANK(
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(1)
)
, "",
CONCATENATE("❼",
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(1)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(1)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
, "/",
[🧪🤍基础规格（清洗版+终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(1)
.REGEXEXTRACT("❼.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❼🔞"),"Ⓜ️❼🔞")
)
)
, "；",
IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❼.*?❼🔞").REGEXMATCH("礼包")
, "",
IF(ISBLANK(
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(2)
)
, "",
CONCATENATE("❼",
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(2)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(2)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
, "/",
[🧪🤍基础规格（清洗版+终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(2)
.REGEXEXTRACT("❼.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❼🔞"),"Ⓜ️❼🔞")
)
)
, "；",
IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❼.*?❼🔞").REGEXMATCH("礼包")
, "",
IF(ISBLANK(
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(3)
)
, "",
CONCATENATE("❼",
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(3)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(3)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
, "/",
[🧪🤍基础规格（清洗版+终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(3)
.REGEXEXTRACT("❼.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❼🔞"),"Ⓜ️❼🔞")
)
)
, "；",
IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❼.*?❼🔞").REGEXMATCH("礼包")
, "",
IF(ISBLANK(
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(4)
)
, "",
CONCATENATE("❼",
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(4)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(4)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
, "/",
[🧪🤍基础规格（清洗版+终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(4)
.REGEXEXTRACT("❼.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❼🔞"),"Ⓜ️❼🔞")
)
)
, "；",
IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❼.*?❼🔞").REGEXMATCH("礼包")
, "",
IF(ISBLANK(
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(5)
)
, "",
CONCATENATE("❼",
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(5)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(5)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
, "/",
[🧪🤍基础规格（清洗版+终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(5)
.REGEXEXTRACT("❼.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❼🔞"),"Ⓜ️❼🔞")
)
)
, "；",
IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❼.*?❼🔞").REGEXMATCH("礼包")
, "",
IF(ISBLANK(
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(6)
)
, "",
CONCATENATE("❼",
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(6)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(6)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
, "/",
[🧪🤍基础规格（清洗版+终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(6)
.REGEXEXTRACT("❼.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❼🔞"),"Ⓜ️❼🔞")
)
)



,CHAR(10),
IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❽.*?❽🔞").REGEXMATCH("礼包")
, "",
IF(ISBLANK(
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(1)
)
, "",
CONCATENATE("❽",
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(1)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(1)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
, "/",
[🧪🤍基础规格（清洗版+终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(1)
.REGEXEXTRACT("❽.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❽🔞"),"Ⓜ️❽🔞")
)
)
, "；",
IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❽.*?❽🔞").REGEXMATCH("礼包")
, "",
IF(ISBLANK(
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(2)
)
, "",
CONCATENATE("❽",
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(2)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(2)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
, "/",
[🧪🤍基础规格（清洗版+终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(2)
.REGEXEXTRACT("❽.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❽🔞"),"Ⓜ️❽🔞")
)
)
, "；",
IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❽.*?❽🔞").REGEXMATCH("礼包")
, "",
IF(ISBLANK(
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(3)
)
, "",
CONCATENATE("❽",
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(3)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(3)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
, "/",
[🧪🤍基础规格（清洗版+终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(3)
.REGEXEXTRACT("❽.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❽🔞"),"Ⓜ️❽🔞")
)
)
, "；",
IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❽.*?❽🔞").REGEXMATCH("礼包")
, "",
IF(ISBLANK(
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(4)
)
, "",
CONCATENATE("❽",
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(4)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(4)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
, "/",
[🧪🤍基础规格（清洗版+终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(4)
.REGEXEXTRACT("❽.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❽🔞"),"Ⓜ️❽🔞")
)
)
, "；",
IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❽.*?❽🔞").REGEXMATCH("礼包")
, "",
IF(ISBLANK(
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(5)
)
, "",
CONCATENATE("❽",
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(5)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(5)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
, "/",
[🧪🤍基础规格（清洗版+终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(5)
.REGEXEXTRACT("❽.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❽🔞"),"Ⓜ️❽🔞")
)
)
, "；",
IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❽.*?❽🔞").REGEXMATCH("礼包")
, "",
IF(ISBLANK(
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(6)
)
, "",
CONCATENATE("❽",
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(6)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(6)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
, "/",
[🧪🤍基础规格（清洗版+终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(6)
.REGEXEXTRACT("❽.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❽🔞"),"Ⓜ️❽🔞")
)
)





,CHAR(10),
IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❾.*?❾🔞").REGEXMATCH("礼包")
, "",
IF(ISBLANK(
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(1)
)
, "",
CONCATENATE("❾",
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(1)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(1)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
, "/",
[🧪🤍基础规格（清洗版+终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(1)
.REGEXEXTRACT("❾.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❾🔞"),"Ⓜ️❾🔞")
)
)
, "；",
IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❾.*?❾🔞").REGEXMATCH("礼包")
, "",
IF(ISBLANK(
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(2)
)
, "",
CONCATENATE("❾",
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(2)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(2)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
, "/",
[🧪🤍基础规格（清洗版+终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(2)
.REGEXEXTRACT("❾.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❾🔞"),"Ⓜ️❾🔞")
)
)
, "；",
IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❾.*?❾🔞").REGEXMATCH("礼包")
, "",
IF(ISBLANK(
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(3)
)
, "",
CONCATENATE("❾",
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(3)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(3)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
, "/",
[🧪🤍基础规格（清洗版+终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(3)
.REGEXEXTRACT("❾.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❾🔞"),"Ⓜ️❾🔞")
)
)
, "；",
IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❾.*?❾🔞").REGEXMATCH("礼包")
, "",
IF(ISBLANK(
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(4)
)
, "",
CONCATENATE("❾",
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(4)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(4)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
, "/",
[🧪🤍基础规格（清洗版+终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(4)
.REGEXEXTRACT("❾.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❾🔞"),"Ⓜ️❾🔞")
)
)
, "；",
IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❾.*?❾🔞").REGEXMATCH("礼包")
, "",
IF(ISBLANK(
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(5)
)
, "",
CONCATENATE("❾",
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(5)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(5)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
, "/",
[🧪🤍基础规格（清洗版+终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(5)
.REGEXEXTRACT("❾.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❾🔞"),"Ⓜ️❾🔞")
)
)
, "；",
IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❾.*?❾🔞").REGEXMATCH("礼包")
, "",
IF(ISBLANK(
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(6)
)
, "",
CONCATENATE("❾",
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(6)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【基础规格】颗粒度]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(6)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
, "/",
[🧪🤍基础规格（清洗版+终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(6)
.REGEXEXTRACT("❾.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❾🔞"),"Ⓜ️❾🔞")
)
)




）
.REGEXREPLACE("\d+(?:\.\d+)?","¥$0")

.REGEXREPLACE("[❶-❾]¥0/?(?:"&[🧠单位合集]&")?Ⓜ️[❶-❾]🔞","")
.REGEXREPLACE("[❶-❾]¥\d+(?:\.\d+)?/Ⓜ️[❶-❾]🔞","")


.REGEXREPLACE("；+","；")
.REGEXREPLACE("^；","")
.REGEXREPLACE("
；","")


.REGEXREPLACE("[;；,，+➕～\\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
💻💚基础单价
CONCATENATE(
[💻💚基础单价（1-3）]
，CHAR(10)，
[💻💚基础单价（4-6）]
，CHAR(10)，
[💻💚基础单价（7-9）]
)

.REGEXREPLACE("(\..*?)0+(/)","$1$2")
.REGEXREPLACE("\.\/","/")

.REGEXREPLACE("[;；,，+➕～\\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
💻🤍初级数量-数字提取 （完整版）勿删！
IF(ISBLANK([🍬商品信息（原始版）]),""，

CONCATENATE(
IF(ISBLANK(
[🧪🤍初级数量（清洗版+终版）].REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(1))
，"❶1❶🔞"，
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(1)
)

,"；"，
IF(ISBLANK(
[🧪🤍初级数量（清洗版+终版）].REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(2))
，"❶1❶🔞"，
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(2)
)

,"；"，
IF(ISBLANK(
[🧪🤍初级数量（清洗版+终版）].REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(3))
，"❶1❶🔞"，
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(3)
)

,"；"，
IF(ISBLANK(
[🧪🤍初级数量（清洗版+终版）].REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(4))
，"❶1❶🔞"，
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(4)
)

,"；"，
IF(ISBLANK(
[🧪🤍初级数量（清洗版+终版）].REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(5))
，"❶1❶🔞"，
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(5)
)

,"；"，
IF(ISBLANK(
[🧪🤍初级数量（清洗版+终版）].REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(6))
，"❶1❶🔞"，
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(6)
)


,CHAR(10),




IF(ISBLANK(
[🧪🤍初级数量（清洗版+终版）].REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(1))
, "❷1❷🔞",
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(1)
)
, "；",
IF(ISBLANK(
[🧪🤍初级数量（清洗版+终版）].REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(2))
, "❷1❷🔞",
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(2)
)
, "；",
IF(ISBLANK(
[🧪🤍初级数量（清洗版+终版）].REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(3))
, "❷1❷🔞",
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(3)
)
, "；",
IF(ISBLANK(
[🧪🤍初级数量（清洗版+终版）].REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(4))
, "❷1❷🔞",
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(4)
)
, "；",
IF(ISBLANK(
[🧪🤍初级数量（清洗版+终版）].REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(5))
, "❷1❷🔞",
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(5)
)
, "；",
IF(ISBLANK(
[🧪🤍初级数量（清洗版+终版）].REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(6))
, "❷1❷🔞",
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(6)
)




,CHAR(10),




IF(ISBLANK(
[🧪🤍初级数量（清洗版+终版）].REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(1))
, "❸1❸🔞",
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(1)
)
, "；",
IF(ISBLANK(
[🧪🤍初级数量（清洗版+终版）].REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(2))
, "❸1❸🔞",
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(2)
)
, "；",
IF(ISBLANK(
[🧪🤍初级数量（清洗版+终版）].REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(3))
, "❸1❸🔞",
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(3)
)
, "；",
IF(ISBLANK(
[🧪🤍初级数量（清洗版+终版）].REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(4))
, "❸1❸🔞",
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(4)
)
, "；",
IF(ISBLANK(
[🧪🤍初级数量（清洗版+终版）].REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(5))
, "❸1❸🔞",
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(5)
)
, "；",
IF(ISBLANK(
[🧪🤍初级数量（清洗版+终版）].REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(6))
, "❸1❸🔞",
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(6)
)


,CHAR(10),




IF(ISBLANK(
[🧪🤍初级数量（清洗版+终版）].REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(1))
, "❹1❹🔞",
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(1)
)
, "；",
IF(ISBLANK(
[🧪🤍初级数量（清洗版+终版）].REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(2))
, "❹1❹🔞",
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(2)
)
, "；",
IF(ISBLANK(
[🧪🤍初级数量（清洗版+终版）].REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(3))
, "❹1❹🔞",
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(3)
)
, "；",
IF(ISBLANK(
[🧪🤍初级数量（清洗版+终版）].REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(4))
, "❹1❹🔞",
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(4)
)
, "；",
IF(ISBLANK(
[🧪🤍初级数量（清洗版+终版）].REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(5))
, "❹1❹🔞",
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(5)
)
, "；",
IF(ISBLANK(
[🧪🤍初级数量（清洗版+终版）].REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(6))
, "❹1❹🔞",
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(6)
)




,CHAR(10),





IF(ISBLANK(
[🧪🤍初级数量（清洗版+终版）].REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(1))
, "❺1❺🔞",
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(1)
)
, "；",
IF(ISBLANK(
[🧪🤍初级数量（清洗版+终版）].REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(2))
, "❺1❺🔞",
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(2)
)
, "；",
IF(ISBLANK(
[🧪🤍初级数量（清洗版+终版）].REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(3))
, "❺1❺🔞",
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(3)
)
, "；",
IF(ISBLANK(
[🧪🤍初级数量（清洗版+终版）].REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(4))
, "❺1❺🔞",
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(4)
)
, "；",
IF(ISBLANK(
[🧪🤍初级数量（清洗版+终版）].REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(5))
, "❺1❺🔞",
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(5)
)
, "；",
IF(ISBLANK(
[🧪🤍初级数量（清洗版+终版）].REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(6))
, "❺1❺🔞",
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(6)
)


,CHAR(10),


IF(ISBLANK(
[🧪🤍初级数量（清洗版+终版）].REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(1))
, "❻1❻🔞",
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(1))
, "；",
IF(ISBLANK(
[🧪🤍初级数量（清洗版+终版）].REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(2))
, "❻1❻🔞",
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(2))
, "；",
IF(ISBLANK(
[🧪🤍初级数量（清洗版+终版）].REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(3))
, "❻1❻🔞",
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(3))
, "；",
IF(ISBLANK(
[🧪🤍初级数量（清洗版+终版）].REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(4))
, "❻1❻🔞",
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(4)
)
, "；",
IF(ISBLANK(
[🧪🤍初级数量（清洗版+终版）].REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(5))
, "❻1❻🔞",
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(5))
, "；",
IF(ISBLANK(
[🧪🤍初级数量（清洗版+终版）].REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(6))
, "❻1❻🔞",
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(6))


,CHAR(10),


IF(ISBLANK(
[🧪🤍初级数量（清洗版+终版）].REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(1))
, "❼1❼🔞",
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(1))
, "；",
IF(ISBLANK(
[🧪🤍初级数量（清洗版+终版）].REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(2))
, "❼1❼🔞",
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(2))
, "；",
IF(ISBLANK(
[🧪🤍初级数量（清洗版+终版）].REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(3))
, "❼1❼🔞",
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(3))
, "；",
IF(ISBLANK(
[🧪🤍初级数量（清洗版+终版）].REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(4))
, "❼1❼🔞",
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(4))
, "；",
IF(ISBLANK(
[🧪🤍初级数量（清洗版+终版）].REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(5))
, "❼1❼🔞",
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(5))
, "；",
IF(ISBLANK(
[🧪🤍初级数量（清洗版+终版）].REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(6))
, "❼1❼🔞",
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(6))



,CHAR(10),


IF(ISBLANK(
[🧪🤍初级数量（清洗版+终版）].REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(1))
, "❽1❽🔞",
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(1))
, "；",
IF(ISBLANK(
[🧪🤍初级数量（清洗版+终版）].REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(2))
, "❽1❽🔞",
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(2))
, "；",
IF(ISBLANK(
[🧪🤍初级数量（清洗版+终版）].REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(3))
, "❽1❽🔞",
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(3))
, "；",
IF(ISBLANK(
[🧪🤍初级数量（清洗版+终版）].REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(4))
, "❽1❽🔞",
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(4))
, "；",
IF(ISBLANK(
[🧪🤍初级数量（清洗版+终版）].REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(5))
, "❽1❽🔞",
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(5))
, "；",
IF(ISBLANK(
[🧪🤍初级数量（清洗版+终版）].REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(6))
, "❽1❽🔞",
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(6))


,CHAR(10),


IF(ISBLANK(
[🧪🤍初级数量（清洗版+终版）].REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(1))
, "❾1❾🔞",
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(1))
, "；",
IF(ISBLANK(
[🧪🤍初级数量（清洗版+终版）].REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(2))
, "❾1❾🔞",
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(2))
, "；",
IF(ISBLANK(
[🧪🤍初级数量（清洗版+终版）].REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(3))
, "❾1❾🔞",
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(3))
, "；",
IF(ISBLANK(
[🧪🤍初级数量（清洗版+终版）].REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(4))
, "❾1❾🔞",
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(4))
, "；",
IF(ISBLANK(
[🧪🤍初级数量（清洗版+终版）].REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(5))
, "❾1❾🔞",
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(5))
, "；",
IF(ISBLANK(
[🧪🤍初级数量（清洗版+终版）].REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(6))
, "❾1❾🔞",
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(6))



))
=====
💻💚数量（最终）-【初级数量】颗粒度（1-5）
IF(ISBLANK([🍬商品信息（原始版）]),""，

CONCATENATE(
IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACT("❶.*?❶🔞").REGEXMATCH("礼包"),
"",

CONCATENATE(
CONCATENATE(
"❶",
[💻🤍初级数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(1)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(1)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(1)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(1)
,"❶🔞"
）
,"；"，
CONCATENATE(
"❶",
[💻🤍初级数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(2)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(2)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(2)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(2)
,"❶🔞"
）

,"；"，
CONCATENATE(
"❶",
[💻🤍初级数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(3)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(3)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(3)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(3)
,"❶🔞"
）


,"；"，
CONCATENATE(
"❶",
[💻🤍初级数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(4)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(4)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(4)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(4)
,"❶🔞"
）



,"；"，
CONCATENATE(
"❶",
[💻🤍初级数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(5)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(5)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(5)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(5)
,"❶🔞"
）



,"；"，
CONCATENATE(
"❶",
[💻🤍初级数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(6)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(6)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(6)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(6)
,"❶🔞"
）

）
）

，CHAR(10)，

IF(
[🧪🤍规格类型（原始值）].REGEXEXTRACT("❷.*?❷🔞").REGEXMATCH("礼包"),
"",
CONCATENATE(
CONCATENATE(
"❷",
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(1)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(1)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(1)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(1),
"❷🔞"
),
"；",
CONCATENATE(
"❷",
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(2)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(2)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(2)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(2),
"❷🔞"
),
"；",
CONCATENATE(
"❷",
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(3)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(3)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(3)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(3),
"❷🔞"
),
"；",
CONCATENATE(
"❷",
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(4)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(4)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(4)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(4),
"❷🔞"
),
"；",
CONCATENATE(
"❷",
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(5)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(5)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(5)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(5),
"❷🔞"
),
"；",
CONCATENATE(
"❷",
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(6)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(6)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(6)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(6),
"❷🔞"
)
)
)


，"
"，

IF(
[🧪🤍规格类型（原始值）].REGEXEXTRACT("❸.*?❸🔞").REGEXMATCH("礼包"),
"",
CONCATENATE(
CONCATENATE(
"❸",
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(1)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(1)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(1)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(1),
"❸🔞"
),
"；",
CONCATENATE(
"❸",
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(2)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(2)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(2)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(2),
"❸🔞"
),
"；",
CONCATENATE(
"❸",
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(3)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(3)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(3)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(3),
"❸🔞"
),
"；",
CONCATENATE(
"❸",
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(4)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(4)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(4)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(4),
"❸🔞"
),
"；",
CONCATENATE(
"❸",
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(5)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(5)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(5)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(5),
"❸🔞"
),
"；",
CONCATENATE(
"❸",
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(6)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(6)

*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(6)

*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(6),
"❸🔞"
)
)
)




，CHAR(10)，



IF(
[🧪🤍规格类型（原始值）].REGEXEXTRACT("❹.*?❹🔞").REGEXMATCH("礼包"),
"",
CONCATENATE(
CONCATENATE(
"❹",
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❹(.*?)❹🔞").NTH(1)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❹(.*?)❹🔞").NTH(1)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❹(.*?)❹🔞").NTH(1)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❹(.*?)❹🔞").NTH(1),
"❹🔞"
),
"；",
CONCATENATE(
"❹",
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❹(.*?)❹🔞").NTH(2)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❹(.*?)❹🔞").NTH(2)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❹(.*?)❹🔞").NTH(2)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❹(.*?)❹🔞").NTH(2),
"❹🔞"
),
"；",
CONCATENATE(
"❹",
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❹(.*?)❹🔞").NTH(3)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❹(.*?)❹🔞").NTH(3)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❹(.*?)❹🔞").NTH(3)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❹(.*?)❹🔞").NTH(3),
"❹🔞"
),
"；",
CONCATENATE(
"❹",
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❹(.*?)❹🔞").NTH(4)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❹(.*?)❹🔞").NTH(4)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❹(.*?)❹🔞").NTH(4)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❹(.*?)❹🔞").NTH(4),
"❹🔞"
),
"；",
CONCATENATE(
"❹",
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❹(.*?)❹🔞").NTH(5)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❹(.*?)❹🔞").NTH(5)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❹(.*?)❹🔞").NTH(5)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❹(.*?)❹🔞").NTH(5),
"❹🔞"
),
"；",
CONCATENATE(
"❹",
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❹(.*?)❹🔞").NTH(6)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❹(.*?)❹🔞").NTH(6)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❹(.*?)❹🔞").NTH(6)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❹(.*?)❹🔞").NTH(6),
"❹🔞"
)
)
)



，CHAR(10)，


IF(
[🧪🤍规格类型（原始值）].REGEXEXTRACT("❺.*?❺🔞").REGEXMATCH("礼包"),
"",
CONCATENATE(
CONCATENATE(
"❺",
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❺(.*?)❺🔞").NTH(1)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❺(.*?)❺🔞").NTH(1)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❺(.*?)❺🔞").NTH(1)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❺(.*?)❺🔞").NTH(1),
"❺🔞"
),
"；",
CONCATENATE(
"❺",
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❺(.*?)❺🔞").NTH(2)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❺(.*?)❺🔞").NTH(2)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❺(.*?)❺🔞").NTH(2)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❺(.*?)❺🔞").NTH(2),
"❺🔞"
),
"；",
CONCATENATE(
"❺",
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❺(.*?)❺🔞").NTH(3)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❺(.*?)❺🔞").NTH(3)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❺(.*?)❺🔞").NTH(3)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❺(.*?)❺🔞").NTH(3),
"❺🔞"
),
"；",
CONCATENATE(
"❺",
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❺(.*?)❺🔞").NTH(4)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❺(.*?)❺🔞").NTH(4)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❺(.*?)❺🔞").NTH(4)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❺(.*?)❺🔞").NTH(4),
"❺🔞"
),
"；",
CONCATENATE(
"❺",
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❺(.*?)❺🔞").NTH(5)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❺(.*?)❺🔞").NTH(5)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❺(.*?)❺🔞").NTH(5)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❺(.*?)❺🔞").NTH(5),
"❺🔞"
),
"；",
CONCATENATE(
"❺",
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❺(.*?)❺🔞").NTH(6)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❺(.*?)❺🔞").NTH(6)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❺(.*?)❺🔞").NTH(6)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❺(.*?)❺🔞").NTH(6),
"❺🔞"
)
)
)

））
.REGEXREPLACE("[❶-❾]0[❶-❾]🔞；?","")

.REGEXREPLACE("[,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
💻💚数量（最终）-【初级数量】颗粒度（6-9）
IF(ISBLANK([🍬商品信息（原始版）]),""，

CONCATENATE(
IF(
[🧪🤍规格类型（原始值）].REGEXEXTRACT("❻.*?❻🔞").REGEXMATCH("礼包"),
"",
CONCATENATE(
CONCATENATE(
"❻",
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(1)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(1)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(1)

*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(1),
"❻🔞"
),
"；",
CONCATENATE(
"❻",
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(2)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(2)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(2)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(2),
"❻🔞"
),
"；",
CONCATENATE(
"❻",
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(3)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(3)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(3)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(3),
"❻🔞"
),
"；",
CONCATENATE(
"❻",
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(4)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(4)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(4)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(4),
"❻🔞"
),
"；",
CONCATENATE(
"❻",
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(5)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(5)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(5)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(5),
"❻🔞"
),
"；",
CONCATENATE(
"❻",
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(6)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(6)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(6)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(6),
"❻🔞"
)
)
)

，CHAR(10)，



IF(
[🧪🤍规格类型（原始值）].REGEXEXTRACT("❼.*?❼🔞").REGEXMATCH("礼包"),
"",
CONCATENATE(
CONCATENATE(
"❼",
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(1)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(1)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(1)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(1),
"❼🔞"
),
"；",
CONCATENATE(
"❼",
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(2)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(2)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(2)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(2),
"❼🔞"
),
"；",
CONCATENATE(
"❼",
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(3)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(3)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(3)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(3),
"❼🔞"
),
"；",
CONCATENATE(
"❼",
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(4)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(4)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(4)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(4),
"❼🔞"
),
"；",
CONCATENATE(
"❼",
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(5)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(5)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(5)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(5),
"❼🔞"
),
"；",
CONCATENATE(
"❼",
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(6)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(6)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(6)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(6),
"❼🔞"
)
)
)


，CHAR(10)，



IF(
[🧪🤍规格类型（原始值）].REGEXEXTRACT("❽.*?❽🔞").REGEXMATCH("礼包"),
"",
CONCATENATE(
CONCATENATE(
"❽",
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(1)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(1)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(1)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(1),
"❽🔞"
),
"；",
CONCATENATE(
"❽",
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(2)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(2)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(2)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(2),
"❽🔞"
),
"；",
CONCATENATE(
"❽",
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(3)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(3)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(3)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(3),
"❽🔞"
),
"；",
CONCATENATE(
"❽",
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(4)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(4)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(4)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(4),
"❽🔞"
),
"；",
CONCATENATE(
"❽",
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(5)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(5)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(5)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(5),
"❽🔞"
),
"；",
CONCATENATE(
"❽",
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(6)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(6)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(6)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(6),
"❽🔞"
)
)
)


，CHAR(10)，



IF(
[🧪🤍规格类型（原始值）].REGEXEXTRACT("❾.*?❾🔞").REGEXMATCH("礼包"),
"",
CONCATENATE(
CONCATENATE(
"❾",
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❾(.*?)❾🔞").NTH(1)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❾(.*?)❾🔞").NTH(1)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❾(.*?)❾🔞").NTH(1)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❾(.*?)❾🔞").NTH(1),
"❾🔞"
),
"；",
CONCATENATE(
"❾",
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❾(.*?)❾🔞").NTH(2)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❾(.*?)❾🔞").NTH(2)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❾(.*?)❾🔞").NTH(2)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❾(.*?)❾🔞").NTH(2),
"❾🔞"
),
"；",
CONCATENATE(
"❾",
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❾(.*?)❾🔞").NTH(3)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❾(.*?)❾🔞").NTH(3)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❾(.*?)❾🔞").NTH(3)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❾(.*?)❾🔞").NTH(3),
"❾🔞"
),
"；",
CONCATENATE(
"❾",
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❾(.*?)❾🔞").NTH(4)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❾(.*?)❾🔞").NTH(4)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❾(.*?)❾🔞").NTH(4)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❾(.*?)❾🔞").NTH(4),
"❾🔞"
),
"；",
CONCATENATE(
"❾",
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❾(.*?)❾🔞").NTH(5)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❾(.*?)❾🔞").NTH(5)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❾(.*?)❾🔞").NTH(5)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❾(.*?)❾🔞").NTH(5),
"❾🔞"
),
"；",
CONCATENATE(
"❾",
[💻🤍初级数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❾(.*?)❾🔞").NTH(6)
*
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❾(.*?)❾🔞").NTH(6)
*
[💻🤍高阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❾(.*?)❾🔞").NTH(6)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❾(.*?)❾🔞").NTH(6),
"❾🔞"
)
)
)



））
.REGEXREPLACE("[❶-❾]0[❶-❾]🔞；?","")

.REGEXREPLACE("[,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
💻💚数量（最终）-【初级数量】颗粒度
CONCATENATE(
[💻💚数量（最终）-【初级数量】颗粒度（1-5）]
，CHAR(10)，
[💻💚数量（最终）-【初级数量】颗粒度（6-9）])
=====
💻💚初级单价（1-3）
CONCATENATE(

IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❶.*?❶🔞").REGEXMATCH("礼包")
，"",

IF(
[💻💚数量（最终）-【初级数量】颗粒度]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(1)
.VALUE()
=1
，""，

CONCATENATE("❶"，
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(1)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【初级数量】颗粒度]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(1)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)

，"/"，

[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(1)
.REGEXEXTRACT("❶.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❶🔞")，"Ⓜ️❶🔞")

））
，"；"，

IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❶.*?❶🔞").REGEXMATCH("礼包")
，"",


IF(
[💻💚数量（最终）-【初级数量】颗粒度]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(2)
.VALUE()
=1
，""，


CONCATENATE("❶"，
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(2)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【初级数量】颗粒度]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(2)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)

，"/"，

[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(2)
.REGEXEXTRACT("❶.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❶🔞")，"Ⓜ️❶🔞")

））

，"；"，

IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❶.*?❶🔞").REGEXMATCH("礼包")
，"",

IF(
[💻💚数量（最终）-【初级数量】颗粒度]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(3)
.VALUE()
=1
，""，

CONCATENATE("❶"，
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(3)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【初级数量】颗粒度]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(3)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)

，"/"，

[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(3)
.REGEXEXTRACT("❶.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❶🔞")，"Ⓜ️❶🔞")

））

，"；"，

IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❶.*?❶🔞").REGEXMATCH("礼包")
，"",

IF(
[💻💚数量（最终）-【初级数量】颗粒度]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(4)
.VALUE()
=1
，""，

CONCATENATE("❶"，
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(4)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【初级数量】颗粒度]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(4)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
，"/"，

[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(4)
.REGEXEXTRACT("❶.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❶🔞")，"Ⓜ️❶🔞")
））

，"；"，

IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❶.*?❶🔞").REGEXMATCH("礼包")
，"",

IF(
[💻💚数量（最终）-【初级数量】颗粒度]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(5)
.VALUE()
=1
，""，

CONCATENATE("❶"，
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(5)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【初级数量】颗粒度]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(5)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
，"/"，

[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(5)
.REGEXEXTRACT("❶.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❶🔞")，"Ⓜ️❶🔞")

））

，"；"，

IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❶.*?❶🔞").REGEXMATCH("礼包")
，"",

IF(
[💻💚数量（最终）-【初级数量】颗粒度]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(6)
.VALUE()
=1
，""，

CONCATENATE("❶"，
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(6)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【初级数量】颗粒度]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(6)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
，"/"，

[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(6)
.REGEXEXTRACT("❶.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❶🔞")，"Ⓜ️❶🔞")

））




，CHAR(10)，




IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❷.*?❷🔞").REGEXMATCH("礼包")
，"",

IF(
[💻💚数量（最终）-【初级数量】颗粒度]
.REGEXEXTRACTALL("❷(.*?)❷🔞")
.NTH(1)
.VALUE()
=1
，""，

CONCATENATE("❷"，
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(1)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【初级数量】颗粒度]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(1)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)

，"/"，

[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(1)
.REGEXEXTRACT("❷.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❷🔞")，"Ⓜ️❷🔞")

））
，"；"，

IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❷.*?❷🔞").REGEXMATCH("礼包")
，"",


IF(
[💻💚数量（最终）-【初级数量】颗粒度]
.REGEXEXTRACTALL("❷(.*?)❷🔞")
.NTH(2)
.VALUE()
=1
，""，


CONCATENATE("❷"，
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(2)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【初级数量】颗粒度]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(2)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)

，"/"，

[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(2)
.REGEXEXTRACT("❷.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❷🔞")，"Ⓜ️❷🔞")

））

，"；"，

IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❷.*?❷🔞").REGEXMATCH("礼包")
，"",

IF(
[💻💚数量（最终）-【初级数量】颗粒度]
.REGEXEXTRACTALL("❷(.*?)❷🔞")
.NTH(3)
.VALUE()
=1
，""，

CONCATENATE("❷"，
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(3)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【初级数量】颗粒度]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(3)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)

，"/"，

[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(3)
.REGEXEXTRACT("❷.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❷🔞")，"Ⓜ️❷🔞")

））

，"；"，

IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❷.*?❷🔞").REGEXMATCH("礼包")
，"",

IF(
[💻💚数量（最终）-【初级数量】颗粒度]
.REGEXEXTRACTALL("❷(.*?)❷🔞")
.NTH(4)
.VALUE()
=1
，""，

CONCATENATE("❷"，
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(4)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【初级数量】颗粒度]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(4)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
，"/"，

[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(4)
.REGEXEXTRACT("❷.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❷🔞")，"Ⓜ️❷🔞")
））

，"；"，

IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❷.*?❷🔞").REGEXMATCH("礼包")
，"",

IF(
[💻💚数量（最终）-【初级数量】颗粒度]
.REGEXEXTRACTALL("❷(.*?)❷🔞")
.NTH(5)
.VALUE()
=1
，""，

CONCATENATE("❷"，
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(5)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【初级数量】颗粒度]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(5)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
，"/"，

[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(5)
.REGEXEXTRACT("❷.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❷🔞")，"Ⓜ️❷🔞")

））

，"；"，

IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❷.*?❷🔞").REGEXMATCH("礼包")
，"",

IF(
[💻💚数量（最终）-【初级数量】颗粒度]
.REGEXEXTRACTALL("❷(.*?)❷🔞")
.NTH(6)
.VALUE()
=1
，""，

CONCATENATE("❷"，
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(6)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【初级数量】颗粒度]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(6)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
，"/"，

[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(6)
.REGEXEXTRACT("❷.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❷🔞")，"Ⓜ️❷🔞")

））

,CHAR(10),


IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❸.*?❸🔞").REGEXMATCH("礼包")
，"",

IF(
[💻💚数量（最终）-【初级数量】颗粒度]
.REGEXEXTRACTALL("❸(.*?)❸🔞")
.NTH(1)
.VALUE()
=1
，""，

CONCATENATE("❸"，
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(1)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【初级数量】颗粒度]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(1)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)

，"/"，

[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(1)
.REGEXEXTRACT("❸.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❸🔞")，"Ⓜ️❸🔞")

））
，"；"，

IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❸.*?❸🔞").REGEXMATCH("礼包")
，"",


IF(
[💻💚数量（最终）-【初级数量】颗粒度]
.REGEXEXTRACTALL("❸(.*?)❸🔞")
.NTH(2)
.VALUE()
=1
，""，


CONCATENATE("❸"，
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(2)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【初级数量】颗粒度]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(2)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)

，"/"，

[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(2)
.REGEXEXTRACT("❸.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❸🔞")，"Ⓜ️❸🔞")

））

，"；"，

IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❸.*?❸🔞").REGEXMATCH("礼包")
，"",

IF(
[💻💚数量（最终）-【初级数量】颗粒度]
.REGEXEXTRACTALL("❸(.*?)❸🔞")
.NTH(3)
.VALUE()
=1
，""，

CONCATENATE("❸"，
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(3)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【初级数量】颗粒度]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(3)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)

，"/"，

[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(3)
.REGEXEXTRACT("❸.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❸🔞")，"Ⓜ️❸🔞")

））

，"；"，

IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❸.*?❸🔞").REGEXMATCH("礼包")
，"",

IF(
[💻💚数量（最终）-【初级数量】颗粒度]
.REGEXEXTRACTALL("❸(.*?)❸🔞")
.NTH(4)
.VALUE()
=1
，""，

CONCATENATE("❸"，
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(4)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【初级数量】颗粒度]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(4)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
，"/"，

[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(4)
.REGEXEXTRACT("❸.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❸🔞")，"Ⓜ️❸🔞")
））

，"；"，

IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❸.*?❸🔞").REGEXMATCH("礼包")
，"",

IF(
[💻💚数量（最终）-【初级数量】颗粒度]
.REGEXEXTRACTALL("❸(.*?)❸🔞")
.NTH(5)
.VALUE()
=1
，""，

CONCATENATE("❸"，
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(5)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【初级数量】颗粒度]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(5)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
，"/"，

[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(5)
.REGEXEXTRACT("❸.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❸🔞")，"Ⓜ️❸🔞")

））

，"；"，

IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❸.*?❸🔞").REGEXMATCH("礼包")
，"",

IF(
[💻💚数量（最终）-【初级数量】颗粒度]
.REGEXEXTRACTALL("❸(.*?)❸🔞")
.NTH(6)
.VALUE()
=1
，""，

CONCATENATE("❸"，
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(6)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【初级数量】颗粒度]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(6)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
，"/"，

[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(6)
.REGEXEXTRACT("❸.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❸🔞")，"Ⓜ️❸🔞")

））



）
.REGEXREPLACE("\d+(?:\.\d+)?","¥$0")

.REGEXREPLACE("[❶-❾]¥0/?(?:"&[🧠单位合集]&")?Ⓜ️[❶-❾]🔞","")
.REGEXREPLACE("[❶-❾]¥\d+(?:\.\d+)?/Ⓜ️[❶-❾]🔞","")

.REGEXREPLACE("；+","；")
.REGEXREPLACE("^；","")
.REGEXREPLACE("
；","")


.REGEXREPLACE("[;；,，+➕～\\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====

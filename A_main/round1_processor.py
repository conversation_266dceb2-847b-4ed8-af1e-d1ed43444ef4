"""
第一轮处理器：所有表的源信息和第一原生字段
按照多轮迭代策略，先处理所有表格的基础字段
"""

import re
import sys
from pathlib import Path
from typing import Dict, List, Any, Optional
import logging

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from A_main.text_processor import TextProcessor
from A_main.formula_converter import FormulaConverter

class Round1Processor:
    """第一轮处理器：源信息和第一原生字段"""
    
    def __init__(self):
        self.logger = logging.getLogger('Round1Processor')
        self.text_processor = TextProcessor()
        self.formula_converter = FormulaConverter()
        
        # 存储各表的处理结果
        self.results = {}
        
        # 表格处理顺序
        self.table_order = [
            "🧠 逻辑表（底库）",
            "📁 信息获取（基建）",
            "🎫 优惠券管理（底库）", 
            "Ⓜ️ 品牌识别（基建）",
            "🛍️ 品类管理（基建）",
            "🔪 SKU解构（基建）",
            "🧪 规格解构（基建）",
            "💰 价格解构（基建）",
            "💻 单价计算（基建）",
            "🔥 价格热度计算（基建）",
            "🐤 SKU重组-网站专用（基建）",
            "🐽 SKU重组-社群专用（基建）",
            "🤖 中转表（工具）"
        ]
    
    def process_all_tables_round1(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理所有表格的第一轮字段"""
        self.logger.info("🔵 开始第一轮处理：源信息和第一原生字段")
        
        # 生成统一的编号
        record_id = raw_data.get("🍬商品信息ID", "auto_" + str(hash(raw_data.get("🍬商品信息（原始版）", "")))[:8])
        
        for table_name in self.table_order:
            self.logger.info(f"📊 处理表格: {table_name}")
            
            try:
                if table_name == "🧠 逻辑表（底库）":
                    result = self._process_logic_table_round1(raw_data, record_id)
                elif table_name == "📁 信息获取（基建）":
                    result = self._process_info_table_round1(raw_data, record_id)
                elif table_name == "Ⓜ️ 品牌识别（基建）":
                    result = self._process_brand_table_round1(raw_data, record_id)
                elif table_name == "🛍️ 品类管理（基建）":
                    result = self._process_category_table_round1(raw_data, record_id)
                elif table_name == "🔪 SKU解构（基建）":
                    result = self._process_sku_table_round1(raw_data, record_id)
                elif table_name == "🧪 规格解构（基建）":
                    result = self._process_spec_table_round1(raw_data, record_id)
                elif table_name == "💰 价格解构（基建）":
                    result = self._process_price_table_round1(raw_data, record_id)
                else:
                    # 其他表格的基础处理
                    result = self._process_basic_table_round1(table_name, raw_data, record_id)
                
                self.results[table_name] = result
                self.logger.info(f"✅ 完成表格: {table_name}")
                
            except Exception as e:
                self.logger.error(f"❌ 表格处理失败 {table_name}: {e}")
                self.results[table_name] = {"status": "失败", "error": str(e)}
        
        self.logger.info("🎉 第一轮处理完成")
        return self.results
    
    def _process_logic_table_round1(self, raw_data: Dict, record_id: str) -> Dict[str, Any]:
        """处理逻辑表的第一轮字段"""
        result = {
            "🧠编号": record_id,
            "table_type": "逻辑表",
            "status": "第一轮完成"
        }
        
        # 逻辑表主要提供基础规则，在第一轮就加载所有规则
        logic_rules = {
            "🧠基础单位": "(?:[Kk]?[Gg]|[Mm][Gg]|[Ll][Bb]|[Mm]?[Ll]|[Cc]?[Mm]|磅|千?克|公?斤)",
            "🧠初级单位": "(?:[个枚支只片颗粒块饼根])",
            "🧠进阶单位": "(?:[板条杯瓶罐袋卷包])",
            "🧠高阶单位": "(?:[桶盒箱])",
            "🧠终极单位": "(?:[份件单期])",
            "🧠乘法符号": "(?:[×✕🞨✗✖️✘Xx*🞱🞲🞳🞴*️✲✱✳︎🞵🞶🞷🞸🞻🞼✴︎※❋])",
            "🧠加法符号": "(?:[+➕＋])",
            "🧠信息类型参考-逻辑-1": "(?:领|券|楼上|(返|反).*?卡|凑单|🧧)",
            "🧠信息类型参考-逻辑-2": "(?:328.*?(?:权益)?[券包]|开.*?(?:会员|卡)|入会)"
        }
        
        # 构建单位合集
        logic_rules["🧠单位合集"] = f"(?:{logic_rules['🧠基础单位']}|{logic_rules['🧠初级单位']}|{logic_rules['🧠进阶单位']}|{logic_rules['🧠高阶单位']}|{logic_rules['🧠终极单位']})"
        
        result.update(logic_rules)
        return result
    
    def _process_info_table_round1(self, raw_data: Dict, record_id: str) -> Dict[str, Any]:
        """处理信息获取表的第一轮字段"""
        result = {
            "📁编号（最终）": record_id,
            "table_type": "信息获取表",
            "status": "第一轮完成"
        }
        
        # 源信息字段
        original_info = raw_data.get("🍬商品信息（原始版）", "")
        result["🍬商品信息（原始版）"] = original_info
        result["🍬商品信息ID"] = raw_data.get("🍬商品信息ID", record_id)
        result["🍬信息收集账号/人"] = raw_data.get("🍬信息收集账号/人", "")
        result["🍬信息来源 / 发车人"] = raw_data.get("🍬信息来源 / 发车人", "")
        result["🍬源信息发布时间（自动）"] = raw_data.get("🍬源信息发布时间（自动）", "")
        result["🍬源信息采集时间（自动）"] = raw_data.get("🍬源信息采集时间（自动）", "")
        
        # 第一原生字段：文本清洗
        if original_info:
            cleaned_info = self.text_processor.clean_text(original_info)
            result["📁🩵商品信息（清洗版）"] = cleaned_info
            
            # 商品信息分割
            split_info = self.text_processor.split_sku_info(cleaned_info, max_parts=9)
            result["📁💜商品信息（终版分割版）"] = "🙂‍↕️🅾️".join(split_info)
            
            # 提取下单链接/口令
            link_pattern = r"(https?://\S+|[a-zA-Z0-9]{11,})"
            links = re.findall(link_pattern, original_info)
            result["📁🩵下单口令/链接提取（原始）"] = "🙂‍↕️🅾️".join(links) if links else ""
            
            # 链接数量统计
            result["📁🩵链接/口令数量（原始版）"] = len(links)
            
            # 平台识别
            result["📁🩵所属平台"] = self._detect_platform(original_info)
            
            # 基础信息类型判断
            result["📁🩵信息类型（唯一值）"] = self.text_processor.classify_info_type(cleaned_info)
        
        return result
    
    def _process_brand_table_round1(self, raw_data: Dict, record_id: str) -> Dict[str, Any]:
        """处理品牌识别表的第一轮字段"""
        result = {
            "Ⓜ️编号（最终）": record_id,
            "table_type": "品牌识别表",
            "status": "第一轮完成"
        }
        
        # 源信息
        original_info = raw_data.get("🍬商品信息（原始版）", "")
        result["🍬商品信息（原始版）"] = original_info
        result["🍬商品信息ID"] = raw_data.get("🍬商品信息ID", record_id)
        
        # 第一原生字段：品牌提取
        if original_info:
            # 使用文本处理器提取品牌
            brands = self.text_processor.extract_brands(original_info)
            result["Ⓜ️🤍品牌提取（原始值）"] = "\n".join(brands) if brands else ""
            
            # 品牌唯一值处理
            unique_brands = list(set(brands)) if brands else []
            result["Ⓜ️🤍品牌（唯一值）"] = "\n".join(unique_brands)
            
            # 品牌识别进度初始状态
            if brands:
                result["Ⓜ️✍️品牌识别进度（手动）"] = "A待验证品牌"
            else:
                result["Ⓜ️✍️品牌识别进度（手动）"] = "A暂无品牌👀"
        
        return result
    
    def _process_category_table_round1(self, raw_data: Dict, record_id: str) -> Dict[str, Any]:
        """处理品类管理表的第一轮字段"""
        result = {
            "🛍️编号（最终）": record_id,
            "table_type": "品类管理表", 
            "status": "第一轮完成"
        }
        
        # 源信息
        original_info = raw_data.get("🍬商品信息（原始版）", "")
        result["🍬商品信息（原始版）"] = original_info
        result["🍬商品信息ID"] = raw_data.get("🍬商品信息ID", record_id)
        
        # 第一原生字段：品类识别
        if original_info:
            # 一级品类识别
            category1 = self._extract_primary_category(original_info)
            result["🛍️🤍😀品类-一级品类（原始值）"] = category1
            
            # 二级品类识别
            category2 = self._extract_secondary_category(original_info)
            result["🛍️🤍😀品类-二级分类（原始值）-优化版"] = category2
            
            # 使用对象识别
            usage_object = self._extract_usage_object(original_info)
            result["🛍️🤍使用对象（原始值）"] = usage_object
        
        return result
    
    def _process_sku_table_round1(self, raw_data: Dict, record_id: str) -> Dict[str, Any]:
        """处理SKU解构表的第一轮字段"""
        result = {
            "🔪编号（最终）": record_id,
            "table_type": "SKU解构表",
            "status": "第一轮完成"
        }
        
        # 源信息
        original_info = raw_data.get("🍬商品信息（原始版）", "")
        result["🍬商品信息（原始版）"] = original_info
        result["🍬商品信息ID"] = raw_data.get("🍬商品信息ID", record_id)
        
        # 第一原生字段：基础SKU解构
        if original_info:
            # SKU数量统计（基于分割结果）
            split_info = self.text_processor.split_sku_info(original_info, max_parts=9)
            result["🔪🖤SKU数量"] = len([part for part in split_info if part.strip()])
            
            # 基础商品名称提取
            result["🔪💚商品名称（基础版）"] = self._extract_product_name(original_info)
            
            # 下单文案提取
            result["🔪🧡下单文案"] = self._extract_order_text(original_info)
            
            # 下单口令/链接提取
            link_pattern = r"(https?://\S+|[a-zA-Z0-9]{11,})"
            links = re.findall(link_pattern, original_info)
            result["🔪🧡下单口令/链接提取"] = "🙂‍↕️🅾️".join(links) if links else ""
        
        return result
    
    def _process_spec_table_round1(self, raw_data: Dict, record_id: str) -> Dict[str, Any]:
        """处理规格解构表的第一轮字段"""
        result = {
            "🧪编号（最终）": record_id,
            "table_type": "规格解构表",
            "status": "第一轮完成"
        }
        
        # 源信息
        original_info = raw_data.get("🍬商品信息（原始版）", "")
        result["🍬商品信息（原始版）"] = original_info
        result["🍬商品信息ID"] = raw_data.get("🍬商品信息ID", record_id)
        
        # 第一原生字段：基础规格提取
        if original_info:
            # 提取规格信息
            specs = self.text_processor.extract_specifications(original_info)
            
            # 规格类型判断
            if specs["weights"]:
                result["🧪🤍规格类型（原始值）"] = "重量规格"
            elif specs["quantities"]:
                result["🧪🤍规格类型（原始值）"] = "数量规格"
            elif specs["packages"]:
                result["🧪🤍规格类型（原始值）"] = "包装规格"
            else:
                result["🧪🤍规格类型（原始值）"] = "无明确规格"
            
            # 规格提取原始值
            all_specs = specs["weights"] + specs["quantities"] + specs["packages"]
            result["🧪🤍规格✖️数量提取（原始版）"] = "🙂‍↕️🅾️".join(all_specs) if all_specs else ""
        
        return result
    
    def _process_price_table_round1(self, raw_data: Dict, record_id: str) -> Dict[str, Any]:
        """处理价格解构表的第一轮字段"""
        result = {
            "💰编号（最终）": record_id,
            "table_type": "价格解构表",
            "status": "第一轮完成"
        }
        
        # 源信息
        original_info = raw_data.get("🍬商品信息（原始版）", "")
        result["🍬商品信息（原始版）"] = original_info
        result["🍬商品信息ID"] = raw_data.get("🍬商品信息ID", record_id)
        
        # 第一原生字段：基础价格提取
        if original_info:
            # 提取价格信息
            prices = self.text_processor.extract_prices(original_info)
            
            if prices:
                # 原始高价（最高价格）
                result["💰🩶原始高价"] = max(prices)
                
                # 原始低价（最低价格）
                result["💰🩶原始低价"] = min(prices)
                
                # 价格数量
                result["💰🩶价格数量"] = len(prices)
                
                # 到手价格（暂时使用最低价）
                result["💰🤎到手价格（终版）"] = min(prices)
            else:
                result["💰🩶原始高价"] = 0
                result["💰🩶原始低价"] = 0
                result["💰🩶价格数量"] = 0
                result["💰🤎到手价格（终版）"] = 0
            
            # 付款类型初步判断
            if "任选" in original_info:
                result["💰🤍😀付款类型"] = "🟧任选下单"
            elif "合并" in original_info or "一起" in original_info:
                result["💰🤍😀付款类型"] = "🟩合并下单"
            else:
                result["💰🤍😀付款类型"] = "⬛️常规付款"
        
        return result
    
    def _process_basic_table_round1(self, table_name: str, raw_data: Dict, record_id: str) -> Dict[str, Any]:
        """处理其他表格的基础第一轮字段"""
        # 提取表格emoji标识
        table_emoji = table_name.split()[0] if table_name else "🔧"
        
        result = {
            f"{table_emoji}编号（最终）": record_id,
            "table_type": table_name,
            "status": "第一轮完成"
        }
        
        # 源信息
        result["🍬商品信息（原始版）"] = raw_data.get("🍬商品信息（原始版）", "")
        result["🍬商品信息ID"] = raw_data.get("🍬商品信息ID", record_id)
        
        return result
    
    # 辅助方法
    def _detect_platform(self, text: str) -> str:
        """检测所属平台"""
        if "u.jd.com" in text:
            return "🔴京东"
        elif "pinduoduo.com" in text:
            return "🟡拼多多"
        elif re.search(r".*?[0-9a-zA-z]@[.0-9a-zA-z]{4,99}.*|◤◥", text):
            return "🟣抖音"
        else:
            return "🍑淘宝"
    
    def _extract_primary_category(self, text: str) -> str:
        """提取一级品类"""
        categories = ["主粮", "零食", "用品", "医疗", "清洁"]
        for category in categories:
            if category in text:
                return category
        return "未分类"
    
    def _extract_secondary_category(self, text: str) -> str:
        """提取二级品类"""
        categories = {
            "罐头": "罐头", "餐盒": "罐头", "餐包": "罐头",
            "冻干": "冻干", "肉干": "冻干",
            "猫砂": "猫砂",
            "驱虫": "驱虫药",
            "玩具": "宠物玩具"
        }
        
        for keyword, category in categories.items():
            if keyword in text:
                return category
        return "其他"
    
    def _extract_usage_object(self, text: str) -> str:
        """提取使用对象"""
        if "猫" in text:
            return "🐱猫"
        elif "狗" in text or "犬" in text:
            return "🐶狗"
        elif "宠物" in text:
            return "🐾宠物通用"
        else:
            return "未知"
    
    def _extract_product_name(self, text: str) -> str:
        """提取商品名称"""
        # 简化的商品名称提取逻辑
        # 去除价格、链接等干扰信息
        cleaned = re.sub(r"💰\d+(?:\.\d+)?", "", text)
        cleaned = re.sub(r"https?://\S+", "", cleaned)
        cleaned = re.sub(r"[a-zA-Z0-9]{11,}", "", cleaned)
        return cleaned.strip()[:50]  # 限制长度
    
    def _extract_order_text(self, text: str) -> str:
        """提取下单文案"""
        # 查找包含下单相关关键词的部分
        order_keywords = ["下单", "购买", "立即", "抢购", "秒杀"]
        for keyword in order_keywords:
            if keyword in text:
                # 提取包含关键词的句子
                sentences = text.split("。")
                for sentence in sentences:
                    if keyword in sentence:
                        return sentence.strip()
        return ""

# 测试代码
if __name__ == "__main__":
    processor = Round1Processor()
    
    # 测试数据
    test_data = {
        "🍬商品信息（原始版）": "爱肯拿 农场盛宴 全猫粮 1.8kg 💰128.5元 u.jd.com/abc123",
        "🍬商品信息ID": "test_001",
        "🍬信息来源 / 发车人": "测试用户"
    }
    
    print("🔵 开始第一轮处理测试")
    results = processor.process_all_tables_round1(test_data)
    
    print("\n📊 处理结果摘要:")
    for table_name, result in results.items():
        status = result.get("status", "未知")
        print(f"  {table_name}: {status}")
    
    print("\n✅ 第一轮处理测试完成")

🐤编号（最终）
TEXT([🔪 ｜ ✍️ sku解构(基建)].COUNTIF(CurrentValue.[🔪编号（参考勿删！）]<[🐤编号（参考勿删！）])+1,"👀000000000")
======
🐤编号（参考勿删！）
自增数字
=====
🍬商品信息（原始版）
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🐤编号（最终）]).[🍬商品信息（原始版）].LISTCOMBINE()
=====
🧠品牌-逻辑
IF([🍬商品信息（原始版）].ISBLANK(),"",[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠品牌-逻辑]!="").[🧠品牌-逻辑].LISTCOMBINE().UNIQUE())
=====
🧠基础单位
IF([🍬商品信息（原始版）].ISBLANK(),"",[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠基础单位]!="").[🧠基础单位].LISTCOMBINE().UNIQUE())
=====
🧠初级单位
IF([🍬商品信息（原始版）].ISBLANK(),"",[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠初级单位]!="").[🧠初级单位].LISTCOMBINE().UNIQUE())
=====
🧠进阶单位
IF([🍬商品信息（原始版）].ISBLANK(),"",[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠进阶单位]!="").[🧠进阶单位].LISTCOMBINE().UNIQUE())
=====
🧠终极单位
IF([🍬商品信息（原始版）].ISBLANK(),"",[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠终极单位]!="").[🧠终极单位].LISTCOMBINE().UNIQUE())
=====
🧠赠送信息参考-逻辑
IF([🍬商品信息（原始版）].ISBLANK(),"",[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠赠送信息-逻辑]!="").[🧠赠送信息-逻辑].LISTCOMBINE().UNIQUE())
=====
🧠加号单位
IF([🍬商品信息（原始版）].ISBLANK(),"",[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠加法符号]!="").[🧠加法符号].LISTCOMBINE().UNIQUE())
=====
🧠高阶单位
IF([🍬商品信息（原始版）].ISBLANK(),"",[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠高阶单位]!="").[🧠高阶单位].LISTCOMBINE().UNIQUE())
=====
🧠单位合集
IF([🍬商品信息（原始版）].ISBLANK(),"",[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠单位合集]!="").[🧠单位合集].LISTCOMBINE().UNIQUE())
=====
🧠下单口令/链接-逻辑
IF([🍬商品信息（原始版）].ISBLANK(),"",[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠下单口令/链接-逻辑]!="").[🧠下单口令/链接-逻辑].LISTCOMBINE().UNIQUE())
=====
🧠领券文案干扰信息-逻辑
IF([🍬商品信息（原始版）].ISBLANK(),"",[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠领券文案干扰信息-逻辑]!="").[🧠领券文案干扰信息-逻辑].LISTCOMBINE().UNIQUE())
=====
🧠价格优势-逻辑
IF([🍬商品信息（原始版）].ISBLANK(),"",[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠价格优势-逻辑]!="").[🧠价格优势-逻辑].LISTCOMBINE().UNIQUE())
=====
🧠需转义符号-逻辑
IF([🍬商品信息（原始版）].ISBLANK(),"",[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠需转义符号-逻辑]!="").[🧠需转义符号-逻辑].LISTCOMBINE().UNIQUE())
=====
📁所属平台
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🐤编号（最终）]).[📁所属平台].LISTCOMBINE()
=====
📁🧡SKU组合
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🐤编号（最终）]).[📁🧡SKU组合].LISTCOMBINE()
=====
📁🧡🖤商品信息组合类型（终版）
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🐤编号（最终）]).[📁🧡🖤商品信息组合类型（终版）].LISTCOMBINE()
=====
🔪🖤商品名称（字段维护专用）
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[🐤编号（最终）]).[🔪🖤商品名称（字段维护专用）].LISTCOMBINE()
=====
🔪💛先导文案
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[🐤编号（最终）]).[🔪💛先导文案].LISTCOMBINE()
=====
🐤🧡SKU组合
IF(
[🔪💛先导文案]
.ISBLANK()
，
[📁🧡SKU组合]
，
[📁🧡SKU组合]
.REGEXREPLACE([🧠领券文案干扰信息-逻辑],"")
.REGEXREPLACE("([❶-❾]).*?("&[🧠价格优势-逻辑]&"|[自营旗舰店]{2,6}).*","$1")

)

.REGEXREPLACE("🪜","")
.REGEXREPLACE("([❶-❾])\s*"&CHAR(10),"$1")
=====
🐤🧡下单口令/链接替换文案
[🐤🧡SKU组合]
.REGEXREPLACE([🧠下单口令/链接-逻辑],"🔗")
.REGEXREPLACE("\n","")
.REGEXREPLACE("([❶-❾])(.*?🔗)","$1$2$1🔞")
.REGEXREPLACE("([❶-❾]🔞.*?)([❶-❾])","$1"&CHAR(10)&"$2")

.REGEXREPLACE("([❶-❾]).*?(?:[领券]{1,2}|兑换?).*?(?:拍|加|凑)?.*?💰.*?🔗.*?([❶-❾]🔞)","$1去购买🗳️$2")

.REGEXREPLACE("([❶-❾]).*?(?:拍).*?(?:凑).*?🔗.*?([❶-❾]🔞)","$1去凑单🛒$2")
.REGEXREPLACE("([❶-❾]).*?(?:[领券]{1,2}|兑换?).*?(?:拍).*?🔗.*?([❶-❾]🔞)","$1去拍单👏$2")
.REGEXREPLACE("([❶-❾]).*?(?:[领券]{1,2}|兑换?|凑单?).*?(?:加).*?🔗.*?([❶-❾]🔞)","$1去加购🛍️$2")
.REGEXREPLACE("([❶-❾]).*?(?:[领券]{1,2}|兑换?).*?(?:付).*?🔗.*?([❶-❾]🔞)","$1去付款💰$2")
.REGEXREPLACE("([❶-❾]).*?(?:[领券]{1,2}兑换?).*?(?:💰).*?🔗.*?([❶-❾]🔞)","$1去购买🗳️$2")

.REGEXREPLACE("([❶-❾]).*?(?:兑换?).*?🔗.*?([❶-❾]🔞)","$1去兑换🪜$2")
.REGEXREPLACE("([❶-❾]).*?(?:[领券]{1,2}).*?🔗.*?([❶-❾]🔞)","$1去领券🎫$2")

.REGEXREPLACE("([❶-❾]).*?💰?.*?(?:拍).*?🔗.*?([❶-❾]🔞)","$1去拍单👏$2")
.REGEXREPLACE("([❶-❾]).*?(?:拍).*?💰.*?🔗.*?([❶-❾]🔞)","$1去购买🗳️$2")

.REGEXREPLACE("([❶-❾]).*?(?:凑).*?🔗.*?([❶-❾]🔞)","$1去凑单🛒$2")
.REGEXREPLACE("([❶-❾]).*?(?:加购).*?🔗.*?([❶-❾]🔞)","$1去加购🛍️$2")
.REGEXREPLACE("([❶-❾]).*?(?:付款?).*?🔗.*?([❶-❾]🔞)","$1去付款💰$2")

.REGEXREPLACE("([❶-❾]).*?🔗.*?([❶-❾]🔞)","$1去购买🗳️$2")

.REGEXEXTRACTALL("([❶-❾].*?(?:领券|兑换|拍单|凑单|加购|付款|下单|购买)\p{Emoji}.*?[❶-❾]🔞)")
.ARRAYJOIN(CHAR(10))

.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🐤🧡下单口令/链接替换文案（终版）
IF(
AND(
[📁所属平台]
.REGEXMATCH("”淘宝")
，

OR(
[🐤🧡下单口令/链接替换文案]
.REGEXEXTRACTALL("[❶-❾]")
.UNIQUE()
.COUNTA()=1
,
[🐤🧡下单口令/链接替换文案]
.REGEXMATCH("(🛒|👏|🛍️|🪜|🎫)")
.NOT()
))
,

[🐤🧡下单口令/链接替换文案]
.REGEXREPLACE("([❶-❾])(?:.*?)([❶-❾]🔞)","$1领券购买🗳️$2")
,
[🐤🧡下单口令/链接替换文案]
)
=====

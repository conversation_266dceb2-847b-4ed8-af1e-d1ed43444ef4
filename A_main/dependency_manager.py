"""
依赖管理器
处理表格间复杂的字段依赖关系，支持多轮迭代计算
"""

from typing import Dict, List, Set, Tuple, Any, Optional
from enum import Enum
import logging
from dataclasses import dataclass

class FieldStatus(Enum):
    """字段状态枚举"""
    NOT_STARTED = "未开始"
    IN_PROGRESS = "计算中"
    COMPLETED = "已完成"
    FAILED = "计算失败"
    WAITING_DEPS = "等待依赖"

class FieldType(Enum):
    """字段类型枚举"""
    SOURCE = "源信息"
    FIRST_NATIVE = "第一原生字段"
    ASSOCIATED = "关联字段"
    SECOND_NATIVE = "第二原生字段"

@dataclass
class FieldDependency:
    """字段依赖定义"""
    source_table: str
    source_field: str
    target_table: str
    target_field: str
    field_type: FieldType

@dataclass
class FieldInfo:
    """字段信息"""
    table_name: str
    field_name: str
    field_type: FieldType
    dependencies: List[FieldDependency]
    status: FieldStatus = FieldStatus.NOT_STARTED
    value: Any = None
    error_message: str = ""

class DependencyManager:
    """依赖管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger('DependencyManager')
        
        # 字段依赖图
        self.field_dependencies: Dict[str, List[FieldDependency]] = {}
        
        # 字段状态跟踪
        self.field_status: Dict[str, Dict[str, FieldInfo]] = {}
        
        # 计算轮次
        self.current_round = 0
        self.max_rounds = 10  # 最大迭代轮次
        
        # 初始化依赖关系
        self._init_dependencies()
    
    def _init_dependencies(self):
        """初始化字段依赖关系"""
        # 这里定义各表格间的依赖关系
        
        # 📁 信息获取表的依赖
        self._add_dependency(
            "📁 信息获取（基建）", "Ⓜ️🤍‼️品牌（标准格式）",
            "Ⓜ️ 品牌识别（基建）", "Ⓜ️🤍‼️品牌（标准格式）",
            FieldType.ASSOCIATED
        )
        
        self._add_dependency(
            "📁 信息获取（基建）", "💰到手价格（终版）",
            "🧪 规格解构（基建）", "💰到手价格（终版）",
            FieldType.ASSOCIATED
        )
        
        self._add_dependency(
            "📁 信息获取（基建）", "🛍️🤍一级品类（唯一值）",
            "🛍️ 品类管理（基建）", "🛍️🤍😀一级品类（唯一值）",
            FieldType.ASSOCIATED
        )
        
        # 🔪 SKU解构表的依赖
        self._add_dependency(
            "🔪 SKU解构（基建）", "🍬商品信息（原始版）",
            "📁 信息获取（基建）", "🍬商品信息（原始版）",
            FieldType.ASSOCIATED
        )
        
        # 🧪 规格解构表的依赖
        self._add_dependency(
            "🧪 规格解构（基建）", "🔪💚商品名称（信息重组专用版）",
            "🔪 SKU解构（基建）", "🔪💚商品名称（信息重组专用版）",
            FieldType.ASSOCIATED
        )
        
        # 💻 单价计算表的依赖
        self._add_dependency(
            "💻 单价计算（基建）", "🧪🤍规格✖️数量✖️起拍数量（终版）",
            "🧪 规格解构（基建）", "🧪🤍规格✖️数量✖️起拍数量（终版）",
            FieldType.ASSOCIATED
        )
        
        # 🤖 中转表的依赖（依赖多个表）
        self._add_dependency(
            "🤖 中转表（工具）", "🤖🏷️品牌（标准格式）",
            "Ⓜ️ 品牌识别（基建）", "Ⓜ️🤍‼️品牌（标准格式）",
            FieldType.ASSOCIATED
        )
        
        self.logger.info(f"✅ 已初始化 {len(self.field_dependencies)} 个依赖关系")
    
    def _add_dependency(self, target_table: str, target_field: str,
                       source_table: str, source_field: str, field_type: FieldType):
        """添加依赖关系"""
        key = f"{target_table}.{target_field}"
        
        if key not in self.field_dependencies:
            self.field_dependencies[key] = []
        
        dependency = FieldDependency(
            source_table=source_table,
            source_field=source_field,
            target_table=target_table,
            target_field=target_field,
            field_type=field_type
        )
        
        self.field_dependencies[key].append(dependency)
    
    def get_field_dependencies(self, table_name: str, field_name: str) -> List[FieldDependency]:
        """获取字段的依赖列表"""
        key = f"{table_name}.{field_name}"
        return self.field_dependencies.get(key, [])
    
    def is_field_ready(self, table_name: str, field_name: str, record_id: str = "default") -> bool:
        """检查字段是否已就绪"""
        if table_name not in self.field_status:
            return False
        
        field_key = f"{field_name}.{record_id}"
        if field_key not in self.field_status[table_name]:
            return False
        
        field_info = self.field_status[table_name][field_key]
        return field_info.status == FieldStatus.COMPLETED
    
    def can_calculate_field(self, table_name: str, field_name: str, record_id: str = "default") -> Tuple[bool, List[str]]:
        """检查字段是否可以计算（所有依赖都已就绪）"""
        dependencies = self.get_field_dependencies(table_name, field_name)
        missing_deps = []
        
        for dep in dependencies:
            if not self.is_field_ready(dep.source_table, dep.source_field, record_id):
                missing_deps.append(f"{dep.source_table}.{dep.source_field}")
        
        return len(missing_deps) == 0, missing_deps
    
    def set_field_status(self, table_name: str, field_name: str, status: FieldStatus,
                        record_id: str = "default", value: Any = None, error_msg: str = ""):
        """设置字段状态"""
        if table_name not in self.field_status:
            self.field_status[table_name] = {}
        
        field_key = f"{field_name}.{record_id}"
        
        if field_key not in self.field_status[table_name]:
            self.field_status[table_name][field_key] = FieldInfo(
                table_name=table_name,
                field_name=field_name,
                field_type=FieldType.SOURCE,  # 默认值，实际使用时会更新
                dependencies=self.get_field_dependencies(table_name, field_name)
            )
        
        field_info = self.field_status[table_name][field_key]
        field_info.status = status
        field_info.value = value
        field_info.error_message = error_msg
    
    def get_ready_fields_by_type(self, field_type: FieldType) -> List[Tuple[str, str]]:
        """获取指定类型的可计算字段"""
        ready_fields = []
        
        for table_name, fields in self.field_status.items():
            for field_key, field_info in fields.items():
                if (field_info.field_type == field_type and 
                    field_info.status == FieldStatus.NOT_STARTED):
                    
                    field_name = field_info.field_name
                    can_calc, _ = self.can_calculate_field(table_name, field_name)
                    
                    if can_calc:
                        ready_fields.append((table_name, field_name))
        
        return ready_fields
    
    def get_processing_plan(self) -> Dict[int, List[Tuple[str, str]]]:
        """获取分轮次的处理计划"""
        plan = {
            1: [],  # 第一轮：源信息和第一原生字段
            2: [],  # 第二轮：关联字段
            3: [],  # 第三轮：第二原生字段
            4: []   # 第四轮：复杂依赖字段
        }
        
        # 根据字段类型分配到不同轮次
        for table_name, fields in self.field_status.items():
            for field_key, field_info in fields.items():
                field_name = field_info.field_name
                
                if field_info.field_type == FieldType.SOURCE:
                    plan[1].append((table_name, field_name))
                elif field_info.field_type == FieldType.FIRST_NATIVE:
                    plan[1].append((table_name, field_name))
                elif field_info.field_type == FieldType.ASSOCIATED:
                    plan[2].append((table_name, field_name))
                elif field_info.field_type == FieldType.SECOND_NATIVE:
                    plan[3].append((table_name, field_name))
                else:
                    plan[4].append((table_name, field_name))
        
        return plan
    
    def get_status_summary(self) -> Dict[str, Any]:
        """获取状态摘要"""
        summary = {
            "total_fields": 0,
            "completed": 0,
            "in_progress": 0,
            "waiting": 0,
            "failed": 0,
            "by_table": {}
        }
        
        for table_name, fields in self.field_status.items():
            table_summary = {
                "total": len(fields),
                "completed": 0,
                "in_progress": 0,
                "waiting": 0,
                "failed": 0
            }
            
            for field_info in fields.values():
                summary["total_fields"] += 1
                
                if field_info.status == FieldStatus.COMPLETED:
                    summary["completed"] += 1
                    table_summary["completed"] += 1
                elif field_info.status == FieldStatus.IN_PROGRESS:
                    summary["in_progress"] += 1
                    table_summary["in_progress"] += 1
                elif field_info.status == FieldStatus.WAITING_DEPS:
                    summary["waiting"] += 1
                    table_summary["waiting"] += 1
                elif field_info.status == FieldStatus.FAILED:
                    summary["failed"] += 1
                    table_summary["failed"] += 1
            
            summary["by_table"][table_name] = table_summary
        
        return summary

# 测试代码
if __name__ == "__main__":
    manager = DependencyManager()
    
    # 测试依赖检查
    can_calc, missing = manager.can_calculate_field("📁 信息获取（基建）", "Ⓜ️🤍‼️品牌（标准格式）")
    print(f"品牌字段可计算: {can_calc}, 缺失依赖: {missing}")
    
    # 获取处理计划
    plan = manager.get_processing_plan()
    print(f"处理计划: {plan}")
    
    # 获取状态摘要
    summary = manager.get_status_summary()
    print(f"状态摘要: {summary}")

🔪编号（最终）
TEXT([🔪 ｜ ✍️ sku解构(基建)].COUNTIF(CurrentValue.[🔪编号（参考勿删！）]<[🔪编号（参考勿删！）])+1,"👀000000000")
=====
🔪编号（参考勿删！）
自增数字
=====
🍬商品信息（原始版）
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🔪编号（最终）]).[🍬商品信息（原始版）].LISTCOMBINE()
======
🍬商品信息ID
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🔪编号（最终）]).[🍬商品信息ID].LISTCOMBINE()
=====
🧠价格优势-逻辑
IF([🍬商品信息（原始版）].ISBLANK(),""，
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠价格优势-逻辑]!="").[🧠价格优势-逻辑].LISTCOMBINE().UNIQUE())
=====
🧠高阶单位
IF([🍬商品信息（原始版）].ISBLANK(),""，
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠高阶单位]!="").[🧠高阶单位].LISTCOMBINE().UNIQUE())
=====
🧠折算价格-逻辑
IF([🍬商品信息（原始版）].ISBLANK(),""，
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠折算价格-逻辑]!="").[🧠折算价格-逻辑].LISTCOMBINE().UNIQUE())
=====
🧠口味-逻辑
IF([🍬商品信息（原始版）].ISBLANK(),""，
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠口味-逻辑]!="").[🧠口味-逻辑].LISTCOMBINE().UNIQUE())
=====
🧠基础单位
IF([🍬商品信息（原始版）].ISBLANK(),""，
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠基础单位]!="").[🧠基础单位].LISTCOMBINE().UNIQUE())
=====
🧠拍凑份数-逻辑
IF([🍬商品信息（原始版）].ISBLANK(),""，
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠拍凑份数-逻辑]!="").[🧠拍凑份数-逻辑].LISTCOMBINE().UNIQUE())
=====
🧠加法符号
IF([🍬商品信息（原始版）].ISBLANK(),""，
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠加法符号]!="").[🧠加法符号].LISTCOMBINE().UNIQUE())
=====
🧠商品名称-逻辑
IF([🍬商品信息（原始版）].ISBLANK(),""，
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠商品名称-逻辑]!="").[🧠商品名称-逻辑].LISTCOMBINE().UNIQUE())
=====
🧠领券文案-逻辑
IF([🍬商品信息（原始版）].ISBLANK(),""，
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠领券文案-逻辑]!="").[🧠领券文案-逻辑].LISTCOMBINE().UNIQUE())
=====
🧠下单文案-逻辑
IF([🍬商品信息（原始版）].ISBLANK(),""，
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠下单文案-逻辑]!="").[🧠下单文案-逻辑].LISTCOMBINE().UNIQUE())
=====
🧠需转义符号-逻辑
IF([🍬商品信息（原始版）].ISBLANK(),""，
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠需转义符号-逻辑]!="").[🧠需转义符号-逻辑].LISTCOMBINE().UNIQUE())
=====
🧠赠送信息-逻辑
IF([🍬商品信息（原始版）].ISBLANK(),""，
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠赠送信息-逻辑]!="").[🧠赠送信息-逻辑].LISTCOMBINE().UNIQUE())
=====
🧠下单口令/链接-逻辑
IF([🍬商品信息（原始版）].ISBLANK(),""，
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠下单口令/链接-逻辑]!="").[🧠下单口令/链接-逻辑].LISTCOMBINE().UNIQUE())
=====
🧠单位合集
IF([🍬商品信息（原始版）].ISBLANK(),""，
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠单位合集]!="").[🧠单位合集].LISTCOMBINE().UNIQUE())
=====
🧠领券文案干扰信息-逻辑
IF([🍬商品信息（原始版）].ISBLANK(),""，
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠领券文案干扰信息-逻辑]!="").[🧠领券文案干扰信息-逻辑].LISTCOMBINE().UNIQUE())
=====
🧠初级单位
IF([🍬商品信息（原始版）].ISBLANK(),""，
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠初级单位]!="").[🧠初级单位].LISTCOMBINE().UNIQUE())
=====
🧠进阶单位
IF([🍬商品信息（原始版）].ISBLANK(),""，
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠进阶单位]!="").[🧠进阶单位].LISTCOMBINE().UNIQUE())
=====
🧠终极单位
IF([🍬商品信息（原始版）].ISBLANK(),""，
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠终极单位]!="").[🧠终极单位].LISTCOMBINE().UNIQUE())
=====
🧠保质期-逻辑
IF([🍬商品信息（原始版）].ISBLANK(),""，
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠保质期-逻辑]!="").[🧠保质期-逻辑].LISTCOMBINE().UNIQUE())
=====
🧠商品介绍-逻辑
IF([🍬商品信息（原始版）].ISBLANK(),""，
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠商品介绍-逻辑]!="").[🧠商品介绍-逻辑].LISTCOMBINE().UNIQUE())
=====
🧠品牌-逻辑
IF([🍬商品信息（原始版）].ISBLANK(),""，
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠品牌-逻辑]!="").[🧠品牌-逻辑].LISTCOMBINE().UNIQUE())
=====
🧠商品价格-逻辑
IF([🍬商品信息（原始版）].ISBLANK(),""，
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠商品价格-逻辑]!="").[🧠商品价格-逻辑].LISTCOMBINE().UNIQUE())
=====
📁所属平台
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🔪编号（最终）]).[📁所属平台].LISTCOMBINE()
=====
📁💜商品信息（终版分割版）
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🔪编号（最终）]).[📁💜商品信息（终版分割版）].LISTCOMBINE()
=====
📁🩵是否需要补全领券信息
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🔪编号（最终）]).[📁🩵是否需要补全领券信息].LISTCOMBINE()
=====
📁🧡商品信息（优化版）
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🔪编号（最终）]).[📁🧡商品信息（补全版）].LISTCOMBINE()
=====
📁🧡商品信息（补全版分割版）
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🔪编号（最终）]).[📁🧡商品信息（补全版分割版）].LISTCOMBINE()
=====
📁🧡SKU组合
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🔪编号（最终）]).[📁🧡SKU组合]

.REGEXREPLACE([🧠下单口令/链接-逻辑],"🔗")

.REGEXREPLACE(CHAR(10),"")
.REGEXREPLACE("(〰\s*)+",CHAR(10))

.REGEXREPLACE("([❶-❾])(.*)","$1$2$1🔞")
.REGEXREPLACE("(?m)^\s*$\r?\n?","")
=====
📁🤍😀商品信息（纯净分割版）
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🔪编号（最终）]).[📁🤍😀商品信息（纯净分割版） -商品名称&价格专享版].LISTCOMBINE()
=====
📁🤍商品信息（纯净分割版）
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🔪编号（最终）]).[📁🤍商品信息（纯净分割版）].LISTCOMBINE()
=====
📁💜链接/口令数量
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🔪编号（最终）]).[📁🩵链接/口令数量（原始版）].LISTCOMBINE()
=====
📁🧡🖤商品信息组合类型（终版）
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🔪编号（最终）]).[📁🧡🖤商品信息组合类型（终版）].LISTCOMBINE()
=====
📁选品定位符
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🔪编号（最终）]).[📁选品定位符].LISTCOMBINE()
=====
📁源信息新鲜度(天)
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🔪编号（最终）]).[📁源信息新鲜度(天)]
=====
📁信息留存时间（天）
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🔪编号（最终）]).[📁信息留存时间（天）]
=====
💻💚单价（信息重组专用版）
[💻 单价计算（基建）].FILTER(CurrentValue.[💻编号（最终）]=[🔪编号（最终）]).[💻💚单价（信息重组专用版）].LISTCOMBINE()
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
💰🤍😀付款类型
[💰 价格解构（基建）].FILTER(CurrentValue.[💰编号（最终）]=[🔪编号（最终）]).[💰🤍😀付款类型].LISTCOMBINE()
=====
💰🤎赠送到手文案(信息重组专用)
[💰 价格解构（基建）].FILTER(CurrentValue.[💰编号（最终）]=[🔪编号（最终）]).[💰🤎赠送到手文案(信息重组专用)].LISTCOMBINE()
=====
💰🩶规格&价格数量对应关系
[💰 价格解构（基建）].FILTER(CurrentValue.[💰编号（最终）]=[🔪编号（最终）]).[💰🤎赠送到手文案(信息重组专用)].LISTCOMBINE()
=====
🔥💜价格优势（信息提取）（唯一值）
[🔥 价格热度计算（基建）].FILTER(CurrentValue.[🔥编号（最终）]=[🔪编号（最终）]).[🔥💜价格优势（信息提取）（唯一值）]
=====
Ⓜ️🤍品牌（唯一值）
[Ⓜ️ ｜ ✍️ 品牌识别（基建）].FILTER(CurrentValue.[Ⓜ️编号（最终）]=[🔪编号（最终）]).[Ⓜ️🤍品牌（唯一值）].LISTCOMBINE()
=====
🐽💚商品信息（重组版）
[🐽 sku重组-社群专用 (基建)].FILTER(CurrentValue.[🐽编号（最终）]=[🔪编号（最终）]).[🐽💚商品信息（重组版）].LISTCOMBINE()
=====
🐤🧡下单口令/链接替换文案（终版）
[🐤 sku重组-网站专用（基建）].FILTER(CurrentValue.[🐤编号（最终）]=[🔪编号（最终）]).[🐤🧡下单口令/链接替换文案（终版）].LISTCOMBINE()
=====
🌀记录清除/删除判断
IF(
[📁源信息新鲜度(天)]
>=[📁信息留存时间（天）]

,"🆑记录待清理"
，""
)
=====
🔪随机表情-勿删！
[♣︎ emoji表（底库）].FILTER(CurrentValue.[类型].REGEXMATCH("活动")).[Emoji].RANDOMITEM()
=====
🔪🤍😀商品名称（原始版）
IF([🍬商品信息（原始版）].TRIM().ISBLANK(),""，

CONCATENATE(

[📁🤍😀商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(1)

.REGEXEXTRACTALL("(.*?(?:(?:"&[🧠商品名称-逻辑]&")|(?:"&[🧠加法符号]&")).*)")



.ARRAYJOIN(CHAR(10))
.REGEXREPLACE("(.*)"，"❶$0❶🔞")

，CHAR(10)，

[📁🤍😀商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(2)
.REGEXEXTRACTALL("(.*?(?:(?:"&[🧠商品名称-逻辑]&")|(?:"&[🧠加法符号]&")).*)")

.ARRAYJOIN(CHAR(10))
.REGEXREPLACE("(.*)"，"❷$0❷🔞")


，CHAR(10)，


[📁🤍😀商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(3)
.REGEXEXTRACTALL("(.*?(?:(?:"&[🧠商品名称-逻辑]&")|(?:"&[🧠加法符号]&")).*)")
.ARRAYJOIN(CHAR(10))

.REGEXREPLACE("(.*)"，"❸$0❸🔞")


，CHAR(10)，



[📁🤍😀商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(4)
.REGEXEXTRACTALL("(.*?(?:(?:"&[🧠商品名称-逻辑]&")|(?:"&[🧠加法符号]&")).*)")

.ARRAYJOIN(CHAR(10))
.REGEXREPLACE("(.*)"，"❹$0❹🔞")


，CHAR(10)，



[📁🤍😀商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(5)
.REGEXEXTRACTALL("(.*?(?:(?:"&[🧠商品名称-逻辑]&")|(?:"&[🧠加法符号]&")).*)")
.ARRAYJOIN(CHAR(10))
.REGEXREPLACE("(.*)"，"❺$0❺🔞")

，CHAR(10)，


[📁🤍😀商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(6)
.REGEXEXTRACTALL("(.*?(?:(?:"&[🧠商品名称-逻辑]&")|(?:"&[🧠加法符号]&")).*)")

.ARRAYJOIN(CHAR(10))
.REGEXREPLACE("(.*)"，"❻$0❻🔞")



，CHAR(10)，


[📁🤍😀商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(7)
.REGEXEXTRACTALL("(.*?(?:(?:"&[🧠商品名称-逻辑]&")|(?:"&[🧠加法符号]&")).*)")
.ARRAYJOIN(CHAR(10))
.REGEXREPLACE("(.*)"，"❼$0❼🔞")

，CHAR(10)，


[📁🤍😀商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(8)
.REGEXEXTRACTALL("(.*?(?:(?:"&[🧠商品名称-逻辑]&")|(?:"&[🧠加法符号]&")).*)")
.ARRAYJOIN(CHAR(10))
.REGEXREPLACE("(.*)"，"❽$0❽🔞")

，CHAR(10)，


[📁🤍😀商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(9)
.REGEXEXTRACTALL("(.*?(?:(?:"&[🧠商品名称-逻辑]&")|(?:"&[🧠加法符号]&")).*)")
.ARRAYJOIN(CHAR(10))
.REGEXREPLACE("(.*)"，"❾$0❾🔞")


))

.REGEXREPLACE("🔗","")

.REGEXREPLACE("[❶-❾](天猫超市|猫超)[❶-❾]🔞","")
.REGEXREPLACE("\s*[,]"&[🧠品牌-逻辑],"")

.REGEXREPLACE("([❶-❾]"&[🧠品牌-逻辑]&")(?:"&[🔪💜推荐理由-常规优势(唯一值)].REGEXREPLACE("✔️‼️","")&"|[一-十双两]拼|试吃|宠物|出品|驱虫药|猫粮|罐头|狗粮|餐包|犬粮|零食|罐罐|罐头|猫砂|冻干|猫条|鸡肉鳕|高纯|樱花)?[❶-❾]🔞\s*"&CHAR(10)&"[❶-❾]","$1")

.REGEXREPLACE([🧠品牌-逻辑]&"+"，"$1")

.REGEXREPLACE("[\u200B-\u200D\uFE00-\uFE0F\u202A-\u202E\u2060-\u206F]|CHAR(8203)|CHAR(8288)","")
.REGEXREPLACE("[\u2028]", CHAR(10))


.REGEXREPLACE("([❶-❾]🔞)\s*([❶-❾])","$1"&CHAR(10)&"$2")
.REGEXREPLACE("[❶-❾](.*?❷🔞)","❷$1")
.REGEXREPLACE("[❶-❾](.*?❸🔞)","❸$1")
.REGEXREPLACE("[❶-❾](.*?❹🔞)","❹$1")
.REGEXREPLACE("[❶-❾](.*?❺🔞)","❺$1")
.REGEXREPLACE("[❶-❾](.*?❻🔞)","❻$1")
.REGEXREPLACE("[❶-❾](.*?❼🔞)","❼$1")
.REGEXREPLACE("[❶-❾](.*?❽🔞)","❽$1")
.REGEXREPLACE("[❶-❾](.*?❾🔞)","❾$1")

.REGEXREPLACE("(❶❶|❷❷|❸❸|❹❹|❺❺|❻❻|❼❼|❽❽|❾❾)🔞","")



.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🔪🤍😀商品名称（规格类型提取专用版）-参考
IF([🍬商品信息（原始版）].TRIM().ISBLANK(),""，

CONCATENATE(

[📁🤍😀商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(1)
.REGEXEXTRACTALL("(.*?(?:"&[🧠商品名称-逻辑]&").*)")



.ARRAYJOIN(CHAR(10))
.REGEXREPLACE("(.*)"，"❶$0❶🔞")

，CHAR(10)，

[📁🤍😀商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(2)
.REGEXEXTRACTALL("(.*?(?:"&[🧠商品名称-逻辑]&").*)")

.ARRAYJOIN(CHAR(10))
.REGEXREPLACE("(.*)"，"❷$0❷🔞")


，CHAR(10)，


[📁🤍😀商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(3)
.REGEXEXTRACTALL("(.*?(?:"&[🧠商品名称-逻辑]&").*)")
.ARRAYJOIN(CHAR(10))

.REGEXREPLACE("(.*)"，"❸$0❸🔞")


，CHAR(10)，



[📁🤍😀商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(4)
.REGEXEXTRACTALL("(.*?(?:"&[🧠商品名称-逻辑]&").*)")

.ARRAYJOIN(CHAR(10))
.REGEXREPLACE("(.*)"，"❹$0❹🔞")


，CHAR(10)，



[📁🤍😀商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(5)
.REGEXEXTRACTALL("(.*?(?:"&[🧠商品名称-逻辑]&").*)")
.ARRAYJOIN(CHAR(10))
.REGEXREPLACE("(.*)"，"❺$0❺🔞")

，CHAR(10)，


[📁🤍😀商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(6)
.REGEXEXTRACTALL("(.*?(?:"&[🧠商品名称-逻辑]&").*)")

.ARRAYJOIN(CHAR(10))
.REGEXREPLACE("(.*)"，"❻$0❻🔞")



，CHAR(10)，


[📁🤍😀商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(7)
.REGEXEXTRACTALL("(.*?(?:"&[🧠商品名称-逻辑]&").*)")
.ARRAYJOIN(CHAR(10))
.REGEXREPLACE("(.*)"，"❼$0❼🔞")

，CHAR(10)，


[📁🤍😀商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(8)
.REGEXEXTRACTALL("(.*?(?:"&[🧠商品名称-逻辑]&").*)")
.ARRAYJOIN(CHAR(10))
.REGEXREPLACE("(.*)"，"❽$0❽🔞")

，CHAR(10)，


[📁🤍😀商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(9)
.REGEXEXTRACTALL("(.*?(?:"&[🧠商品名称-逻辑]&").*)")
.ARRAYJOIN(CHAR(10))
.REGEXREPLACE("(.*)"，"❾$0❾🔞")


))

.REGEXREPLACE("🔗","")
.REGEXREPLACE("\s*[,]"&[🧠品牌-逻辑],"")

.REGEXREPLACE("([❶-❾]"&[🧠品牌-逻辑]&")(?:"&[🔪💜推荐理由-常规优势(唯一值)].REGEXREPLACE("✔️‼️","")&"|[一-十双两]拼|试吃|宠物|出品|[官方旗舰店]{1,5}|驱虫药|猫粮|罐头|狗粮|餐包|犬粮|零食|罐罐|罐头|猫砂|冻干|猫条|鸡肉鳕|高纯|樱花)?[❶-❾]🔞\s*"&CHAR(10)&"[❶-❾]","$1")

.REGEXREPLACE([🧠品牌-逻辑]&"+"，"$1")

.REGEXREPLACE("[\u200B-\u200D\uFE00-\uFE0F\u202A-\u202E\u2060-\u206F]|CHAR(8203)|CHAR(8288)","")
.REGEXREPLACE("[\u2028]", CHAR(10))


.REGEXREPLACE("([❶-❾]🔞)\s*([❶-❾])","$1"&CHAR(10)&"$2")
.REGEXREPLACE("[❶-❾](.*?❷🔞)","❷$1")
.REGEXREPLACE("[❶-❾](.*?❸🔞)","❸$1")
.REGEXREPLACE("[❶-❾](.*?❹🔞)","❹$1")
.REGEXREPLACE("[❶-❾](.*?❺🔞)","❺$1")
.REGEXREPLACE("[❶-❾](.*?❻🔞)","❻$1")
.REGEXREPLACE("[❶-❾](.*?❼🔞)","❼$1")
.REGEXREPLACE("[❶-❾](.*?❽🔞)","❽$1")
.REGEXREPLACE("[❶-❾](.*?❾🔞)","❾$1")

.REGEXREPLACE("(❶❶|❷❷|❸❸|❹❹|❺❺|❻❻|❼❼|❽❽|❾❾)🔞","")



.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🔪💚商品名称（信息重组专用版）
IF([🍬商品信息（原始版）].TRIM().ISBLANK(),"",

[🔪🤍😀商品名称（原始版）].TRIM()
.REGEXREPLACE(CHAR(10)&"|[🔥‼️✔️]","")

.REGEXREPLACE(
[🔪🤍😀商品价格（原始版）].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(1)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")

.REGEXREPLACE(
[🔪🤍😀商品价格（原始版）].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(2)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")


.REGEXREPLACE(
[🔪🤍😀商品价格（原始版）].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(3)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")


.REGEXREPLACE(
[🔪🤍😀商品价格（原始版）].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(4)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")

.REGEXREPLACE(
[🔪🤍😀商品价格（原始版）].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(5)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")


.REGEXREPLACE(
[🔪🤍😀商品价格（原始版）].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(6)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")


.REGEXREPLACE(
[🔪🤍😀商品价格（原始版）].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(7)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")


.REGEXREPLACE(
[🔪🤍😀商品价格（原始版）].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(8)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")

.REGEXREPLACE(
[🔪🤍😀商品价格（原始版）].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(9)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")


.REGEXREPLACE("🚾🚾🚾🚾🚾🚾🚾","无实际意义，为了看信息更方便")


.REGEXREPLACE(
[🔪🤍😀商品介绍].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(1)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")

.REGEXREPLACE(
[🔪🤍😀商品介绍].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(2)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")


.REGEXREPLACE(
[🔪🤍😀商品介绍].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(3)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")


.REGEXREPLACE(
[🔪🤍😀商品介绍].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(4)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")

.REGEXREPLACE("🚾🚾🚾🚾🚾🚾🚾","无实际意义，为了看信息更方便")



.REGEXREPLACE(
[🔪🧡领券文案].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(1)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")

.REGEXREPLACE(
[🔪🧡领券文案].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(2)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")


.REGEXREPLACE(
[🔪🧡领券文案].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(3)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")


.REGEXREPLACE("🚾🚾🚾🚾🚾🚾🚾","无实际意义，为了看信息更方便")

.REGEXREPLACE(
[🔪🧡下单文案].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(1)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")

.REGEXREPLACE(
[🔪🧡下单文案].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(2)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")

.REGEXREPLACE(
[🔪🧡下单文案].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(3)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")


.REGEXREPLACE(
[🔪🧡下单文案].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(4)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")


.REGEXREPLACE(
[🔪🧡下单文案].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(5)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")


.REGEXREPLACE(
[🔪🧡下单文案].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(6)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")

.REGEXREPLACE(
[🔪🧡下单文案].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(7)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")


.REGEXREPLACE(
[🔪🧡下单文案].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(8)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")


.REGEXREPLACE(
[🔪🧡下单文案].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(9)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")



.REGEXREPLACE("🚾🚾🚾🚾🚾🚾🚾","无实际意义，为了看信息更方便")


.REGEXREPLACE(
[🔪🤍😀拍凑份数(原始版)].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.TRIM()
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(1)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")

.REGEXREPLACE(
[🔪🤍😀拍凑份数(原始版)].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(2)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")


.REGEXREPLACE(
[🔪🤍😀拍凑份数(原始版)].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(3)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")



.REGEXREPLACE(
[🔪🤍😀拍凑份数(原始版)].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(4)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")


.REGEXREPLACE(
[🔪🤍😀拍凑份数(原始版)].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(5)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")


.REGEXREPLACE(
[🔪🤍😀拍凑份数(原始版)].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(6)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")


.REGEXREPLACE(
[🔪🤍😀拍凑份数(原始版)].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(7)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")


.REGEXREPLACE(
[🔪🤍😀拍凑份数(原始版)].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(8)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")


.REGEXREPLACE(
[🔪🤍😀拍凑份数(原始版)].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(9)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")

.REGEXREPLACE("🚾🚾🚾🚾🚾🚾🚾","无实际意义，为了看信息更方便")


.REGEXREPLACE(
[🔪💛推荐理由（合并版）].TRIM()
.REGEXREPLACE("✨.*✨","")
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("(.*?‼️)|(.*?✔️)").NTH(1)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")



.REGEXREPLACE(
[🔪💛推荐理由（合并版）].TRIM()
.REGEXREPLACE("✨.*✨","")
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("(.*?‼️)|(.*?✔️)").NTH(2)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")




.REGEXREPLACE(
[🔪💛推荐理由（合并版）].TRIM()
.REGEXREPLACE("✨.*✨","")
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("(.*?‼️)|(.*?✔️)").NTH(3)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")




.REGEXREPLACE(
[🔪💛推荐理由（合并版）].TRIM()
.REGEXREPLACE("✨.*✨","")
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("(.*?‼️)|(.*?✔️)").NTH(4)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")




.REGEXREPLACE(
[🔪💛推荐理由（合并版）].TRIM()
.REGEXREPLACE("✨.*✨","")
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("(.*?‼️)|(.*?✔️)").NTH(5)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")




.REGEXREPLACE(
[🔪💛推荐理由（合并版）].TRIM()
.REGEXREPLACE("✨.*✨","")
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("(.*?‼️)|(.*?✔️)").NTH(6)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")


.REGEXREPLACE(
[🔪💛推荐理由（合并版）].TRIM()
.REGEXREPLACE("✨.*✨","")
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("(.*?‼️)|(.*?✔️)").NTH(7)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")



.REGEXREPLACE("🚾🚾🚾🚾🚾🚾🚾","无实际意义，为了看信息更方便")


)

.TRIM()
.REGEXREPLACE("º+([❶-❾]🔞)","$1")
.REGEXREPLACE("º","+")

.REGEXREPLACE("\s*[,]"&[🧠品牌-逻辑],"")

.REGEXREPLACE("([❶-❾]"&[🧠品牌-逻辑]&"(?:"&[🔪💜推荐理由-常规优势(唯一值)].REGEXREPLACE("✔️‼️","")&"|试吃|宠物|出品|驱虫药|猫粮|罐头|狗粮|餐包|犬粮|零食|罐罐|罐头|猫砂|冻干|猫条|鸡肉鳕|高纯|樱花)?)[❶-❾]🔞\s*"&CHAR(10)&"[❶-❾]","$1")
.REGEXREPLACE([🧠品牌-逻辑]&"+"，"$1")

.REGEXREPLACE("([❶-❾])[,，。]","$1")
.REGEXREPLACE("[❶-❾][,。，]?\s?[❶-❾]🔞","")

.REGEXREPLACE("❶🔞\s?❶",CHAR(10))
.REGEXREPLACE("❷🔞\s?❷",CHAR(10))
.REGEXREPLACE("❸🔞\s?❸",CHAR(10))
.REGEXREPLACE("❹🔞\s?❹",CHAR(10))
.REGEXREPLACE("❺🔞\s?❺",CHAR(10))
.REGEXREPLACE("❻🔞\s?❻",CHAR(10))
.REGEXREPLACE("❼🔞\s?❼",CHAR(10))
.REGEXREPLACE("❽🔞\s?❽",CHAR(10))
.REGEXREPLACE("❾🔞\s?❾",CHAR(10))


.REGEXREPLACE("❶🔞\s?"&CHAR(10)&"❶",CHAR(10))
.REGEXREPLACE("❷🔞\s?"&CHAR(10)&"❷",CHAR(10))
.REGEXREPLACE("❸🔞\s?"&CHAR(10)&"❸",CHAR(10))
.REGEXREPLACE("❹🔞\s?"&CHAR(10)&"❹",CHAR(10))
.REGEXREPLACE("❺🔞\s?"&CHAR(10)&"❺",CHAR(10))
.REGEXREPLACE("❻🔞\s?"&CHAR(10)&"❻",CHAR(10))
.REGEXREPLACE("❼🔞\s?"&CHAR(10)&"❼",CHAR(10))
.REGEXREPLACE("❽🔞\s?"&CHAR(10)&"❽",CHAR(10))
.REGEXREPLACE("❾🔞\s?"&CHAR(10)&"❾",CHAR(10))


.REGEXREPLACE("([❶-❾])(【】)","$1")
.REGEXEXTRACTALL("[❶-❾][\s\S]*?[❶-❾]🔞")

.ARRAYJOIN(CHAR(10)&"〰️〰️〰️"&CHAR(10))

.REGEXREPLACE("[🐓🍽]","")
.REGEXREPLACE("🔟","10")
.REGEXREPLACE("＊","*")

.REGEXREPLACE("𝑨","A")
.REGEXREPLACE("𝑩","B")
.REGEXREPLACE("𝑪","C")
.REGEXREPLACE("𝑫","D")
.REGEXREPLACE("𝑬","E")
.REGEXREPLACE("𝑭","F")
.REGEXREPLACE("𝑮","G")
.REGEXREPLACE("𝑯","H")
.REGEXREPLACE("𝑰","I")
.REGEXREPLACE("𝑱","J")
.REGEXREPLACE("𝑲","K")
.REGEXREPLACE("𝑳","L")
.REGEXREPLACE("𝑴","M")
.REGEXREPLACE("𝑵","N")
.REGEXREPLACE("𝑶","O")
.REGEXREPLACE("𝑷","P")
.REGEXREPLACE("𝑸","Q")
.REGEXREPLACE("𝑹","R")
.REGEXREPLACE("𝑺","S")
.REGEXREPLACE("𝑻","T")
.REGEXREPLACE("𝑼","U")
.REGEXREPLACE("𝑽","V")
.REGEXREPLACE("𝑾","W")
.REGEXREPLACE("𝑿","X")
.REGEXREPLACE("𝒀","Y")
.REGEXREPLACE("𝒁","Z")

.REGEXREPLACE("星益生趣","SC星益生趣")
.REGEXREPLACE("喵彩","Cator喵彩")
.REGEXREPLACE("名创优品","MINISO名创优品")
.REGEXREPLACE("顽皮","Wanpy顽皮")
.REGEXREPLACE("泡咔","PAWKA泡咔")
.REGEXREPLACE("爱肯拿","ACANA爱肯拿")
.REGEXREPLACE("自然光环","Halo自然光环")
.REGEXREPLACE("法米娜","Farmina法米娜")
.REGEXREPLACE("美士","Nutro美士")
.REGEXREPLACE("fiVEboy","FiveBoy")
.REGEXREPLACE("VEtwish","Vetwish")
.REGEXREPLACE("Hound&Gatos","HG")
.REGEXREPLACE("(礼蓝动保|海正动保|分别)","")

.REGEXREPLACE("^([❶-❾])[,，+➕～!！\s]+","$1")
.REGEXREPLACE("[,，+➕～!！\s]+$([❶-❾])", "$1")

.REGEXREPLACE("\\("&[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠需转义符号-逻辑]!="").[🧠需转义符号-逻辑]&")","$1")


.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🔪🖤商品名称（字段维护专用）
IF(
OR(
[🍬商品信息（原始版）]
.TRIM().ISBLANK()
，

[📁选品定位符]
.REGEXMATCH("🛒")
.NOT()
）
,""，
IF(
AND(
[🔪💚商品名称（信息重组专用版）]
.ISBLANK()
,
[🔪🤍😀商品名称（原始版）]
.ISBLANK()
),

[🔪💚赠送信息（信息重组专用版）]
，
IF(
[🔪💚商品名称（信息重组专用版）]
.ISBLANK()
,
[🔪🤍😀商品名称（原始版）]
，
[🔪💚商品名称（信息重组专用版）]
)))
.TRIM()

.REGEXREPLACE([🔪🤍😀赠送信息（原始版）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞").NTH(1).TRIM(),"")


.REGEXREPLACE([🔪🤍😀赠送信息（原始版）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞").NTH(2),"")

.REGEXREPLACE([🔪🤍😀赠送信息（原始版）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞").NTH(3),"")


.REGEXREPLACE([🔪🤍😀赠送信息（原始版）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞").NTH(4),"")


.REGEXREPLACE([🔪🤍😀赠送信息（原始版）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞").NTH(5),"")



.REGEXREPLACE([🔪🤍😀赠送信息（原始版）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞").NTH(6),"")



.REGEXREPLACE([🔪🤍😀赠送信息（原始版）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞").NTH(7),"")



.REGEXREPLACE([🔪🤍😀赠送信息（原始版）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞").NTH(8),"")



.REGEXREPLACE([🔪🤍😀商品介绍]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞").NTH(1),"")


.REGEXREPLACE([🔪🤍😀商品介绍]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞").NTH(2),"")


.REGEXREPLACE([🔪🤍😀商品介绍]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞").NTH(3),"")

.REGEXREPLACE([🔪🤍😀商品介绍]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞").NTH(4),"")

.REGEXREPLACE([🔪🤍😀商品介绍]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞").NTH(5),"")



.REGEXREPLACE([🔪🤍😀商品介绍]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞").NTH(6),"")


.REGEXREPLACE([🔪🤍😀商品介绍]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞").NTH(7),"")



.REGEXREPLACE([🔪🤍😀商品介绍]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞").NTH(8),"")


.REGEXREPLACE([🔪💜拼团]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("【(.*)】"),"")


.REGEXREPLACE(
[🔪💜推荐理由-对标情况+时间紧迫性（唯一值）]
.REGEXEXTRACTALL(".*?‼️").NTH(1)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]","")
.REGEXREPLACE("[+*]","\$0"),"")


.REGEXREPLACE(
[🔪💜推荐理由-对标情况+时间紧迫性（唯一值）]
.REGEXEXTRACTALL(".*?‼️").NTH(2)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]","")
.REGEXREPLACE("[+*]","\$0"),"")

.REGEXREPLACE(
[🔪💜推荐理由-对标情况+时间紧迫性（唯一值）]
.REGEXEXTRACTALL(".*?‼️").NTH(3)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]","")
.REGEXREPLACE("[+*]","\$0"),"")

.REGEXREPLACE(
[🔪💜推荐理由-对标情况+时间紧迫性（唯一值）]
.REGEXEXTRACTALL(".*?‼️").NTH(4)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]","")
.REGEXREPLACE("[+*]","\$0"),"")

.REGEXREPLACE(
[🔪💜推荐理由-对标情况+时间紧迫性（唯一值）]
.REGEXEXTRACTALL(".*?‼️").NTH(5)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]","")
.REGEXREPLACE("[+*]","\$0"),"")


.REGEXREPLACE("㊙️㊙️㊙️㊙️","清除赠送相关信息")
.REGEXREPLACE("送一","送1️⃣")
.REGEXREPLACE("叠💰?\d+","")
.REGEXREPLACE("(?:猫粮|罐头|狗粮|餐包|犬粮|零食|罐罐|猫砂|冻干|猫条)?[88VIPviplusPLUS首单叠福袋部分人金币相当于凑单到手折算一起付]{0,8}💰\d+(?:\.\d+)?","$1")
.REGEXREPLACE("[,，。；].*?券","")
.REGEXREPLACE("送1️⃣","送一")

.REGEXREPLACE("\+?[赠送]{1,2}(?:[\s\S]*?\+)*.*?([❶-❾]🔞)","$1")
.REGEXREPLACE("[❶-❾](送一|赠品|[+➕＋])\s?[❶-❾]🔞","")
.REGEXREPLACE(CHAR(10)&"([+➕＋]|[❶-❾]🔞)","$1")
.REGEXREPLACE("[,，。；;]\s?([❶-❾]🔞)","$1")
.REGEXREPLACE("([❶-❾])[+➕＋,，；。]","$1")

.REGEXREPLACE("㊙️㊙️㊙️㊙️","清除规格前面的多余空格")

.REGEXREPLACE(" (\d+(?:\.\d+)?(?:"&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠终极单位]&").*?[❶]🔞)","$1")


.REGEXREPLACE("㊙️㊙️㊙️㊙️","定位符显示优化")

.REGEXREPLACE("❶[\s\S]*?❶🔞",[🔪💜临期情况].REGEXEXTRACT("❶.*?❶🔞")&"$0")
.REGEXREPLACE("❷[\s\S]*?❷🔞",[🔪💜临期情况].REGEXEXTRACT("❷.*?❷🔞")&"$0")
.REGEXREPLACE("❸[\s\S]*?❸🔞",[🔪💜临期情况].REGEXEXTRACT("❸.*?❸🔞")&"$0")
.REGEXREPLACE("❹[\s\S]*?❹🔞",[🔪💜临期情况].REGEXEXTRACT("❹.*?❹🔞")&"$0")
.REGEXREPLACE("❺[\s\S]*?❺🔞",[🔪💜临期情况].REGEXEXTRACT("❺.*?❺🔞")&"$0")
.REGEXREPLACE("❻[\s\S]*?❻🔞",[🔪💜临期情况].REGEXEXTRACT("❻.*?❻🔞")&"$0")
.REGEXREPLACE("❼[\s\S]*?❼🔞",[🔪💜临期情况].REGEXEXTRACT("❼.*?❼🔞")&"$0")
.REGEXREPLACE("❽[\s\S]*?❽🔞",[🔪💜临期情况].REGEXEXTRACT("❽.*?❽🔞")&"$0")
.REGEXREPLACE("❾[\s\S]*?❾🔞",[🔪💜临期情况].REGEXEXTRACT("❾.*?❾🔞")&"$0")


.REGEXREPLACE("❶[\s\S]*?❶🔞",[🔪💜拼团].REGEXEXTRACT("❶.*?❶🔞")&"$0")
.REGEXREPLACE("❷[\s\S]*?❷🔞",[🔪💜拼团].REGEXEXTRACT("❷.*?❷🔞")&"$0")
.REGEXREPLACE("❸[\s\S]*?❸🔞",[🔪💜拼团].REGEXEXTRACT("❸.*?❸🔞")&"$0")
.REGEXREPLACE("❹[\s\S]*?❹🔞",[🔪💜拼团].REGEXEXTRACT("❹.*?❹🔞")&"$0")
.REGEXREPLACE("❺[\s\S]*?❺🔞",[🔪💜拼团].REGEXEXTRACT("❺.*?❺🔞")&"$0")
.REGEXREPLACE("❻[\s\S]*?❻🔞",[🔪💜拼团].REGEXEXTRACT("❻.*?❻🔞")&"$0")
.REGEXREPLACE("❼[\s\S]*?❼🔞",[🔪💜拼团].REGEXEXTRACT("❼.*?❼🔞")&"$0")
.REGEXREPLACE("❽[\s\S]*?❽🔞",[🔪💜拼团].REGEXEXTRACT("❽.*?❽🔞")&"$0")
.REGEXREPLACE("❾[\s\S]*?❾🔞",[🔪💜拼团].REGEXEXTRACT("❾.*?❾🔞")&"$0")

.REGEXREPLACE("❶🔞❶","")
.REGEXREPLACE("❷🔞❷","")
.REGEXREPLACE("❸🔞❸","")
.REGEXREPLACE("❹🔞❹","")
.REGEXREPLACE("❺🔞❺","")
.REGEXREPLACE("❻🔞❻","")
.REGEXREPLACE("❼🔞❼","")
.REGEXREPLACE("❽🔞❽","")
.REGEXREPLACE("❾🔞❾","")
.REGEXREPLACE("[❶-❾]\s*[❶-❾]🔞","")

.REGEXREPLACE("([❶-❾]🔞)","$0"&CHAR(10))
.REGEXREPLACE("(〰️\s*)+",CHAR(10)&"$0"&CHAR(10))


.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🔪🤍😀赠送信息（原始版）
CONCATENATE(

[📁🤍😀商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(1)
.REGEXEXTRACTALL([🧠赠送信息-逻辑])
.ARRAYJOIN("
")
.REGEXREPLACE(".*","❶$0❶🔞")
.REGEXREPLACE(CHAR(10),"❶🔞"&CHAR(10)&"❶")



,CHAR(10)，

[📁🤍😀商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(2)
.REGEXEXTRACTALL([🧠赠送信息-逻辑])
.ARRAYJOIN("
")
.REGEXREPLACE(".*","❷$0❷🔞")
.REGEXREPLACE(CHAR(10),"❷🔞"&CHAR(10)&"❷")


,CHAR(10)，


[📁🤍😀商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(3)
.REGEXEXTRACTALL([🧠赠送信息-逻辑])
.ARRAYJOIN("
")
.REGEXREPLACE(".*","❸$0❸🔞")
.REGEXREPLACE(CHAR(10),"❸🔞"&CHAR(10)&"❸")


,CHAR(10)，

[📁🤍😀商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(4)
.REGEXEXTRACTALL([🧠赠送信息-逻辑])
.ARRAYJOIN("
")
.REGEXREPLACE(".*","❹$0❹🔞")
.REGEXREPLACE(CHAR(10),"❹🔞"&CHAR(10)&"❹")


,CHAR(10)，

[📁🤍😀商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(5)
.REGEXEXTRACTALL([🧠赠送信息-逻辑])
.ARRAYJOIN("
")
.REGEXREPLACE(".*","❺$0❺🔞")
.REGEXREPLACE(CHAR(10),"❺🔞"&CHAR(10)&"❺")


,CHAR(10)，


[📁🤍😀商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(6)
.REGEXEXTRACTALL([🧠赠送信息-逻辑])
.ARRAYJOIN("
")
.REGEXREPLACE(".*"，"❻$0❻🔞")
.REGEXREPLACE(CHAR(10),"❻🔞"&CHAR(10)&"❻")


,CHAR(10),

[📁🤍😀商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(7)
.REGEXEXTRACTALL([🧠赠送信息-逻辑])
.ARRAYJOIN("
")
.REGEXREPLACE(".*","❼$0❼🔞")
.REGEXREPLACE(CHAR(10),"❼🔞"&CHAR(10)&"❼")

,CHAR(10),

[📁🤍😀商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(8)
.REGEXEXTRACTALL([🧠赠送信息-逻辑])
.ARRAYJOIN("
")
.REGEXREPLACE(".*","❽$0❽🔞")
.REGEXREPLACE(CHAR(10),"❽🔞"&CHAR(10)&"❽")

,CHAR(10),

[📁🤍😀商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(9)
.REGEXEXTRACTALL([🧠赠送信息-逻辑])
.ARRAYJOIN("
")
.REGEXREPLACE(".*","❾$0❾🔞")
.REGEXREPLACE(CHAR(10),"❾🔞"&CHAR(10)&"❾")


)
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").UNIQUE().ARRAYJOIN(CHAR(10))

.REGEXREPLACE("([❶-❾]🔞)+","$1")
.REGEXREPLACE("([❶-❾])+","$1")
.REGEXREPLACE("⚛️","")
.REGEXREPLACE("([❶-❾]).*?送货上门|送达|送到家.*","$1")
.REGEXREPLACE(".*?配送.*","")

.REGEXREPLACE("(.*?\d+(?:\.\d+)?(%|％).*)"，"")


.REGEXREPLACE("^[❶-❾]*🔞","")
.REGEXREPLACE("
[❶-❾]*🔞","")
.REGEXREPLACE("[,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🔪💚赠送信息（信息重组专用版）
IF([🍬商品信息（原始版）].TRIM().ISBLANK(),""，

CONCATENATE(

IF(
OR(
[🔪🤍😀商品价格（原始版）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXREPLACE("[❶-❾🔞🔥‼️✔️]","").TRIM()

.CONTAINTEXT(
[🔪🤍😀赠送信息（原始版）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(1)
.REGEXREPLACE("[❶-❾🔞🔥‼️✔️]","").TRIM()
)

,

[🔪🤍😀商品名称（原始版）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXREPLACE("[❶-❾🔞🔥‼️✔️]","").TRIM()

.CONTAINTEXT(
[🔪🤍😀赠送信息（原始版）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(1)
.REGEXREPLACE("[❶-❾🔞🔥‼️✔️]","").TRIM()
)

,

[🔪🤍😀拍凑份数(原始版)]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXREPLACE("[❶-❾🔞🔥‼️✔️]","").TRIM()

.CONTAINTEXT(
[🔪🤍😀赠送信息（原始版）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(1)
.REGEXREPLACE("[❶-❾🔞🔥‼️✔️]","").TRIM()
)

,

[🔪💚商品介绍 （信息重组专用版）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXREPLACE("[❶-❾🔞🔥‼️✔️]","").TRIM()

.CONTAINTEXT(
[🔪🤍😀赠送信息（原始版）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(1)
.REGEXREPLACE("[❶-❾🔞🔥‼️✔️]","").TRIM()
)


)
,

[🔪🤍😀赠送信息（原始版）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(1)
.REGEXREPLACE(".*","")

，

[🔪🤍😀赠送信息（原始版）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(1))




,CHAR(10),



IF(
OR(
[🔪🤍😀商品价格（原始版）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXREPLACE("[❶-❾🔞🔥‼️✔️]","").TRIM()

.CONTAINTEXT(
[🔪🤍😀赠送信息（原始版）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(2)
.REGEXREPLACE("[❶-❾🔞🔥‼️✔️]","").TRIM()
),

[🔪🤍😀商品名称（原始版）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXREPLACE("[❶-❾🔞🔥‼️✔️]","").TRIM()

.CONTAINTEXT(
[🔪🤍😀赠送信息（原始版）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(2)
.REGEXREPLACE("[❶-❾🔞🔥‼️✔️]","").TRIM()
),

[🔪🤍😀拍凑份数(原始版)].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXREPLACE("[❶-❾🔞🔥‼️✔️]","").TRIM()

.CONTAINTEXT(
[🔪🤍😀赠送信息（原始版）].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(2)
.REGEXREPLACE("[❶-❾🔞🔥‼️✔️]","").TRIM()
),

[🔪💚商品介绍 （信息重组专用版）].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXREPLACE("[❶-❾🔞🔥‼️✔️]","").TRIM()

.CONTAINTEXT(
[🔪🤍😀赠送信息（原始版）].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(2)
.REGEXREPLACE("[❶-❾🔞🔥‼️✔️]","").TRIM()
)

),
[🔪🤍😀赠送信息（原始版）].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(2)
.REGEXREPLACE(".*","")

,
[🔪🤍😀赠送信息（原始版）].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(2)
)



,CHAR(10),

IF(
OR(
[🔪🤍😀商品价格（原始版）].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXREPLACE("[❶-❾🔞🔥‼️✔️]","").TRIM()

.CONTAINTEXT(
[🔪🤍😀赠送信息（原始版）].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(3)
.REGEXREPLACE("[❶-❾🔞🔥‼️✔️]","").TRIM()
),

[🔪🤍😀商品名称（原始版）].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXREPLACE("[❶-❾🔞🔥‼️✔️]","").TRIM()

.CONTAINTEXT(
[🔪🤍😀赠送信息（原始版）].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(3)
.REGEXREPLACE("[❶-❾🔞🔥‼️✔️]","").TRIM()
),

[🔪🤍😀拍凑份数(原始版)].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXREPLACE("[❶-❾🔞🔥‼️✔️]","").TRIM()

.CONTAINTEXT(
[🔪🤍😀赠送信息（原始版）].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(3)
.REGEXREPLACE("[❶-❾🔞🔥‼️✔️]","").TRIM()
),

[🔪💚商品介绍 （信息重组专用版）].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXREPLACE("[❶-❾🔞🔥‼️✔️]","").TRIM()

.CONTAINTEXT(
[🔪🤍😀赠送信息（原始版）].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(3)
.REGEXREPLACE("[❶-❾🔞🔥‼️✔️]","").TRIM()
)

),
[🔪🤍😀赠送信息（原始版）].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(3)
.REGEXREPLACE(".*","")

,
[🔪🤍😀赠送信息（原始版）].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(3)
)


,CHAR(10),

IF(
OR(
[🔪🤍😀商品价格（原始版）].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXREPLACE("[❶-❾🔞🔥‼️✔️]","").TRIM()

.CONTAINTEXT(
[🔪🤍😀赠送信息（原始版）].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(4)
.REGEXREPLACE("[❶-❾🔞🔥‼️✔️]","").TRIM()
),

[🔪🤍😀商品名称（原始版）].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXREPLACE("[❶-❾🔞🔥‼️✔️]","").TRIM()

.CONTAINTEXT(
[🔪🤍😀赠送信息（原始版）].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(4)
.REGEXREPLACE("[❶-❾🔞🔥‼️✔️]","").TRIM()
),

[🔪🤍😀拍凑份数(原始版)].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXREPLACE("[❶-❾🔞🔥‼️✔️]","").TRIM()

.CONTAINTEXT(
[🔪🤍😀赠送信息（原始版）].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(4)
.REGEXREPLACE("[❶-❾🔞🔥‼️✔️]","").TRIM()
),

[🔪💚商品介绍 （信息重组专用版）].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXREPLACE("[❶-❾🔞🔥‼️✔️]","").TRIM()

.CONTAINTEXT(
[🔪🤍😀赠送信息（原始版）].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(4)
.REGEXREPLACE("[❶-❾🔞🔥‼️✔️]","").TRIM()
)

),
[🔪🤍😀赠送信息（原始版）].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(4)
.REGEXREPLACE(".*","")

,
[🔪🤍😀赠送信息（原始版）].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(4)
)






))
.REGEXREPLACE("\\([+*])","$1")
.REGEXREPLACE("🚾🚾🚾🚾🚾🚾🚾","无实际意义，为了看信息更方便")



.REGEXREPLACE(
[🔪💛推荐理由（合并版）].TRIM()
.REGEXREPLACE("✨.*✨","")
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("(.*?‼️)|(.*?✔️)").NTH(1)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")


.REGEXREPLACE(
[🔪💛推荐理由（合并版）].TRIM()
.REGEXREPLACE("✨.*✨","")
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("(.*?‼️)|(.*?✔️)").NTH(2)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")



.REGEXREPLACE(
[🔪💛推荐理由（合并版）].TRIM()
.REGEXREPLACE("✨.*✨","")
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("(.*?‼️)|(.*?✔️)").NTH(3)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")


.REGEXREPLACE(
[🔪💛推荐理由（合并版）].TRIM()
.REGEXREPLACE("✨.*✨","")
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("(.*?‼️)|(.*?✔️)").NTH(4)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")


.REGEXREPLACE("🚾🚾🚾🚾🚾🚾🚾","无实际意义，为了看信息更方便")



.TRIM()
.REGEXREPLACE("º+([❶-❾]🔞)","$1")
.REGEXREPLACE("º","+")


.REGEXREPLACE([🧠品牌-逻辑]&"+"，"$1")
.REGEXREPLACE("\\([+*])","$1")


.REGEXREPLACE("([❶-❾])[,，。]","$1")
.REGEXREPLACE("[❶-❾][,，。]?[❶-❾]🔞","")

.REGEXREPLACE("❶🔞\s?❶",CHAR(10))
.REGEXREPLACE("❷🔞\s?❷",CHAR(10))
.REGEXREPLACE("❸🔞\s?❸",CHAR(10))
.REGEXREPLACE("❹🔞\s?❹",CHAR(10))
.REGEXREPLACE("❺🔞\s?❺",CHAR(10))
.REGEXREPLACE("❻🔞\s?❻",CHAR(10))
.REGEXREPLACE("❼🔞\s?❼",CHAR(10))
.REGEXREPLACE("❽🔞\s?❽",CHAR(10))
.REGEXREPLACE("❾🔞\s?❾",CHAR(10))

.REGEXREPLACE("❶🔞\s?"&CHAR(10)&"❶",CHAR(10))
.REGEXREPLACE("❷🔞\s?"&CHAR(10)&"❷",CHAR(10))
.REGEXREPLACE("❸🔞\s?"&CHAR(10)&"❸",CHAR(10))
.REGEXREPLACE("❹🔞\s?"&CHAR(10)&"❹",CHAR(10))
.REGEXREPLACE("❺🔞\s?"&CHAR(10)&"❺",CHAR(10))
.REGEXREPLACE("❻🔞\s?"&CHAR(10)&"❻",CHAR(10))
.REGEXREPLACE("❼🔞\s?"&CHAR(10)&"❼",CHAR(10))
.REGEXREPLACE("❽🔞\s?"&CHAR(10)&"❽",CHAR(10))
.REGEXREPLACE("❾🔞\s?"&CHAR(10)&"❾",CHAR(10))

.REGEXREPLACE("❶🔞❶","")
.REGEXREPLACE("❷🔞❷","")
.REGEXREPLACE("❸🔞❸","")
.REGEXREPLACE("❹🔞❹","")
.REGEXREPLACE("❺🔞❺","")
.REGEXREPLACE("❻🔞❻","")
.REGEXREPLACE("❼🔞❼","")
.REGEXREPLACE("❽🔞❽","")
.REGEXREPLACE("❾🔞❾","")



.REGEXEXTRACTALL("[❶-❾][\s\S]*?[❶-❾]🔞")
.ARRAYJOIN(CHAR(10)&"〰️〰️〰️"&CHAR(10))

.REGEXREPLACE("\\("&[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠需转义符号-逻辑]!="").[🧠需转义符号-逻辑]&")","$1")

.REGEXREPLACE("[,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🔪🖤赠送信息（字段维护专用）
IF(
OR(
[🍬商品信息（原始版）]
.TRIM().ISBLANK()
，

[📁选品定位符]
.REGEXMATCH("🎫|📦")
）
，""，
[🔪🤍😀赠送信息（原始版）]）


.REGEXREPLACE("㊙️㊙️㊙️㊙️㊙️","排除保质期、商品名称、商品介绍相关的信息")

.REGEXREPLACE([🧠保质期-逻辑]，"")
.REGEXREPLACE(CHAR(10)&"|[🔥‼️✔️]","")

.REGEXREPLACE(
[🔪🖤商品名称（字段维护专用）].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(1)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")

.REGEXREPLACE(
[🔪🖤商品名称（字段维护专用）].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(2)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")


.REGEXREPLACE(
[🔪🖤商品名称（字段维护专用）].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(3)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")


.REGEXREPLACE(
[🔪🖤商品名称（字段维护专用）].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(4)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")

.REGEXREPLACE(
[🔪🖤商品名称（字段维护专用）].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(5)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")


.REGEXREPLACE(
[🔪🖤商品名称（字段维护专用）].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(6)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")


.REGEXREPLACE(
[🔪🖤商品名称（字段维护专用）].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(7)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")


.REGEXREPLACE(
[🔪🖤商品名称（字段维护专用）].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(8)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")

.REGEXREPLACE(
[🔪🖤商品名称（字段维护专用）].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(9)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")


.REGEXREPLACE("🚾🚾🚾🚾🚾🚾🚾","无实际意义，为了看信息更方便")




.REGEXREPLACE(
[🔪🤍😀商品介绍].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(1)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")

.REGEXREPLACE(
[🔪🤍😀商品介绍].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(2)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")


.REGEXREPLACE(
[🔪🤍😀商品介绍].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(3)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")


.REGEXREPLACE(
[🔪🤍😀商品介绍].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(4)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")

.REGEXREPLACE(
[🔪🤍😀商品介绍].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(5)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")


.REGEXREPLACE(
[🔪🤍😀商品介绍].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(6)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")


.REGEXREPLACE(
[🔪🤍😀商品介绍].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(7)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")


.REGEXREPLACE(
[🔪🤍😀商品介绍].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(8)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")

.REGEXREPLACE(
[🔪🤍😀商品介绍].TRIM()
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(9)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")


.REGEXREPLACE("🚾🚾🚾🚾🚾🚾🚾","无实际意义，为了看信息更方便")



.TRIM()
.REGEXREPLACE("º+([❶-❾]🔞)","$1")
.REGEXREPLACE("º","+")


.REGEXREPLACE("叠💰?\d+","")
.REGEXREPLACE("(?:猫粮|罐头|狗粮|餐包|犬粮|零食|罐罐|猫砂|冻干|猫条)?[88VIPviplusPLUS首单叠福袋部分人金币相当于凑单到手折算一起付]{0,8}💰\d+(?:\.\d+)?","")
.REGEXREPLACE("[,，。；;]?(赠品)?(💰|价值)\d+(?:\.\d+)?","")
.REGEXREPLACE("[,，。；;].*?券","")
.REGEXREPLACE("[,，。；;]折.*?\d+(?:\.\d+)?\/("&[🧠单位合集]&")","")


.REGEXREPLACE(".*?送货上门|送达.*","$1")
.REGEXREPLACE(".*?配送.*","")

.REGEXREPLACE("[,，。；;]*\s?([❶-❻]🔞)","$1")
.REGEXREPLACE("[+﹢🞡🞢＋🞣🞤🞥🞦🞧➕]","+")

.REGEXREPLACE("❶🔞"&CHAR(10)&"❶"," ")
.REGEXREPLACE("❷🔞"&CHAR(10)&"❷"," ")
.REGEXREPLACE("❸🔞"&CHAR(10)&"❸"," ")
.REGEXREPLACE("❹🔞"&CHAR(10)&"❹"," ")
.REGEXREPLACE("❺🔞"&CHAR(10)&"❺"," ")
.REGEXREPLACE("❻🔞"&CHAR(10)&"❻"," ")
.REGEXREPLACE("❼🔞"&CHAR(10)&"❼"," ")
.REGEXREPLACE("❽🔞"&CHAR(10)&"❽"," ")
.REGEXREPLACE("❾🔞"&CHAR(10)&"❾"," ")

.REGEXREPLACE("❶🔞❶","")
.REGEXREPLACE("❷🔞❷","")
.REGEXREPLACE("❸🔞❸","")
.REGEXREPLACE("❹🔞❹","")
.REGEXREPLACE("❺🔞❺","")
.REGEXREPLACE("❻🔞❻","")
.REGEXREPLACE("❼🔞❼","")
.REGEXREPLACE("❽🔞❽","")
.REGEXREPLACE("❾🔞❾","")


.REGEXREPLACE("([❶-❾])+","$1")
.REGEXREPLACE("([❶-❾])\+","$1送")
.REGEXREPLACE("([❶-❾])(试吃)","$1送$2")
.REGEXREPLACE("(赠|送)+","$1")
.REGEXREPLACE(" +","+")
.REGEXREPLACE("([❶-❾])([，,。;]|\d+(?:\.\d+)?(?:"&[🧠单位合集]&")?)","$1")
.REGEXREPLACE("[❶-❾](赠送?|送?试吃)[❶-❾]🔞","")
.REGEXEXTRACTALL("[❶-❾][\s\S]*?[❶-❾]🔞")

.ARRAYJOIN(CHAR(10)&"〰️〰️〰️"&CHAR(10))



.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
======
🔪🤍😀商品价格（原始版）
CONCATENATE(
[📁🤍😀商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(1)
.REGEXEXTRACTALL([🧠商品价格-逻辑])
.ARRAYJOIN("🔥"&CHAR(10))
.REGEXREPLACE("(.*)"，"❶$0❶🔞")

，CHAR(10)，


[📁🤍😀商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(2)
.REGEXEXTRACTALL([🧠商品价格-逻辑])
.ARRAYJOIN("🔥"&CHAR(10))
.REGEXREPLACE("(.*)"，"❷$0❷🔞")

，CHAR(10)，



[📁🤍😀商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(3)
.REGEXEXTRACTALL([🧠商品价格-逻辑])
.ARRAYJOIN("🔥"&CHAR(10))
.REGEXREPLACE("(.*)"，"❸$0❸🔞")

，CHAR(10)，

[📁🤍😀商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(4)
.REGEXEXTRACTALL([🧠商品价格-逻辑])
.ARRAYJOIN("🔥"&CHAR(10))
.REGEXREPLACE("(.*)"，"❹$0❹🔞")

，CHAR(10)，


[📁🤍😀商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(5)
.REGEXEXTRACTALL([🧠商品价格-逻辑])
.ARRAYJOIN("🔥"&CHAR(10))
.REGEXREPLACE("(.*)"，"❺$0❺🔞")

，CHAR(10)，

[📁🤍😀商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(6)
.REGEXEXTRACTALL([🧠商品价格-逻辑])
.ARRAYJOIN("🔥"&CHAR(10))
.REGEXREPLACE("(.*)"，"❻$0❻🔞")

，CHAR(10)，

[📁🤍😀商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(7)
.REGEXEXTRACTALL([🧠商品价格-逻辑])
.ARRAYJOIN("🔥"&CHAR(10))
.REGEXREPLACE("(.*)"，"❼$0❼🔞")

，CHAR(10)，

[📁🤍😀商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(8)
.REGEXEXTRACTALL([🧠商品价格-逻辑])
.ARRAYJOIN("🔥"&CHAR(10))
.REGEXREPLACE("(.*)"，"❽$0❽🔞")


，CHAR(10)，

[📁🤍😀商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(9)
.REGEXEXTRACTALL([🧠商品价格-逻辑])
.ARRAYJOIN("🔥"&CHAR(10))
.REGEXREPLACE("(.*)"，"❾$0❾🔞")


)

.REGEXREPLACE(" ","，")
.REGEXREPLACE("(❶❶|❷❷|❸❸|❹❹|❺❺|❻❻|❼❼|❽❽|❾❾)🔞","")
.REGEXREPLACE("[,，+➕～=🟰]([❶-❾]🔞)","$1")
.REGEXREPLACE("\.0+([❶-❾]🔞)","$1")
.REGEXREPLACE("[,，+➕～=🟰]([❶-❾]🔞)","$1")

.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🔪💚商品价格（信息重组专用版）
[🔪🤍😀商品价格（原始版）].TRIM()
.REGEXREPLACE("🔥[❶-❾]🔞"&CHAR(10)&"[❶-❾](淘|叠|有|凑)?(88[Vv][ii][Pp]|[Pp][Ll][Uu][Ss]|💰|折算|相当于|晒图|实际到手|到手|[没有]{0,2}学生号|.*?价值|部分(?:人|金币|礼金)?|[新老]客(?:回购)?|尾款|首单|金币|单|消费券|福袋|.*?[好评返现卡送]{1,4}|＝)","，$1$2")
.REGEXREPLACE("🔥","")



.REGEXREPLACE("[\\']","")



.REGEXREPLACE(
[🔪🤍😀商品介绍].TRIM()
.REGEXREPLACE("✨.*✨","")
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("(.*?‼️)|(.*?✔️)").NTH(1)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")



.REGEXREPLACE(
[🔪🤍😀商品介绍].TRIM()
.REGEXREPLACE("✨.*✨","")
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("(.*?‼️)|(.*?✔️)").NTH(2)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")



.REGEXREPLACE(
[🔪🤍😀商品介绍].TRIM()
.REGEXREPLACE("✨.*✨","")
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("(.*?‼️)|(.*?✔️)").NTH(3)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")




.REGEXREPLACE(
[🔪🤍😀商品介绍].TRIM()
.REGEXREPLACE("✨.*✨","")
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("(.*?‼️)|(.*?✔️)").NTH(4)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")



.REGEXREPLACE(
[🔪🤍😀商品介绍].TRIM()
.REGEXREPLACE("✨.*✨","")
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("(.*?‼️)|(.*?✔️)").NTH(5)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")



.REGEXREPLACE(
[🔪🤍😀商品介绍].TRIM()
.REGEXREPLACE("✨.*✨","")
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("(.*?‼️)|(.*?✔️)").NTH(6)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")


.REGEXREPLACE("🚾🚾🚾🚾🚾🚾🚾","无实际意义，为了看信息更方便")



.REGEXREPLACE(
[🔪💜推荐理由-对标情况+时间紧迫性（唯一值）].TRIM()
.REGEXREPLACE("✨.*✨","")
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("(.*?‼️)|(.*?✔️)").NTH(1)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")



.REGEXREPLACE(
[🔪💜推荐理由-对标情况+时间紧迫性（唯一值）].TRIM()
.REGEXREPLACE("✨.*✨","")
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("(.*?‼️)|(.*?✔️)").NTH(2)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")




.REGEXREPLACE(
[🔪💜推荐理由-对标情况+时间紧迫性（唯一值）].TRIM()
.REGEXREPLACE("✨.*✨","")
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("(.*?‼️)|(.*?✔️)").NTH(3)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")




.REGEXREPLACE(
[🔪💜推荐理由-对标情况+时间紧迫性（唯一值）].TRIM()
.REGEXREPLACE("✨.*✨","")
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("(.*?‼️)|(.*?✔️)").NTH(4)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")




.REGEXREPLACE(
[🔪💜推荐理由-对标情况+时间紧迫性（唯一值）].TRIM()
.REGEXREPLACE("✨.*✨","")
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("(.*?‼️)|(.*?✔️)").NTH(5)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")




.REGEXREPLACE(
[🔪💜推荐理由-对标情况+时间紧迫性（唯一值）].TRIM()
.REGEXREPLACE("✨.*✨","")
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("(.*?‼️)|(.*?✔️)").NTH(6)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")



.REGEXREPLACE("🚾🚾🚾🚾🚾🚾🚾","无实际意义，为了看信息更方便")



.REGEXREPLACE(
[🔪💜保质期].TRIM()
.REGEXREPLACE("✨.*✨","")
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("(.*?‼️)|(.*?✔️)").NTH(1)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")



.REGEXREPLACE(
[🔪💜保质期].TRIM()
.REGEXREPLACE("✨.*✨","")
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("(.*?‼️)|(.*?✔️)").NTH(2)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")



.REGEXREPLACE(
[🔪💜保质期].TRIM()
.REGEXREPLACE("✨.*✨","")
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("(.*?‼️)|(.*?✔️)").NTH(3)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")




.REGEXREPLACE(
[🔪💜保质期].TRIM()
.REGEXREPLACE("✨.*✨","")
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("(.*?‼️)|(.*?✔️)").NTH(4)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")



.REGEXREPLACE(
[🔪💜保质期].TRIM()
.REGEXREPLACE("✨.*✨","")
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("(.*?‼️)|(.*?✔️)").NTH(5)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")



.REGEXREPLACE(
[🔪💜保质期].TRIM()
.REGEXREPLACE("✨.*✨","")
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("(.*?‼️)|(.*?✔️)").NTH(6)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")


.REGEXREPLACE("🚾🚾🚾🚾🚾🚾🚾","无实际意义，为了看信息更方便")


.TRIM()
.REGEXREPLACE("º","")
.REGEXREPLACE("([❶-❾]🔞)\s*([❶-❾])","$1"&CHAR(10)&"$2")
.REGEXREPLACE("\\("&[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[🔪编号（最终）]).[🧠需转义符号-逻辑]&")","$1")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🔪💜折算价格提取
CONCATENATE(

[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🔪编号（最终）]).[📁💜sku分割1️⃣]
.REGEXEXTRACTALL([🧠折算价格-逻辑])
.ARRAYJOIN("❶🔞；❶")
.REGEXREPLACE(",","")
.REGEXREPLACE(".*","❶$0❶🔞")



,CHAR(10),




[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🔪编号（最终）]).[📁💜sku分割2️⃣]
.REGEXEXTRACTALL([🧠折算价格-逻辑])
.ARRAYJOIN("❷🔞；❷")
.REGEXREPLACE(",","")
.REGEXREPLACE(".*", "❷$0❷🔞")



,CHAR(10),


[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🔪编号（最终）]).[📁💜sku分割3️⃣]
.REGEXEXTRACTALL([🧠折算价格-逻辑])
.ARRAYJOIN("❸🔞；❸")
.REGEXREPLACE(",","")
.REGEXREPLACE(".*", "❸$0❸🔞")


,CHAR(10),


[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🔪编号（最终）]).[📁💜sku分割4️⃣]
.REGEXEXTRACTALL([🧠折算价格-逻辑])
.ARRAYJOIN("❹🔞；❹")
.REGEXREPLACE(",","")
.REGEXREPLACE(".*", "❹$0❹🔞")



,CHAR(10),


[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🔪编号（最终）]).[📁💜sku分割5️⃣]
.REGEXEXTRACTALL([🧠折算价格-逻辑])
.ARRAYJOIN("❺🔞；❺")
.REGEXREPLACE(",","")
.REGEXREPLACE(".*", "❺$0❺🔞")



,CHAR(10),


[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🔪编号（最终）]).[🤖💜sku分割6️⃣]
.REGEXEXTRACTALL([🧠折算价格-逻辑])
.ARRAYJOIN("❻🔞；❻")
.REGEXREPLACE(",","")
.REGEXREPLACE(".*", "❻$0❻🔞")




,CHAR(10),


[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🔪编号（最终）]).[🤖💜sku分割7️⃣]
.REGEXEXTRACTALL([🧠折算价格-逻辑])
.ARRAYJOIN("❼🔞；❼")
.REGEXREPLACE(",","")
.REGEXREPLACE(".*", "❼$0❼🔞")




,CHAR(10),


[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🔪编号（最终）]).[🤖💜sku分割8️⃣]
.REGEXEXTRACTALL([🧠折算价格-逻辑])
.ARRAYJOIN("❽🔞；❽")
.REGEXREPLACE(",","")
.REGEXREPLACE(".*","❽$0❽🔞")



,CHAR(10),


[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🔪编号（最终）]).[🤖💜sku分割9️⃣]
.REGEXEXTRACTALL([🧠折算价格-逻辑])
.ARRAYJOIN("❾🔞；❾")
.REGEXREPLACE(",","")
.REGEXREPLACE(".*", "❾$0❾🔞")

)

.REGEXREPLACE("\\("&[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠需转义符号-逻辑]!="").[🧠需转义符号-逻辑]&")","$1")


.REGEXREPLACE("^[❶-❾]*🔞","")
.REGEXREPLACE("
[❶-❾]*🔞","")


.REGEXREPLACE("[,，+➕～\s*]+([❶-❾]🔞)", "$1")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🔪🖤SKU数量
IF(
[💰 价格解构（基建）].FILTER(CurrentValue.[💰编号（最终）]=[🔪编号（最终）]).[💰🤍😀付款类型].LISTCOMBINE()
.REGEXMATCH("常规")
,

[💰 价格解构（基建）].FILTER(CurrentValue.[💰编号（最终）]=[🔪编号（最终）]).[💰🩶原始高价]
.REGEXEXTRACTALL("[❶-❾]")
.UNIQUE()
.COUNTA()

，
[🧪 规格解构（基建）].FILTER(CurrentValue.[🧪编号（最终）]=[🔪编号（最终）]).[🧪🤍规格✖️数量提取（原始版）]
.REGEXEXTRACTALL("[❶-❾]")
.UNIQUE()
.COUNTA()
）
=====
🔪💚价格优势（信息重组专用版）
[🔥 价格热度计算（基建）].FILTER(CurrentValue.[🔥编号（最终）]=[🔪编号（最终）]).[🔥价格优势（终版）（唯一值）]
=====
🔪💜保质期
CONCATENATE(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(1)
.REGEXEXTRACTALL([🧠保质期-逻辑])
.ARRAYJOIN("
")
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXREPLACE([🧠保质期-逻辑]，"❶$0❶🔞")


,CHAR(10),


[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(2)
.REGEXEXTRACTALL([🧠保质期-逻辑])
.ARRAYJOIN("
")
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXREPLACE([🧠保质期-逻辑]，"❷$0❷🔞")


,CHAR(10),





[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(3)
.REGEXEXTRACTALL([🧠保质期-逻辑])
.ARRAYJOIN("
")
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXREPLACE([🧠保质期-逻辑]，"❸$0❸🔞")


,CHAR(10),




[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(4)
.REGEXEXTRACTALL([🧠保质期-逻辑])
.ARRAYJOIN("
")
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXREPLACE([🧠保质期-逻辑]，"❹$0❹🔞")


,CHAR(10),




[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(5)
.REGEXEXTRACTALL([🧠保质期-逻辑])
.ARRAYJOIN("
")
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXREPLACE([🧠保质期-逻辑]，"❺$0❺🔞")


,CHAR(10),




[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(6)
.REGEXEXTRACTALL([🧠保质期-逻辑])
.ARRAYJOIN("
")
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXREPLACE([🧠保质期-逻辑]，"❻$0❻🔞")


,CHAR(10),



[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(7)
.REGEXEXTRACTALL([🧠保质期-逻辑])
.ARRAYJOIN("
")
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXREPLACE([🧠保质期-逻辑]，"❼$0❼🔞")


,CHAR(10),



[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(8)
.REGEXEXTRACTALL([🧠保质期-逻辑])
.ARRAYJOIN("
")
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXREPLACE([🧠保质期-逻辑]，"❽$0❽🔞")


,CHAR(10),



[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(9)
.REGEXEXTRACTALL([🧠保质期-逻辑])
.ARRAYJOIN("
")
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXREPLACE([🧠保质期-逻辑]，"❾$0❾🔞")


)
.CONCATENATE("🔞")
.REGEXREPLACE("豪车|好价|好车|低价|史低|羊毛|豪🚗|[❗️]"，""）
.REGEXREPLACE("[好新]效期","")


.REGEXREPLACE("\\("&[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[🔪编号（最终）]).[🧠需转义符号-逻辑]&")","$1")


.REGEXREPLACE("[❶-❾][❶-❾]🔞","")
.REGEXREPLACE("^\s*🔞","")

.REGEXREPLACE("[,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🔪💜临期情况
CONCATENATE(
[🔪💜保质期]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXREPLACE(".*?新鲜.*","")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(1)
.REGEXREPLACE("([❶-❾]).*?([❶-❾]🔞)","$1临期$2")

,CHAR(10),

[🔪💜保质期]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXREPLACE(".*?新鲜.*","")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(2)
.REGEXREPLACE("([❶-❾]).*?([❶-❾]🔞)","$1临期$2")


,CHAR(10),

[🔪💜保质期]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXREPLACE(".*?新鲜.*","")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(3)
.REGEXREPLACE("([❶-❾]).*?([❶-❾]🔞)","$1临期$2")


,CHAR(10),

[🔪💜保质期]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXREPLACE(".*?新鲜.*","")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(4)
.REGEXREPLACE("([❶-❾]).*?([❶-❾]🔞)","$1临期$2")



,CHAR(10),

[🔪💜保质期]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXREPLACE(".*?新鲜.*","")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(5)
.REGEXREPLACE("([❶-❾]).*?([❶-❾]🔞)","$1临期$2")


,CHAR(10),

[🔪💜保质期]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXREPLACE(".*?新鲜.*","")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(6)
.REGEXREPLACE("([❶-❾]).*?([❶-❾]🔞)","$1临期$2")

,CHAR(10),

[🔪💜保质期]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXREPLACE(".*?新鲜.*","")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(7)
.REGEXREPLACE("([❶-❾]).*?([❶-❾]🔞)","$1临期$2")

,CHAR(10),

[🔪💜保质期]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXREPLACE(".*?新鲜.*","")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(8)
.REGEXREPLACE("([❶-❾]).*?([❶-❾]🔞)","$1临期$2")

,CHAR(10),

[🔪💜保质期]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXREPLACE(".*?新鲜.*","")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(9)
.REGEXREPLACE("([❶-❾]).*?([❶-❾]🔞)","$1临期$2")

)

.REGEXEXTRACTALL("[❶-❾]临期[❶-❾]🔞")
.UNIQUE()


.REGEXREPLACE("临期","【$0】")
.REGEXREPLACE("[,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🔪💚保质期（信息重组专用版）
CONCATENATE(

IF(
[🔪💜保质期]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(1)
.ISBLANK()
,"",

IF(
OR(
[🔪💚商品名称（信息重组专用版）]
.REGEXREPLACE("[❶-❾🔞]","")
.CONTAINTEXT(
[🔪💜保质期]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(1)
.REGEXREPLACE("[❶-❾🔞]",""))

，

[🔪💚商品价格（信息重组专用版）]
.REGEXREPLACE("[❶-❾🔞]","")
.CONTAINTEXT(
[🔪💜保质期]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(1)
.REGEXREPLACE("[❶-❾🔞]",""))


）

，
[🔪💜保质期]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(1)
.REGEXREPLACE("[❶-❾].*?[❶-❾]🔞","")
,
[🔪💜保质期]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(1)
))


，CHAR(10)，


IF(
[🔪💜保质期]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(2)
.ISBLANK()
,"",

IF(
OR(
[🔪💚商品名称（信息重组专用版）]
.REGEXREPLACE("[❶-❾🔞]","")
.CONTAINTEXT(
[🔪💜保质期]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(2)
.REGEXREPLACE("[❶-❾🔞]",""))

,

[🔪💚商品价格（信息重组专用版）]
.REGEXREPLACE("[❶-❾🔞]","")
.CONTAINTEXT(
[🔪💜保质期]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(2)
.REGEXREPLACE("[❶-❾🔞]",""))


)

,
[🔪💜保质期]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(2)
.REGEXREPLACE("[❶-❾].*?[❶-❾]🔞","")
,
[🔪💜保质期]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(2)
))

,CHAR(10),

IF(
[🔪💜保质期]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(3)
.ISBLANK()
,"",

IF(
OR(
[🔪💚商品名称（信息重组专用版）]
.REGEXREPLACE("[❶-❾🔞]","")
.CONTAINTEXT(
[🔪💜保质期]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(3)
.REGEXREPLACE("[❶-❾🔞]",""))

,

[🔪💚商品价格（信息重组专用版）]
.REGEXREPLACE("[❶-❾🔞]","")
.CONTAINTEXT(
[🔪💜保质期]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(3)
.REGEXREPLACE("[❶-❾🔞]",""))


)

,
[🔪💜保质期]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(3)
.REGEXREPLACE("[❶-❾].*?[❶-❾]🔞","")
,
[🔪💜保质期]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(3)
))

,CHAR(10),

IF(
[🔪💜保质期]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(4)
.ISBLANK()
,"",

IF(
OR(
[🔪💚商品名称（信息重组专用版）]
.REGEXREPLACE("[❶-❾🔞]","")
.CONTAINTEXT(
[🔪💜保质期]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(4)
.REGEXREPLACE("[❶-❾🔞]",""))

,

[🔪💚商品价格（信息重组专用版）]
.REGEXREPLACE("[❶-❾🔞]","")
.CONTAINTEXT(
[🔪💜保质期]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(4)
.REGEXREPLACE("[❶-❾🔞]",""))


)

,
[🔪💜保质期]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(4)
.REGEXREPLACE("[❶-❾].*?[❶-❾]🔞","")
,
[🔪💜保质期]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(4)
))

,CHAR(10),

IF(
[🔪💜保质期]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(5)
.ISBLANK()
,"",

IF(
OR(
[🔪💚商品名称（信息重组专用版）]
.REGEXREPLACE("[❶-❾🔞]","")
.CONTAINTEXT(
[🔪💜保质期]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(5)
.REGEXREPLACE("[❶-❾🔞]",""))

,

[🔪💚商品价格（信息重组专用版）]
.REGEXREPLACE("[❶-❾🔞]","")
.CONTAINTEXT(
[🔪💜保质期]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(5)
.REGEXREPLACE("[❶-❾🔞]",""))


)

,
[🔪💜保质期]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(5)
.REGEXREPLACE("[❶-❾].*?[❶-❾]🔞","")
,
[🔪💜保质期]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(5)
))

,CHAR(10),

IF(
[🔪💜保质期]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(6)
.ISBLANK()
,"",

IF(
OR(
[🔪💚商品名称（信息重组专用版）]
.REGEXREPLACE("[❶-❾🔞]","")
.CONTAINTEXT(
[🔪💜保质期]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(6)
.REGEXREPLACE("[❶-❾🔞]",""))

,

[🔪💚商品价格（信息重组专用版）]
.REGEXREPLACE("[❶-❾🔞]","")
.CONTAINTEXT(
[🔪💜保质期]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(6)
.REGEXREPLACE("[❶-❾🔞]",""))


)

,
[🔪💜保质期]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(6)
.REGEXREPLACE("[❶-❾].*?[❶-❾]🔞","")
,
[🔪💜保质期]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(6)
))

,CHAR(10),

IF(
[🔪💜保质期]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(7)
.ISBLANK()
,"",

IF(
OR(
[🔪💚商品名称（信息重组专用版）]
.REGEXREPLACE("[❶-❾🔞]","")
.CONTAINTEXT(
[🔪💜保质期]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(7)
.REGEXREPLACE("[❶-❾🔞]",""))

,

[🔪💚商品价格（信息重组专用版）]
.REGEXREPLACE("[❶-❾🔞]","")
.CONTAINTEXT(
[🔪💜保质期]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(7)
.REGEXREPLACE("[❶-❾🔞]",""))


)

,
[🔪💜保质期]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(7)
.REGEXREPLACE("[❶-❾].*?[❶-❾]🔞","")
,
[🔪💜保质期]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(7)
))

,CHAR(10),

IF(
[🔪💜保质期]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(8)
.ISBLANK()
,"",

IF(
OR(
[🔪💚商品名称（信息重组专用版）]
.REGEXREPLACE("[❶-❾🔞]","")
.CONTAINTEXT(
[🔪💜保质期]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(8)
.REGEXREPLACE("[❶-❾🔞]",""))

,

[🔪💚商品价格（信息重组专用版）]
.REGEXREPLACE("[❶-❾🔞]","")
.CONTAINTEXT(
[🔪💜保质期]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(8)
.REGEXREPLACE("[❶-❾🔞]",""))


)

,
[🔪💜保质期]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(8)
.REGEXREPLACE("[❶-❾].*?[❶-❾]🔞","")
,
[🔪💜保质期]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(8)
))

,CHAR(10),

IF(
[🔪💜保质期]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(9)
.ISBLANK()
,"",

IF(
OR(
[🔪💚商品名称（信息重组专用版）]
.REGEXREPLACE("[❶-❾🔞]","")
.CONTAINTEXT(
[🔪💜保质期]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(9)
.REGEXREPLACE("[❶-❾🔞]",""))

,

[🔪💚商品价格（信息重组专用版）]
.REGEXREPLACE("[❶-❾🔞]","")
.CONTAINTEXT(
[🔪💜保质期]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(9)
.REGEXREPLACE("[❶-❾🔞]",""))


)

,
[🔪💜保质期]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(9)
.REGEXREPLACE("[❶-❾].*?[❶-❾]🔞","")
,
[🔪💜保质期]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(9)
))


)

.TRIM()
.REGEXREPLACE(" ","")


.REGEXREPLACE(
[🔪💜推荐理由-常规优势(唯一值)]
.REGEXREPLACE("[✔️]",""),"")

.REGEXREPLACE("[❶-❾]临期[❶-❾]🔞","")
.REGEXREPLACE("[,;.，；。]([❶-❾])","$1")


.REGEXREPLACE("([❶-❾])([\s\S]*?)([❶-❾]🔞)","$1"&[🔪随机表情-勿删！]&"$2$3")

.REGEXREPLACE("\\("&[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[🔪编号（最终）]).[🧠需转义符号-逻辑]&")","$1")

.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🔪💜所属活动（唯一值）
[📁💜商品信息（终版分割版）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("百亿补贴|百补|年货节")
.UNIQUE()
.REGEXREPLACE("百补","百亿补贴")


.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🔪💜推荐理由-常规优势(唯一值)
[📁💜商品信息（终版分割版）]
.REGEXEXTRACTALL("(?:♎️
|(超高性价比(?:首选)?)|(性价比首选)|(福利放送)|(超[高多]好评)|(木薯天花板)|(优先试用)|(反馈好用)|(反馈很好)|(刚需入)|(天热必备)|(人宠可[吃喝用])|(猫[狗犬]可[吃喝用])|([人宠犬猫狗]{2}通用)|(活动很?[稀少]+)|([稀少]+活动)|(独家首发)|(新品(?:首发)?)|(复杂车)|♎️
(\d+[天月年]有效防潮)|((?:今晚)?\d+[号点].*?[付结]尾?款)|(锁鲜\d+个?[月天日年])|♎️

|(大厂出品)|(出口品质)|♎️
|(新鲜效期)|(全?新包装)|(\d+[升Ll]超?大?容量)|♎️
|(百亿补贴)|(赠品有限)|♎️

|((?:美国|英国|法国|德国|日本|意大利|加拿大|芬兰|新西兰|瑞典|新加坡|台湾|西班牙|荷兰|韩国|菲律宾|挪威|海外)(?:原装)?进口)|(北美原野系列)|♎️

|(猫狗通用)|(全[犬猫狗]通用)|(多款可选)|(全口味都有)|(多口味可选)|(混合口味)|(全尺码同价)|(超?[大小]包装)|(全新包装)|(专为养宠家庭研制)|(升级款)|(囤货[装款])|♎️
|(可冲厕所)|(众砂可混)|(初食率(?:\d+(?:\.\d+)?%))|♎️

|((?:近期|历史|全网)最低价?)|(史低)|(近期[好低]价)|(近历史[好低]价)|([最超]?[好新]效期)|♎️
|(官方正品)|(正品保证)|(日期新鲜)|(可查防伪)|(防伪可查)|(整盒带码)|((?:整盒)?不刮码)|♎️
|(驱虫必备)|(限量礼金)|((?:国[家际])?兽药.*?认证)|((?:国[家际])?GMP认证)|(MP&GSP双重认证)|(广谱驱杀)|♎️
|((?:可(?:反复)?(?:[拍领]|下单)|限量)\d+(?:"&[🧠终极单位]&"|次))|(可反复领券)|(买(?:\d+|[一二三四五六七八九十])[赠送](?:\d+|[一二三四五六七八九十]))|♎️
|(公益.*?平台)|(可喂流浪猫)|♎️
|((?:[猫狗犬]舍|宠物店)专供)|♎️


|((?:抖音|淘宝|京东|小?红[书薯]|天猫|全网|全年|复购|回购|线上|线下|店铺|亚宠展|单品|口碑)?(?:猫粮|罐头|狗粮|餐包|犬粮|零食|罐罐|猫砂|冻干|猫条|驱虫)?(?:累计)?(?:热销|爆卖|月销|日销|年销|已售)(?:榜(?:[Tt][Oo][Pp]|第))?(?:\d+|[一二三四五六七八九十])(?:[wkWK十亿万千]{1,2})?[名枚片支颗粒瓶罐板个只桶条块袋包盒箱件份]?\+?)|♎️

|((?:抖音|淘宝|京东|小?红[书薯]|天猫|全网|全年|复购|回购|线上|线下|店铺|亚宠展|单品|口碑)(?:猫粮|罐头|狗粮|餐包|犬粮|零食|罐罐|猫砂|冻干|猫条|驱虫)?.*?(?:榜)(?:[Tt][Oo][Pp]|第)(?:\d+|[一二三四五六七八九十])?[名]?)|♎️

|((?:猫粮|罐头|狗粮|餐包|犬粮|零食|罐罐|猫砂|冻干|猫条|驱虫).*?(?:榜)(?:[Tt][Oo][Pp]|第)(?:\d+|[一二三四五六七八九十])?[名]?)|♎️


|((?:抖音|淘宝|京东|小红书|天猫|全网|全年|复购|回购|线上|线下|店铺|亚宠展|单品|口碑)?(?:猫粮|罐头|狗粮|餐包|犬粮|零食|罐罐|猫砂|冻干|猫条)?(?:爆卖|爆款)[款品]?)|♎️
|((?:抖音|淘宝|京东|小红书|天猫|全网|全年|复购|回购|线上|线下|店铺|亚宠展|单品|口碑)?(?:超?高?人气)(?:猫粮|罐头|狗粮|餐包|犬粮|零食|罐罐|猫砂|冻干|猫条)?)|♎️
|((?:抖音|淘宝|京东|小红书|天猫|全网|全年|线上|线下|店铺|亚宠展|单品|口碑)?(?:猫粮|罐头|狗粮|餐包|犬粮|零食|罐罐|猫砂|冻干|猫条)?(?:无限[回复]购)[款品]?)|♎️
)")
.UNIQUE()
.ARRAYJOIN("✔️")
.CONCATENATE("✔️")
.REGEXREPLACE("[✔️]+","✔️")
.REGEXREPLACE("^✔️","")


.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🔪💜推荐理由-对标情况+时间紧迫性（唯一值）
[📁💜商品信息（终版分割版）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("(对标"&[🧠品牌-逻辑]&")|(配方比"&[🧠品牌-逻辑]&"还好)|("&[🧠品牌-逻辑]&"(?:同厂|平替|升级款|同款(配方)?|同品质|同配方))|♎️
|(\d+点结束)|(预售)|(随时无)|(手慢无)|(限时福利)|(随时结束)|(仅\d+单)|(速度)|(仅限今天)")
.UNIQUE()
.ARRAYJOIN("‼️"&CHAR(10))
.CONCATENATE("‼️")
.REGEXREPLACE("^‼️","")

.REGEXREPLACE(","&[🧠品牌-逻辑],"")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🔪💜推荐理由-运输优势（唯一值）
[📁💜商品信息（终版分割版）]
.REGEXREPLACE([🧠商品价格-逻辑],"")
.REGEXREPLACE([🧠商品介绍-逻辑],"")


.REGEXEXTRACTALL("((?:自有|直营|自营|官方|官旗|海[内外]|国[内外])(?:[Tt][Oo][Pp])?)|(淘?工厂|厂家|旗舰店|(?:天猫)?国际(?:超市)?|猫超|京东|京喜|美团|顺丰|天猫超市|授权店)|(送货上门|配送到家|送货到家|直销|发货|直发|当日达|次日达|包邮)")
.ARRAYJOIN("")
.UNIQUE()
.REGEXREPLACE("官方旗舰店|旗舰店","官旗")
.REGEXREPLACE("天猫(国际)?超市","猫超")
.REGEXREPLACE("国际","天猫国际")

.REGEXEXTRACTALL("(自有|直营|自营|官方|官旗|海[内外]|国[内外]|[Tt][Oo][Pp]|淘?工厂|厂家|旗舰店|天猫国际(超市)?|猫超|国际|京东|京喜|美团|顺丰|天猫超市|官旗|旗舰店|授权店|发货|送货上门|配送到家|送货到家|直发|直销|当日达|次日达|包邮)")
.UNIQUE()
.ARRAYJOIN("")

.ARRAYJOIN("‼️"&CHAR(10))
.CONCATENATE("‼️")
.REGEXREPLACE("(京东|京喜|美团|顺丰)+‼️","‼️")
.REGEXREPLACE("^‼️","")


.REGEXREPLACE("[,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🔪💛先导文案
IF(
CONCATENATE(
[🔪💚价格优势（信息重组专用版）]，
[🔪💜临期情况].REGEXREPLACE("[❶-❾🔞【]","").SPLIT(CHAR(10)).UNIQUE().REGEXREPLACE("(.*?)】","$1‼️")，
[🔪💜推荐理由-运输优势（唯一值）]，
[🔪💜推荐理由-对标情况+时间紧迫性（唯一值）]，
[🔪💜推荐理由-常规优势(唯一值)]）
.REGEXREPLACE("(官旗|自营|猫超|天猫国际)([‼️✔️])","$1发货$2")
.REGEXREPLACE("(新品)([‼️✔️])","$1首发$2")
.REGEXREPLACE("(史低)([‼️✔️])","历史低价$2")

.len()>15

,
CONCATENATE(
[🔪💚价格优势（信息重组专用版）]，
[🔪💜临期情况].REGEXREPLACE("[❶-❾🔞【]","").SPLIT(CHAR(10)).UNIQUE().REGEXREPLACE("(.*?)】","$1‼️")，
[🔪💜推荐理由-运输优势（唯一值）]，
[🔪💜推荐理由-对标情况+时间紧迫性（唯一值）]，
[🔪💜推荐理由-常规优势(唯一值)]）
.REGEXREPLACE("(官旗|自营|猫超|天猫国际)([‼️✔️])","$1发货$2")
.REGEXREPLACE("(新品)([‼️✔️])","$1首发$2")
.REGEXREPLACE("(史低)([‼️✔️])","历史低价$2")
.REGEXREPLACE("(.{4,10})([✨])", "$1$2"&CHAR(10))

,
CONCATENATE(
[🔪💚价格优势（信息重组专用版）]，
[🔪💜临期情况].REGEXREPLACE("[❶-❾🔞【]","").SPLIT(CHAR(10)).UNIQUE().REGEXREPLACE("(.*?)】","$1‼️")，
[🔪💜推荐理由-运输优势（唯一值）]，
[🔪💜推荐理由-对标情况+时间紧迫性（唯一值）]，
[🔪💜推荐理由-常规优势(唯一值)])
.REGEXREPLACE("(官旗|自营|猫超|天猫国际)([‼️✔️])","$1发货$2")
.REGEXREPLACE("(新品)([‼️✔️])","$1首发$2")
.REGEXREPLACE("(史低)([‼️✔️])","历史低价$2"))

.REGEXREPLACE("(.{8,18})([✔️‼️])", "$1$2"&CHAR(10))
.REGEXREPLACE(" ","")


.REGEXREPLACE("^(‼️|✔️)","")
.REGEXREPLACE("✨‼️","✨")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🔪💛先导文案(阉割版)
IF(
CONCATENATE(
[🔥💜价格优势（信息提取）（唯一值）]，
[🔪💜临期情况].REGEXREPLACE("[❶-❾🔞【]","").SPLIT(CHAR(10)).UNIQUE().REGEXREPLACE("(.*?)】","$1‼️")，
[🔪💜推荐理由-运输优势（唯一值）]，
[🔪💜推荐理由-对标情况+时间紧迫性（唯一值）]，
[🔪💜推荐理由-常规优势(唯一值)]）
.REGEXREPLACE("(官旗|自营|猫超|天猫国际)([‼️✔️])","$1发货$2")
.REGEXREPLACE("(新品)([‼️✔️])","$1首发$2")
.REGEXREPLACE("(史低)([‼️✔️])","历史低价$2")

.len()>15

,
CONCATENATE(
[🔥💜价格优势（信息提取）（唯一值）]，
[🔪💜临期情况].REGEXREPLACE("[❶-❾🔞【]","").SPLIT(CHAR(10)).UNIQUE().REGEXREPLACE("(.*?)】","$1‼️")，
[🔪💜推荐理由-运输优势（唯一值）]，
[🔪💜推荐理由-对标情况+时间紧迫性（唯一值）]，
[🔪💜推荐理由-常规优势(唯一值)]）
.REGEXREPLACE("(官旗|自营|猫超|天猫国际)([‼️✔️])","$1发货$2")
.REGEXREPLACE("(新品)([‼️✔️])","$1首发$2")
.REGEXREPLACE("(史低)([‼️✔️])","历史低价$2")
.REGEXREPLACE("(.{4,10})([✨])", "$1$2"&CHAR(10))

,
CONCATENATE(
[🔥💜价格优势（信息提取）（唯一值）]，
[🔪💜临期情况].REGEXREPLACE("[❶-❾🔞【]","").SPLIT(CHAR(10)).UNIQUE().REGEXREPLACE("(.*?)】","$1‼️")，
[🔪💜推荐理由-运输优势（唯一值）]，
[🔪💜推荐理由-对标情况+时间紧迫性（唯一值）]，
[🔪💜推荐理由-常规优势(唯一值)])
.REGEXREPLACE("(官旗|自营|猫超|天猫国际)([‼️✔️])","$1发货$2")
.REGEXREPLACE("(新品)([‼️✔️])","$1首发$2")
.REGEXREPLACE("(史低)([‼️✔️])","历史低价$2"))

.REGEXREPLACE("(.{8,18})([✔️‼️])", "$1$2"&CHAR(10))
.REGEXREPLACE(" ","")


.REGEXREPLACE("^(‼️|✔️)","")
.REGEXREPLACE("✨‼️","✨")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🔪💛推荐理由（合并版）
CONCATENATE(
[🔪💜临期情况].REGEXREPLACE("[❶-❾🔞【]","").SPLIT(CHAR(10)).UNIQUE().REGEXREPLACE("(.*?)】","$1‼️")，
[🔪💜推荐理由-运输优势（唯一值）]，
[🔪💜推荐理由-对标情况+时间紧迫性（唯一值）]，
[🔪💜推荐理由-常规优势(唯一值)])

.REGEXREPLACE("^(‼️|✔️)","")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🔪💜拼团
CONCATENATE(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(1)
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("(?:\d+|[一二三四五六七八九十])人拼?团")
.ARRAYJOIN("
")
.REGEXREPLACE("(?:\d+|[一二三四五六七八九十])人拼?团"，"❶【$0】❶🔞")

，CHAR(10)，


[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(2)
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("(?:\d+|[一二三四五六七八九十])人拼?团")
.ARRAYJOIN("
")
.REGEXREPLACE("(?:\d+|[一二三四五六七八九十])人拼?团"，"❷【$0】❷🔞")


，CHAR(10)，


[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(3)
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("(?:\d+|[一二三四五六七八九十])人拼?团")
.ARRAYJOIN("
")
.REGEXREPLACE("(?:\d+|[一二三四五六七八九十])人拼?团"，"❸【$0】❸🔞")


，CHAR(10)，


[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(4)
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("(?:\d+|[一二三四五六七八九十])人拼?团")
.ARRAYJOIN("
")
.REGEXREPLACE("(?:\d+|[一二三四五六七八九十])人拼?团"，"❹【$0】❹🔞")


，CHAR(10)，


[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(5)
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("(?:\d+|[一二三四五六七八九十])人拼?团")
.ARRAYJOIN("
")
.REGEXREPLACE("(?:\d+|[一二三四五六七八九十])人拼?团"，"❺【$0】❺🔞")

，CHAR(10)，


[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(6)
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("(?:\d+|[一二三四五六七八九十])人拼?团")
.ARRAYJOIN("
")
.REGEXREPLACE("(?:\d+|[一二三四五六七八九十])人拼?团"，"❻【$0】❻🔞")


，CHAR(10)，

[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(7)
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("(?:\d+|[一二三四五六七八九十])人拼?团")
.ARRAYJOIN("
")
.REGEXREPLACE("(?:\d+|[一二三四五六七八九十])人拼?团","❼【$0】❼🔞")



，CHAR(10)，

[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(8)
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("(?:\d+|[一二三四五六七八九十])人拼?团")
.ARRAYJOIN("
")
.REGEXREPLACE("(?:\d+|[一二三四五六七八九十])人拼?团","❽【$0】❽🔞")




，CHAR(10)，



[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(9)
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("(?:\d+|[一二三四五六七八九十])人拼?团")
.ARRAYJOIN("
")
.REGEXREPLACE("(?:\d+|[一二三四五六七八九十])人拼?团","❾【$0】❾🔞")


)

.REGEXREPLACE("^[❶-❾]*🔞","")
.REGEXREPLACE("
[❶-❾]*🔞","")

.REGEXREPLACE("二","2")
.REGEXREPLACE("三","3")
.REGEXREPLACE("四","4")
.REGEXREPLACE("拼","")


.REGEXREPLACE("[,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🔪💚拍凑份数（信息重组专用版）
IF([🍬商品信息（原始版）].TRIM().ISBLANK(),""，

CONCATENATE(

IF(
OR(
[🔪🤍😀商品价格（原始版）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXREPLACE("[❶-❾🔞🔥‼️✔️]","")

.CONTAINTEXT(
[🔪🤍😀拍凑份数(原始版)]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(1)
.REGEXREPLACE("[❶-❾🔞🔥‼️✔️]","")
)


,

[🔪🧡领券文案]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXREPLACE("[❶-❾🔞🔥‼️✔️]","")

.CONTAINTEXT(
[🔪🤍😀拍凑份数(原始版)]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(1)
.REGEXREPLACE("[❶-❾🔞🔥‼️✔️]","")
)

)
,

[🔪🤍😀拍凑份数(原始版)]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(1)
.REGEXREPLACE(".*","")

，

[🔪🤍😀拍凑份数(原始版)]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(1))



,CHAR(10),

IF(
OR(
[🔪🤍😀商品价格（原始版）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXREPLACE("[❶-❾🔞🔥‼️✔️]","")

.CONTAINTEXT(
[🔪🤍😀拍凑份数(原始版)]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(2)
.REGEXREPLACE("[❶-❾🔞🔥‼️✔️]","")
),

[🔪🧡领券文案]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXREPLACE("[❶-❾🔞🔥‼️✔️]","")

.CONTAINTEXT(
[🔪🤍😀拍凑份数(原始版)]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(2)
.REGEXREPLACE("[❶-❾🔞🔥‼️✔️]","")
)

),
[🔪🤍😀拍凑份数(原始版)]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(2)
.REGEXREPLACE(".*","")

,
[🔪🤍😀拍凑份数(原始版)]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(2)
)


,CHAR(10),

IF(
OR(
[🔪🤍😀商品价格（原始版）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXREPLACE("[❶-❾🔞🔥‼️✔️]","")

.CONTAINTEXT(
[🔪🤍😀拍凑份数(原始版)]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(3)
.REGEXREPLACE("[❶-❾🔞🔥‼️✔️]","")
),

[🔪🧡领券文案]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXREPLACE("[❶-❾🔞🔥‼️✔️]","")

.CONTAINTEXT(
[🔪🤍😀拍凑份数(原始版)]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(3)
.REGEXREPLACE("[❶-❾🔞🔥‼️✔️]","")
)

),
[🔪🤍😀拍凑份数(原始版)]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(3)
.REGEXREPLACE(".*","")

,
[🔪🤍😀拍凑份数(原始版)]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(3)
)



,CHAR(10),

IF(
OR(
[🔪🤍😀商品价格（原始版）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXREPLACE("[❶-❾🔞🔥‼️✔️]","")

.CONTAINTEXT(
[🔪🤍😀拍凑份数(原始版)]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(4)
.REGEXREPLACE("[❶-❾🔞🔥‼️✔️]","")
),

[🔪🧡领券文案]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXREPLACE("[❶-❾🔞🔥‼️✔️]","")

.CONTAINTEXT(
[🔪🤍😀拍凑份数(原始版)]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(4)
.REGEXREPLACE("[❶-❾🔞🔥‼️✔️]","")
)

),
[🔪🤍😀拍凑份数(原始版)]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(4)
.REGEXREPLACE(".*","")

,
[🔪🤍😀拍凑份数(原始版)]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(4)
)




,CHAR(10),

IF(
OR(
[🔪🤍😀商品价格（原始版）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXREPLACE("[❶-❾🔞🔥‼️✔️]","")

.CONTAINTEXT(
[🔪🤍😀拍凑份数(原始版)]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(5)
.REGEXREPLACE("[❶-❾🔞🔥‼️✔️]","")
),

[🔪🧡领券文案]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXREPLACE("[❶-❾🔞🔥‼️✔️]","")

.CONTAINTEXT(
[🔪🤍😀拍凑份数(原始版)]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(5)
.REGEXREPLACE("[❶-❾🔞🔥‼️✔️]","")
)

),
[🔪🤍😀拍凑份数(原始版)]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(5)
.REGEXREPLACE(".*","")

,
[🔪🤍😀拍凑份数(原始版)]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(5)
)



,CHAR(10),

IF(
OR(
[🔪🤍😀商品价格（原始版）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXREPLACE("[❶-❾🔞🔥‼️✔️]","")

.CONTAINTEXT(
[🔪🤍😀拍凑份数(原始版)]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(6)
.REGEXREPLACE("[❶-❾🔞🔥‼️✔️]","")
),

[🔪🧡领券文案]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXREPLACE("[❶-❾🔞🔥‼️✔️]","")

.CONTAINTEXT(
[🔪🤍😀拍凑份数(原始版)]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(6)
.REGEXREPLACE("[❶-❾🔞🔥‼️✔️]","")
)

),
[🔪🤍😀拍凑份数(原始版)]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(6)
.REGEXREPLACE(".*","")

,
[🔪🤍😀拍凑份数(原始版)]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(6)
)




,CHAR(10),

IF(
OR(
[🔪🤍😀商品价格（原始版）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXREPLACE("[❶-❾🔞🔥‼️✔️]","")

.CONTAINTEXT(
[🔪🤍😀拍凑份数(原始版)]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(7)
.REGEXREPLACE("[❶-❾🔞🔥‼️✔️]","")
),

[🔪🧡领券文案]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXREPLACE("[❶-❾🔞🔥‼️✔️]","")

.CONTAINTEXT(
[🔪🤍😀拍凑份数(原始版)]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(7)
.REGEXREPLACE("[❶-❾🔞🔥‼️✔️]","")
)

),
[🔪🤍😀拍凑份数(原始版)]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(7)
.REGEXREPLACE(".*","")

,
[🔪🤍😀拍凑份数(原始版)]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(7)
)




))
.REGEXREPLACE("\\([+*])","$1")

.REGEXREPLACE("🚾🚾🚾🚾🚾🚾🚾","无实际意义，为了看信息更方便")

.TRIM()
.REGEXREPLACE("º","")

.REGEXREPLACE("[❶-❾]💰.*","")
.REGEXREPLACE("[❶-❾][❶-❾]🔞","")
.REGEXREPLACE("([❶-❾]🔞)\s*?([❶-❾])","$1"&CHAR(10)&"$2")

.REGEXREPLACE("([❶-❾])([\s\S]*?)([❶-❾]🔞)","$1"&[🔪随机表情-勿删！]&"$2$3")

.REGEXREPLACE("\\("&[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[🔪编号（最终）]).[🧠需转义符号-逻辑]&")","$1")


.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🔪🤍😀拍凑份数(原始版)
CONCATENATE(
[📁🤍😀商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(1)
.REGEXEXTRACTALL([🧠拍凑份数-逻辑])
.UNIQUE()
.ARRAYJOIN(CHAR(10))
.REGEXREPLACE("(.*)","❶$0❶🔞")




，CHAR(10)，

[📁🤍😀商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(2)
.REGEXEXTRACTALL([🧠拍凑份数-逻辑])
.UNIQUE()
.ARRAYJOIN(CHAR(10))
.REGEXREPLACE("(.*)","❷$0❷🔞")



，CHAR(10)，

[📁🤍😀商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(3)
.REGEXEXTRACTALL([🧠拍凑份数-逻辑])
.UNIQUE()
.ARRAYJOIN(CHAR(10))
.REGEXREPLACE("(.*)","❸$0❸🔞")




，CHAR(10)，

[📁🤍😀商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(4)
.REGEXEXTRACTALL([🧠拍凑份数-逻辑])
.UNIQUE()
.ARRAYJOIN(CHAR(10))
.REGEXREPLACE("(.*)","❹$0❹🔞")



，CHAR(10)，

[📁🤍😀商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(5)
.REGEXEXTRACTALL([🧠拍凑份数-逻辑])
.UNIQUE()
.ARRAYJOIN(CHAR(10))
.REGEXREPLACE("(.*)","❺$0❺🔞")





，CHAR(10)，

[📁🤍😀商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(6)
.REGEXEXTRACTALL([🧠拍凑份数-逻辑])
.UNIQUE()
.ARRAYJOIN(CHAR(10))
.REGEXREPLACE("(.*)","❻$0❻🔞")



，CHAR(10)，

[📁🤍😀商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(7)
.REGEXEXTRACTALL([🧠拍凑份数-逻辑])
.UNIQUE()
.ARRAYJOIN(CHAR(10))
.REGEXREPLACE("(.*)","❼$0❼🔞")





，CHAR(10)，

[📁🤍😀商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(8)
.REGEXEXTRACTALL([🧠拍凑份数-逻辑])
.UNIQUE()
.ARRAYJOIN(CHAR(10))
.REGEXREPLACE("(.*)","❽$0❽🔞")




，CHAR(10)，

[📁🤍😀商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(9)
.REGEXEXTRACTALL([🧠拍凑份数-逻辑])
.UNIQUE()
.ARRAYJOIN(CHAR(10))
.REGEXREPLACE("(.*)","❾$0❾🔞")

）

.REGEXREPLACE("("&[🧠单位合集]&")件","$1")
.REGEXREPLACE("[❶-❾][❶-❾]🔞","")
.REGEXREPLACE("[，,;.。、？]([❶-❾]🔞)","$1")

.REGEXREPLACE("[,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🔪💚是否同时存在拍凑信息
IF(
AND(
[🔪💚拍凑份数（信息重组专用版）]
.REGEXREPLACE("凑.*?💰","")
.REGEXMATCH("(买|拍|加购|加车|加购物车)")

,
[🔪💚拍凑份数（信息重组专用版）]
.REGEXREPLACE("凑.*?💰","")
.REGEXMATCH("(凑)")


),

"是"
，
""
)
=====
🔪🧡领券文案
CONCATENATE(
[📁🧡商品信息（补全版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(1)
.REGEXEXTRACTALL([🧠领券文案-逻辑])
.ARRAYJOIN("
")
.REGEXREPLACE([🧠领券文案-逻辑]，"❶$0❶🔞")

,CHAR(10),


[📁🧡商品信息（补全版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(2)
.REGEXEXTRACTALL([🧠领券文案-逻辑])
.ARRAYJOIN("
")
.REGEXREPLACE([🧠领券文案-逻辑]，"❷$0❷🔞")

,CHAR(10),



[📁🧡商品信息（补全版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(3)
.REGEXEXTRACTALL([🧠领券文案-逻辑])
.ARRAYJOIN("
")
.REGEXREPLACE([🧠领券文案-逻辑]，"❸$0❸🔞")


,CHAR(10),



[📁🧡商品信息（补全版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(4)
.REGEXEXTRACTALL([🧠领券文案-逻辑])
.ARRAYJOIN("
")
.REGEXREPLACE([🧠领券文案-逻辑]，"❹$0❹🔞")


,CHAR(10),



[📁🧡商品信息（补全版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(5)
.REGEXEXTRACTALL([🧠领券文案-逻辑])
.ARRAYJOIN("
")
.REGEXREPLACE([🧠领券文案-逻辑]，"❺$0❺🔞")


,CHAR(10),



[📁🧡商品信息（补全版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(6)
.REGEXEXTRACTALL([🧠领券文案-逻辑])
.ARRAYJOIN("
")
.REGEXREPLACE([🧠领券文案-逻辑]，"❻$0❻🔞")


,CHAR(10),

[📁🧡商品信息（补全版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(7)
.REGEXEXTRACTALL([🧠领券文案-逻辑])
.ARRAYJOIN("
")
.REGEXREPLACE([🧠领券文案-逻辑],"❼$0❼🔞")

,CHAR(10),

[📁🧡商品信息（补全版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(8)
.REGEXEXTRACTALL([🧠领券文案-逻辑])
.ARRAYJOIN("
")
.REGEXREPLACE([🧠领券文案-逻辑],"❽$0❽🔞")


,CHAR(10),

[📁🧡商品信息（补全版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(9)
.REGEXEXTRACTALL([🧠领券文案-逻辑])
.ARRAYJOIN("
")
.REGEXREPLACE([🧠领券文案-逻辑],"❾$0❾🔞")



)

.REGEXREPLACE("🪜","楼上")
.REGEXREPLACE("[❌⚛️]","")

.REGEXREPLACE("(?:豪车|好价|好车|历史低价|历史最低价|历史新低价|史低)❗*","")


.REGEXREPLACE("^[❶-❾]*🔞","")
.REGEXREPLACE("
[❶-❾]*🔞","")
.REGEXREPLACE("([❶-❾])[,，?？.。+➕～\s]","$1")

.REGEXREPLACE("^"&[🧠品牌-逻辑]，""）
.REGEXREPLACE("[官方自营旗舰店]{2,6}❗️?","")
.REGEXREPLACE("[【，,。]?(?:自有|直营|自营)?(淘工厂|官方旗舰店|天猫国际超市|猫超|天猫超市|官旗|旗舰店|直营|自营)(?:直营|自营)?[】，,。]?❗️?","")
.REGEXREPLACE("[,，?？.。+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🔪💚领券文案 （信息重组专用版）
[🔪🧡领券文案]

.REGEXREPLACE(
[🔪🤍😀商品价格（原始版）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❻].*?[❶-❻]🔞").NTH(1)
.REGEXREPLACE(CHAR(10)&"|[❶-❻🔞🔥‼️✔️]",""),"")

.REGEXREPLACE(
[🔪🤍😀商品价格（原始版）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❻].*?[❶-❻]🔞").NTH(2)
.REGEXREPLACE(CHAR(10)&"|[❶-❻🔞🔥‼️✔️]",""),"")


.REGEXREPLACE(
[🔪🤍😀商品价格（原始版）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❻].*?[❶-❻]🔞").NTH(3)
.REGEXREPLACE(CHAR(10)&"|[❶-❻🔞🔥‼️✔️]",""),"")


.REGEXREPLACE(
[🔪🤍😀商品价格（原始版）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❻].*?[❶-❻]🔞").NTH(4)
.REGEXREPLACE(CHAR(10)&"|[❶-❻🔞🔥‼️✔️]",""),"")


.REGEXREPLACE(
[🔪🤍😀商品价格（原始版）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❻].*?[❶-❻]🔞").NTH(5)
.REGEXREPLACE(CHAR(10)&"|[❶-❻🔞🔥‼️✔️]",""),"")

.REGEXREPLACE(
[🔪🤍😀商品价格（原始版）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❻].*?[❶-❻]🔞").NTH(6)
.REGEXREPLACE(CHAR(10)&"|[❶-❻🔞🔥‼️✔️]",""),"")

.REGEXREPLACE(
[🔪🤍😀商品价格（原始版）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❻].*?[❶-❻]🔞").NTH(7)
.REGEXREPLACE(CHAR(10)&"|[❶-❻🔞🔥‼️✔️]",""),"")


.REGEXREPLACE(
[🔪🤍😀商品价格（原始版）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❻].*?[❶-❻]🔞").NTH(8)
.REGEXREPLACE(CHAR(10)&"|[❶-❻🔞🔥‼️✔️]",""),"")

.REGEXREPLACE(
[🔪🤍😀商品价格（原始版）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❻].*?[❶-❻]🔞").NTH(9)
.REGEXREPLACE(CHAR(10)&"|[❶-❻🔞🔥‼️✔️]",""),"")


.REGEXREPLACE("🚾🚾🚾🚾🚾🚾🚾","无实际意义，为了看信息更方便")


.REGEXREPLACE(
[🔪🤍😀赠送信息（原始版）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❻].*?[❶-❻]🔞").NTH(1)
.REGEXREPLACE(CHAR(10)&"|[❶-❻🔞🔥‼️✔️]",""),"")

.REGEXREPLACE(
[🔪🤍😀赠送信息（原始版）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❻].*?[❶-❻]🔞").NTH(2)
.REGEXREPLACE(CHAR(10)&"|[❶-❻🔞🔥‼️✔️]",""),"")


.REGEXREPLACE(
[🔪🤍😀赠送信息（原始版）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❻].*?[❶-❻]🔞").NTH(3)
.REGEXREPLACE(CHAR(10)&"|[❶-❻🔞🔥‼️✔️]",""),"")


.REGEXREPLACE(
[🔪🤍😀赠送信息（原始版）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❻].*?[❶-❻]🔞").NTH(4)
.REGEXREPLACE(CHAR(10)&"|[❶-❻🔞🔥‼️✔️]",""),"")


.REGEXREPLACE(
[🔪🤍😀赠送信息（原始版）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❻].*?[❶-❻]🔞").NTH(5)
.REGEXREPLACE(CHAR(10)&"|[❶-❻🔞🔥‼️✔️]",""),"")

.REGEXREPLACE(
[🔪🤍😀赠送信息（原始版）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❻].*?[❶-❻]🔞").NTH(6)
.REGEXREPLACE(CHAR(10)&"|[❶-❻🔞🔥‼️✔️]",""),"")

.REGEXREPLACE(
[🔪🤍😀赠送信息（原始版）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❻].*?[❶-❻]🔞").NTH(7)
.REGEXREPLACE(CHAR(10)&"|[❶-❻🔞🔥‼️✔️]",""),"")


.REGEXREPLACE(
[🔪🤍😀赠送信息（原始版）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❻].*?[❶-❻]🔞").NTH(8)
.REGEXREPLACE(CHAR(10)&"|[❶-❻🔞🔥‼️✔️]",""),"")

.REGEXREPLACE(
[🔪🤍😀赠送信息（原始版）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❻].*?[❶-❻]🔞").NTH(9)
.REGEXREPLACE(CHAR(10)&"|[❶-❻🔞🔥‼️✔️]",""),"")


.REGEXREPLACE("🚾🚾🚾🚾🚾🚾🚾","无实际意义，为了看信息更方便")


.REGEXREPLACE(
[🔪💜推荐理由-对标情况+时间紧迫性（唯一值）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL(".*?‼️").NTH(1)
.REGEXREPLACE(CHAR(10)&"|[❶-❻🔞🔥‼️✔️]",""),"")

.REGEXREPLACE(
[🔪💜推荐理由-对标情况+时间紧迫性（唯一值）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL(".*?‼️").NTH(2)
.REGEXREPLACE(CHAR(10)&"|[❶-❻🔞🔥‼️✔️]",""),"")

.REGEXREPLACE(
[🔪💜推荐理由-对标情况+时间紧迫性（唯一值）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL(".*?‼️").NTH(3)
.REGEXREPLACE(CHAR(10)&"|[❶-❻🔞🔥‼️✔️]",""),"")

.REGEXREPLACE(
[🔪💜推荐理由-对标情况+时间紧迫性（唯一值）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL(".*?‼️").NTH(4)
.REGEXREPLACE(CHAR(10)&"|[❶-❻🔞🔥‼️✔️]",""),"")



.REGEXREPLACE(
[🔪💜推荐理由-对标情况+时间紧迫性（唯一值）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL(".*?‼️").NTH(5)
.REGEXREPLACE(CHAR(10)&"|[❶-❻🔞🔥‼️✔️]",""),"")



.REGEXREPLACE(
[🔪💜推荐理由-对标情况+时间紧迫性（唯一值）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL(".*?‼️").NTH(6)
.REGEXREPLACE(CHAR(10)&"|[❶-❻🔞🔥‼️✔️]",""),"")


.REGEXREPLACE(
[🔪💜推荐理由-对标情况+时间紧迫性（唯一值）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL(".*?‼️").NTH(3)
.REGEXREPLACE(CHAR(10)&"|[❶-❻🔞🔥‼️✔️]",""),"")



.REGEXREPLACE("🚾🚾🚾🚾🚾🚾🚾","无实际意义，为了看信息更方便")



.REGEXREPLACE(
[🔪💜推荐理由-常规优势(唯一值)]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL(".*?✔️").NTH(1)
.REGEXREPLACE(CHAR(10)&"|[❶-❻🔞🔥‼️✔️]",""),"")

.REGEXREPLACE(
[🔪💜推荐理由-常规优势(唯一值)]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL(".*?✔️").NTH(2)
.REGEXREPLACE(CHAR(10)&"|[❶-❻🔞🔥‼️✔️]",""),"")


.REGEXREPLACE(
[🔪💜推荐理由-常规优势(唯一值)]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL(".*?✔️").NTH(3)
.REGEXREPLACE(CHAR(10)&"|[❶-❻🔞🔥‼️✔️]",""),"")


.REGEXREPLACE(
[🔪💜推荐理由-常规优势(唯一值)]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL(".*?✔️").NTH(4)
.REGEXREPLACE(CHAR(10)&"|[❶-❻🔞🔥‼️✔️]",""),"")

.REGEXREPLACE(
[🔪💜推荐理由-常规优势(唯一值)]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL(".*?✔️").NTH(5)
.REGEXREPLACE(CHAR(10)&"|[❶-❻🔞🔥‼️✔️]",""),"")


.REGEXREPLACE(
[🔪💜推荐理由-常规优势(唯一值)]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL(".*?✔️").NTH(6)
.REGEXREPLACE(CHAR(10)&"|[❶-❻🔞🔥‼️✔️]",""),"")




.REGEXREPLACE("🚾🚾🚾🚾🚾🚾🚾","无实际意义，为了看信息更方便")


.REGEXREPLACE(
[🔪💜推荐理由-运输优势（唯一值）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL(".*?✔️").NTH(1)
.REGEXREPLACE(CHAR(10)&"|[❶-❻🔞🔥‼️✔️]",""),"")

.REGEXREPLACE(
[🔪💜推荐理由-运输优势（唯一值）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL(".*?✔️").NTH(2)
.REGEXREPLACE(CHAR(10)&"|[❶-❻🔞🔥‼️✔️]",""),"")


.REGEXREPLACE(
[🔪💜推荐理由-运输优势（唯一值）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL(".*?✔️").NTH(3)
.REGEXREPLACE(CHAR(10)&"|[❶-❻🔞🔥‼️✔️]",""),"")



.REGEXREPLACE(
[🔪💜推荐理由-运输优势（唯一值）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL(".*?✔️").NTH(4)
.REGEXREPLACE(CHAR(10)&"|[❶-❻🔞🔥‼️✔️]",""),"")

.REGEXREPLACE(
[🔪💜推荐理由-运输优势（唯一值）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL(".*?✔️").NTH(5)
.REGEXREPLACE(CHAR(10)&"|[❶-❻🔞🔥‼️✔️]",""),"")


.REGEXREPLACE(
[🔪💜推荐理由-运输优势（唯一值）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL(".*?✔️").NTH(6)
.REGEXREPLACE(CHAR(10)&"|[❶-❻🔞🔥‼️✔️]",""),"")



.REGEXREPLACE("🚾🚾🚾🚾🚾🚾🚾","无实际意义，为了看信息更方便")

.REGEXREPLACE("º","")

.REGEXREPLACE("[❶-❾]\s*[❶-❾]🔞","")
.REGEXREPLACE("([❶-❾])([\s\S]*?)([❶-❾]🔞)","$1"&[🔪随机表情-勿删！]&"$2$3")

.REGEXREPLACE("\\("&[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠需转义符号-逻辑]!="").[🧠需转义符号-逻辑]&")","$1")

.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🔪🧡下单文案
CONCATENATE(
[📁🧡商品信息（补全版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(1)
.REGEXEXTRACTALL([🧠下单文案-逻辑])


.ARRAYJOIN("
")
.REGEXREPLACE([🧠下单文案-逻辑]，"❶$0❶🔞")



，CHAR(10)，




[📁🧡商品信息（补全版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(2)
.REGEXEXTRACTALL([🧠下单文案-逻辑])
.ARRAYJOIN("
")
.REGEXREPLACE([🧠下单文案-逻辑]，"❷$0❷🔞")




，CHAR(10)，



[📁🧡商品信息（补全版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(3)
.REGEXEXTRACTALL([🧠下单文案-逻辑])
.ARRAYJOIN("
")
.REGEXREPLACE([🧠下单文案-逻辑]，"❸$0❸🔞")




，CHAR(10)，




[📁🧡商品信息（补全版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(4)
.REGEXEXTRACTALL([🧠下单文案-逻辑])
.ARRAYJOIN("
")
.REGEXREPLACE([🧠下单文案-逻辑]，"❹$0❹🔞")




，CHAR(10)，



[📁🧡商品信息（补全版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(5)
.REGEXEXTRACTALL([🧠下单文案-逻辑])
.ARRAYJOIN("
")
.REGEXREPLACE([🧠下单文案-逻辑]，"❺$0❺🔞")





，CHAR(10)，



[📁🧡商品信息（补全版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(6)
.REGEXEXTRACTALL([🧠下单文案-逻辑])
.ARRAYJOIN("
")
.REGEXREPLACE([🧠下单文案-逻辑]，"❻$0❻🔞")


，CHAR(10)，

[📁🧡商品信息（补全版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(7)
.REGEXEXTRACTALL([🧠下单文案-逻辑])
.ARRAYJOIN("
")
.REGEXREPLACE([🧠下单文案-逻辑],"❼$0❼🔞")


，CHAR(10)，

[📁🧡商品信息（补全版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(8)
.REGEXEXTRACTALL([🧠下单文案-逻辑])
.ARRAYJOIN("
")
.REGEXREPLACE([🧠下单文案-逻辑],"❽$0❽🔞")


，CHAR(10)，

[📁🧡商品信息（补全版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(9)
.REGEXEXTRACTALL([🧠下单文案-逻辑])
.ARRAYJOIN("
")
.REGEXREPLACE([🧠下单文案-逻辑],"❾$0❾🔞")


)
.REGEXREPLACE("🪜","楼上")
.REGEXREPLACE(" ","")
.REGEXREPLACE("[,，。]?"&[🧠折算价格-逻辑],"")
.REGEXREPLACE("([❶-❾])(.*)","$1$2$1🔞")
.REGEXREPLACE("(❶❶|❷❷|❸❸|❹❹|❺❺|❻❻|❼❼|❽❽|❾❾)🔞([❶-❾]🔞)?","")

.REGEXREPLACE("[,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🔪💚下单文案 （信息重组专用版）
CONCATENATE(

IF(
[🔪🧡下单文案]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(1)
.ISBLANK()
,"",

IF(
OR(
[🔪💚领券文案 （信息重组专用版）]
.REGEXREPLACE("[❶-❾🔞]","")
.CONTAINTEXT(
[🔪🧡下单文案]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(1)
.REGEXREPLACE("[❶-❾🔞]",""))
,
[🔪💚商品价格（信息重组专用版）]
.REGEXREPLACE("[❶-❾🔞]","")
.CONTAINTEXT(
[🔪🧡下单文案]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(1)
.REGEXREPLACE("[❶-❾🔞]",""))

,
[🔪🤍😀拍凑份数(原始版)]
.REGEXREPLACE("[❶-❾🔞]","")
.CONTAINTEXT(
[🔪🧡下单文案]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(1)
.REGEXREPLACE("[❶-❾🔞]",""))

)

,
[🔪🧡下单文案]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(1)
.REGEXREPLACE("[❶-❾].*?[❶-❾]🔞","")
,
[🔪🧡下单文案]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(1)
))

,CHAR(10),


IF(
[🔪🧡下单文案]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(2)
.ISBLANK()
,"",

IF(
OR(
[🔪💚领券文案 （信息重组专用版）]
.REGEXREPLACE("[❶-❾🔞]","")
.CONTAINTEXT(
[🔪🧡下单文案]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(2)
.REGEXREPLACE("[❶-❾🔞]",""))
,
[🔪💚商品价格（信息重组专用版）]
.REGEXREPLACE("[❶-❾🔞]","")
.CONTAINTEXT(
[🔪🧡下单文案]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(2)
.REGEXREPLACE("[❶-❾🔞]",""))

,
[🔪🤍😀拍凑份数(原始版)]
.REGEXREPLACE("[❶-❾🔞]","")
.CONTAINTEXT(
[🔪🧡下单文案]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(2)
.REGEXREPLACE("[❶-❾🔞]",""))

)

,
[🔪🧡下单文案]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(2)
.REGEXREPLACE("[❶-❾].*?[❶-❾]🔞","")
,
[🔪🧡下单文案]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(2)
))



,CHAR(10),


IF(
[🔪🧡下单文案]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(3)
.ISBLANK()
,"",

IF(
OR(
[🔪💚领券文案 （信息重组专用版）]
.REGEXREPLACE("[❶-❾🔞]","")
.CONTAINTEXT(
[🔪🧡下单文案]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(3)
.REGEXREPLACE("[❶-❾🔞]",""))
,
[🔪💚商品价格（信息重组专用版）]
.REGEXREPLACE("[❶-❾🔞]","")
.CONTAINTEXT(
[🔪🧡下单文案]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(3)
.REGEXREPLACE("[❶-❾🔞]",""))

,
[🔪🤍😀拍凑份数(原始版)]
.REGEXREPLACE("[❶-❾🔞]","")
.CONTAINTEXT(
[🔪🧡下单文案]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(3)
.REGEXREPLACE("[❶-❾🔞]",""))

)

,
[🔪🧡下单文案]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(3)
.REGEXREPLACE("[❶-❾].*?[❶-❾]🔞","")
,
[🔪🧡下单文案]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(3)
))




,CHAR(10),


IF(
[🔪🧡下单文案]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(4)
.ISBLANK()
,"",

IF(
OR(
[🔪💚领券文案 （信息重组专用版）]
.REGEXREPLACE("[❶-❾🔞]","")
.CONTAINTEXT(
[🔪🧡下单文案]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(4)
.REGEXREPLACE("[❶-❾🔞]",""))
,
[🔪💚商品价格（信息重组专用版）]
.REGEXREPLACE("[❶-❾🔞]","")
.CONTAINTEXT(
[🔪🧡下单文案]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(4)
.REGEXREPLACE("[❶-❾🔞]",""))

,
[🔪🤍😀拍凑份数(原始版)]
.REGEXREPLACE("[❶-❾🔞]","")
.CONTAINTEXT(
[🔪🧡下单文案]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(4)
.REGEXREPLACE("[❶-❾🔞]",""))

)

,
[🔪🧡下单文案]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(4)
.REGEXREPLACE("[❶-❾].*?[❶-❾]🔞","")
,
[🔪🧡下单文案]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(4)
))



,CHAR(10),


IF(
[🔪🧡下单文案]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(5)
.ISBLANK()
,"",

IF(
OR(
[🔪💚领券文案 （信息重组专用版）]
.REGEXREPLACE("[❶-❾🔞]","")
.CONTAINTEXT(
[🔪🧡下单文案]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(5)
.REGEXREPLACE("[❶-❾🔞]",""))
,
[🔪💚商品价格（信息重组专用版）]
.REGEXREPLACE("[❶-❾🔞]","")
.CONTAINTEXT(
[🔪🧡下单文案]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(5)
.REGEXREPLACE("[❶-❾🔞]",""))

,
[🔪🤍😀拍凑份数(原始版)]
.REGEXREPLACE("[❶-❾🔞]","")
.CONTAINTEXT(
[🔪🧡下单文案]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(5)
.REGEXREPLACE("[❶-❾🔞]",""))

)

,
[🔪🧡下单文案]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(5)
.REGEXREPLACE("[❶-❾].*?[❶-❾]🔞","")
,
[🔪🧡下单文案]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(5)
))




,CHAR(10),


IF(
[🔪🧡下单文案]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(6)
.ISBLANK()
,"",

IF(
OR(
[🔪💚领券文案 （信息重组专用版）]
.REGEXREPLACE("[❶-❾🔞]","")
.CONTAINTEXT(
[🔪🧡下单文案]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(6)
.REGEXREPLACE("[❶-❾🔞]",""))
,
[🔪💚商品价格（信息重组专用版）]
.REGEXREPLACE("[❶-❾🔞]","")
.CONTAINTEXT(
[🔪🧡下单文案]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(6)
.REGEXREPLACE("[❶-❾🔞]",""))

,
[🔪🤍😀拍凑份数(原始版)]
.REGEXREPLACE("[❶-❾🔞]","")
.CONTAINTEXT(
[🔪🧡下单文案]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(6)
.REGEXREPLACE("[❶-❾🔞]",""))

)

,
[🔪🧡下单文案]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(6)
.REGEXREPLACE("[❶-❾].*?[❶-❾]🔞","")
,
[🔪🧡下单文案]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(6)
))



,CHAR(10),



IF(
[🔪🧡下单文案]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(7)
.ISBLANK()
,"",

IF(
OR(
[🔪💚领券文案 （信息重组专用版）]
.REGEXREPLACE("[❶-❾🔞]","")
.CONTAINTEXT(
[🔪🧡下单文案]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(7)
.REGEXREPLACE("[❶-❾🔞]",""))
,
[🔪💚商品价格（信息重组专用版）]
.REGEXREPLACE("[❶-❾🔞]","")
.CONTAINTEXT(
[🔪🧡下单文案]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(7)
.REGEXREPLACE("[❶-❾🔞]",""))

,
[🔪🤍😀拍凑份数(原始版)]
.REGEXREPLACE("[❶-❾🔞]","")
.CONTAINTEXT(
[🔪🧡下单文案]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(7)
.REGEXREPLACE("[❶-❾🔞]",""))

)

,
[🔪🧡下单文案]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(7)
.REGEXREPLACE("[❶-❾].*?[❶-❾]🔞","")
,
[🔪🧡下单文案]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(7)
))


,CHAR(10),



IF(
[🔪🧡下单文案]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(8)
.ISBLANK()
,"",

IF(
OR(
[🔪💚领券文案 （信息重组专用版）]
.REGEXREPLACE("[❶-❾🔞]","")
.CONTAINTEXT(
[🔪🧡下单文案]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(8)
.REGEXREPLACE("[❶-❾🔞]",""))
,
[🔪💚商品价格（信息重组专用版）]
.REGEXREPLACE("[❶-❾🔞]","")
.CONTAINTEXT(
[🔪🧡下单文案]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(8)
.REGEXREPLACE("[❶-❾🔞]",""))

,
[🔪🤍😀拍凑份数(原始版)]
.REGEXREPLACE("[❶-❾🔞]","")
.CONTAINTEXT(
[🔪🧡下单文案]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(8)
.REGEXREPLACE("[❶-❾🔞]",""))

)

,
[🔪🧡下单文案]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(8)
.REGEXREPLACE("[❶-❾].*?[❶-❾]🔞","")
,
[🔪🧡下单文案]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(8)
))


,CHAR(10),


IF(
[🔪🧡下单文案]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(9)
.ISBLANK()
,"",

IF(
OR(
[🔪💚领券文案 （信息重组专用版）]
.REGEXREPLACE("[❶-❾🔞]","")
.CONTAINTEXT(
[🔪🧡下单文案]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(9)
.REGEXREPLACE("[❶-❾🔞]",""))
,
[🔪💚商品价格（信息重组专用版）]
.REGEXREPLACE("[❶-❾🔞]","")
.CONTAINTEXT(
[🔪🧡下单文案]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(9)
.REGEXREPLACE("[❶-❾🔞]",""))

,
[🔪🤍😀拍凑份数(原始版)]
.REGEXREPLACE("[❶-❾🔞]","")
.CONTAINTEXT(
[🔪🧡下单文案]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(9)
.REGEXREPLACE("[❶-❾🔞]",""))

)

,
[🔪🧡下单文案]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(9)
.REGEXREPLACE("[❶-❾].*?[❶-❾]🔞","")
,
[🔪🧡下单文案]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(9)
))



)
.REGEXREPLACE("º","")
.REGEXREPLACE("\\("&[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠需转义符号-逻辑]!="").[🧠需转义符号-逻辑]&")","$1")

.REGEXREPLACE("([❶-❾])([\s\S]*?)([❶-❾]🔞)","$1"&[🔪随机表情-勿删！]&"$2$3")

.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🔪🤍😀商品介绍
CONCATENATE(
[📁🤍😀商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(1)
.REGEXEXTRACTALL([🧠商品介绍-逻辑])
.ARRAYJOIN(CHAR(10))
.REGEXREPLACE([🧠商品介绍-逻辑]，"❶$0❶🔞")

，CHAR(10)，


[📁🤍😀商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(2)
.REGEXEXTRACTALL([🧠商品介绍-逻辑])
.ARRAYJOIN(CHAR(10))
.REGEXREPLACE([🧠商品介绍-逻辑]，"❷$0❷🔞")


，CHAR(10)，


[📁🤍😀商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(3)
.REGEXEXTRACTALL([🧠商品介绍-逻辑])
.ARRAYJOIN(CHAR(10))
.REGEXREPLACE([🧠商品介绍-逻辑]，"❸$0❸🔞")

，CHAR(10)，


[📁🤍😀商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(4)
.REGEXEXTRACTALL([🧠商品介绍-逻辑])
.ARRAYJOIN(CHAR(10))
.REGEXREPLACE([🧠商品介绍-逻辑]，"❹$0❹🔞")


，CHAR(10)，


[📁🤍😀商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(5)
.REGEXEXTRACTALL([🧠商品介绍-逻辑])
.ARRAYJOIN(CHAR(10))
.REGEXREPLACE([🧠商品介绍-逻辑]，"❺$0❺🔞")


，CHAR(10)，


[📁🤍😀商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(6)
.REGEXEXTRACTALL([🧠商品介绍-逻辑])
.ARRAYJOIN(CHAR(10))
.REGEXREPLACE([🧠商品介绍-逻辑]，"❻$0❻🔞")


，CHAR(10)，

[📁🤍😀商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(7)
.REGEXEXTRACTALL([🧠商品介绍-逻辑])
.ARRAYJOIN(CHAR(10))
.REGEXREPLACE([🧠商品介绍-逻辑],"❼$0❼🔞")

，CHAR(10)，

[📁🤍😀商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(8)
.REGEXEXTRACTALL([🧠商品介绍-逻辑])
.ARRAYJOIN(CHAR(10))
.REGEXREPLACE([🧠商品介绍-逻辑],"❽$0❽🔞")

，CHAR(10)，

[📁🤍😀商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(9)
.REGEXEXTRACTALL([🧠商品介绍-逻辑])
.ARRAYJOIN(CHAR(10))
.REGEXREPLACE([🧠商品介绍-逻辑],"❾$0❾🔞")


)


.REGEXREPLACE("🔗","")


.REGEXREPLACE(".*?"&[🧠领券文案-逻辑]&"|"&[🧠下单文案-逻辑]&"|"&[🧠赠送信息-逻辑]&"|"&[🧠保质期-逻辑]&"|"&[🧠拍凑份数-逻辑]&".*","")

.REGEXREPLACE(".*?(\*|到手|💰|🧧|饿了么|美团外卖|寄快递|复制|收藏|[Pp][Ll][Uu][Ss]|88[Vv][Ii][Pp]|首单|凑单).*","")
.REGEXREPLACE(".*?(贵|优惠|充值|手机|便宜|拍|领|凑单?|加[购车]|任选|到手|相当于|换算|最[高低抵扣折].*?\d+).*","")
.REGEXREPLACE(".*?\d+("&[🧠单位合集]&")(\*|才|折|\/).*","")
.REGEXREPLACE(".*?\d+(\/)("&[🧠单位合集]&").*","")


.REGEXREPLACE(".*?"&[🧠品牌-逻辑]&".*","")
.REGEXREPLACE("(.*?\d+("&[🧠单位合集]&").*)","")

.REGEXREPLACE("〰〰〰","")


.REGEXREPLACE("([❶-❾])\n","$1")
.REGEXREPLACE("\n([❶-❾]🔞)","$1")

.REGEXREPLACE("^(?:\s*([❶-❾🔞]|\d+(?:"&[🧠单位合集]&")?)+\s*)$","")

.REGEXREPLACE("[，，；。]\s*([❶-❾]🔞)","$1")
.REGEXREPLACE("[❶-❾]\s*([领拍凑加领叠]|\d+(?:\.\d+)?[:：]?)\s*[❶-❾]🔞",)
.REGEXREPLACE("(❶\s*❶|❷\s*❷|❸\s*❸|❹\s*❹|❺\s*❺|❻\s*❻|❼\s*❼|❽\s*❽|❾\s*❾)🔞","")



.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🔪💚商品介绍 （信息重组专用版）
IF([🍬商品信息（原始版）].ISBLANK(),""，
CONCATENATE(
IF(
[🔪💛推荐理由（合并版）]

.REGEXMATCH(
[🔪🤍😀商品介绍]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?🔞").NTH(1)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""))

，""

，
[🔪🤍😀商品介绍]
.REGEXEXTRACTALL("[❶-❾].*?🔞").NTH(1))




,CHAR(10),

IF(
[🔪💛推荐理由（合并版）]

.REGEXMATCH(
[🔪🤍😀商品名称（原始版）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?🔞").NTH(2)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""))

，""

，
[🔪🤍😀商品介绍]
.REGEXEXTRACTALL("[❶-❾].*?🔞").NTH(2))





,CHAR(10),

IF(
[🔪💛推荐理由（合并版）]

.REGEXMATCH(
[🔪🤍😀商品名称（原始版）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?🔞").NTH(3)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""))

，""

，
[🔪🤍😀商品介绍]
.REGEXEXTRACTALL("[❶-❾].*?🔞").NTH(3))



,CHAR(10),

IF(
[🔪💛推荐理由（合并版）]

.REGEXMATCH(
[🔪🤍😀商品名称（原始版）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?🔞").NTH(4)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""))

，""

，
[🔪🤍😀商品介绍]
.REGEXEXTRACTALL("[❶-❾].*?🔞").NTH(4))




,CHAR(10),

IF(
[🔪💛推荐理由（合并版）]

.REGEXMATCH(
[🔪🤍😀商品名称（原始版）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?🔞").NTH(5)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""))

，""

，
[🔪🤍😀商品介绍]
.REGEXEXTRACTALL("[❶-❾].*?🔞").NTH(5))





,CHAR(10),

IF(
[🔪💛推荐理由（合并版）]

.REGEXMATCH(
[🔪🤍😀商品名称（原始版）]
.REGEXREPLACE([🧠需转义符号-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾].*?🔞").NTH(6)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""))

，""

，
[🔪🤍😀商品介绍]
.REGEXEXTRACTALL("[❶-❾].*?🔞").NTH(6))

))
.REGEXREPLACE("🚾🚾🚾🚾🚾🚾🚾","无实际意义，为了看信息更方便")

.REGEXEXTRACTALL("(.*?(?:[%％]|靓毛|爆毛|化毛|排毛|肠胃|补水|美毛|美发|去毛|增肥|发腮|开结|亮泽|浮毛|除臭|清除口臭|磨牙|吸水|结块|尿骚|冲厕所|除螨|众砂可混|护肠|消化|吸收|泪痕|免疫|滋补|护胃|软便|强健骨骼|牙结石|营养|挑食|锁水|反渗|关节|补钙|健骨|异味|训犬|逗猫|可冲马桶|不留痕|不粘底|解馋|解渴|耳螨|锁臭|释香|净味|秒吸|瞬吸|洁牙|口腔健康|肠道|蠕虫|♎️
|配方|成分|初食率|诱食剂|天然|工艺|质地|♎️
|蛋白|益生菌|脂肪|益生元|含肉量|乳糖|高肉|♎️
|提升|补充|添加|升级|改善|呵护|保护|防护|防腐|安抚|舒缓|缓解|抑制|防止|防治|[加高]密|加厚|预防|流失|创新|自研|[全多]方位|高效|持久|锁味|留香|克星|涵盖|滋养|锁住|有效|消杀|驱杀|杀灭|易吸收|入料|易于|提取|快速|全面|满足|科学|渗透|严选|洗护|还原|♎️
|纯度|浓厚|浓郁|神器|助手|专用|畅销|经典款|标准|系列|推荐|整切|整颗|整只|♎️
|耐用|耐磨|性价比|营养均衡|适口|高品质|纯|轻巧|便捷|优质|入口即化|药效好|品质|真材实料|♎️
|温和|细腻|柔滑|不刺激|清新空气|去火|降火|清热|过瘾|\d+[亿]).*)")

.ARRAYJOIN(10)

.TRIM()
.REGEXREPLACE("º","")


.REGEXREPLACE("\u{1F600}-\u{1F64F}\u{2600}-\u{26FF}", "")
.REGEXREPLACE("[❗️✅]","")

.REGEXREPLACE("^[❶-❾]*🔞","")
.REGEXREPLACE("
[❶-❾]*🔞","")

.REGEXREPLACE("([❶-❾])([\s\S]*?)([❶-❾]🔞)","$1"&[🔪随机表情-勿删！]&"$2$3")

.REGEXREPLACE("\\("&[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠需转义符号-逻辑]!="").[🧠需转义符号-逻辑]&")","$1")

.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🔪🧡下单口令/链接提取
CONCATENATE(
[📁🧡商品信息（补全版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(1)
.REGEXEXTRACTALL([🧠下单口令/链接-逻辑])
.ARRAYJOIN("
")
.REGEXREPLACE([🧠下单口令/链接-逻辑]，"❶$0❶🔞")



，CHAR(10)，



[📁🧡商品信息（补全版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(2)
.REGEXEXTRACTALL([🧠下单口令/链接-逻辑])
.ARRAYJOIN("
")
.REGEXREPLACE([🧠下单口令/链接-逻辑]，"❷$0❷🔞")


，CHAR(10)，




[📁🧡商品信息（补全版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(3)
.REGEXEXTRACTALL([🧠下单口令/链接-逻辑])
.ARRAYJOIN("
")
.REGEXREPLACE([🧠下单口令/链接-逻辑]，"❸$0❸🔞")


，CHAR(10)，



[📁🧡商品信息（补全版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(4)
.REGEXEXTRACTALL([🧠下单口令/链接-逻辑])
.ARRAYJOIN("
")
.REGEXREPLACE([🧠下单口令/链接-逻辑]，"❹$0❹🔞")


，CHAR(10)，



[📁🧡商品信息（补全版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(5)
.REGEXEXTRACTALL([🧠下单口令/链接-逻辑])
.ARRAYJOIN("
")
.REGEXREPLACE([🧠下单口令/链接-逻辑]，"❺$0❺🔞")


，CHAR(10)，




[📁🧡商品信息（补全版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(6)
.REGEXEXTRACTALL([🧠下单口令/链接-逻辑])
.ARRAYJOIN("
")
.REGEXREPLACE([🧠下单口令/链接-逻辑]，"❻$0❻🔞")


，CHAR(10)，

[📁🧡商品信息（补全版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(7)
.REGEXEXTRACTALL([🧠下单口令/链接-逻辑])
.ARRAYJOIN("
")
.REGEXREPLACE([🧠下单口令/链接-逻辑],"❼$0❼🔞")


，CHAR(10)，

[📁🧡商品信息（补全版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(8)
.REGEXEXTRACTALL([🧠下单口令/链接-逻辑])
.ARRAYJOIN("
")
.REGEXREPLACE([🧠下单口令/链接-逻辑],"❽$0❽🔞")

，CHAR(10)，


[📁🧡商品信息（补全版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(9)
.REGEXEXTRACTALL([🧠下单口令/链接-逻辑])
.ARRAYJOIN("
")
.REGEXREPLACE([🧠下单口令/链接-逻辑],"❾$0❾🔞")

)

.REGEXREPLACE([🧠品牌-逻辑],"")
.REGEXREPLACE("❶🔞\s?"&CHAR(10)&"❶",CHAR(10))
.REGEXREPLACE("❷🔞\s?"&CHAR(10)&"❷",CHAR(10))
.REGEXREPLACE("❸🔞\s?"&CHAR(10)&"❸",CHAR(10))
.REGEXREPLACE("❹🔞\s?"&CHAR(10)&"❹",CHAR(10))
.REGEXREPLACE("❺🔞\s?"&CHAR(10)&"❺",CHAR(10))
.REGEXREPLACE("❻🔞\s?"&CHAR(10)&"❻",CHAR(10))
.REGEXREPLACE("❼🔞\s?"&CHAR(10)&"❼",CHAR(10))
.REGEXREPLACE("❽🔞\s?"&CHAR(10)&"❽",CHAR(10))
.REGEXREPLACE("❾🔞\s?"&CHAR(10)&"❾",CHAR(10))


.REGEXEXTRACTALL("[❶-❾][\s\S]*?[❶-❾]🔞")
.ARRAYJOIN(CHAR(10)&"〰️〰️〰️"&CHAR(10))


.REGEXREPLACE("([❶-❾][❶-❾]🔞)(\r?\n(\s*〰️\s*)+)?","")
.REGEXREPLACE("^\s*🔞","")

.REGEXREPLACE("[,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====


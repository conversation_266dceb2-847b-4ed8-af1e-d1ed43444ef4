"""
第二轮处理器：所有表的关联字段
基于第一轮的结果，处理表格间的关联字段
"""

import re
import sys
from pathlib import Path
from typing import Dict, List, Any, Optional
import logging

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from A_main.text_processor import TextProcessor
from A_main.formula_converter import FormulaConverter

class Round2Processor:
    """第二轮处理器：关联字段"""
    
    def __init__(self):
        self.logger = logging.getLogger('Round2Processor')
        self.text_processor = TextProcessor()
        self.formula_converter = FormulaConverter()
    
    def process_all_tables_round2(self, round1_results: Dict[str, Any]) -> Dict[str, Any]:
        """处理所有表格的第二轮字段（关联字段）"""
        self.logger.info("🟡 开始第二轮处理：关联字段")
        
        # 更新结果，在原有基础上添加关联字段
        updated_results = {}
        
        for table_name, round1_data in round1_results.items():
            self.logger.info(f"📊 处理表格关联字段: {table_name}")
            
            try:
                if table_name == "📁 信息获取（基建）":
                    updated_data = self._process_info_table_round2(round1_data, round1_results)
                elif table_name == "Ⓜ️ 品牌识别（基建）":
                    updated_data = self._process_brand_table_round2(round1_data, round1_results)
                elif table_name == "🛍️ 品类管理（基建）":
                    updated_data = self._process_category_table_round2(round1_data, round1_results)
                elif table_name == "🔪 SKU解构（基建）":
                    updated_data = self._process_sku_table_round2(round1_data, round1_results)
                elif table_name == "🧪 规格解构（基建）":
                    updated_data = self._process_spec_table_round2(round1_data, round1_results)
                elif table_name == "💰 价格解构（基建）":
                    updated_data = self._process_price_table_round2(round1_data, round1_results)
                elif table_name == "💻 单价计算（基建）":
                    updated_data = self._process_unit_price_table_round2(round1_data, round1_results)
                elif table_name == "🤖 中转表（工具）":
                    updated_data = self._process_transfer_table_round2(round1_data, round1_results)
                else:
                    # 其他表格保持第一轮结果
                    updated_data = round1_data.copy()
                    updated_data["status"] = "第二轮完成"
                
                updated_results[table_name] = updated_data
                self.logger.info(f"✅ 完成表格关联字段: {table_name}")
                
            except Exception as e:
                self.logger.error(f"❌ 表格关联字段处理失败 {table_name}: {e}")
                updated_data = round1_data.copy()
                updated_data["status"] = "第二轮失败"
                updated_data["error"] = str(e)
                updated_results[table_name] = updated_data
        
        self.logger.info("🎉 第二轮处理完成")
        return updated_results
    
    def _process_info_table_round2(self, round1_data: Dict, all_results: Dict) -> Dict[str, Any]:
        """处理信息获取表的关联字段"""
        result = round1_data.copy()
        result["status"] = "第二轮完成"
        
        # 关联品牌识别表的结果
        brand_table = all_results.get("Ⓜ️ 品牌识别（基建）", {})
        if brand_table:
            # Ⓜ️🤍‼️品牌（标准格式）
            brand_standard = brand_table.get("Ⓜ️🤍品牌（唯一值）", "")
            result["Ⓜ️🤍‼️品牌（标准格式）"] = self._standardize_brand(brand_standard)
            
            # Ⓜ️✍️品牌填写判断（手动）
            result["Ⓜ️✍️品牌填写判断（手动）"] = brand_table.get("Ⓜ️✍️品牌识别进度（手动）", "")
        
        # 关联规格解构表的结果
        spec_table = all_results.get("🧪 规格解构（基建）", {})
        if spec_table:
            # 💰到手价格（终版）
            result["💰到手价格（终版）"] = spec_table.get("💰到手价格（终版）", 0)
        
        # 关联价格解构表的结果
        price_table = all_results.get("💰 价格解构（基建）", {})
        if price_table:
            # 💰💚单价-【基础规格】颗粒度
            unit_price = self._calculate_unit_price(
                price_table.get("💰🤎到手价格（终版）", 0),
                spec_table.get("🧪🤍规格✖️数量提取（原始版）", "")
            )
            result["💰💚单价-【基础规格】颗粒度"] = unit_price
        
        # 关联品类管理表的结果
        category_table = all_results.get("🛍️ 品类管理（基建）", {})
        if category_table:
            # 🛍️🤍一级品类（唯一值）
            result["🛍️🤍一级品类（唯一值）"] = category_table.get("🛍️🤍😀品类-一级品类（原始值）", "")
            
            # 🛍️🤍使用对象（唯一值）
            result["🛍️🤍使用对象（唯一值）"] = category_table.get("🛍️🤍使用对象（原始值）", "")
        
        return result
    
    def _process_brand_table_round2(self, round1_data: Dict, all_results: Dict) -> Dict[str, Any]:
        """处理品牌识别表的关联字段"""
        result = round1_data.copy()
        result["status"] = "第二轮完成"
        
        # 关联信息获取表的分割结果
        info_table = all_results.get("📁 信息获取（基建）", {})
        if info_table:
            # 📁💜商品信息（终版分割版）
            result["📁💜商品信息（终版分割版）"] = info_table.get("📁💜商品信息（终版分割版）", "")
            
            # 基于分割结果进行更精确的品牌识别
            split_info = info_table.get("📁💜商品信息（终版分割版）", "").split("🙂‍↕️🅾️")
            
            # 从每个分割部分提取品牌
            all_brands = []
            for part in split_info:
                if part.strip():
                    brands = self.text_processor.extract_brands(part)
                    all_brands.extend(brands)
            
            # 更新品牌信息
            if all_brands:
                unique_brands = list(set(all_brands))
                result["Ⓜ️🤍品牌（唯一值）"] = "\n".join(unique_brands)
                result["Ⓜ️🤍品牌提取（原始值）"] = "\n".join(all_brands)
        
        # 品牌标准化处理
        brand_unique = result.get("Ⓜ️🤍品牌（唯一值）", "")
        if brand_unique:
            result["Ⓜ️🤍‼️品牌（标准格式）"] = self._standardize_brand(brand_unique)
            result["Ⓜ️品牌识别进度（终版）"] = "B已入品牌库✅"
        else:
            result["Ⓜ️🤍‼️品牌（标准格式）"] = ""
            result["Ⓜ️品牌识别进度（终版）"] = "A暂无品牌👀"
        
        return result
    
    def _process_category_table_round2(self, round1_data: Dict, all_results: Dict) -> Dict[str, Any]:
        """处理品类管理表的关联字段"""
        result = round1_data.copy()
        result["status"] = "第二轮完成"
        
        # 关联信息获取表的分割结果
        info_table = all_results.get("📁 信息获取（基建）", {})
        if info_table:
            # 📁💜商品信息（终版分割版）
            result["📁💜商品信息（终版分割版）"] = info_table.get("📁💜商品信息（终版分割版）", "")
            
            # 基于分割结果进行更精确的品类识别
            split_info = info_table.get("📁💜商品信息（终版分割版）", "").split("🙂‍↕️🅾️")
            
            # 重新分析品类
            primary_categories = []
            secondary_categories = []
            
            for part in split_info:
                if part.strip():
                    primary = self._extract_primary_category_advanced(part)
                    secondary = self._extract_secondary_category_advanced(part)
                    
                    if primary:
                        primary_categories.append(primary)
                    if secondary:
                        secondary_categories.append(secondary)
            
            # 更新品类信息
            if primary_categories:
                result["🛍️🤍😀品类-一级品类（原始值）"] = list(set(primary_categories))[0]  # 取第一个
            
            if secondary_categories:
                result["🛍️🤍😀品类-二级分类（原始值）-优化版"] = list(set(secondary_categories))[0]  # 取第一个
        
        # 品类正确性检查
        primary_category = result.get("🛍️🤍😀品类-一级品类（原始值）", "")
        if primary_category and primary_category != "未分类":
            result["🛍️品类正确性check进度"] = "✅品类已确认"
        else:
            result["🛍️品类正确性check进度"] = "🙅品类待确认"
        
        return result
    
    def _process_sku_table_round2(self, round1_data: Dict, all_results: Dict) -> Dict[str, Any]:
        """处理SKU解构表的关联字段"""
        result = round1_data.copy()
        result["status"] = "第二轮完成"
        
        # 关联信息获取表的结果
        info_table = all_results.get("📁 信息获取（基建）", {})
        if info_table:
            # 📁🤍😀商品信息（纯净分割版）
            result["📁🤍😀商品信息（纯净分割版）"] = info_table.get("📁💜商品信息（终版分割版）", "")
            
            # 📁🧡🖤商品信息组合类型（终版）
            info_type = info_table.get("📁🩵信息类型（唯一值）", "")
            result["📁🧡🖤商品信息组合类型（终版）"] = info_type
        
        # 基于关联信息进行SKU重组
        split_info = result.get("📁🤍😀商品信息（纯净分割版）", "").split("🙂‍↕️🅾️")
        
        # 商品名称信息重组专用版
        product_names = []
        for i, part in enumerate(split_info, 1):
            if part.strip():
                cleaned_name = self._extract_product_name_advanced(part)
                if cleaned_name:
                    product_names.append(f"❶{cleaned_name}❶🔞")
        
        result["🔪💚商品名称（信息重组专用版）"] = "".join(product_names)
        
        return result
    
    def _process_spec_table_round2(self, round1_data: Dict, all_results: Dict) -> Dict[str, Any]:
        """处理规格解构表的关联字段"""
        result = round1_data.copy()
        result["status"] = "第二轮完成"
        
        # 关联SKU解构表的结果
        sku_table = all_results.get("🔪 SKU解构（基建）", {})
        if sku_table:
            # 🔪💚商品名称（信息重组专用版）
            result["🔪💚商品名称（信息重组专用版）"] = sku_table.get("🔪💚商品名称（信息重组专用版）", "")
        
        # 关联价格解构表的结果
        price_table = all_results.get("💰 价格解构（基建）", {})
        if price_table:
            # 💰到手价格（终版）
            result["💰到手价格（终版）"] = price_table.get("💰🤎到手价格（终版）", 0)
            
            # 💰🤍😀付款类型
            result["💰🤍😀付款类型"] = price_table.get("💰🤍😀付款类型", "")
        
        # 基于关联信息进行规格计算
        product_names = result.get("🔪💚商品名称（信息重组专用版）", "")
        if product_names:
            # 从商品名称中提取规格信息
            specs = self._extract_specs_from_product_names(product_names)
            result["🧪🤍规格✖️数量✖️起拍数量（终版）"] = specs
        
        return result
    
    def _process_price_table_round2(self, round1_data: Dict, all_results: Dict) -> Dict[str, Any]:
        """处理价格解构表的关联字段"""
        result = round1_data.copy()
        result["status"] = "第二轮完成"
        
        # 关联SKU解构表的结果
        sku_table = all_results.get("🔪 SKU解构（基建）", {})
        if sku_table:
            # 🔪🤍😀下单文案
            result["🔪🤍😀下单文案"] = sku_table.get("🔪🧡下单文案", "")
            
            # 🔪🖤sku数量（勿删）
            result["🔪🖤sku数量（勿删）"] = sku_table.get("🔪🖤SKU数量", 0)
        
        # 关联规格解构表的结果
        spec_table = all_results.get("🧪 规格解构（基建）", {})
        if spec_table:
            # 🧪🤍规格类型（原始值）
            result["🧪🤍规格类型（原始值）"] = spec_table.get("🧪🤍规格类型（原始值）", "")
            
            # 🧪🤍规格✖️数量✖️起拍数量
            result["🧪🤍规格✖️数量✖️起拍数量"] = spec_table.get("🧪🤍规格✖️数量提取（原始版）", "")
        
        return result
    
    def _process_unit_price_table_round2(self, round1_data: Dict, all_results: Dict) -> Dict[str, Any]:
        """处理单价计算表的关联字段"""
        result = round1_data.copy()
        result["status"] = "第二轮完成"
        
        # 关联规格解构表的结果
        spec_table = all_results.get("🧪 规格解构（基建）", {})
        price_table = all_results.get("💰 价格解构（基建）", {})
        
        if spec_table and price_table:
            # 🧪🤍规格✖️数量✖️起拍数量（终版）
            specs = spec_table.get("🧪🤍规格✖️数量✖️起拍数量（终版）", "")
            result["🧪🤍规格✖️数量✖️起拍数量（终版）"] = specs
            
            # 💰🤎到手价格（终版）
            price = price_table.get("💰🤎到手价格（终版）", 0)
            result["💰🤎到手价格（终版）"] = price
            
            # 计算基础单价
            if specs and price > 0:
                unit_price = self._calculate_advanced_unit_price(price, specs)
                result["💻💚基础单价"] = unit_price
            else:
                result["💻💚基础单价"] = 0
        
        return result
    
    def _process_transfer_table_round2(self, round1_data: Dict, all_results: Dict) -> Dict[str, Any]:
        """处理中转表的关联字段"""
        result = round1_data.copy()
        result["status"] = "第二轮完成"
        
        # 关联品牌识别表
        brand_table = all_results.get("Ⓜ️ 品牌识别（基建）", {})
        if brand_table:
            result["🤖🏷️品牌（标准格式）"] = brand_table.get("Ⓜ️🤍‼️品牌（标准格式）", "")
        
        # 关联品类管理表
        category_table = all_results.get("🛍️ 品类管理（基建）", {})
        if category_table:
            result["🤖🏷️一级品类"] = category_table.get("🛍️🤍😀品类-一级品类（原始值）", "")
            result["🤖🏷️使用对象"] = category_table.get("🛍️🤍使用对象（原始值）", "")
        
        # 关联信息获取表
        info_table = all_results.get("📁 信息获取（基建）", {})
        if info_table:
            result["🤖🏷️信息类型"] = info_table.get("📁🩵信息类型（唯一值）", "")
            result["🤖🏷️平台"] = info_table.get("📁🩵所属平台", "")
        
        # 关联价格表
        price_table = all_results.get("💰 价格解构（基建）", {})
        if price_table:
            result["🤖🏷️到手价格"] = price_table.get("💰🤎到手价格（终版）", 0)
        
        # 关联单价计算表
        unit_price_table = all_results.get("💻 单价计算（基建）", {})
        if unit_price_table:
            result["🤖🏷️基础单价"] = unit_price_table.get("💻💚基础单价", 0)
        
        # 设置信息组合类型
        info_type = result.get("🤖🏷️信息类型", "")
        platform = result.get("🤖🏷️平台", "")
        
        if info_type and platform:
            result["🤖🏷️信息组合类型"] = f"{platform}-{info_type}"
        
        return result
    
    # 辅助方法
    def _standardize_brand(self, brand_text: str) -> str:
        """品牌标准化"""
        if not brand_text:
            return ""
        
        # 简单的品牌标准化逻辑
        brands = brand_text.split("\n")
        standardized = []
        
        for brand in brands:
            brand = brand.strip()
            if brand:
                # 基本清理
                brand = re.sub(r'[^\w\s]', '', brand)
                brand = brand.title()  # 首字母大写
                standardized.append(brand)
        
        return "\n".join(standardized)
    
    def _calculate_unit_price(self, price: float, specs: str) -> float:
        """计算单价"""
        if not price or not specs:
            return 0
        
        # 简化的单价计算
        # 提取数字规格
        numbers = re.findall(r'\d+(?:\.\d+)?', specs)
        if numbers:
            total_quantity = sum(float(n) for n in numbers)
            if total_quantity > 0:
                return round(price / total_quantity, 2)
        
        return price
    
    def _extract_primary_category_advanced(self, text: str) -> str:
        """高级一级品类提取"""
        categories = {
            "主粮": ["主粮", "猫粮", "狗粮", "粮食"],
            "零食": ["零食", "小食", "肉干", "冻干", "罐头"],
            "用品": ["用品", "玩具", "窝", "碗", "牵引"],
            "医疗": ["驱虫", "药品", "医疗", "保健"],
            "清洁": ["猫砂", "清洁", "洗护"]
        }
        
        for category, keywords in categories.items():
            for keyword in keywords:
                if keyword in text:
                    return category
        
        return "未分类"
    
    def _extract_secondary_category_advanced(self, text: str) -> str:
        """高级二级品类提取"""
        categories = {
            "干粮": ["干粮", "颗粒", "kibble"],
            "湿粮": ["湿粮", "罐头", "餐盒", "餐包"],
            "冻干": ["冻干", "freeze", "dried"],
            "肉干": ["肉干", "jerky", "strips"],
            "猫砂": ["猫砂", "litter", "sand"],
            "玩具": ["玩具", "toy", "ball"],
            "驱虫药": ["驱虫", "deworming", "parasite"]
        }
        
        for category, keywords in categories.items():
            for keyword in keywords:
                if keyword.lower() in text.lower():
                    return category
        
        return "其他"
    
    def _extract_product_name_advanced(self, text: str) -> str:
        """高级商品名称提取"""
        # 去除价格、链接、特殊符号
        cleaned = re.sub(r"💰\d+(?:\.\d+)?", "", text)
        cleaned = re.sub(r"https?://\S+", "", cleaned)
        cleaned = re.sub(r"[a-zA-Z0-9]{11,}", "", cleaned)
        cleaned = re.sub(r"[🙂‍↕️🅾️❶-❾🔞]", "", cleaned)
        
        # 保留中文、英文、数字、常见符号
        cleaned = re.sub(r"[^\w\s\.\-\(\)（）]", " ", cleaned)
        cleaned = re.sub(r"\s+", " ", cleaned)
        
        return cleaned.strip()[:30]  # 限制长度
    
    def _extract_specs_from_product_names(self, product_names: str) -> str:
        """从商品名称中提取规格"""
        if not product_names:
            return ""
        
        # 提取所有规格信息
        specs = []
        parts = product_names.split("❶")
        
        for part in parts:
            if "❶🔞" in part:
                content = part.replace("❶🔞", "")
                # 提取规格
                spec_matches = re.findall(r'\d+(?:\.\d+)?(?:g|kg|ml|l|个|只|片|颗)', content)
                specs.extend(spec_matches)
        
        return "🙂‍↕️🅾️".join(specs) if specs else ""
    
    def _calculate_advanced_unit_price(self, price: float, specs: str) -> float:
        """高级单价计算"""
        if not price or not specs:
            return 0
        
        # 解析规格信息
        spec_parts = specs.split("🙂‍↕️🅾️")
        total_weight = 0
        
        for spec in spec_parts:
            # 提取数字和单位
            match = re.match(r'(\d+(?:\.\d+)?)([a-zA-Z]+)', spec)
            if match:
                value, unit = match.groups()
                value = float(value)
                
                # 单位转换为克
                if unit.lower() in ['kg']:
                    value *= 1000
                elif unit.lower() in ['g']:
                    pass  # 已经是克
                elif unit.lower() in ['个', '只', '片', '颗']:
                    value *= 10  # 假设每个10克
                
                total_weight += value
        
        if total_weight > 0:
            return round(price / total_weight * 100, 2)  # 每100g价格
        
        return price

# 测试代码
if __name__ == "__main__":
    # 模拟第一轮结果
    from round1_processor import Round1Processor
    
    processor1 = Round1Processor()
    processor2 = Round2Processor()
    
    # 测试数据
    test_data = {
        "🍬商品信息（原始版）": "爱肯拿 农场盛宴 全猫粮 1.8kg 💰128.5元 u.jd.com/abc123",
        "🍬商品信息ID": "test_001",
        "🍬信息来源 / 发车人": "测试用户"
    }
    
    print("🔵 第一轮处理")
    round1_results = processor1.process_all_tables_round1(test_data)
    
    print("\n🟡 第二轮处理")
    round2_results = processor2.process_all_tables_round2(round1_results)
    
    print("\n📊 第二轮处理结果摘要:")
    for table_name, result in round2_results.items():
        status = result.get("status", "未知")
        print(f"  {table_name}: {status}")
    
    print("\n✅ 第二轮处理测试完成")

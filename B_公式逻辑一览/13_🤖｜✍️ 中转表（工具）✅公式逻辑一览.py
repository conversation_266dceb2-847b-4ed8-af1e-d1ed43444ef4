
🤖编号（最终）
TEXT([🤖｜✍️ 中转表（工具）]
.COUNTIF(CurrentValue.[🤖编号（参考勿删！）]<[🤖编号（参考勿删！）])+1

，"👀000000000"）
======

🤖编号（参考勿删！）
自增数字
=====
🍬商品信息（原始版）
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🤖编号（最终）]).[🍬商品信息（原始版）].LISTCOMBINE()
=====
🍬商品信息ID
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🤖编号（最终）]).[🍬商品信息ID].LISTCOMBINE()
=====
🍬同步到商品数据库进度
自动同步数据
=====
🍬源信息发布时间
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🤖编号（最终）]).[🍬源信息发布时间（自动）]
=====
🍬转链结果
自动同步数据
=====
🍬源信息采集时间
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🤖编号（最终）]).[🍬源信息采集时间（自动）]
=====
🍬🏷️信息来源 / 发车人
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🤖编号（最终）]).[🍬信息来源 / 发车人].LISTCOMBINE()
=====
🌀品类正确性check进度

[🛍️ ｜✍️ 品类｜口味｜制作工艺 ｜ 使用对象 ｜ 成长期（基建）].FILTER(CurrentValue.[🛍️编号（最终）]=[🤖编号（最终）]).[🛍️品类正确性check进度].LISTCOMBINE()
=====
🌀品牌填写check进度
[Ⓜ️ ｜ ✍️ 品牌识别（基建）].FILTER(CurrentValue.[Ⓜ️编号（最终）]=[🤖编号（最终）]).[Ⓜ️品牌填写check进度].LISTCOMBINE().UNIQUE()

.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🌀解构进度
IF(
[🍬商品信息（原始版）]
.ISBLANK(),
""，

IF(
AND(
[🤖🏷️信息组合类型]
.ISBLANK()
.NOT()
，

[🤖🏷️平台]
.ISBLANK()
.NOT()


),"✅Ready"，"🙅𝑖𝑛𝑔"))
=====
🌀是否入《商品数据》库
IF(
[🍬商品信息（原始版）]
.ISBLANK()
,"",


IF(
[🌀同步到商品数据库进度]
.REGEXMATCH("已同步")
，"💼已入库"，


IF(
AND(
[🌀解构进度]
.REGEXMATCH("✅")
,


[🤖🏷️信息类型]
.REGEXMATCH("🛒")
,

[🤖🏷️使用对象]
.REGEXMATCH("[猫狗犬]")
,


[🤖🏷️SKU重复情况]
.REGEXMATCH("🆕")
，

[🌀品牌填写check进度]
.REGEXMATCH("✅")

，
[🌀品类正确性check进度]
.REGEXMATCH("✅")

，
[🤖🏷️平台]
.REGEXMATCH("淘宝")
，

[🤖🏷️信息组合类型]
.REGEXMATCH("☀️")

)
,"✅待入库"，

"🙅无需入库")))
=====
🌀同步到商品数据库进度
[🍬同步到商品数据库进度]
=====
🌀信息留存时间(天)
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🤖编号（最终）]).[📁✍️信息留存时间（天）]
=====
🌀源信息新鲜度(天)
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🤖编号（最终）]).[📁源信息新鲜度(天)]
=====
🌀源信息发布时间(最终) -精确到天
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🤖编号（最终）]).[📁源信息发布时间(最终) -精确到天]
=====
🌀记录清除/删除判断
IF(
[🌀源信息新鲜度(天)]
>=[🌀信息留存时间(天)]

,"🆑记录待清理"
，""
)
=====
🥝API商品ID
自动同步数据
=====
🤖飞书记录ID
RECORD_ID()
===
🤖商品名称
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[🤖编号（最终）]).[🔪🖤商品名称（字段维护专用）].LISTCOMBINE()
.REGEXREPLACE("[❶-❻🔞]","")

.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🤖赠送详情
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[🤖编号（最终）]).[🔪🖤赠送信息（字段维护专用）].LISTCOMBINE()
.REGEXREPLACE("[❶-❻]\+[❶-❻]🔞","")
.REGEXREPLACE("[❶-❻🔞]","")
.REGEXREPLACE(".*?💰.*","")

.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🤖推荐理由
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[🤖编号（最终）]).[🔪💛先导文案]
.REGEXREPLACE("✨.*✨","")
.REGEXREPLACE("‼️","✔️")
.REGEXREPLACE("(临期|预售)✔️","")
.REGEXREPLACE("(✔️)?.*?发货✔️","$1")
.REGEXREPLACE("^✔️","")


.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🤖商品介绍
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[🤖编号（最终）]).[🔪💚商品介绍 （信息重组专用版）].LISTCOMBINE()
.REGEXREPLACE("[❶-❻🔞]","")

.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🤖保质期
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[🤖编号（最终）]).[🔪💜保质期].LISTCOMBINE()
.REGEXREPLACE("临期","")
.REGEXREPLACE("[❶-❻🔞]","")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")

====
🤖下单文案
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[🤖编号（最终）]).[🔪💚下单文案 （信息重组专用版）].LISTCOMBINE()
.REGEXREPLACE("[❶-❻🔞]","")

.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🤖到手价格
[💰 价格解构（基建）].FILTER(CurrentValue.[💰编号（最终）]=[🤖编号（最终）]).[💰🤎到手价格（终版）].LISTCOMBINE()
.REGEXREPLACE("[❶-❻🔞]","")

.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🤖到手价格详情（重组版）
[💰 价格解构（基建）].FILTER(CurrentValue.[💰编号（最终）]=[🤖编号（最终）]).[💰🖤到手价格详情（字段维护专用）].LISTCOMBINE()
.REGEXREPLACE("[❶-❻🔞]","")

.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🤖单价
[💻 单价计算（基建）].FILTER(CurrentValue.[💻编号（最终）]=[🤖编号（最终）]).[💻🖤单价（字段维护专用）]
.REGEXREPLACE("[❶-❻🔞']","")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🤖商品规格✖️数量
[🧪 规格解构（基建）].FILTER(CurrentValue.[🧪编号（最终）]=[🤖编号（最终）]).[🧪🖤规格✖️数量提取（字段维护专用）]
.REGEXREPLACE("[❶-❻🔞]","")

.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🤖商品信息（组合版）
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🤖编号（最终）]).[📁🧡SKU组合].LISTCOMBINE()
=====
🤖商品信息（重组版）
[🐽 sku重组-社群专用 (基建)].FILTER(CurrentValue.[🐽编号（最终）]=[🤖编号（最终）]).[🐽💚商品信息（重组版）].LISTCOMBINE()
=====
🤖商品信息（发车版）
[🚗｜✍️ 手动发车工作台(工具)❌（暂时不计算）].FILTER(CurrentValue.[🚗编号（最终）]=[🤖编号（最终）]).[🚗💚商品信息（发车版）].LISTCOMBINE()
=====
🤖🏷️平台
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🤖编号（最终）]).[📁所属平台].LISTCOMBINE()
.REGEXREPLACE("[❶-❾🔞]","")
.REGEXREPLACE("(【|】)","")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🤖🏷️二级分类
[🛍️ ｜✍️ 品类｜口味｜制作工艺 ｜ 使用对象 ｜ 成长期（基建）].FILTER(CurrentValue.[🛍️编号（最终）]=[🤖编号（最终）]).[🛍️🤍😀二级品类（唯一值）].LISTCOMBINE()
.REGEXREPLACE("[❶-❾🔞]","")
.REGEXREPLACE("(【|】)","")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🤖🏷️一级分类
[🛍️ ｜✍️ 品类｜口味｜制作工艺 ｜ 使用对象 ｜ 成长期（基建）].FILTER(CurrentValue.[🛍️编号（最终）]=[🤖编号（最终）]).[🛍️🤍😀一级品类（唯一值）].LISTCOMBINE()
.REGEXREPLACE("[❶-❾🔞]","")
.REGEXREPLACE("(【|】)","")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🤖🏷️SKU数量
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[🤖编号（最终）]).[🔪🖤SKU数量].LISTCOMBINE()
=====
🤖🏷️SKU重复情况
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🤖编号（最终）]).[📁⚫️SKU是否重复].LISTCOMBINE()
.REGEXREPLACE("[❶-❻🔞]","")

.REGEXREPLACE("[❶-❾🔞]","")
.REGEXREPLACE("(【|】)","")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🤖🏷️制作工艺
[🛍️ ｜✍️ 品类｜口味｜制作工艺 ｜ 使用对象 ｜ 成长期（基建）].FILTER(CurrentValue.[🛍️编号（最终）]=[🤖编号（最终）]).[🛍️💜制作工艺（唯一值）].LISTCOMBINE()

.REGEXREPLACE("[❶-❾🔞]","")
.REGEXREPLACE("(【|】)","")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🤖🏷️规格类型
[🧪 规格解构（基建）].FILTER(CurrentValue.[🧪编号（最终）]=[🤖编号（最终）]).[🧪规格类型（唯一值）]

.REGEXREPLACE("[❶-❾🔞]","")
.REGEXREPLACE("(【|】)","")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🤖🏷️临期情况
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[🤖编号（最终）]).[🔪💜临期情况].LISTCOMBINE()
.REGEXREPLACE("[❶-❾🔞]","")
.REGEXREPLACE("(【|】)","")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🤖🏷️规格&价格数量对应关系
[💰 价格解构（基建）].FILTER(CurrentValue.[💰编号（最终）]=[🤖编号（最终）]).[💰🩶规格&价格数量对应关系].LISTCOMBINE()
.REGEXREPLACE("[❶-❾🔞]","")
.REGEXREPLACE("(【|】)","")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🤖🏷️信息组合类型
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[🤖编号（最终）]).[📁🧡🖤商品信息组合类型（终版）].LISTCOMBINE()

.REGEXREPLACE("[❶-❾🔞]","")
.REGEXREPLACE("(【|】)","")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🤖🏷️预售情况
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🤖编号（最终）]).[📁预售情况].LISTCOMBINE()

.REGEXREPLACE("[❶-❾🔞]","")
.REGEXREPLACE("(【|】)","")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🤖🏷️付款类型
[💰 价格解构（基建）].FILTER(CurrentValue.[💰编号（最终）]=[🤖编号（最终）]).[💰🤍😀付款类型]

.REGEXREPLACE("[❶-❾🔞]","")
.REGEXREPLACE("(【|】)","")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🤖🏷️所属活动
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[🤖编号（最终）]).[🔪💜所属活动（唯一值）].LISTCOMBINE()
.REGEXREPLACE("[❶-❾🔞]","")
.REGEXREPLACE("(【|】)","")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")

=====
🤖🏷️拼团情况
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[🤖编号（最终）]).[🔪💜拼团].LISTCOMBINE()
.REGEXREPLACE("[❶-❾🔞]","")
.REGEXREPLACE("(【|】)","")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")

=====
🤖🏷️信息类型
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🤖编号（最终）]).[📁🩵信息类型（唯一值）].LISTCOMBINE()
.REGEXREPLACE("[❶-❾🔞]","")
.REGEXREPLACE("(【|】)","")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🤖🏷️链接/口令数量
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🤖编号（最终）]).[📁🧡链接/口令数量（终版）].LISTCOMBINE()
=====
🤖🏷️价格优势
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[🤖编号（最终）]).[🔪💚价格优势（信息重组专用版）].LISTCOMBINE()
.REGEXREPLACE("[❶-❻🔞✨有]","")

.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🤖🏷️会员价情况
[💰 价格解构（基建）].FILTER(CurrentValue.[💰编号（最终）]=[🤖编号（最终）]).[💰🖤会员价类型（字段维护专用）].LISTCOMBINE()
.REGEXREPLACE("[❶-❾🔞]","")
.REGEXREPLACE("(【|】)","")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🤖🏷️使用对象
[🛍️ ｜✍️ 品类｜口味｜制作工艺 ｜ 使用对象 ｜ 成长期（基建）].FILTER(CurrentValue.[🛍️编号（最终）]=[🤖编号（最终）]).[🛍️💜使用对象(唯一值)].LISTCOMBINE()
.REGEXREPLACE("[❶-❾🔞]","")
.REGEXREPLACE("(【|】)","")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🤖🏷️适用宠物成长期
[🛍️ ｜✍️ 品类｜口味｜制作工艺 ｜ 使用对象 ｜ 成长期（基建）].FILTER(CurrentValue.[🛍️编号（最终）]=[🤖编号（最终）]).[🛍️💜适用宠物成长期（唯一值）].LISTCOMBINE()
.REGEXREPLACE("[❶-❾🔞]","")
.REGEXREPLACE("(【|】)","")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🤖🏷️口味
[🛍️ ｜✍️ 品类｜口味｜制作工艺 ｜ 使用对象 ｜ 成长期（基建）].FILTER(CurrentValue.[🛍️编号（最终）]=[🤖编号（最终）]).[🛍️💜口味（唯一值）].LISTCOMBINE()

.REGEXREPLACE("[❶-❾🔞]","")
.REGEXREPLACE("(【|】)","")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🤖🏷️品牌
[Ⓜ️ ｜ ✍️ 品牌识别（基建）].FILTER(CurrentValue.[Ⓜ️编号（最终）]=[🤖编号（最终）]).[Ⓜ️🖤品牌（标准格式）（字段维护专用）].LISTCOMBINE()

.REGEXREPLACE("[❶-❾🔞]","")
.REGEXREPLACE("(【|】)","")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🤖🏷️品牌归属
[Ⓜ️ ｜ ✍️ 品牌识别（基建）].FILTER(CurrentValue.[Ⓜ️编号（最终）]=[🤖编号（最终）]).[Ⓜ️🤍品牌归属].LISTCOMBINE()
.REGEXREPLACE("[❶-❾🔞]","")
.REGEXREPLACE("(【|】)","")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🤖🏷️品牌梯度
[Ⓜ️ ｜ ✍️ 品牌识别（基建）].FILTER(CurrentValue.[Ⓜ️编号（最终）]=[🤖编号（最终）]).[Ⓜ️🤍品牌梯度].LISTCOMBINE()

.REGEXREPLACE("[❶-❾🔞]","")
.REGEXREPLACE("(【|】)","")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")


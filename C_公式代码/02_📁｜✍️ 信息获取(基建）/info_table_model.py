import sys
from pathlib import Path
from datetime import datetime

# 手动指定项目根目录（优先级最高，确保正确）
project_root = "/Volumes/阿方方备份/Nars-个人wiki/Nars-代码管理"
sys.path.append(project_root)  # 只保留这一行路径添加，去掉其他路径计算

# 导入其他模块
from A_连接MongoDB.MongoDB_connector import MongoDBConnector
from A_config.settings import FIELD_MARKERS, ICON_TO_COLLECTION

class InfoTableModel:
    def __init__(self):
        # 数据库连接
        self.connector = MongoDBConnector()
        self.connected = self.connector.connect()
        self.table_name = "信息获取（基建）"
        self.icon = FIELD_MARKERS[self.table_name]
        self.collection = self.connector.get_collection(ICON_TO_COLLECTION[self.icon])
        
        # 字段定义
        self.fields = {
            "原始商品名称": f"{self.icon}原始商品名称",
            "清洗后商品名称": f"{self.icon}清洗后商品名称",
            "品牌ID": f"{self.icon}品牌ID",
            "品牌评分": f"{self.icon}品牌评分",
            "数据状态": f"{self.icon}数据状态"
        }

    def insert(self, data):
        """插入数据"""
        if not self.connected:
            return None
            
        try:
            data["插入时间"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            return self.collection.insert_one(data).inserted_id
        except Exception as e:
            print(f"❌ 插入失败: {e}")
            return None

    def update(self, data_id, field, value):
        """更新字段"""
        if not self.connected:
            return False
            
        try:
            self.collection.update_one({"_id": data_id}, {"$set": {field: value}})
            return True
        except Exception as e:
            print(f"❌ 更新失败: {e}")
            return False

    def get(self, data_id):
        """查询数据"""
        return self.collection.find_one({"_id": data_id})

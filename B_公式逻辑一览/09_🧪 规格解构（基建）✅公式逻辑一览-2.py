
🧪🤍规格✖️数量提取（单位回溯版）-参考6
CONCATENATE(
IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(1)
.ISBLANK()
，"",

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(1)
.REGEXMATCH("磅")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(1)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷0.90718474"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(1).REGEXEXTRACTALL([🧠基础单位])&"®")
,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(1)
.REGEXMATCH("kg|公斤|千克")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(1)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷2"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(1).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(1)
.REGEXMATCH("克|g")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(1)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2×500"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(1).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(1)
.REGEXMATCH("ml")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(1)
.REGEXREPLACE("((\d+(?:\.\d+)?)l)","®$2×1000"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(1).REGEXEXTRACTALL([🧠基础单位])&"®")

，
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(1)

)))))

,"；"，


IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(2)
.ISBLANK()
，"",

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(2)
.REGEXMATCH("磅")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(2)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷0.90718474"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(2).REGEXEXTRACTALL([🧠基础单位])&"®")
,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(2)
.REGEXMATCH("kg|公斤|千克")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(2)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷2"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(2).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(2)
.REGEXMATCH("克|g")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(2)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2×500"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(2).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(2)
.REGEXMATCH("ml")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(2)
.REGEXREPLACE("((\d+(?:\.\d+)?)l)","®$2×1000"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(2).REGEXEXTRACTALL([🧠基础单位])&"®")

，
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(2)

)))))

,"；"，

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(3)
.ISBLANK()
，"",

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(3)
.REGEXMATCH("磅")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(3)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷0.90718474"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(3).REGEXEXTRACTALL([🧠基础单位])&"®")
,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(3)
.REGEXMATCH("kg|公斤|千克")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(3)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷2"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(3).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(3)
.REGEXMATCH("克|g")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(3)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2×500"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(3).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(3)
.REGEXMATCH("ml")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(3)
.REGEXREPLACE("((\d+(?:\.\d+)?)l)","®$2×1000"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(3).REGEXEXTRACTALL([🧠基础单位])&"®")

，
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(3)

)))))

,"；"，

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(4)
.ISBLANK()
，"",

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(4)
.REGEXMATCH("磅")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(4)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷0.90718474"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(4).REGEXEXTRACTALL([🧠基础单位])&"®")
,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(4)
.REGEXMATCH("kg|公斤|千克")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(4)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷2"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(4).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(4)
.REGEXMATCH("克|g")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(4)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2×500"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(4).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(4)
.REGEXMATCH("ml")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(4)
.REGEXREPLACE("((\d+(?:\.\d+)?)l)","®$2×1000"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(4).REGEXEXTRACTALL([🧠基础单位])&"®")

，
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(4)

)))))

,"；"，

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(5)
.ISBLANK()
，"",

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(5)
.REGEXMATCH("磅")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(5)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷0.90718474"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(5).REGEXEXTRACTALL([🧠基础单位])&"®")
,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(5)
.REGEXMATCH("kg|公斤|千克")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(5)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷2"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(5).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(5)
.REGEXMATCH("克|g")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(5)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2×500"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(5).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(5)
.REGEXMATCH("ml")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(5)
.REGEXREPLACE("((\d+(?:\.\d+)?)l)","®$2×1000"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(5).REGEXEXTRACTALL([🧠基础单位])&"®")

，
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(5)

)))))

,"；"，

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(6)
.ISBLANK()
，"",

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(6)
.REGEXMATCH("磅")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(6)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷0.90718474"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(6).REGEXEXTRACTALL([🧠基础单位])&"®")
,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(6)
.REGEXMATCH("kg|公斤|千克")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(6)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷2"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(6).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(6)
.REGEXMATCH("克|g")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(6)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2×500"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(6).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(6)
.REGEXMATCH("ml")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(6)
.REGEXREPLACE("((\d+(?:\.\d+)?)l)","®$2×1000"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(6).REGEXEXTRACTALL([🧠基础单位])&"®")

，
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(6)

)))))

)

.REGEXREPLACE("；+","；")
.REGEXREPLACE("^；","")
.REGEXREPLACE("；$","")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🧪🤍规格✖️数量提取（单位回溯版）-参考7
CONCATENATE(
IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(1)
.ISBLANK()
，"",

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(1)
.REGEXMATCH("磅")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(1)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷0.90718474"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(1).REGEXEXTRACTALL([🧠基础单位])&"®")
,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(1)
.REGEXMATCH("kg|公斤|千克")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(1)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷2"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(1).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(1)
.REGEXMATCH("克|g")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(1)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2×500"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(1).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(1)
.REGEXMATCH("ml")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(1)
.REGEXREPLACE("((\d+(?:\.\d+)?)l)","®$2×1000"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(1).REGEXEXTRACTALL([🧠基础单位])&"®")

，
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(1)

)))))

,"；"，


IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(2)
.ISBLANK()
，"",

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(2)
.REGEXMATCH("磅")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(2)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷0.90718474"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(2).REGEXEXTRACTALL([🧠基础单位])&"®")
,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(2)
.REGEXMATCH("kg|公斤|千克")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(2)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷2"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(2).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(2)
.REGEXMATCH("克|g")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(2)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2×500"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(2).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(2)
.REGEXMATCH("ml")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(2)
.REGEXREPLACE("((\d+(?:\.\d+)?)l)","®$2×1000"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(2).REGEXEXTRACTALL([🧠基础单位])&"®")

，
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(2)

)))))

,"；"，

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(3)
.ISBLANK()
，"",

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(3)
.REGEXMATCH("磅")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(3)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷0.90718474"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(3).REGEXEXTRACTALL([🧠基础单位])&"®")
,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(3)
.REGEXMATCH("kg|公斤|千克")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(3)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷2"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(3).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(3)
.REGEXMATCH("克|g")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(3)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2×500"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(3).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(3)
.REGEXMATCH("ml")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(3)
.REGEXREPLACE("((\d+(?:\.\d+)?)l)","®$2×1000"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(3).REGEXEXTRACTALL([🧠基础单位])&"®")

，
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(3)

)))))

,"；"，

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(4)
.ISBLANK()
，"",

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(4)
.REGEXMATCH("磅")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(4)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷0.90718474"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(4).REGEXEXTRACTALL([🧠基础单位])&"®")
,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(4)
.REGEXMATCH("kg|公斤|千克")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(4)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷2"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(4).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(4)
.REGEXMATCH("克|g")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(4)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2×500"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(4).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(4)
.REGEXMATCH("ml")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(4)
.REGEXREPLACE("((\d+(?:\.\d+)?)l)","®$2×1000"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(4).REGEXEXTRACTALL([🧠基础单位])&"®")

，
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(4)

)))))

,"；"，

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(5)
.ISBLANK()
，"",

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(5)
.REGEXMATCH("磅")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(5)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷0.90718474"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(5).REGEXEXTRACTALL([🧠基础单位])&"®")
,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(5)
.REGEXMATCH("kg|公斤|千克")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(5)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷2"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(5).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(5)
.REGEXMATCH("克|g")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(5)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2×500"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(5).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(5)
.REGEXMATCH("ml")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(5)
.REGEXREPLACE("((\d+(?:\.\d+)?)l)","®$2×1000"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(5).REGEXEXTRACTALL([🧠基础单位])&"®")

，
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(5)

)))))

,"；"，

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(6)
.ISBLANK()
，"",

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(6)
.REGEXMATCH("磅")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(6)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷0.90718474"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(6).REGEXEXTRACTALL([🧠基础单位])&"®")
,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(6)
.REGEXMATCH("kg|公斤|千克")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(6)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷2"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(6).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(6)
.REGEXMATCH("克|g")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(6)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2×500"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(6).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(6)
.REGEXMATCH("ml")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(6)
.REGEXREPLACE("((\d+(?:\.\d+)?)l)","®$2×1000"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(6).REGEXEXTRACTALL([🧠基础单位])&"®")

，
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(6)

)))))

)

.REGEXREPLACE("；+","；")
.REGEXREPLACE("^；","")
.REGEXREPLACE("；$","")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🧪🤍规格✖️数量提取（单位回溯版）-参考8
CONCATENATE(
IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(1)
.ISBLANK()
，"",

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(1)
.REGEXMATCH("磅")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(1)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷0.90718474"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(1).REGEXEXTRACTALL([🧠基础单位])&"®")
,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(1)
.REGEXMATCH("kg|公斤|千克")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(1)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷2"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(1).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(1)
.REGEXMATCH("克|g")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(1)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2×500"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(1).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(1)
.REGEXMATCH("ml")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(1)
.REGEXREPLACE("((\d+(?:\.\d+)?)l)","®$2×1000"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(1).REGEXEXTRACTALL([🧠基础单位])&"®")

，
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(1)

)))))

,"；"，


IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(2)
.ISBLANK()
，"",

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(2)
.REGEXMATCH("磅")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(2)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷0.90718474"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(2).REGEXEXTRACTALL([🧠基础单位])&"®")
,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(2)
.REGEXMATCH("kg|公斤|千克")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(2)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷2"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(2).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(2)
.REGEXMATCH("克|g")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(2)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2×500"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(2).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(2)
.REGEXMATCH("ml")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(2)
.REGEXREPLACE("((\d+(?:\.\d+)?)l)","®$2×1000"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(2).REGEXEXTRACTALL([🧠基础单位])&"®")

，
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(2)

)))))

,"；"，

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(3)
.ISBLANK()
，"",

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(3)
.REGEXMATCH("磅")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(3)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷0.90718474"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(3).REGEXEXTRACTALL([🧠基础单位])&"®")
,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(3)
.REGEXMATCH("kg|公斤|千克")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(3)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷2"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(3).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(3)
.REGEXMATCH("克|g")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(3)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2×500"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(3).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(3)
.REGEXMATCH("ml")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(3)
.REGEXREPLACE("((\d+(?:\.\d+)?)l)","®$2×1000"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(3).REGEXEXTRACTALL([🧠基础单位])&"®")

，
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(3)

)))))

,"；"，

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(4)
.ISBLANK()
，"",

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(4)
.REGEXMATCH("磅")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(4)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷0.90718474"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(4).REGEXEXTRACTALL([🧠基础单位])&"®")
,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(4)
.REGEXMATCH("kg|公斤|千克")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(4)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷2"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(4).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(4)
.REGEXMATCH("克|g")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(4)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2×500"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(4).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(4)
.REGEXMATCH("ml")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(4)
.REGEXREPLACE("((\d+(?:\.\d+)?)l)","®$2×1000"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(4).REGEXEXTRACTALL([🧠基础单位])&"®")

，
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(4)

)))))

,"；"，

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(5)
.ISBLANK()
，"",

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(5)
.REGEXMATCH("磅")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(5)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷0.90718474"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(5).REGEXEXTRACTALL([🧠基础单位])&"®")
,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(5)
.REGEXMATCH("kg|公斤|千克")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(5)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷2"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(5).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(5)
.REGEXMATCH("克|g")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(5)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2×500"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(5).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(5)
.REGEXMATCH("ml")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(5)
.REGEXREPLACE("((\d+(?:\.\d+)?)l)","®$2×1000"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(5).REGEXEXTRACTALL([🧠基础单位])&"®")

，
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(5)

)))))

,"；"，

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(6)
.ISBLANK()
，"",

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(6)
.REGEXMATCH("磅")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(6)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷0.90718474"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(6).REGEXEXTRACTALL([🧠基础单位])&"®")
,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(6)
.REGEXMATCH("kg|公斤|千克")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(6)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷2"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(6).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(6)
.REGEXMATCH("克|g")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(6)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2×500"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(6).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(6)
.REGEXMATCH("ml")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(6)
.REGEXREPLACE("((\d+(?:\.\d+)?)l)","®$2×1000"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(6).REGEXEXTRACTALL([🧠基础单位])&"®")

，
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(6)

)))))

)

.REGEXREPLACE("；+","；")
.REGEXREPLACE("^；","")
.REGEXREPLACE("；$","")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🧪🤍规格✖️数量提取（单位回溯版）-参考9
CONCATENATE(
IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(1)
.ISBLANK()
，"",

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(1)
.REGEXMATCH("磅")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(1)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷0.90718474"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(1).REGEXEXTRACTALL([🧠基础单位])&"®")
,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(1)
.REGEXMATCH("kg|公斤|千克")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(1)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷2"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(1).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(1)
.REGEXMATCH("克|g")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(1)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2×500"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(1).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(1)
.REGEXMATCH("ml")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(1)
.REGEXREPLACE("((\d+(?:\.\d+)?)l)","®$2×1000"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(1).REGEXEXTRACTALL([🧠基础单位])&"®")

，
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(1)

)))))

,"；"，


IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(2)
.ISBLANK()
，"",

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(2)
.REGEXMATCH("磅")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(2)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷0.90718474"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(2).REGEXEXTRACTALL([🧠基础单位])&"®")
,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(2)
.REGEXMATCH("kg|公斤|千克")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(2)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷2"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(2).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(2)
.REGEXMATCH("克|g")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(2)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2×500"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(2).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(2)
.REGEXMATCH("ml")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(2)
.REGEXREPLACE("((\d+(?:\.\d+)?)l)","®$2×1000"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(2).REGEXEXTRACTALL([🧠基础单位])&"®")

，
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(2)

)))))

,"；"，

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(3)
.ISBLANK()
，"",

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(3)
.REGEXMATCH("磅")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(3)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷0.90718474"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(3).REGEXEXTRACTALL([🧠基础单位])&"®")
,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(3)
.REGEXMATCH("kg|公斤|千克")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(3)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷2"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(3).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(3)
.REGEXMATCH("克|g")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(3)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2×500"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(3).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(3)
.REGEXMATCH("ml")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(3)
.REGEXREPLACE("((\d+(?:\.\d+)?)l)","®$2×1000"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(3).REGEXEXTRACTALL([🧠基础单位])&"®")

，
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(3)

)))))

,"；"，

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(4)
.ISBLANK()
，"",

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(4)
.REGEXMATCH("磅")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(4)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷0.90718474"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(4).REGEXEXTRACTALL([🧠基础单位])&"®")
,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(4)
.REGEXMATCH("kg|公斤|千克")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(4)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷2"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(4).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(4)
.REGEXMATCH("克|g")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(4)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2×500"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(4).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(4)
.REGEXMATCH("ml")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(4)
.REGEXREPLACE("((\d+(?:\.\d+)?)l)","®$2×1000"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(4).REGEXEXTRACTALL([🧠基础单位])&"®")

，
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(4)

)))))

,"；"，

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(5)
.ISBLANK()
，"",

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(5)
.REGEXMATCH("磅")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(5)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷0.90718474"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(5).REGEXEXTRACTALL([🧠基础单位])&"®")
,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(5)
.REGEXMATCH("kg|公斤|千克")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(5)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷2"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(5).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(5)
.REGEXMATCH("克|g")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(5)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2×500"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(5).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(5)
.REGEXMATCH("ml")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(5)
.REGEXREPLACE("((\d+(?:\.\d+)?)l)","®$2×1000"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(5).REGEXEXTRACTALL([🧠基础单位])&"®")

，
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(5)

)))))

,"；"，

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(6)
.ISBLANK()
，"",

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(6)
.REGEXMATCH("磅")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(6)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷0.90718474"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(6).REGEXEXTRACTALL([🧠基础单位])&"®")
,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(6)
.REGEXMATCH("kg|公斤|千克")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(6)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷2"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(6).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(6)
.REGEXMATCH("克|g")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(6)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2×500"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(6).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(6)
.REGEXMATCH("ml")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(6)
.REGEXREPLACE("((\d+(?:\.\d+)?)l)","®$2×1000"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(6).REGEXEXTRACTALL([🧠基础单位])&"®")

，
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(6)

)))))

)

.REGEXREPLACE("；+","；")
.REGEXREPLACE("^；","")
.REGEXREPLACE("；$","")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🧪🤍规格✖️数量提取（单位回溯版）-参考
CONCATENATE(
[🧪🤍规格✖️数量提取（单位回溯版）-参考1]
，CHAR(10)，
[🧪🤍规格✖️数量提取（单位回溯版）-参考2]
，CHAR(10)，
[🧪🤍规格✖️数量提取（单位回溯版）-参考3]
，CHAR(10)，
[🧪🤍规格✖️数量提取（单位回溯版）-参考4]
，CHAR(10)，
[🧪🤍规格✖️数量提取（单位回溯版）-参考5]
，CHAR(10)，
[🧪🤍规格✖️数量提取（单位回溯版）-参考6]
，CHAR(10)，
[🧪🤍规格✖️数量提取（单位回溯版）-参考7]
，CHAR(10)，
[🧪🤍规格✖️数量提取（单位回溯版）-参考8]
，CHAR(10)，
[🧪🤍规格✖️数量提取（多维度规格版）（9）])
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🧪🔥🤍规格✖️数量提取(单位回溯版)
IF([🍬商品信息（原始版）].ISBLANK()，""，

[🧪🤍规格✖️数量提取（单位回溯版）-参考])

.REGEXREPLACE(
[🧪🤍规格✖️数量提取（单位回溯版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(®\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(1)
,

IF(
[🧪🤍规格✖️数量提取（单位回溯版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(1)
.ISBLANK()
,""
,

([🧪🤍规格✖️数量提取（单位回溯版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(1)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.VALUE()

/

[🧪🤍规格✖️数量提取（单位回溯版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(1)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE())
.ROUNDUP(3)

))

.REGEXREPLACE("Ⓜ️分割符","")


.REGEXREPLACE(
[🧪🤍规格✖️数量提取（单位回溯版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(®\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(2)
,

IF(
[🧪🤍规格✖️数量提取（单位回溯版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(2)
.ISBLANK()
,""
,

([🧪🤍规格✖️数量提取（单位回溯版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(2)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.VALUE()

/

[🧪🤍规格✖️数量提取（单位回溯版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(2)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE())
.ROUNDUP(3)

))



.REGEXREPLACE("Ⓜ️分割符","")


.REGEXREPLACE(
[🧪🤍规格✖️数量提取（单位回溯版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(®\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(3)
,

IF(
[🧪🤍规格✖️数量提取（单位回溯版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(3)
.ISBLANK()
,""
,

([🧪🤍规格✖️数量提取（单位回溯版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(3)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.VALUE()

/

[🧪🤍规格✖️数量提取（单位回溯版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(3)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE())
.ROUNDUP(3)

))



.REGEXREPLACE("Ⓜ️分割符","")


.REGEXREPLACE(
[🧪🤍规格✖️数量提取（单位回溯版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(®\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(4)
,

IF(
[🧪🤍规格✖️数量提取（单位回溯版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(4)
.ISBLANK()
,""
,

([🧪🤍规格✖️数量提取（单位回溯版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(4)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.VALUE()

/

[🧪🤍规格✖️数量提取（单位回溯版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(4)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE())
.ROUNDUP(3)

))




.REGEXREPLACE("Ⓜ️分割符","")


.REGEXREPLACE(
[🧪🤍规格✖️数量提取（单位回溯版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(®\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(5)
,

IF(
[🧪🤍规格✖️数量提取（单位回溯版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(5)
.ISBLANK()
,""
,

([🧪🤍规格✖️数量提取（单位回溯版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(5)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.VALUE()

/

[🧪🤍规格✖️数量提取（单位回溯版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(5)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE())
.ROUNDUP(3)

))




.REGEXREPLACE("Ⓜ️分割符","")


.REGEXREPLACE(
[🧪🤍规格✖️数量提取（单位回溯版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(®\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(6)
,

IF(
[🧪🤍规格✖️数量提取（单位回溯版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(6)
.ISBLANK()
,""
,

([🧪🤍规格✖️数量提取（单位回溯版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(6)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.VALUE()

/

[🧪🤍规格✖️数量提取（单位回溯版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(6)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE())
.ROUNDUP(3)

))



.REGEXREPLACE("Ⓜ️分割符","")


.REGEXREPLACE(
[🧪🤍规格✖️数量提取（单位回溯版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(®\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(7)
,

IF(
[🧪🤍规格✖️数量提取（单位回溯版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(7)
.ISBLANK()
,""
,

([🧪🤍规格✖️数量提取（单位回溯版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(7)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.VALUE()

/

[🧪🤍规格✖️数量提取（单位回溯版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(7)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE())
.ROUNDUP(3)

))


.REGEXREPLACE("Ⓜ️分割符","")


.REGEXREPLACE(
[🧪🤍规格✖️数量提取（单位回溯版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(®\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(8)
,

IF(
[🧪🤍规格✖️数量提取（单位回溯版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(8)
.ISBLANK()
,""
,

([🧪🤍规格✖️数量提取（单位回溯版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(8)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.VALUE()

/

[🧪🤍规格✖️数量提取（单位回溯版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(8)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE())
.ROUNDUP(3)

))


.REGEXREPLACE("🔥🔥Ⓜ️分割符🔥🔥","")


.REGEXREPLACE(
[🧪🤍规格✖️数量提取（单位回溯版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(®\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(1)
,

IF(
[🧪🤍规格✖️数量提取（单位回溯版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(1)
.ISBLANK()
,""
,

([🧪🤍规格✖️数量提取（单位回溯版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(1)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.VALUE()

*

[🧪🤍规格✖️数量提取（单位回溯版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(1)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE())
.ROUNDUP(3)

))



.REGEXREPLACE("Ⓜ️分割符","")


.REGEXREPLACE(
[🧪🤍规格✖️数量提取（单位回溯版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(®\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(2)
,

IF(
[🧪🤍规格✖️数量提取（单位回溯版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(2)
.ISBLANK()
,""
,

([🧪🤍规格✖️数量提取（单位回溯版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(2)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.VALUE()

*

[🧪🤍规格✖️数量提取（单位回溯版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(2)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE())
.ROUNDUP(3)

))




.REGEXREPLACE("Ⓜ️分割符","")


.REGEXREPLACE(
[🧪🤍规格✖️数量提取（单位回溯版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(®\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(3)
,

IF(
[🧪🤍规格✖️数量提取（单位回溯版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(3)
.ISBLANK()
,""
,

([🧪🤍规格✖️数量提取（单位回溯版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(3)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.VALUE()

*

[🧪🤍规格✖️数量提取（单位回溯版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(3)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE())
.ROUNDUP(3)

))



.REGEXREPLACE("Ⓜ️分割符","")


.REGEXREPLACE(
[🧪🤍规格✖️数量提取（单位回溯版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(®\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(4)
,

IF(
[🧪🤍规格✖️数量提取（单位回溯版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(4)
.ISBLANK()
,""
,

([🧪🤍规格✖️数量提取（单位回溯版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(4)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.VALUE()

*

[🧪🤍规格✖️数量提取（单位回溯版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(4)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE())
.ROUNDUP(3)

))




.REGEXREPLACE("Ⓜ️分割符","")


.REGEXREPLACE(
[🧪🤍规格✖️数量提取（单位回溯版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(®\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(5)
,

IF(
[🧪🤍规格✖️数量提取（单位回溯版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(5)
.ISBLANK()
,""
,

([🧪🤍规格✖️数量提取（单位回溯版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(5)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.VALUE()

*

[🧪🤍规格✖️数量提取（单位回溯版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(5)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE())
.ROUNDUP(3)

))




.REGEXREPLACE("Ⓜ️分割符","")


.REGEXREPLACE(
[🧪🤍规格✖️数量提取（单位回溯版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(®\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(6)
,

IF(
[🧪🤍规格✖️数量提取（单位回溯版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(6)
.ISBLANK()
,""
,

([🧪🤍规格✖️数量提取（单位回溯版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(6)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.VALUE()

*

[🧪🤍规格✖️数量提取（单位回溯版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(6)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE())
.ROUNDUP(3)

))


.REGEXREPLACE("Ⓜ️分割符","")


.REGEXREPLACE(
[🧪🤍规格✖️数量提取（单位回溯版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(®\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(7)
,

IF(
[🧪🤍规格✖️数量提取（单位回溯版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(7)
.ISBLANK()
,""
,

([🧪🤍规格✖️数量提取（单位回溯版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(7)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.VALUE()

*

[🧪🤍规格✖️数量提取（单位回溯版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(7)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE())
.ROUNDUP(3)

))



.REGEXREPLACE("Ⓜ️分割符","")


.REGEXREPLACE(
[🧪🤍规格✖️数量提取（单位回溯版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(®\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(8)
,

IF(
[🧪🤍规格✖️数量提取（单位回溯版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(8)
.ISBLANK()
,""
,

([🧪🤍规格✖️数量提取（单位回溯版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(8)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.VALUE()

*

[🧪🤍规格✖️数量提取（单位回溯版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(8)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE())
.ROUNDUP(3)

))

.REGEXREPLACE("®","")
.REGEXREPLACE("\.00[1-9](磅)","$1")
.REGEXREPLACE("(\.\d+)0[1-9](磅)","$1$2")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🧪🖤规格✖️数量提取（字段维护专用）
IF([🍬商品信息（原始版）].ISBLANK()，""，

CONCATENATE(
IF(
[💰规格&价格数量对应关系]
.REGEXEXTRACTALL("❶.*?❶🔞")
.REGEXMATCH("♣️|♠️")
,

[🧪🔥🤍规格✖️数量提取(单位回溯版)]
.REGEXEXTRACTALL("❶.*?❶🔞")
.UNIQUE()
.ARRAYJOIN("；")
.REGEXREPLACE("[❶-❾]🔞[;；][❶-❾]"," / ")

，
[🧪🔥🤍规格✖️数量提取(单位回溯版)]
.REGEXEXTRACTALL("❶.*?❶🔞").ARRAYJOIN("；"))


,CHAR(10),

IF(
[💰规格&价格数量对应关系]
.REGEXEXTRACTALL("❷.*?❷🔞")
.REGEXMATCH("♣️|♠️")
,

[🧪🔥🤍规格✖️数量提取(单位回溯版)]
.REGEXEXTRACTALL("❷.*?❷🔞")
.UNIQUE()
.ARRAYJOIN("；")
.REGEXREPLACE("[❶-❾]🔞[;；][❶-❾]"," / ")

，
[🧪🔥🤍规格✖️数量提取(单位回溯版)]
.REGEXEXTRACTALL("❷.*?❷🔞").ARRAYJOIN("；"))



,CHAR(10),

IF(
[💰规格&价格数量对应关系]
.REGEXEXTRACTALL("❸.*?❸🔞")
.REGEXMATCH("♣️|♠️")
,

[🧪🔥🤍规格✖️数量提取(单位回溯版)]
.REGEXEXTRACTALL("❸.*?❸🔞")
.UNIQUE()
.ARRAYJOIN("；")
.REGEXREPLACE("[❶-❾]🔞[;；][❶-❾]"," / ")

，
[🧪🔥🤍规格✖️数量提取(单位回溯版)]
.REGEXEXTRACTALL("❸.*?❸🔞").ARRAYJOIN("；"))


,CHAR(10),

IF(
[💰规格&价格数量对应关系]
.REGEXEXTRACTALL("❹.*?❹🔞")
.REGEXMATCH("♣️|♠️")
,

[🧪🔥🤍规格✖️数量提取(单位回溯版)]
.REGEXEXTRACTALL("❹.*?❹🔞")
.UNIQUE()
.ARRAYJOIN("；")
.REGEXREPLACE("[❶-❾]🔞[;；][❶-❾]"," / ")

，
[🧪🔥🤍规格✖️数量提取(单位回溯版)]
.REGEXEXTRACTALL("❹.*?❹🔞").ARRAYJOIN("；"))


,CHAR(10),

IF(
[💰规格&价格数量对应关系]
.REGEXEXTRACTALL("❺.*?❺🔞")
.REGEXMATCH("♣️|♠️")
,

[🧪🔥🤍规格✖️数量提取(单位回溯版)]
.REGEXEXTRACTALL("❺.*?❺🔞")
.UNIQUE()
.ARRAYJOIN("；")
.REGEXREPLACE("[❶-❾]🔞[;；][❶-❾]"," / ")

，
[🧪🔥🤍规格✖️数量提取(单位回溯版)]
.REGEXEXTRACTALL("❺.*?❺🔞").ARRAYJOIN("；"))

,CHAR(10),

IF(
[💰规格&价格数量对应关系]
.REGEXEXTRACTALL("❻.*?❻🔞")
.REGEXMATCH("♣️|♠️")
,

[🧪🔥🤍规格✖️数量提取(单位回溯版)]
.REGEXEXTRACTALL("❻.*?❻🔞")
.UNIQUE()
.ARRAYJOIN("；")
.REGEXREPLACE("[❶-❾]🔞[;；][❶-❾]"," / ")

，
[🧪🔥🤍规格✖️数量提取(单位回溯版)]
.REGEXEXTRACTALL("❻.*?❻🔞").ARRAYJOIN("；"))






,CHAR(10),

IF(
[💰规格&价格数量对应关系]
.REGEXEXTRACTALL("❼.*?❼🔞")
.REGEXMATCH("♣️|♠️")
,

[🧪🔥🤍规格✖️数量提取(单位回溯版)]
.REGEXEXTRACTALL("❼.*?❼🔞")
.UNIQUE()
.ARRAYJOIN("；")
.REGEXREPLACE("[❶-❾]🔞[;；][❶-❾]"," / ")

，
[🧪🔥🤍规格✖️数量提取(单位回溯版)]
.REGEXEXTRACTALL("❼.*?❼🔞").ARRAYJOIN("；"))


,CHAR(10),


IF(
[💰规格&价格数量对应关系]
.REGEXEXTRACTALL("❽.*?❽🔞")
.REGEXMATCH("♣️|♠️")
,

[🧪🔥🤍规格✖️数量提取(单位回溯版)]
.REGEXEXTRACTALL("❽.*?❽🔞")
.UNIQUE()
.ARRAYJOIN("；")
.REGEXREPLACE("[❶-❾]🔞[;；][❶-❾]"," / ")

，
[🧪🔥🤍规格✖️数量提取(单位回溯版)]
.REGEXEXTRACTALL("❽.*?❽🔞").ARRAYJOIN("；"))



,CHAR(10),


IF(
[💰规格&价格数量对应关系]
.REGEXEXTRACTALL("❾.*?❾🔞")
.REGEXMATCH("♣️|♠️")
,

[🧪🔥🤍规格✖️数量提取(单位回溯版)]
.REGEXEXTRACTALL("❾.*?❾🔞")
.UNIQUE()
.ARRAYJOIN("；")
.REGEXREPLACE("[❶-❾]🔞[;；][❶-❾]"," / ")

，
[🧪🔥🤍规格✖️数量提取(单位回溯版)]
.REGEXEXTRACTALL("❾.*?❾🔞").ARRAYJOIN("；"))





))

.REGEXREPLACE("\*","×")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🧪🤍😀起拍份数（原始版）
[🔪 ｜ ✍️ sku解构(基建)]
.FILTER(CurrentValue.[🔪编号（最终）]=[🧪编号（最终）])
.[🔪🤍😀拍凑份数(原始版)]

.REGEXREPLACE("㊙️㊙️㊙️清除出现在开头的价格信息，因为涉及到plus价格的时候，其中的l容易在下一个欧逻辑中被识别为单位","")

.REGEXREPLACE("〰+[，；。,]?","")
.REGEXREPLACE("(88[Vv][Ii][Pp]|[Pp][Ll][Uu][Ss])[，；。,]?","")
.REGEXREPLACE("([❶-❾])((?:88[Vv][Ii][Pp]|[Pp][Ll][Uu][Ss])?💰.*?[,，；;。])+","$1")
.REGEXREPLACE("[，；。,]?💰.*?([❶-❾]🔞)","$1")


.REGEXREPLACE("㊙️㊙️㊙️处理商品标题相关的信息","")
.REGEXREPLACE("([❶-❾])(?:.*?(?:"&[Ⓜ️ ｜ ✍️ 品牌识别（基建）]
.FILTER(CurrentValue.[Ⓜ️编号（最终）]=[🧪编号（最终）])
.[🧠品牌-逻辑]&").*?(?:"&[🧠单位合集]&")[，；。,]?)","$1")

.REGEXREPLACE("\+.*?[,，。]","")

.REGEXREPLACE("㊙️㊙️㊙️清除到手**规格后面的所有干扰信息","")


.REGEXREPLACE("(到手共?\d+(?:"&[🧠单位合集]&")).*?([❶-❾])","$1$2")

.REGEXREPLACE("㊙️㊙️㊙️清除 **小规格拍 **大规 格这类结构中后面的所有干扰信息","")

.REGEXREPLACE("(\d+(?:"&[🧠单位合集]&")(?:选项)?拍(?:\d+(?:"&[🧠单位合集]&"))).*?([❶-❾]🔞)","$1$2")

.REGEXREPLACE("㊙️㊙️㊙️在完成上述几个逻辑后，再清除所有规格单位后面的价格信息","")

.REGEXREPLACE("(.*?(?:"&[🧠单位合集]&")(?:选项)?).*?💰.*?([❶-❾]🔞)","$1$2")



.REGEXREPLACE(" ","")
.REGEXREPLACE("(?:\d+个)?(?:凑单?).*?([❶-❾])","$1")
.REGEXREPLACE(".*?0?(?:添加).*","")
.REGEXREPLACE("[买领]\d+.*?券.*?([❶-❾])","$1")
.REGEXREPLACE("([❶-❾].*?)\d+[款种号](?:口味)?[,，。]?","$1")
.REGEXREPLACE("([❶-❾]).*?第?\s*\d+个选项[,，。]?","$1")
.REGEXREPLACE("可.*?拍\d+次.*?([❶-❾])","$1")


.REGEXREPLACE("拍1:","")
.REGEXREPLACE("[买拍]0","")


.REGEXREPLACE("[❶-❾][，,。:：]?[❶-❾]🔞","")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🧪🤍😀起拍份数（单位统一版）-参考
[🧪🤍😀起拍份数（原始版）]
.REGEXREPLACE("到手","共")
.REGEXREPLACE("共+","共")
.REGEXREPLACE("[,，。；;]?共.*?([❶-❾]🔞)","$1")

.REGEXREPLACE("㊙️㊙️㊙️㊙️分割符👇：用来处理 拍几发几 的计算格式问题","")

.REGEXREPLACE("([❶-❾]).*?[拍买]\d+(?:"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&"|"&[🧠终极单位]&")?([发]\d+.*?[❶-❾]🔞)"，"$1$2")


.REGEXREPLACE("㊙️㊙️㊙️㊙️分割符👇：用来处理 拍几送同款（同款后面没有信息） 的计算格式问题","")

.REGEXREPLACE("([❶-❾]).*?([拍买]\d+)("&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&"|"&[🧠终极单位]&")?[赠送]同款([❶-❾]🔞)"，"$1$2+1$4$3")


.REGEXREPLACE("㊙️㊙️㊙️㊙️分割符👇：用来处理 拍几送几 的计算格式问题","")

.REGEXREPLACE("([❶-❾]).*?([拍买]\d+(?:\.\d+)?)("&[🧠基础单位]&")[赠送](?:同款)?(\d+(?:\.\d+)?)(?:"&[🧠基础单位]&")(.*?[❶-❾]🔞)"，"$1$2+$4$5$6")

.REGEXREPLACE("([❶-❾]).*?([拍买]\d+(?:\.\d+)?)("&[🧠初级单位]&")[赠送](?:同款)?(\d+(?:\.\d+)?)(?:"&[🧠初级单位]&")(.*?[❶-❾]🔞)"，"$1$2+$4$5$6")

.REGEXREPLACE("([❶-❾]).*?([拍买]\d+(?:\.\d+)?)("&[🧠进阶单位]&")[赠送](?:同款)?(\d+(?:\.\d+)?)(?:"&[🧠进阶单位]&")(.*?[❶-❾]🔞)"，"$1$2+$4$5$6")


.REGEXREPLACE("([❶-❾]).*?([拍买]\d+(?:\.\d+)?)("&[🧠高阶单位]&")[赠送](?:同款)?(\d+(?:\.\d+)?)(?:"&[🧠高阶单位]&")(.*?[❶-❾]🔞)"，"$1$2+$4$5$6")

.REGEXREPLACE("([❶-❾]).*?([拍买]\d+(?:\.\d+)?)("&[🧠终极单位]&")[赠送](?:同款)?(\d+(?:\.\d+)?)(?:"&[🧠终极单位]&")(.*?[❶-❾]🔞)"，"$1$2+$4$5$6")

.REGEXREPLACE("([❶-❾]).*?([拍买]\d+)("&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&"|"&[🧠终极单位]&")?[赠送](\d+)(.*?[❶-❾]🔞)"，"$1$2+$4$5$6")


.REGEXREPLACE("Ⓜ️Ⓜ️Ⓜ️Ⓜ️Ⓜ️Ⓜ️分割符👇：用来处理单位统一的问题","")
.REGEXREPLACE("磅","×0.90718474斤")
.REGEXREPLACE("kg|公斤|千克","×2斤")
.REGEXREPLACE("克|g","÷500斤")
.REGEXREPLACE("ml","÷1000l")

.REGEXREPLACE("(?:任选|加[购车]?|加购物车|买)\s*(\d+)","拍$1")
.REGEXREPLACE("[❶-❾]拍1[件:：]?\s*[❶-❾]🔞","")


.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🧪🤍😀起拍份数（单位统一版）
[🧪🤍😀起拍份数（单位统一版）-参考]


.REGEXREPLACE(
[🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?\+\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(1)
.REGEXREPLACE([🧠需转义符号参考-逻辑],"\$0")

,

IF(
[🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?\+\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(1)
.ISBLANK()
,""
,

([🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?\+\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(1)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.VALUE()

+

[🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?\+\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(1)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE())
.ROUNDUP(3)

))



.REGEXREPLACE("Ⓜ️分割符","")

.REGEXREPLACE(
[🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?\+\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(2)
.REGEXREPLACE([🧠需转义符号参考-逻辑],"\$0")
,

IF(
[🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?\+\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(2)
.ISBLANK()
,""
,

([🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?\+\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(2)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.VALUE()

+

[🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?\+\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(2)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE())
.ROUNDUP(3)

))



.REGEXREPLACE("Ⓜ️分割符","")

.REGEXREPLACE(
[🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?\+\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(3)
.REGEXREPLACE([🧠需转义符号参考-逻辑],"\$0")
,

IF(
[🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?\+\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(3)
.ISBLANK()
,""
,

([🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?\+\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(3)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.VALUE()

+

[🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?\+\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(3)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE())
.ROUNDUP(3)

))



.REGEXREPLACE("Ⓜ️分割符","")

.REGEXREPLACE(
[🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?\+\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(4)
.REGEXREPLACE([🧠需转义符号参考-逻辑],"\$0")
,

IF(
[🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?\+\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(4)
.ISBLANK()
,""
,

([🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?\+\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(4)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.VALUE()

+

[🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?\+\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(4)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE())
.ROUNDUP(3)

))


.REGEXREPLACE("Ⓜ️分割符","")

.REGEXREPLACE(
[🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?\+\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(5)
.REGEXREPLACE([🧠需转义符号参考-逻辑],"\$0")
,

IF(
[🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?\+\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(5)
.ISBLANK()
,""
,

([🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?\+\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(5)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.VALUE()

+

[🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?\+\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(5)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE())
.ROUNDUP(3)

))



.REGEXREPLACE("🔥🔥Ⓜ️分割符🔥🔥","")



.REGEXREPLACE(
[🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(1)
,

IF(
[🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(1)
.ISBLANK()
,""
,

([🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(1)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.VALUE()

/

[🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(1)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE())
.ROUNDUP(3)

))

.REGEXREPLACE("Ⓜ️分割符","")


.REGEXREPLACE(
[🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(2)
,

IF(
[🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(2)
.ISBLANK()
,""
,

([🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(2)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.VALUE()

/

[🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(2)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE())
.ROUNDUP(3)

))



.REGEXREPLACE("Ⓜ️分割符","")


.REGEXREPLACE(
[🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(3)
,

IF(
[🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(3)
.ISBLANK()
,""
,

([🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(3)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.VALUE()

/

[🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(3)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE())
.ROUNDUP(3)

))



.REGEXREPLACE("Ⓜ️分割符","")


.REGEXREPLACE(
[🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(4)
,

IF(
[🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(4)
.ISBLANK()
,""
,

([🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(4)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.VALUE()

/

[🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(4)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE())
.ROUNDUP(3)

))




.REGEXREPLACE("Ⓜ️分割符","")


.REGEXREPLACE(
[🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(5)
,

IF(
[🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(5)
.ISBLANK()
,""
,

([🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(5)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.VALUE()

/

[🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(5)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE())
.ROUNDUP(3)

))




.REGEXREPLACE("Ⓜ️分割符","")


.REGEXREPLACE(
[🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(6)
,

IF(
[🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(6)
.ISBLANK()
,""
,

([🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(6)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.VALUE()

/

[🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(6)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE())
.ROUNDUP(3)

))



.REGEXREPLACE("🔥🔥Ⓜ️分割符🔥🔥","")


.REGEXREPLACE(
[🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(1)
,

IF(
[🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(1)
.ISBLANK()
,""
,

([🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(1)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.VALUE()

*

[🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(1)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE())
.ROUNDUP(3)

))



.REGEXREPLACE("Ⓜ️分割符","")


.REGEXREPLACE(
[🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(2)
,

IF(
[🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(2)
.ISBLANK()
,""
,

([🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(2)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.VALUE()

*

[🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(2)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE())
.ROUNDUP(3)

))




.REGEXREPLACE("Ⓜ️分割符","")


.REGEXREPLACE(
[🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(3)
,

IF(
[🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(3)
.ISBLANK()
,""
,

([🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(3)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.VALUE()

*

[🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(3)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE())
.ROUNDUP(3)

))



.REGEXREPLACE("Ⓜ️分割符","")


.REGEXREPLACE(
[🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(4)
,

IF(
[🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(4)
.ISBLANK()
,""
,

([🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(4)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.VALUE()

*

[🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(4)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE())
.ROUNDUP(3)

))




.REGEXREPLACE("Ⓜ️分割符","")


.REGEXREPLACE(
[🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(5)
,

IF(
[🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(5)
.ISBLANK()
,""
,

([🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(5)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.VALUE()

*

[🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(5)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE())
.ROUNDUP(3)

))




.REGEXREPLACE("Ⓜ️分割符","")


.REGEXREPLACE(
[🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(6)
,

IF(
[🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(6)
.ISBLANK()
,""
,

([🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(6)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.VALUE()

*

[🧪🤍😀起拍份数（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(6)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE())
.ROUNDUP(3)

))

.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🧪🤍😀起拍份数（清洗版）（1-5）
IF([🍬商品信息（原始版）].ISBLANK()，""，

CONCATENATE(
IF(
OR(
AND(

[💰到手价格（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.COUNTA()=1
,
[🧪🤍😀起拍份数（原始版）]
.REGEXEXTRACTALL("❶.*?([拍买]\d+[赠送发]\d+).*?❶🔞")
.COUNTA()
>

[💰到手价格（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.COUNTA())

,

AND(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.COUNTA()=1
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.COUNTA()<=1
,
[🧪🤍😀起拍份数（原始版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.COUNTA()
>

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.COUNTA())

)

,
[🧪🤍😀起拍份数（单位统一版）]
.REGEXREPLACE("("&[🧠终极单位]&"|[:,，。])","")
.REGEXEXTRACTALL("❶.*?❶🔞")
.UNIQUE()
，

IF(
OR(
[🧪🤍😀起拍份数（单位统一版）]
.REGEXEXTRACTALL("❶.*?❶🔞").ARRAYJOIN()
.REGEXREPLACE("拍","")
=
[🧪🤍规格✖️数量提取（原始版）]
.REGEXEXTRACTALL("❶.*?❶🔞").ARRAYJOIN()
，

AND(
[🧪🤍😀起拍份数（单位统一版）]
.REGEXEXTRACTALL("❶.*?❶🔞").ARRAYJOIN()
.REGEXMATCH("共.*?\d+")
,
[🧪🤍规格✖️数量提取（多维度规格版）]
.REGEXEXTRACTALL("❶.*?❶🔞").ARRAYJOIN()
.REGEXREPLACE("[❶-❾🔞]","")

.REGEXMATCH(
[🧪🤍😀起拍份数（单位统一版）]
.REGEXEXTRACTALL("❶.*?共.*?(\d+(?:\.\d+)?"&[🧠单位合集]&").*?❶🔞").ARRAYJOIN()

))
，


[🧪🤍规格✖️数量提取（原始版）]
.REGEXEXTRACTALL("❶.*?❶🔞").ARRAYJOIN()
.REGEXEXTRACTALL("(\d+(?:\.\d+)?(?:"&[🧠单位合集]&"))").ARRAYJOIN()
.REGEXREPLACE("[❶-❾🔞]","")

.REGEXMATCH(
[🧪🤍😀起拍份数（单位统一版）]
.REGEXEXTRACTALL("❶.*?拍(\d+(?:\.\d+)?(?:"&[🧠单位合集]&")?).*?❶🔞").ARRAYJOIN())


,

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?\*.*?❶🔞").ARRAYJOIN()
.REGEXEXTRACTALL("\*(\d+(?:\.\d+)?)(?:"&[🧠单位合集]&")?").ARRAYJOIN()
.REGEXREPLACE("[❶-❾🔞]","")

=
[🧪🤍😀起拍份数（单位统一版）]
.REGEXEXTRACTALL("❶.*?拍(\d+(?:\.\d+)?)(?:"&[🧠单位合集]&")?.*?❶🔞").ARRAYJOIN()

)

,""
,
[🧪🤍😀起拍份数（单位统一版）]
.REGEXEXTRACTALL("❶.*?❶🔞").ARRAYJOIN("；")

))



,CHAR(10),

IF(

OR(
AND(

[💰到手价格（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.COUNTA()=1
,
[🧪🤍😀起拍份数（原始版）]
.REGEXEXTRACTALL("❷.*?([拍买]\d+[赠送发]\d+).*?❷🔞")
.COUNTA()
>

[💰到手价格（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.COUNTA())

,

AND(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.COUNTA()=1
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.COUNTA()<=1
,
[🧪🤍😀起拍份数（原始版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.COUNTA()
>

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.COUNTA())

)

,
[🧪🤍😀起拍份数（单位统一版）]
.REGEXREPLACE("("&[🧠终极单位]&"|[:,，。])","")
.REGEXEXTRACTALL("❷.*?❷🔞")
.UNIQUE()
，

IF(
OR(
[🧪🤍😀起拍份数（单位统一版）]
.REGEXEXTRACTALL("❷.*?❷🔞").ARRAYJOIN()
.REGEXREPLACE("拍","")
=
[🧪🤍规格✖️数量提取（原始版）]
.REGEXEXTRACTALL("❷.*?❷🔞").ARRAYJOIN()
，

AND(
[🧪🤍😀起拍份数（单位统一版）]
.REGEXEXTRACTALL("❷.*?❷🔞").ARRAYJOIN()
.REGEXMATCH("共.*?\d+")
,
[🧪🤍规格✖️数量提取（多维度规格版）]
.REGEXEXTRACTALL("❷.*?❷🔞").ARRAYJOIN()
.REGEXREPLACE("[❶-❾🔞]","")

.REGEXMATCH(
[🧪🤍😀起拍份数（单位统一版）]
.REGEXEXTRACTALL("❷.*?共.*?(\d+(?:\.\d+)?"&[🧠单位合集]&").*?❷🔞").ARRAYJOIN()

))
，


[🧪🤍规格✖️数量提取（原始版）]
.REGEXEXTRACTALL("❷.*?❷🔞").ARRAYJOIN()
.REGEXEXTRACTALL("(\d+(?:\.\d+)?(?:"&[🧠单位合集]&"))").ARRAYJOIN()
.REGEXREPLACE("[❶-❾🔞]","")

.REGEXMATCH(
[🧪🤍😀起拍份数（单位统一版）]
.REGEXEXTRACTALL("❷.*?拍(\d+(?:\.\d+)?(?:"&[🧠单位合集]&")?).*?❷🔞").ARRAYJOIN())


,

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?\*.*?❷🔞").ARRAYJOIN()
.REGEXEXTRACTALL("\*(\d+(?:\.\d+)?)(?:"&[🧠单位合集]&")?").ARRAYJOIN()
.REGEXREPLACE("[❶-❾🔞]","")

=
[🧪🤍😀起拍份数（单位统一版）]
.REGEXEXTRACTALL("❷.*?拍(\d+(?:\.\d+)?)(?:"&[🧠单位合集]&")?.*?❷🔞").ARRAYJOIN()

)

,""
,
[🧪🤍😀起拍份数（单位统一版）]
.REGEXEXTRACTALL("❷.*?❷🔞").ARRAYJOIN("；")

))





,CHAR(10),

IF(

OR(
AND(

[💰到手价格（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.COUNTA()=1
,
[🧪🤍😀起拍份数（原始版）]
.REGEXEXTRACTALL("❸.*?([拍买]\d+[赠送发]\d+).*?❸🔞")
.COUNTA()
>

[💰到手价格（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.COUNTA())


,

AND(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.COUNTA()=1
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.COUNTA()<=1
,
[🧪🤍😀起拍份数（原始版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.COUNTA()
>
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.COUNTA())
)

,
[🧪🤍😀起拍份数（单位统一版）]
.REGEXREPLACE("("&[🧠终极单位]&"|[:,，。])","")
.REGEXEXTRACTALL("❸.*?❸🔞")
.UNIQUE()
，

IF(
OR(
[🧪🤍😀起拍份数（单位统一版）]
.REGEXEXTRACTALL("❸.*?❸🔞").ARRAYJOIN()
.REGEXREPLACE("拍","")
=
[🧪🤍规格✖️数量提取（原始版）]
.REGEXEXTRACTALL("❸.*?❸🔞").ARRAYJOIN()
，

AND(
[🧪🤍😀起拍份数（单位统一版）]
.REGEXEXTRACTALL("❸.*?❸🔞").ARRAYJOIN()
.REGEXMATCH("共.*?\d+")
,
[🧪🤍规格✖️数量提取（多维度规格版）]
.REGEXEXTRACTALL("❸.*?❸🔞").ARRAYJOIN()
.REGEXREPLACE("[❶-❾🔞]","")

.REGEXMATCH(
[🧪🤍😀起拍份数（单位统一版）]
.REGEXEXTRACTALL("❸.*?共.*?(\d+(?:\.\d+)?"&[🧠单位合集]&").*?❸🔞").ARRAYJOIN()

))
，


[🧪🤍规格✖️数量提取（原始版）]
.REGEXEXTRACTALL("❸.*?❸🔞").ARRAYJOIN()
.REGEXEXTRACTALL("(\d+(?:\.\d+)?(?:"&[🧠单位合集]&"))").ARRAYJOIN()
.REGEXREPLACE("[❶-❾🔞]","")

.REGEXMATCH(
[🧪🤍😀起拍份数（单位统一版）]
.REGEXEXTRACTALL("❸.*?拍(\d+(?:\.\d+)?(?:"&[🧠单位合集]&")?).*?❸🔞").ARRAYJOIN())


,

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?\*.*?❸🔞").ARRAYJOIN()
.REGEXEXTRACTALL("\*(\d+(?:\.\d+)?)(?:"&[🧠单位合集]&")?").ARRAYJOIN()
.REGEXREPLACE("[❶-❾🔞]","")

=
[🧪🤍😀起拍份数（单位统一版）]
.REGEXEXTRACTALL("❸.*?拍(\d+(?:\.\d+)?)(?:"&[🧠单位合集]&")?.*?❸🔞").ARRAYJOIN()

)

,""
,
[🧪🤍😀起拍份数（单位统一版）]
.REGEXEXTRACTALL("❸.*?❸🔞").ARRAYJOIN("；")

))



,CHAR(10),

IF(

OR(
AND(

[💰到手价格（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.COUNTA()=1
,
[🧪🤍😀起拍份数（原始版）]
.REGEXEXTRACTALL("❹.*?([拍买]\d+[赠送发]\d+).*?❹🔞")
.COUNTA()
>

[💰到手价格（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.COUNTA())

,

AND(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.COUNTA()=1
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.COUNTA()<=1
,
[🧪🤍😀起拍份数（原始版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.COUNTA()
>

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.COUNTA())

)

,
[🧪🤍😀起拍份数（单位统一版）]
.REGEXREPLACE("("&[🧠终极单位]&"|[:,，。])","")
.REGEXEXTRACTALL("❹.*?❹🔞")
.UNIQUE()
，

IF(
OR(
[🧪🤍😀起拍份数（单位统一版）]
.REGEXEXTRACTALL("❹.*?❹🔞").ARRAYJOIN()
.REGEXREPLACE("拍","")
=
[🧪🤍规格✖️数量提取（原始版）]
.REGEXEXTRACTALL("❹.*?❹🔞").ARRAYJOIN()
，

AND(
[🧪🤍😀起拍份数（单位统一版）]
.REGEXEXTRACTALL("❹.*?❹🔞").ARRAYJOIN()
.REGEXMATCH("共.*?\d+")
,
[🧪🤍规格✖️数量提取（多维度规格版）]
.REGEXEXTRACTALL("❹.*?❹🔞").ARRAYJOIN()
.REGEXREPLACE("[❶-❾🔞]","")

.REGEXMATCH(
[🧪🤍😀起拍份数（单位统一版）]
.REGEXEXTRACTALL("❹.*?共.*?(\d+(?:\.\d+)?"&[🧠单位合集]&").*?❹🔞").ARRAYJOIN()

))
，


[🧪🤍规格✖️数量提取（原始版）]
.REGEXEXTRACTALL("❹.*?❹🔞").ARRAYJOIN()
.REGEXEXTRACTALL("(\d+(?:\.\d+)?(?:"&[🧠单位合集]&"))").ARRAYJOIN()
.REGEXREPLACE("[❶-❾🔞]","")

.REGEXMATCH(
[🧪🤍😀起拍份数（单位统一版）]
.REGEXEXTRACTALL("❹.*?拍(\d+(?:\.\d+)?(?:"&[🧠单位合集]&")?).*?❹🔞").ARRAYJOIN())


,

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?\*.*?❹🔞").ARRAYJOIN()
.REGEXEXTRACTALL("\*(\d+(?:\.\d+)?)(?:"&[🧠单位合集]&")?").ARRAYJOIN()
.REGEXREPLACE("[❶-❾🔞]","")

=
[🧪🤍😀起拍份数（单位统一版）]
.REGEXEXTRACTALL("❹.*?拍(\d+(?:\.\d+)?)(?:"&[🧠单位合集]&")?.*?❹🔞").ARRAYJOIN()

)

,""
,
[🧪🤍😀起拍份数（单位统一版）]
.REGEXEXTRACTALL("❹.*?❹🔞").ARRAYJOIN("；")

))





,CHAR(10),

IF(
OR(
AND(

[💰到手价格（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.COUNTA()=1
,
[🧪🤍😀起拍份数（原始版）]
.REGEXEXTRACTALL("❺.*?([拍买]\d+[赠送发]\d+).*?❺🔞")
.COUNTA()
>

[💰到手价格（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.COUNTA())

,

AND(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.COUNTA()=1
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.COUNTA()<=1
,
[🧪🤍😀起拍份数（原始版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.COUNTA()
>

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.COUNTA())

)


,
[🧪🤍😀起拍份数（单位统一版）]
.REGEXREPLACE("("&[🧠终极单位]&"|[:,，。])","")
.REGEXEXTRACTALL("❺.*?❺🔞")
.UNIQUE()
，

IF(
OR(
[🧪🤍😀起拍份数（单位统一版）]
.REGEXEXTRACTALL("❺.*?❺🔞").ARRAYJOIN()
.REGEXREPLACE("拍","")
=
[🧪🤍规格✖️数量提取（原始版）]
.REGEXEXTRACTALL("❺.*?❺🔞").ARRAYJOIN()
，

AND(
[🧪🤍😀起拍份数（单位统一版）]
.REGEXEXTRACTALL("❺.*?❺🔞").ARRAYJOIN()
.REGEXMATCH("共.*?\d+")
,
[🧪🤍规格✖️数量提取（多维度规格版）]
.REGEXEXTRACTALL("❺.*?❺🔞").ARRAYJOIN()
.REGEXREPLACE("[❶-❾🔞]","")

.REGEXMATCH(
[🧪🤍😀起拍份数（单位统一版）]
.REGEXEXTRACTALL("❺.*?共.*?(\d+(?:\.\d+)?"&[🧠单位合集]&").*?❺🔞").ARRAYJOIN()

))
，


[🧪🤍规格✖️数量提取（原始版）]
.REGEXEXTRACTALL("❺.*?❺🔞").ARRAYJOIN()
.REGEXEXTRACTALL("(\d+(?:\.\d+)?(?:"&[🧠单位合集]&"))").ARRAYJOIN()
.REGEXREPLACE("[❶-❾🔞]","")

.REGEXMATCH(
[🧪🤍😀起拍份数（单位统一版）]
.REGEXEXTRACTALL("❺.*?拍(\d+(?:\.\d+)?(?:"&[🧠单位合集]&")?).*?❺🔞").ARRAYJOIN())


,

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?\*.*?❺🔞").ARRAYJOIN()
.REGEXEXTRACTALL("\*(\d+(?:\.\d+)?)(?:"&[🧠单位合集]&")?").ARRAYJOIN()
.REGEXREPLACE("[❶-❾🔞]","")

=
[🧪🤍😀起拍份数（单位统一版）]
.REGEXEXTRACTALL("❺.*?拍(\d+(?:\.\d+)?)(?:"&[🧠单位合集]&")?.*?❺🔞").ARRAYJOIN()

)

,""
,
[🧪🤍😀起拍份数（单位统一版）]
.REGEXEXTRACTALL("❺.*?❺🔞").ARRAYJOIN("；")

))



))

.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🧪🤍😀起拍份数（清洗版）（6-9）
IF([🍬商品信息（原始版）].ISBLANK()，""，

CONCATENATE(

IF(
OR(
AND(

[💰到手价格（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.COUNTA()=1
,
[🧪🤍😀起拍份数（原始版）]
.REGEXEXTRACTALL("❻.*?([拍买]\d+[赠送发]\d+).*?❻🔞")
.COUNTA()
>

[💰到手价格（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.COUNTA())

,

AND(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.COUNTA()=1
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.COUNTA()<=1
,
[🧪🤍😀起拍份数（原始版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.COUNTA()
>

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.COUNTA())

)



,
[🧪🤍😀起拍份数（单位统一版）]
.REGEXREPLACE("("&[🧠终极单位]&"|[:,，。])","")
.REGEXEXTRACTALL("❻.*?❻🔞")
.UNIQUE()
，

IF(
OR(
[🧪🤍😀起拍份数（单位统一版）]
.REGEXEXTRACTALL("❻.*?❻🔞").ARRAYJOIN()
.REGEXREPLACE("拍","")
=
[🧪🤍规格✖️数量提取（原始版）]
.REGEXEXTRACTALL("❻.*?❻🔞").ARRAYJOIN()
，

AND(
[🧪🤍😀起拍份数（单位统一版）]
.REGEXEXTRACTALL("❻.*?❻🔞").ARRAYJOIN()
.REGEXMATCH("共.*?\d+")
,
[🧪🤍规格✖️数量提取（多维度规格版）]
.REGEXEXTRACTALL("❻.*?❻🔞").ARRAYJOIN()
.REGEXREPLACE("[❶-❾🔞]","")

.REGEXMATCH(
[🧪🤍😀起拍份数（单位统一版）]
.REGEXEXTRACTALL("❻.*?共.*?(\d+(?:\.\d+)?"&[🧠单位合集]&").*?❻🔞").ARRAYJOIN()

))
，


[🧪🤍规格✖️数量提取（原始版）]
.REGEXEXTRACTALL("❻.*?❻🔞").ARRAYJOIN()
.REGEXEXTRACTALL("(\d+(?:\.\d+)?(?:"&[🧠单位合集]&"))").ARRAYJOIN()
.REGEXREPLACE("[❶-❾🔞]","")

.REGEXMATCH(
[🧪🤍😀起拍份数（单位统一版）]
.REGEXEXTRACTALL("❻.*?拍(\d+(?:\.\d+)?(?:"&[🧠单位合集]&")?).*?❻🔞").ARRAYJOIN())


,

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?\*.*?❻🔞").ARRAYJOIN()
.REGEXEXTRACTALL("\*(\d+(?:\.\d+)?)(?:"&[🧠单位合集]&")?").ARRAYJOIN()
.REGEXREPLACE("[❶-❾🔞]","")

=
[🧪🤍😀起拍份数（单位统一版）]
.REGEXEXTRACTALL("❻.*?拍(\d+(?:\.\d+)?)(?:"&[🧠单位合集]&")?.*?❻🔞").ARRAYJOIN()

)

,""
,
[🧪🤍😀起拍份数（单位统一版）]
.REGEXEXTRACTALL("❻.*?❻🔞").ARRAYJOIN("；")

))


,CHAR(10),

IF(
OR(
AND(

[💰到手价格（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.COUNTA()=1
,
[🧪🤍😀起拍份数（原始版）]
.REGEXEXTRACTALL("❼.*?([拍买]\d+[赠送发]\d+).*?❼🔞")
.COUNTA()
>

[💰到手价格（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.COUNTA())

,

AND(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.COUNTA()=1
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.COUNTA()<=1
,
[🧪🤍😀起拍份数（原始版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.COUNTA()
>

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.COUNTA())

)



,
[🧪🤍😀起拍份数（单位统一版）]
.REGEXREPLACE("("&[🧠终极单位]&"|[:,，。])","")
.REGEXEXTRACTALL("❼.*?❼🔞")
.UNIQUE()
，

IF(
OR(
[🧪🤍😀起拍份数（单位统一版）]
.REGEXEXTRACTALL("❼.*?❼🔞").ARRAYJOIN()
.REGEXREPLACE("拍","")
=
[🧪🤍规格✖️数量提取（原始版）]
.REGEXEXTRACTALL("❼.*?❼🔞").ARRAYJOIN()
，

AND(
[🧪🤍😀起拍份数（单位统一版）]
.REGEXEXTRACTALL("❼.*?❼🔞").ARRAYJOIN()
.REGEXMATCH("共.*?\d+")
,
[🧪🤍规格✖️数量提取（多维度规格版）]
.REGEXEXTRACTALL("❼.*?❼🔞").ARRAYJOIN()
.REGEXREPLACE("[❶-❾🔞]","")

.REGEXMATCH(
[🧪🤍😀起拍份数（单位统一版）]
.REGEXEXTRACTALL("❼.*?共.*?(\d+(?:\.\d+)?"&[🧠单位合集]&").*?❼🔞").ARRAYJOIN()

))
，


[🧪🤍规格✖️数量提取（原始版）]
.REGEXEXTRACTALL("❼.*?❼🔞").ARRAYJOIN()
.REGEXEXTRACTALL("(\d+(?:\.\d+)?(?:"&[🧠单位合集]&"))").ARRAYJOIN()
.REGEXREPLACE("[❶-❾🔞]","")

.REGEXMATCH(
[🧪🤍😀起拍份数（单位统一版）]
.REGEXEXTRACTALL("❼.*?拍(\d+(?:\.\d+)?(?:"&[🧠单位合集]&")?).*?❼🔞").ARRAYJOIN())


,

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?\*.*?❼🔞").ARRAYJOIN()
.REGEXEXTRACTALL("\*(\d+(?:\.\d+)?)(?:"&[🧠单位合集]&")?").ARRAYJOIN()
.REGEXREPLACE("[❶-❾🔞]","")

=
[🧪🤍😀起拍份数（单位统一版）]
.REGEXEXTRACTALL("❼.*?拍(\d+(?:\.\d+)?)(?:"&[🧠单位合集]&")?.*?❼🔞").ARRAYJOIN()

)

,""
,
[🧪🤍😀起拍份数（单位统一版）]
.REGEXEXTRACTALL("❼.*?❼🔞").ARRAYJOIN("；")

))



,CHAR(10),

IF(

OR(
AND(

[💰到手价格（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.COUNTA()=1
,
[🧪🤍😀起拍份数（原始版）]
.REGEXEXTRACTALL("❽.*?([拍买]\d+[赠送发]\d+).*?❽🔞")
.COUNTA()
>

[💰到手价格（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.COUNTA())

,

AND(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.COUNTA()=1
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.COUNTA()<=1
,
[🧪🤍😀起拍份数（原始版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.COUNTA()
>

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.COUNTA())

)

,
[🧪🤍😀起拍份数（单位统一版）]
.REGEXREPLACE("("&[🧠终极单位]&"|[:,，。])","")
.REGEXEXTRACTALL("❽.*?❽🔞")
.UNIQUE()
，

IF(
OR(
[🧪🤍😀起拍份数（单位统一版）]
.REGEXEXTRACTALL("❽.*?❽🔞").ARRAYJOIN()
.REGEXREPLACE("拍","")
=
[🧪🤍规格✖️数量提取（原始版）]
.REGEXEXTRACTALL("❽.*?❽🔞").ARRAYJOIN()
，

AND(
[🧪🤍😀起拍份数（单位统一版）]
.REGEXEXTRACTALL("❽.*?❽🔞").ARRAYJOIN()
.REGEXMATCH("共.*?\d+")
,
[🧪🤍规格✖️数量提取（多维度规格版）]
.REGEXEXTRACTALL("❽.*?❽🔞").ARRAYJOIN()
.REGEXREPLACE("[❶-❾🔞]","")

.REGEXMATCH(
[🧪🤍😀起拍份数（单位统一版）]
.REGEXEXTRACTALL("❽.*?共.*?(\d+(?:\.\d+)?"&[🧠单位合集]&").*?❽🔞").ARRAYJOIN()

))
，


[🧪🤍规格✖️数量提取（原始版）]
.REGEXEXTRACTALL("❽.*?❽🔞").ARRAYJOIN()
.REGEXEXTRACTALL("(\d+(?:\.\d+)?(?:"&[🧠单位合集]&"))").ARRAYJOIN()
.REGEXREPLACE("[❶-❾🔞]","")

.REGEXMATCH(
[🧪🤍😀起拍份数（单位统一版）]
.REGEXEXTRACTALL("❽.*?拍(\d+(?:\.\d+)?(?:"&[🧠单位合集]&")?).*?❽🔞").ARRAYJOIN())


,

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?\*.*?❽🔞").ARRAYJOIN()
.REGEXEXTRACTALL("\*(\d+(?:\.\d+)?)(?:"&[🧠单位合集]&")?").ARRAYJOIN()
.REGEXREPLACE("[❶-❾🔞]","")

=
[🧪🤍😀起拍份数（单位统一版）]
.REGEXEXTRACTALL("❽.*?拍(\d+(?:\.\d+)?)(?:"&[🧠单位合集]&")?.*?❽🔞").ARRAYJOIN()

)

,""
,
[🧪🤍😀起拍份数（单位统一版）]
.REGEXEXTRACTALL("❽.*?❽🔞").ARRAYJOIN("；")

))




,CHAR(10),

IF(

OR(
AND(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.COUNTA()=1
,
[🧪🤍😀起拍份数（原始版）]
.REGEXEXTRACTALL("❾.*?([拍买]\d+[赠送发]\d+).*?❾🔞")
.COUNTA()
>

[💰到手价格（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.COUNTA())

,

AND(
[💰到手价格（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.COUNTA()=1
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.COUNTA()<=1
,
[🧪🤍😀起拍份数（原始版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.COUNTA()
>

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.COUNTA())

)


,
[🧪🤍😀起拍份数（单位统一版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.REGEXREPLACE("("&[🧠终极单位]&"|[:,，。])","")
.UNIQUE()
，

IF(
OR(
[🧪🤍😀起拍份数（单位统一版）]
.REGEXEXTRACTALL("❾.*?❾🔞").ARRAYJOIN()
.REGEXREPLACE("拍","")
=
[🧪🤍规格✖️数量提取（原始版）]
.REGEXEXTRACTALL("❾.*?❾🔞").ARRAYJOIN()
，

AND(
[🧪🤍😀起拍份数（单位统一版）]
.REGEXEXTRACTALL("❾.*?❾🔞").ARRAYJOIN()
.REGEXMATCH("共.*?\d+")
,
[🧪🤍规格✖️数量提取（多维度规格版）]
.REGEXEXTRACTALL("❾.*?❾🔞").ARRAYJOIN()
.REGEXREPLACE("[❶-❾🔞]","")

.REGEXMATCH(
[🧪🤍😀起拍份数（单位统一版）]
.REGEXEXTRACTALL("❾.*?共.*?(\d+(?:\.\d+)?"&[🧠单位合集]&").*?❾🔞").ARRAYJOIN()

))
，


[🧪🤍规格✖️数量提取（原始版）]
.REGEXEXTRACTALL("❾.*?❾🔞").ARRAYJOIN()
.REGEXEXTRACTALL("(\d+(?:\.\d+)?(?:"&[🧠单位合集]&"))").ARRAYJOIN()
.REGEXREPLACE("[❶-❾🔞]","")

.REGEXMATCH(
[🧪🤍😀起拍份数（单位统一版）]
.REGEXEXTRACTALL("❾.*?拍(\d+(?:\.\d+)?(?:"&[🧠单位合集]&")?).*?❾🔞").ARRAYJOIN())


,

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?\*.*?❾🔞").ARRAYJOIN()
.REGEXEXTRACTALL("\*(\d+(?:\.\d+)?)(?:"&[🧠单位合集]&")?").ARRAYJOIN()
.REGEXREPLACE("[❶-❾🔞]","")

=
[🧪🤍😀起拍份数（单位统一版）]
.REGEXEXTRACTALL("❾.*?拍(\d+(?:\.\d+)?)(?:"&[🧠单位合集]&")?.*?❾🔞").ARRAYJOIN()

)

,""
,
[🧪🤍😀起拍份数（单位统一版）]
.REGEXEXTRACTALL("❾.*?❾🔞").ARRAYJOIN("；")

))

))

.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🧪🤍😀起拍份数（终版）
CONCATENATE(
[🧪🤍😀起拍份数（清洗版）（1-5）]
，CHAR(10)，
[🧪🤍😀起拍份数（清洗版）（6-9）]
)

.REGEXREPLACE("共.*?([❶-❾]🔞)","$1")
.REGEXEXTRACTALL("([❶-❾]).*?拍(\d+(?:\.\d+)?件?).*?([❶-❾]🔞)")
.ARRAYJOIN(CHAR(10))
.REGEXREPLACE(",","")
.REGEXREPLACE("[❶-❾]1[件]?\s*[❶-❾]🔞","")
.REGEXREPLACE("\d+(?:\.\d+)?","$0件")
.REGEXREPLACE("[件份单]件","件")

.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🧪🔥🤍规格✖️数量✖️起拍数量
IF([🍬商品信息（原始版）].ISBLANK()，""，

CONCATENATE(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("(；?❶.*?❶🔞)")
.NTH(1)
，"*"，

[🧪🤍😀起拍份数（终版）]
.REGEXEXTRACTALL("(；?❶.*?❶🔞)")
.NTH(1)

,CHAR(10),


[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("；?❶.*?❶🔞")
.NTH(2)
，"*"，

[🧪🤍😀起拍份数（终版）]
.REGEXEXTRACTALL("；?❶.*?❶🔞")
.NTH(2)

,CHAR(10),


[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("；?❶.*?❶🔞")
.NTH(3)
，"*"，

[🧪🤍😀起拍份数（终版）]
.REGEXEXTRACTALL("；?❶.*?❶🔞")
.NTH(3)

,CHAR(10),


[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("；?❶.*?❶🔞")
.NTH(4)
，"*"，

[🧪🤍😀起拍份数（终版）]
.REGEXEXTRACTALL("；?❶.*?❶🔞")
.NTH(4)

,CHAR(10),


[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("；?❶.*?❶🔞")
.NTH(5)
，"*"，

[🧪🤍😀起拍份数（终版）]
.REGEXEXTRACTALL("；?❶.*?❶🔞")
.NTH(5)

,CHAR(10),


[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("；?❶.*?❶🔞")
.NTH(6)
，"*"，

[🧪🤍😀起拍份数（终版）]
.REGEXEXTRACTALL("；?❶.*?❶🔞")
.NTH(6)

,CHAR(10),


[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("；?❷.*?❷🔞")
.NTH(1)
，"*"，

[🧪🤍😀起拍份数（终版）]
.REGEXEXTRACTALL("；?❷.*?❷🔞")
.NTH(1)

,CHAR(10),


[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("；?❷.*?❷🔞")
.NTH(2)
，"*"，

[🧪🤍😀起拍份数（终版）]
.REGEXEXTRACTALL("；?❷.*?❷🔞")
.NTH(2)

,CHAR(10),


[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("；?❷.*?❷🔞")
.NTH(3)
，"*"，

[🧪🤍😀起拍份数（终版）]
.REGEXEXTRACTALL("；?❷.*?❷🔞")
.NTH(3)

,CHAR(10),


[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("；?❷.*?❷🔞")
.NTH(4)
，"*"，

[🧪🤍😀起拍份数（终版）]
.REGEXEXTRACTALL("；?❷.*?❷🔞")
.NTH(4)


,CHAR(10),


[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("；?❷.*?❷🔞")
.NTH(5)
，"*"，

[🧪🤍😀起拍份数（终版）]
.REGEXEXTRACTALL("；?❷.*?❷🔞")
.NTH(5)


,CHAR(10),


[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("；?❷.*?❷🔞")
.NTH(6)
，"*"，

[🧪🤍😀起拍份数（终版）]
.REGEXEXTRACTALL("；?❷.*?❷🔞")
.NTH(6)

,CHAR(10),

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("；?❸.*?❸🔞")
.NTH(1)
，"*"，

[🧪🤍😀起拍份数（终版）]
.REGEXEXTRACTALL("；?❸.*?❸🔞")
.NTH(1)

,CHAR(10),

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("；?❸.*?❸🔞")
.NTH(2)
，"*"，

[🧪🤍😀起拍份数（终版）]
.REGEXEXTRACTALL("；?❸.*?❸🔞")
.NTH(2)

,CHAR(10),

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("；?❸.*?❸🔞")
.NTH(3)
，"*"，

[🧪🤍😀起拍份数（终版）]
.REGEXEXTRACTALL("；?❸.*?❸🔞")
.NTH(3)

,CHAR(10),

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("；?❸.*?❸🔞")
.NTH(4)
，"*"，

[🧪🤍😀起拍份数（终版）]
.REGEXEXTRACTALL("；?❸.*?❸🔞")
.NTH(4)

,CHAR(10),

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("；?❸.*?❸🔞")
.NTH(5)
，"*"，

[🧪🤍😀起拍份数（终版）]
.REGEXEXTRACTALL("；?❸.*?❸🔞")
.NTH(5)

,CHAR(10),

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("；?❸.*?❸🔞")
.NTH(6)
，"*"，

[🧪🤍😀起拍份数（终版）]
.REGEXEXTRACTALL("；?❸.*?❸🔞")
.NTH(6)

,CHAR(10),

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("；?❹.*?❹🔞")
.NTH(1)
，"*"，

[🧪🤍😀起拍份数（终版）]
.REGEXEXTRACTALL("；?❹.*?❹🔞")
.NTH(1)

,CHAR(10),

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("；?❹.*?❹🔞")
.NTH(2)
，"*"，

[🧪🤍😀起拍份数（终版）]
.REGEXEXTRACTALL("；?❹.*?❹🔞")
.NTH(2)

,CHAR(10),

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("；?❹.*?❹🔞")
.NTH(3)
，"*"，

[🧪🤍😀起拍份数（终版）]
.REGEXEXTRACTALL("；?❹.*?❹🔞")
.NTH(3)

,CHAR(10),

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("；?❹.*?❹🔞")
.NTH(4)
，"*"，

[🧪🤍😀起拍份数（终版）]
.REGEXEXTRACTALL("；?❹.*?❹🔞")
.NTH(4)

,CHAR(10),

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("；?❹.*?❹🔞")
.NTH(5)
，"*"，

[🧪🤍😀起拍份数（终版）]
.REGEXEXTRACTALL("；?❹.*?❹🔞")
.NTH(5)

,CHAR(10),

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("；?❹.*?❹🔞")
.NTH(6)
，"*"，

[🧪🤍😀起拍份数（终版）]
.REGEXEXTRACTALL("；?❹.*?❹🔞")
.NTH(6)

,CHAR(10),

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("；?❺.*?❺🔞")
.NTH(1)
，"*"，

[🧪🤍😀起拍份数（终版）]
.REGEXEXTRACTALL("；?❺.*?❺🔞")
.NTH(1)

,CHAR(10),

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("；?❺.*?❺🔞")
.NTH(2)
，"*"，

[🧪🤍😀起拍份数（终版）]
.REGEXEXTRACTALL("；?❺.*?❺🔞")
.NTH(2)

,CHAR(10),

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("；?❺.*?❺🔞")
.NTH(3)
，"*"，

[🧪🤍😀起拍份数（终版）]
.REGEXEXTRACTALL("；?❺.*?❺🔞")
.NTH(3)

,CHAR(10),

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("；?❺.*?❺🔞")
.NTH(4)
，"*"，

[🧪🤍😀起拍份数（终版）]
.REGEXEXTRACTALL("；?❺.*?❺🔞")
.NTH(4)

,CHAR(10),

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("；?❺.*?❺🔞")
.NTH(5)
，"*"，

[🧪🤍😀起拍份数（终版）]
.REGEXEXTRACTALL("；?❺.*?❺🔞")
.NTH(5)

,CHAR(10),

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("；?❺.*?❺🔞")
.NTH(6)
，"*"，

[🧪🤍😀起拍份数（终版）]
.REGEXEXTRACTALL("；?❺.*?❺🔞")
.NTH(6)

,CHAR(10),

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("；?❻.*?❻🔞")
.NTH(1)
，"*"，

[🧪🤍😀起拍份数（终版）]
.REGEXEXTRACTALL("；?❻.*?❻🔞")
.NTH(1)

,CHAR(10),

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("；?❻.*?❻🔞")
.NTH(2)
，"*"，

[🧪🤍😀起拍份数（终版）]
.REGEXEXTRACTALL("；?❻.*?❻🔞")
.NTH(2)

,CHAR(10),

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("；?❻.*?❻🔞")
.NTH(3)
，"*"，

[🧪🤍😀起拍份数（终版）]
.REGEXEXTRACTALL("；?❻.*?❻🔞")
.NTH(3)

,CHAR(10),

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("；?❻.*?❻🔞")
.NTH(4)
，"*"，

[🧪🤍😀起拍份数（终版）]
.REGEXEXTRACTALL("；?❻.*?❻🔞")
.NTH(4)

,CHAR(10),

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("；?❻.*?❻🔞")
.NTH(5)
，"*"，

[🧪🤍😀起拍份数（终版）]
.REGEXEXTRACTALL("；?❻.*?❻🔞")
.NTH(5)

,CHAR(10),

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("；?❻.*?❻🔞")
.NTH(6)
，"*"，

[🧪🤍😀起拍份数（终版）]
.REGEXEXTRACTALL("；?❻.*?❻🔞")
.NTH(6)

,CHAR(10),

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("；?❼.*?❼🔞")
.NTH(1)
，"*"，

[🧪🤍😀起拍份数（终版）]
.REGEXEXTRACTALL("；?❼.*?❼🔞")
.NTH(1)

,CHAR(10),

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("；?❼.*?❼🔞")
.NTH(2)
，"*"，

[🧪🤍😀起拍份数（终版）]
.REGEXEXTRACTALL("；?❼.*?❼🔞")
.NTH(2)

,CHAR(10),

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("；?❼.*?❼🔞")
.NTH(3)
，"*"，

[🧪🤍😀起拍份数（终版）]
.REGEXEXTRACTALL("；?❼.*?❼🔞")
.NTH(3)

,CHAR(10),

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("；?❼.*?❼🔞")
.NTH(4)
，"*"，

[🧪🤍😀起拍份数（终版）]
.REGEXEXTRACTALL("；?❼.*?❼🔞")
.NTH(4)

,CHAR(10),

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("；?❼.*?❼🔞")
.NTH(5)
，"*"，

[🧪🤍😀起拍份数（终版）]
.REGEXEXTRACTALL("；?❼.*?❼🔞")
.NTH(5)

,CHAR(10),

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("；?❼.*?❼🔞")
.NTH(6)
，"*"，

[🧪🤍😀起拍份数（终版）]
.REGEXEXTRACTALL("；?❼.*?❼🔞")
.NTH(6)


,CHAR(10),

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("；?❽.*?❽🔞")
.NTH(1)
，"*"，

[🧪🤍😀起拍份数（终版）]
.REGEXEXTRACTALL("；?❽.*?❽🔞")
.NTH(1)

,CHAR(10),

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("；?❽.*?❽🔞")
.NTH(2)
，"*"，

[🧪🤍😀起拍份数（终版）]
.REGEXEXTRACTALL("；?❽.*?❽🔞")
.NTH(2)

,CHAR(10),

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("；?❽.*?❽🔞")
.NTH(3)
，"*"，

[🧪🤍😀起拍份数（终版）]
.REGEXEXTRACTALL("；?❽.*?❽🔞")
.NTH(3)

,CHAR(10),

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("；?❽.*?❽🔞")
.NTH(4)
，"*"，

[🧪🤍😀起拍份数（终版）]
.REGEXEXTRACTALL("；?❽.*?❽🔞")
.NTH(4)

,CHAR(10),

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("；?❽.*?❽🔞")
.NTH(5)
，"*"，

[🧪🤍😀起拍份数（终版）]
.REGEXEXTRACTALL("；?❽.*?❽🔞")
.NTH(5)

,CHAR(10),

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("；?❽.*?❽🔞")
.NTH(6)
，"*"，

[🧪🤍😀起拍份数（终版）]
.REGEXEXTRACTALL("；?❽.*?❽🔞")
.NTH(6)


,CHAR(10),

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("；?❾.*?❾🔞")
.NTH(1)
，"*"，

[🧪🤍😀起拍份数（终版）]
.REGEXEXTRACTALL("；?❾.*?❾🔞")
.NTH(1)

,CHAR(10),

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("；?❾.*?❾🔞")
.NTH(2)
，"*"，

[🧪🤍😀起拍份数（终版）]
.REGEXEXTRACTALL("；?❾.*?❾🔞")
.NTH(2)

,CHAR(10),

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("；?❾.*?❾🔞")
.NTH(3)
，"*"，

[🧪🤍😀起拍份数（终版）]
.REGEXEXTRACTALL("；?❾.*?❾🔞")
.NTH(3)

,CHAR(10),

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("；?❾.*?❾🔞")
.NTH(4)
，"*"，

[🧪🤍😀起拍份数（终版）]
.REGEXEXTRACTALL("；?❾.*?❾🔞")
.NTH(4)

,CHAR(10),

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("；?❾.*?❾🔞")
.NTH(5)
，"*"，

[🧪🤍😀起拍份数（终版）]
.REGEXEXTRACTALL("；?❾.*?❾🔞")
.NTH(5)

,CHAR(10),

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("；?❾.*?❾🔞")
.NTH(6)
，"*"，

[🧪🤍😀起拍份数（终版）]
.REGEXEXTRACTALL("；?❾.*?❾🔞")
.NTH(6)

))


.REGEXREPLACE("\*\s*；","；")
.REGEXREPLACE("(\*\s?)+","*")
.REGEXREPLACE("^\*","")
.REGEXREPLACE("\*+$","")
.REGEXREPLACE("❶🔞\*❶","*")
.REGEXREPLACE("❷🔞\*❷","*")
.REGEXREPLACE("❸🔞\*❸","*")
.REGEXREPLACE("❹🔞\*❹","*")
.REGEXREPLACE("❺🔞\*❺","*")
.REGEXREPLACE("❻🔞\*❻","*")
.REGEXREPLACE("❼🔞\*❼","*")
.REGEXREPLACE("❽🔞\*❽","*")
.REGEXREPLACE("❾🔞\*❾","*")
.REGEXREPLACE("[；;]","")
.REGEXREPLACE("([❶-❾]🔞)\s*\*([❶-❾])","$1"&CHAR(10)&"$2")
.REGEXREPLACE("([❶-❾]🔞)\s*([❶-❾])"，"$1"&CHAR(10)&"$2")



.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🧪🤍😀终极数量
[🧪🔥🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("([❶-❾]).*?(?:(?:\d+).*?\*)(\d+)"&[🧠终极单位]&".*?([❶-❾]🔞)|♎️
|([❶-❾]).*?(?:\d+"&[🧠进阶单位]&")\*(\d+)([❶-❾]🔞)|([❶-❾]).*?(?:\d+"&[🧠高阶单位]&")\*(\d+)([❶-❾]🔞)")
.ARRAYJOIN(CHAR(10))
.REGEXREPLACE(",","")
.REGEXREPLACE("[❶-❾]1[❶-❾]🔞","")
=====
🧪🤍基础规格（原始版）CONCATENATE(
[🧪🔥🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❶.*❶🔞")
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ARRAYJOIN("；")
.REGEXREPLACE([🧠规格提取参考-逻辑], "❶$0❶🔞")


,CHAR(10),



[🧪🔥🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❷.*❷🔞")
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ARRAYJOIN("；")
.REGEXREPLACE([🧠规格提取参考-逻辑], "❷$0❷🔞")



,CHAR(10),


[🧪🔥🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❸.*❸🔞")
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ARRAYJOIN("；")
.REGEXREPLACE([🧠规格提取参考-逻辑], "❸$0❸🔞")


,CHAR(10),



[🧪🔥🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❹.*❹🔞")
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ARRAYJOIN("；")
.REGEXREPLACE([🧠规格提取参考-逻辑], "❹$0❹🔞")



,CHAR(10),



[🧪🔥🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❺.*❺🔞")
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ARRAYJOIN("；")
.REGEXREPLACE([🧠规格提取参考-逻辑], "❺$0❺🔞")



,CHAR(10),


[🧪🔥🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❻.*❻🔞")
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ARRAYJOIN("；")
.REGEXREPLACE([🧠规格提取参考-逻辑], "❻$0❻🔞")



,CHAR(10),


[🧪🔥🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❼.*❼🔞")
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ARRAYJOIN("；")
.REGEXREPLACE([🧠规格提取参考-逻辑], "❼$0❼🔞")



,CHAR(10),



[🧪🔥🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❽.*❽🔞")
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ARRAYJOIN("；")
.REGEXREPLACE([🧠规格提取参考-逻辑], "❽$0❽🔞")



，CHAR(10)，


[🧪🔥🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❾.*❾🔞")
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ARRAYJOIN("；")
.REGEXREPLACE([🧠规格提取参考-逻辑], "❾$0❾🔞")


)

.REGEXREPLACE("🎁","")
.REGEXREPLACE("[；*]?\d+(?:\.\d+)?("&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&"|"&[🧠终极单位]&")；?","")

.REGEXREPLACE([🧠乘法符号]&"\d+(?:\.\d+)?","")

.REGEXREPLACE("[❶-❾][❶-❾]🔞；?","")
.REGEXREPLACE("[;；,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🧪🤍基础规格-单位提取
[🧪🤍基础规格（原始版）]
.REGEXREPLACE("\d+(?:\.\d+)?"，"")
=====
🧪🤍基础规格-数字提取
[🧪🤍基础规格（原始版）]
.REGEXREPLACE([🧠基础单位]，"")
.REGEXREPLACE("[,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🧪🤍基础规格（清洗版+终版）
CONCATENATE(
  IF(
    [🧪🤍规格类型（原始值）].REGEXEXTRACT("❶.*?❶🔞").REGEXMATCH("礼包"),
    "",
    CONCATENATE(
      [🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❶.*🔞").SPLIT("；").NTH(1),
      [🧪🤍基础规格-单位提取].REGEXEXTRACTALL("❶.*🔞").SPLIT("；").NTH(1),
      "；",
      [🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❶.*🔞").SPLIT("；").NTH(2),
      [🧪🤍基础规格-单位提取].REGEXEXTRACTALL("❶.*🔞").SPLIT("；").NTH(2),
      "；",
      [🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❶.*🔞").SPLIT("；").NTH(3),
      [🧪🤍基础规格-单位提取].REGEXEXTRACTALL("❶.*🔞").SPLIT("；").NTH(3),
      "；",
      [🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❶.*🔞").SPLIT("；").NTH(4),
      [🧪🤍基础规格-单位提取].REGEXEXTRACTALL("❶.*🔞").SPLIT("；").NTH(4),
      "；",
      [🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❶.*🔞").SPLIT("；").NTH(5),
      [🧪🤍基础规格-单位提取].REGEXEXTRACTALL("❶.*🔞").SPLIT("；").NTH(5),
      "；",
      [🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❶.*🔞").SPLIT("；").NTH(6),
      [🧪🤍基础规格-单位提取].REGEXEXTRACTALL("❶.*🔞").SPLIT("；").NTH(6)
    )
  )

  
,CHAR(10),


  IF(
    [🧪🤍规格类型（原始值）].REGEXEXTRACT("❷.*?❷🔞").REGEXMATCH("礼包"),
    "",
    CONCATENATE(
      [🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❷.*🔞").SPLIT("；").NTH(1),
      [🧪🤍基础规格-单位提取].REGEXEXTRACTALL("❷.*🔞").SPLIT("；").NTH(1),
      "；",
      [🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❷.*🔞").SPLIT("；").NTH(2),
      [🧪🤍基础规格-单位提取].REGEXEXTRACTALL("❷.*🔞").SPLIT("；").NTH(2),
      "；",
      [🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❷.*🔞").SPLIT("；").NTH(3),
      [🧪🤍基础规格-单位提取].REGEXEXTRACTALL("❷.*🔞").SPLIT("；").NTH(3),
      "；",
      [🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❷.*🔞").SPLIT("；").NTH(4),
      [🧪🤍基础规格-单位提取].REGEXEXTRACTALL("❷.*🔞").SPLIT("；").NTH(4),
      "；",
      [🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❷.*🔞").SPLIT("；").NTH(5),
      [🧪🤍基础规格-单位提取].REGEXEXTRACTALL("❷.*🔞").SPLIT("；").NTH(5),
      "；",
      [🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❷.*🔞").SPLIT("；").NTH(6),
      [🧪🤍基础规格-单位提取].REGEXEXTRACTALL("❷.*🔞").SPLIT("；").NTH(6)
    )
  )
," 
",
  IF(
    [🧪🤍规格类型（原始值）].REGEXEXTRACT("❸.*?❸🔞").REGEXMATCH("礼包"),
    "",
    CONCATENATE(
      [🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❸.*🔞").SPLIT("；").NTH(1),
      [🧪🤍基础规格-单位提取].REGEXEXTRACTALL("❸.*🔞").SPLIT("；").NTH(1),
      "；",
      [🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❸.*🔞").SPLIT("；").NTH(2),
      [🧪🤍基础规格-单位提取].REGEXEXTRACTALL("❸.*🔞").SPLIT("；").NTH(2),
      "；",
      [🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❸.*🔞").SPLIT("；").NTH(3),
      [🧪🤍基础规格-单位提取].REGEXEXTRACTALL("❸.*🔞").SPLIT("；").NTH(3),
      "；",
      [🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❸.*🔞").SPLIT("；").NTH(4),
      [🧪🤍基础规格-单位提取].REGEXEXTRACTALL("❸.*🔞").SPLIT("；").NTH(4),
      "；",
      [🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❸.*🔞").SPLIT("；").NTH(5),
      [🧪🤍基础规格-单位提取].REGEXEXTRACTALL("❸.*🔞").SPLIT("；").NTH(5),
      "；",
      [🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❸.*🔞").SPLIT("；").NTH(6),
      [🧪🤍基础规格-单位提取].REGEXEXTRACTALL("❸.*🔞").SPLIT("；").NTH(6)
    )
  )
  
,CHAR(10),


  IF(
    [🧪🤍规格类型（原始值）].REGEXEXTRACT("❹.*?❹🔞").REGEXMATCH("礼包"),
    "",
    CONCATENATE(
      [🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❹.*🔞").SPLIT("；").NTH(1),
      [🧪🤍基础规格-单位提取].REGEXEXTRACTALL("❹.*🔞").SPLIT("；").NTH(1),
      "；",
      [🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❹.*🔞").SPLIT("；").NTH(2),
      [🧪🤍基础规格-单位提取].REGEXEXTRACTALL("❹.*🔞").SPLIT("；").NTH(2),
      "；",
      [🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❹.*🔞").SPLIT("；").NTH(3),
      [🧪🤍基础规格-单位提取].REGEXEXTRACTALL("❹.*🔞").SPLIT("；").NTH(3),
      "；",
      [🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❹.*🔞").SPLIT("；").NTH(4),
      [🧪🤍基础规格-单位提取].REGEXEXTRACTALL("❹.*🔞").SPLIT("；").NTH(4),
      "；",
      [🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❹.*🔞").SPLIT("；").NTH(5),
      [🧪🤍基础规格-单位提取].REGEXEXTRACTALL("❹.*🔞").SPLIT("；").NTH(5),
      "；",
      [🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❹.*🔞").SPLIT("；").NTH(6),
      [🧪🤍基础规格-单位提取].REGEXEXTRACTALL("❹.*🔞").SPLIT("；").NTH(6)
    )
  )
  
,CHAR(10),


  IF(
    [🧪🤍规格类型（原始值）].REGEXEXTRACT("❺.*?❺🔞").REGEXMATCH("礼包"),
    "",
    CONCATENATE(
      [🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❺.*🔞").SPLIT("；").NTH(1),
      [🧪🤍基础规格-单位提取].REGEXEXTRACTALL("❺.*🔞").SPLIT("；").NTH(1),
      "；",
      [🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❺.*🔞").SPLIT("；").NTH(2),
      [🧪🤍基础规格-单位提取].REGEXEXTRACTALL("❺.*🔞").SPLIT("；").NTH(2),
      "；",
      [🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❺.*🔞").SPLIT("；").NTH(3),
      [🧪🤍基础规格-单位提取].REGEXEXTRACTALL("❺.*🔞").SPLIT("；").NTH(3),
      "；",
      [🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❺.*🔞").SPLIT("；").NTH(4),
      [🧪🤍基础规格-单位提取].REGEXEXTRACTALL("❺.*🔞").SPLIT("；").NTH(4),
      "；",
      [🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❺.*🔞").SPLIT("；").NTH(5),
      [🧪🤍基础规格-单位提取].REGEXEXTRACTALL("❺.*🔞").SPLIT("；").NTH(5),
      "；",
      [🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❺.*🔞").SPLIT("；").NTH(6),
      [🧪🤍基础规格-单位提取].REGEXEXTRACTALL("❺.*🔞").SPLIT("；").NTH(6)
    )
  )
  
,CHAR(10),

  IF(
    [🧪🤍规格类型（原始值）].REGEXEXTRACT("❻.*?❻🔞").REGEXMATCH("礼包"),
    "",
    CONCATENATE(
      [🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❻.*🔞").SPLIT("；").NTH(1),
      [🧪🤍基础规格-单位提取].REGEXEXTRACTALL("❻.*🔞").SPLIT("；").NTH(1),
      "；",
      [🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❻.*🔞").SPLIT("；").NTH(2),
      [🧪🤍基础规格-单位提取].REGEXEXTRACTALL("❻.*🔞").SPLIT("；").NTH(2),
      "；",
      [🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❻.*🔞").SPLIT("；").NTH(3),
      [🧪🤍基础规格-单位提取].REGEXEXTRACTALL("❻.*🔞").SPLIT("；").NTH(3),
      "；",
      [🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❻.*🔞").SPLIT("；").NTH(4),
      [🧪🤍基础规格-单位提取].REGEXEXTRACTALL("❻.*🔞").SPLIT("；").NTH(4),
      "；",
      [🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❻.*🔞").SPLIT("；").NTH(5),
      [🧪🤍基础规格-单位提取].REGEXEXTRACTALL("❻.*🔞").SPLIT("；").NTH(5),
      "；",
      [🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❻.*🔞").SPLIT("；").NTH(6),
      [🧪🤍基础规格-单位提取].REGEXEXTRACTALL("❻.*🔞").SPLIT("；").NTH(6)
    )
  )


，CHAR(10)，

IF(
  [🧪🤍规格类型（原始值）].REGEXEXTRACT("❼.*?❼🔞").REGEXMATCH("礼包"),
  "",
  CONCATENATE(
    [🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❼.*🔞").SPLIT("；").NTH(1),
    [🧪🤍基础规格-单位提取].REGEXEXTRACTALL("❼.*🔞").SPLIT("；").NTH(1),
    "；",
    [🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❼.*🔞").SPLIT("；").NTH(2),
    [🧪🤍基础规格-单位提取].REGEXEXTRACTALL("❼.*🔞").SPLIT("；").NTH(2),
    "；",
    [🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❼.*🔞").SPLIT("；").NTH(3),
    [🧪🤍基础规格-单位提取].REGEXEXTRACTALL("❼.*🔞").SPLIT("；").NTH(3),
    "；",
    [🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❼.*🔞").SPLIT("；").NTH(4),
    [🧪🤍基础规格-单位提取].REGEXEXTRACTALL("❼.*🔞").SPLIT("；").NTH(4),
    "；",
    [🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❼.*🔞").SPLIT("；").NTH(5),
    [🧪🤍基础规格-单位提取].REGEXEXTRACTALL("❼.*🔞").SPLIT("；").NTH(5),
    "；",
    [🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❼.*🔞").SPLIT("；").NTH(6),
    [🧪🤍基础规格-单位提取].REGEXEXTRACTALL("❼.*🔞").SPLIT("；").NTH(6)
  )
)


，CHAR(10)，


IF(
  [🧪🤍规格类型（原始值）].REGEXEXTRACT("❽.*?❽🔞").REGEXMATCH("礼包"),
  "",
  CONCATENATE(
    [🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❽.*🔞").SPLIT("；").NTH(1),
    [🧪🤍基础规格-单位提取].REGEXEXTRACTALL("❽.*🔞").SPLIT("；").NTH(1),
    "；",
    [🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❽.*🔞").SPLIT("；").NTH(2),
    [🧪🤍基础规格-单位提取].REGEXEXTRACTALL("❽.*🔞").SPLIT("；").NTH(2),
    "；",
    [🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❽.*🔞").SPLIT("；").NTH(3),
    [🧪🤍基础规格-单位提取].REGEXEXTRACTALL("❽.*🔞").SPLIT("；").NTH(3),
    "；",
    [🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❽.*🔞").SPLIT("；").NTH(4),
    [🧪🤍基础规格-单位提取].REGEXEXTRACTALL("❽.*🔞").SPLIT("；").NTH(4),
    "；",
    [🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❽.*🔞").SPLIT("；").NTH(5),
    [🧪🤍基础规格-单位提取].REGEXEXTRACTALL("❽.*🔞").SPLIT("；").NTH(5),
    "；",
    [🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❽.*🔞").SPLIT("；").NTH(6),
    [🧪🤍基础规格-单位提取].REGEXEXTRACTALL("❽.*🔞").SPLIT("；").NTH(6)
  )
)


，CHAR(10)，

IF(
  [🧪🤍规格类型（原始值）].REGEXEXTRACT("❾.*?❾🔞").REGEXMATCH("礼包"),
  "",
  CONCATENATE(
    [🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❾.*🔞").SPLIT("；").NTH(1),
    [🧪🤍基础规格-单位提取].REGEXEXTRACTALL("❾.*🔞").SPLIT("；").NTH(1),
    "；",
    [🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❾.*🔞").SPLIT("；").NTH(2),
    [🧪🤍基础规格-单位提取].REGEXEXTRACTALL("❾.*🔞").SPLIT("；").NTH(2),
    "；",
    [🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❾.*🔞").SPLIT("；").NTH(3),
    [🧪🤍基础规格-单位提取].REGEXEXTRACTALL("❾.*🔞").SPLIT("；").NTH(3),
    "；",
    [🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❾.*🔞").SPLIT("；").NTH(4),
    [🧪🤍基础规格-单位提取].REGEXEXTRACTALL("❾.*🔞").SPLIT("；").NTH(4),
    "；",
    [🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❾.*🔞").SPLIT("；").NTH(5),
    [🧪🤍基础规格-单位提取].REGEXEXTRACTALL("❾.*🔞").SPLIT("；").NTH(5),
    "；",
    [🧪🤍基础规格-数字提取].REGEXEXTRACTALL("❾.*🔞").SPLIT("；").NTH(6),
    [🧪🤍基础规格-单位提取].REGEXEXTRACTALL("❾.*🔞").SPLIT("；").NTH(6)
  )
)


)

.REGEXREPLACE("\s*","")
.REGEXREPLACE("；+","；")
.REGEXREPLACE("^；","")

.REGEXREPLACE("[❶-❾]🔞[❶-❾]","")
.REGEXREPLACE("[❶-❾](?:"&[🧠基础单位]&[🧠初级单位]&[🧠进阶单位]&"|"&[🧠高阶单位]&")[❶-❾]🔞[;；]?","")
.REGEXREPLACE("([❶-❾])"&[🧠乘法符号],"$1")
.REGEXREPLACE([🧠乘法符号]&"([❶-❾])","$1")

.REGEXREPLACE("[;；。.,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🧪🤍初级数量（原始版）
CONCATENATE(
[🧪🔥🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❶.*❶🔞")
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ARRAYJOIN("；")
.REGEXREPLACE([🧠规格提取参考-逻辑], "❶$0❶🔞")


,CHAR(10),



[🧪🔥🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❷.*❷🔞")
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ARRAYJOIN("；")
.REGEXREPLACE([🧠规格提取参考-逻辑], "❷$0❷🔞")



,CHAR(10),


[🧪🔥🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❸.*❸🔞")
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ARRAYJOIN("；")
.REGEXREPLACE([🧠规格提取参考-逻辑], "❸$0❸🔞")


,CHAR(10),



[🧪🔥🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❹.*❹🔞")
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ARRAYJOIN("；")
.REGEXREPLACE([🧠规格提取参考-逻辑], "❹$0❹🔞")



,CHAR(10),



[🧪🔥🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❺.*❺🔞")
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ARRAYJOIN("；")
.REGEXREPLACE([🧠规格提取参考-逻辑], "❺$0❺🔞")



,CHAR(10),


[🧪🔥🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❻.*❻🔞")
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ARRAYJOIN("；")
.REGEXREPLACE([🧠规格提取参考-逻辑], "❻$0❻🔞")



,CHAR(10),


[🧪🔥🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❼.*❼🔞")
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ARRAYJOIN("；")
.REGEXREPLACE([🧠规格提取参考-逻辑], "❼$0❼🔞")



,CHAR(10),



[🧪🔥🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❽.*❽🔞")
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ARRAYJOIN("；")
.REGEXREPLACE([🧠规格提取参考-逻辑], "❽$0❽🔞")



，CHAR(10)，


[🧪🔥🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❾.*❾🔞")
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ARRAYJOIN("；")
.REGEXREPLACE([🧠规格提取参考-逻辑], "❾$0❾🔞")


)


.REGEXREPLACE("🎁","")
.REGEXREPLACE("[；*]?\*?\d+(?:\.\d+)?("&[🧠基础单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&"|"&[🧠终极单位]&")；?","")

.REGEXREPLACE("(\d+"&[🧠初级单位]&")(?:\*.*?)*([❶-❾]🔞)","$1$2")

.REGEXREPLACE("([❶-❾])"&[🧠乘法符号],"$1")
.REGEXREPLACE([🧠乘法符号]&"([❶-❾])","$1")


.REGEXREPLACE("[❶-❾][❶-❾]🔞；?","")
.REGEXREPLACE("[;；,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🧪🤍初级数量-单位提取（参考）
[🧪🤍初级数量（原始版）]
.REGEXREPLACE("\d+(?:\.\d+)?"，"")
.REGEXREPLACE("(❶❶|❷❷|❸❸|❹❹|❺❺|❻❻|❼❼|❽❽|❾❾)🔞；?","")
.REGEXREPLACE("^；","")


.REGEXREPLACE("[;；,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🧪🤍初级数量-单位提取（终版）
CONCATENATE(
IF(ISBLANK(
[🧪🤍初级数量-单位提取（参考）].REGEXEXTRACTALL("❶.*❶🔞"))，

IF([🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❶.*❶🔞").REGEXMATCH("主粮|肉干|冻干")
，"❶袋❶🔞；❶袋❶🔞；❶袋❶🔞；❶袋❶🔞；❶袋❶🔞；❶袋❶🔞"
，

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❶.*❶🔞").REGEXMATCH("猫砂|餐包")
，"❶包❶🔞；❶包❶🔞；❶包❶🔞；❶包❶🔞；❶包❶🔞；❶包❶🔞"
，

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❶.*❶🔞").REGEXMATCH("猫条|狗条")
，"❶支❶🔞；❶支❶🔞；❶支❶🔞；❶支❶🔞；❶支❶🔞；❶支❶🔞"
，
IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❶.*❶🔞").REGEXMATCH("罐头")
，"❶罐❶🔞；❶罐❶🔞；❶罐❶🔞；❶罐❶🔞；❶罐❶🔞；❶罐❶🔞"
，

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❶.*❶🔞").REGEXMATCH("餐盒")
，"❶盒❶🔞；❶盒❶🔞；❶盒❶🔞；❶盒❶🔞；❶盒❶🔞；❶盒❶🔞"
，

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❶.*❶🔞").REGEXMATCH("除臭剂|消毒剂")
，"❶瓶❶🔞；❶瓶❶🔞；❶瓶❶🔞；❶瓶❶🔞；❶瓶❶🔞；❶瓶❶🔞"
，


IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❶.*❶🔞").REGEXMATCH("其他")
,""

)))))))
，
CONCATENATE(
[🧪🤍初级数量-单位提取（参考）].REGEXEXTRACT("❶.*?❶🔞")
,"；"，
[🧪🤍初级数量-单位提取（参考）].REGEXEXTRACT("❶.*?❶🔞")
,"；"，

[🧪🤍初级数量-单位提取（参考）].REGEXEXTRACT("❶.*?❶🔞")
,"；"，

[🧪🤍初级数量-单位提取（参考）].REGEXEXTRACT("❶.*?❶🔞")
,"；"，

[🧪🤍初级数量-单位提取（参考）].REGEXEXTRACT("❶.*?❶🔞")
,"；"，

[🧪🤍初级数量-单位提取（参考）].REGEXEXTRACT("❶.*?❶🔞")


))


，CHAR(10)，


IF(ISBLANK(
[🧪🤍初级数量-单位提取（参考）].REGEXEXTRACTALL("❷.*❷🔞")),

IF([🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❷.*❷🔞").REGEXMATCH("主粮|肉干|冻干"),
"❷袋❷🔞；❷袋❷🔞；❷袋❷🔞；❷袋❷🔞；❷袋❷🔞；❷袋❷🔞",

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❷.*❷🔞").REGEXMATCH("猫砂|餐包"),
"❷包❷🔞；❷包❷🔞；❷包❷🔞；❷包❷🔞；❷包❷🔞；❷包❷🔞",

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❷.*❷🔞").REGEXMATCH("猫条|狗条"),
"❷支❷🔞；❷支❷🔞；❷支❷🔞；❷支❷🔞；❷支❷🔞；❷支❷🔞",

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❷.*❷🔞").REGEXMATCH("罐头"),
"❷罐❷🔞；❷罐❷🔞；❷罐❷🔞；❷罐❷🔞；❷罐❷🔞；❷罐❷🔞",

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❷.*❷🔞").REGEXMATCH("餐盒"),
"❷盒❷🔞；❷盒❷🔞；❷盒❷🔞；❷盒❷🔞；❷盒❷🔞；❷盒❷🔞",

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❷.*❷🔞").REGEXMATCH("除臭剂|消毒剂"),
"❷瓶❷🔞；❷瓶❷🔞；❷瓶❷🔞；❷瓶❷🔞；❷瓶❷🔞；❷瓶❷🔞",

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❷.*❷🔞").REGEXMATCH("其他"),
""

))))))),
CONCATENATE(
[🧪🤍初级数量-单位提取（参考）].REGEXEXTRACT("❷.*?❷🔞"),
"；",
[🧪🤍初级数量-单位提取（参考）].REGEXEXTRACT("❷.*?❷🔞"),
"；",
[🧪🤍初级数量-单位提取（参考）].REGEXEXTRACT("❷.*?❷🔞"),
"；",
[🧪🤍初级数量-单位提取（参考）].REGEXEXTRACT("❷.*?❷🔞"),
"；",
[🧪🤍初级数量-单位提取（参考）].REGEXEXTRACT("❷.*?❷🔞"),
"；",
[🧪🤍初级数量-单位提取（参考）].REGEXEXTRACT("❷.*?❷🔞")
))




，CHAR(10)，


IF(ISBLANK(
[🧪🤍初级数量-单位提取（参考）].REGEXEXTRACTALL("❸.*❸🔞")),

IF([🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❸.*❸🔞").REGEXMATCH("主粮|肉干|冻干"),
"❸袋❸🔞；❸袋❸🔞；❸袋❸🔞；❸袋❸🔞；❸袋❸🔞；❸袋❸🔞",

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❸.*❸🔞").REGEXMATCH("猫砂|餐包"),
"❸包❸🔞；❸包❸🔞；❸包❸🔞；❸包❸🔞；❸包❸🔞；❸包❸🔞",

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❸.*❸🔞").REGEXMATCH("猫条|狗条"),
"❸支❸🔞；❸支❸🔞；❸支❸🔞；❸支❸🔞；❸支❸🔞；❸支❸🔞",

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❸.*❸🔞").REGEXMATCH("罐头"),
"❸罐❸🔞；❸罐❸🔞；❸罐❸🔞；❸罐❸🔞；❸罐❸🔞；❸罐❸🔞",

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❸.*❸🔞").REGEXMATCH("餐盒"),
"❸盒❸🔞；❸盒❸🔞；❸盒❸🔞；❸盒❸🔞；❸盒❸🔞；❸盒❸🔞",

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❸.*❸🔞").REGEXMATCH("除臭剂|消毒剂"),
"❸瓶❸🔞；❸瓶❸🔞；❸瓶❸🔞；❸瓶❸🔞；❸瓶❸🔞；❸瓶❸🔞",

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❸.*❸🔞").REGEXMATCH("其他"),
""

))))))),
CONCATENATE(
[🧪🤍初级数量-单位提取（参考）].REGEXEXTRACT("❸.*?❸🔞"),
"；",
[🧪🤍初级数量-单位提取（参考）].REGEXEXTRACT("❸.*?❸🔞"),
"；",
[🧪🤍初级数量-单位提取（参考）].REGEXEXTRACT("❸.*?❸🔞"),
"；",
[🧪🤍初级数量-单位提取（参考）].REGEXEXTRACT("❸.*?❸🔞"),
"；",
[🧪🤍初级数量-单位提取（参考）].REGEXEXTRACT("❸.*?❸🔞"),
"；",
[🧪🤍初级数量-单位提取（参考）].REGEXEXTRACT("❸.*?❸🔞")
))


，CHAR(10)，


IF(ISBLANK(
[🧪🤍初级数量-单位提取（参考）].REGEXEXTRACTALL("❹.*❹🔞")),

IF([🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❹.*❹🔞").REGEXMATCH("主粮|肉干|冻干"),
"❹袋❹🔞；❹袋❹🔞；❹袋❹🔞；❹袋❹🔞；❹袋❹🔞；❹袋❹🔞",

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❹.*❹🔞").REGEXMATCH("猫砂|餐包"),
"❹包❹🔞；❹包❹🔞；❹包❹🔞；❹包❹🔞；❹包❹🔞；❹包❹🔞",

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❹.*❹🔞").REGEXMATCH("猫条|狗条"),
"❹支❹🔞；❹支❹🔞；❹支❹🔞；❹支❹🔞；❹支❹🔞；❹支❹🔞",

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❹.*❹🔞").REGEXMATCH("罐头"),
"❹罐❹🔞；❹罐❹🔞；❹罐❹🔞；❹罐❹🔞；❹罐❹🔞；❹罐❹🔞",

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❹.*❹🔞").REGEXMATCH("餐盒"),
"❹盒❹🔞；❹盒❹🔞；❹盒❹🔞；❹盒❹🔞；❹盒❹🔞；❹盒❹🔞",

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❹.*❹🔞").REGEXMATCH("除臭剂|消毒剂"),
"❹瓶❹🔞；❹瓶❹🔞；❹瓶❹🔞；❹瓶❹🔞；❹瓶❹🔞；❹瓶❹🔞",

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❹.*❹🔞").REGEXMATCH("其他"),
""

))))))),
CONCATENATE(
[🧪🤍初级数量-单位提取（参考）].REGEXEXTRACT("❹.*?❹🔞"),
"；",
[🧪🤍初级数量-单位提取（参考）].REGEXEXTRACT("❹.*?❹🔞"),
"；",
[🧪🤍初级数量-单位提取（参考）].REGEXEXTRACT("❹.*?❹🔞"),
"；",
[🧪🤍初级数量-单位提取（参考）].REGEXEXTRACT("❹.*?❹🔞"),
"；",
[🧪🤍初级数量-单位提取（参考）].REGEXEXTRACT("❹.*?❹🔞"),
"；",
[🧪🤍初级数量-单位提取（参考）].REGEXEXTRACT("❹.*?❹🔞")
))



，CHAR(10)，


IF(ISBLANK(
[🧪🤍初级数量-单位提取（参考）].REGEXEXTRACTALL("❺.*❺🔞")),

IF([🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❺.*❺🔞").REGEXMATCH("主粮|肉干|冻干"),
"❺袋❺🔞；❺袋❺🔞；❺袋❺🔞；❺袋❺🔞；❺袋❺🔞；❺袋❺🔞",

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❺.*❺🔞").REGEXMATCH("猫砂|餐包"),
"❺包❺🔞；❺包❺🔞；❺包❺🔞；❺包❺🔞；❺包❺🔞；❺包❺🔞",

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❺.*❺🔞").REGEXMATCH("猫条|狗条"),
"❺支❺🔞；❺支❺🔞；❺支❺🔞；❺支❺🔞；❺支❺🔞；❺支❺🔞",

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❺.*❺🔞").REGEXMATCH("罐头"),
"❺罐❺🔞；❺罐❺🔞；❺罐❺🔞；❺罐❺🔞；❺罐❺🔞；❺罐❺🔞",

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❺.*❺🔞").REGEXMATCH("餐盒"),
"❺盒❺🔞；❺盒❺🔞；❺盒❺🔞；❺盒❺🔞；❺盒❺🔞；❺盒❺🔞",

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❺.*❺🔞").REGEXMATCH("除臭剂|消毒剂"),
"❺瓶❺🔞；❺瓶❺🔞；❺瓶❺🔞；❺瓶❺🔞；❺瓶❺🔞；❺瓶❺🔞",

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❺.*❺🔞").REGEXMATCH("其他"),
""

))))))),
CONCATENATE(
[🧪🤍初级数量-单位提取（参考）].REGEXEXTRACT("❺.*?❺🔞"),
"；",
[🧪🤍初级数量-单位提取（参考）].REGEXEXTRACT("❺.*?❺🔞"),
"；",
[🧪🤍初级数量-单位提取（参考）].REGEXEXTRACT("❺.*?❺🔞"),
"；",
[🧪🤍初级数量-单位提取（参考）].REGEXEXTRACT("❺.*?❺🔞"),
"；",
[🧪🤍初级数量-单位提取（参考）].REGEXEXTRACT("❺.*?❺🔞"),
"；",
[🧪🤍初级数量-单位提取（参考）].REGEXEXTRACT("❺.*?❺🔞")
))



，CHAR(10)，


IF(ISBLANK(
[🧪🤍初级数量-单位提取（参考）].REGEXEXTRACTALL("❻.*❻🔞")),

IF([🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❻.*❻🔞").REGEXMATCH("主粮|肉干|冻干"),
"❻袋❻🔞；❻袋❻🔞；❻袋❻🔞；❻袋❻🔞；❻袋❻🔞；❻袋❻🔞",

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❻.*❻🔞").REGEXMATCH("猫砂|餐包"),
"❻包❻🔞；❻包❻🔞；❻包❻🔞；❻包❻🔞；❻包❻🔞；❻包❻🔞",

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❻.*❻🔞").REGEXMATCH("猫条|狗条"),
"❻支❻🔞；❻支❻🔞；❻支❻🔞；❻支❻🔞；❻支❻🔞；❻支❻🔞",

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❻.*❻🔞").REGEXMATCH("罐头"),
"❻罐❻🔞；❻罐❻🔞；❻罐❻🔞；❻罐❻🔞；❻罐❻🔞；❻罐❻🔞",

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❻.*❻🔞").REGEXMATCH("餐盒"),
"❻盒❻🔞；❻盒❻🔞；❻盒❻🔞；❻盒❻🔞；❻盒❻🔞；❻盒❻🔞",

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❻.*❻🔞").REGEXMATCH("除臭剂|消毒剂"),
"❻瓶❻🔞；❻瓶❻🔞；❻瓶❻🔞；❻瓶❻🔞；❻瓶❻🔞；❻瓶❻🔞",

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❻.*❻🔞").REGEXMATCH("其他"),
""

))))))),
CONCATENATE(
[🧪🤍初级数量-单位提取（参考）].REGEXEXTRACT("❻.*?❻🔞"),
"；",
[🧪🤍初级数量-单位提取（参考）].REGEXEXTRACT("❻.*?❻🔞"),
"；",
[🧪🤍初级数量-单位提取（参考）].REGEXEXTRACT("❻.*?❻🔞"),
"；",
[🧪🤍初级数量-单位提取（参考）].REGEXEXTRACT("❻.*?❻🔞"),
"；",
[🧪🤍初级数量-单位提取（参考）].REGEXEXTRACT("❻.*?❻🔞"),
"；",
[🧪🤍初级数量-单位提取（参考）].REGEXEXTRACT("❻.*?❻🔞")
))


，CHAR(10)，



IF(ISBLANK(
[🧪🤍初级数量-单位提取（参考）].REGEXEXTRACTALL("❼.*❼🔞")),

IF([🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❼.*❼🔞").REGEXMATCH("主粮|肉干|冻干"),
"❼袋❼🔞；❼袋❼🔞；❼袋❼🔞；❼袋❼🔞；❼袋❼🔞；❼袋❼🔞",

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❼.*❼🔞").REGEXMATCH("猫砂|餐包"),
"❼包❼🔞；❼包❼🔞；❼包❼🔞；❼包❼🔞；❼包❼🔞；❼包❼🔞",

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❼.*❼🔞").REGEXMATCH("猫条|狗条"),
"❼支❼🔞；❼支❼🔞；❼支❼🔞；❼支❼🔞；❼支❼🔞；❼支❼🔞",

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❼.*❼🔞").REGEXMATCH("罐头"),
"❼罐❼🔞；❼罐❼🔞；❼罐❼🔞；❼罐❼🔞；❼罐❼🔞；❼罐❼🔞",

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❼.*❼🔞").REGEXMATCH("餐盒"),
"❼盒❼🔞；❼盒❼🔞；❼盒❼🔞；❼盒❼🔞；❼盒❼🔞；❼盒❼🔞",

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❼.*❼🔞").REGEXMATCH("除臭剂|消毒剂"),
"❼瓶❼🔞；❼瓶❼🔞；❼瓶❼🔞；❼瓶❼🔞；❼瓶❼🔞；❼瓶❼🔞",

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❼.*❼🔞").REGEXMATCH("其他"),
""

))))))),
CONCATENATE(
[🧪🤍初级数量-单位提取（参考）].REGEXEXTRACT("❼.*?❼🔞"),
"；",
[🧪🤍初级数量-单位提取（参考）].REGEXEXTRACT("❼.*?❼🔞"),
"；",
[🧪🤍初级数量-单位提取（参考）].REGEXEXTRACT("❼.*?❼🔞"),
"；",
[🧪🤍初级数量-单位提取（参考）].REGEXEXTRACT("❼.*?❼🔞"),
"；",
[🧪🤍初级数量-单位提取（参考）].REGEXEXTRACT("❼.*?❼🔞"),
"；",
[🧪🤍初级数量-单位提取（参考）].REGEXEXTRACT("❼.*?❼🔞")
))


，CHAR(10)，


IF(ISBLANK(
[🧪🤍初级数量-单位提取（参考）].REGEXEXTRACTALL("❽.*❽🔞")),

IF([🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❽.*❽🔞").REGEXMATCH("主粮|肉干|冻干"),
"❽袋❽🔞；❽袋❽🔞；❽袋❽🔞；❽袋❽🔞；❽袋❽🔞；❽袋❽🔞",

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❽.*❽🔞").REGEXMATCH("猫砂|餐包"),
"❽包❽🔞；❽包❽🔞；❽包❽🔞；❽包❽🔞；❽包❽🔞；❽包❽🔞",

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❽.*❽🔞").REGEXMATCH("猫条|狗条"),
"❽支❽🔞；❽支❽🔞；❽支❽🔞；❽支❽🔞；❽支❽🔞；❽支❽🔞",

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❽.*❽🔞").REGEXMATCH("罐头"),
"❽罐❽🔞；❽罐❽🔞；❽罐❽🔞；❽罐❽🔞；❽罐❽🔞；❽罐❽🔞",

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❽.*❽🔞").REGEXMATCH("餐盒"),
"❽盒❽🔞；❽盒❽🔞；❽盒❽🔞；❽盒❽🔞；❽盒❽🔞；❽盒❽🔞",

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❽.*❽🔞").REGEXMATCH("除臭剂|消毒剂"),
"❽瓶❽🔞；❽瓶❽🔞；❽瓶❽🔞；❽瓶❽🔞；❽瓶❽🔞；❽瓶❽🔞",

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❽.*❽🔞").REGEXMATCH("其他"),
""

))))))),
CONCATENATE(
[🧪🤍初级数量-单位提取（参考）].REGEXEXTRACT("❽.*?❽🔞"),
"；",
[🧪🤍初级数量-单位提取（参考）].REGEXEXTRACT("❽.*?❽🔞"),
"；",
[🧪🤍初级数量-单位提取（参考）].REGEXEXTRACT("❽.*?❽🔞"),
"；",
[🧪🤍初级数量-单位提取（参考）].REGEXEXTRACT("❽.*?❽🔞"),
"；",
[🧪🤍初级数量-单位提取（参考）].REGEXEXTRACT("❽.*?❽🔞"),
"；",
[🧪🤍初级数量-单位提取（参考）].REGEXEXTRACT("❽.*?❽🔞")
))


，CHAR(10)，



IF(ISBLANK(
[🧪🤍初级数量-单位提取（参考）].REGEXEXTRACTALL("❾.*❾🔞")),

IF([🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❾.*❾🔞").REGEXMATCH("主粮|肉干|冻干"),
"❾袋❾🔞；❾袋❾🔞；❾袋❾🔞；❾袋❾🔞；❾袋❾🔞；❾袋❾🔞",

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❾.*❾🔞").REGEXMATCH("猫砂|餐包"),
"❾包❾🔞；❾包❾🔞；❾包❾🔞；❾包❾🔞；❾包❾🔞；❾包❾🔞",

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❾.*❾🔞").REGEXMATCH("猫条|狗条"),
"❾支❾🔞；❾支❾🔞；❾支❾🔞；❾支❾🔞；❾支❾🔞；❾支❾🔞",

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❾.*❾🔞").REGEXMATCH("罐头"),
"❾罐❾🔞；❾罐❾🔞；❾罐❾🔞；❾罐❾🔞；❾罐❾🔞；❾罐❾🔞",

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❾.*❾🔞").REGEXMATCH("餐盒"),
"❾盒❾🔞；❾盒❾🔞；❾盒❾🔞；❾盒❾🔞；❾盒❾🔞；❾盒❾🔞",

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❾.*❾🔞").REGEXMATCH("除臭剂|消毒剂"),
"❾瓶❾🔞；❾瓶❾🔞；❾瓶❾🔞；❾瓶❾🔞；❾瓶❾🔞；❾瓶❾🔞",

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❾.*❾🔞").REGEXMATCH("其他"),
""

))))))),
CONCATENATE(
[🧪🤍初级数量-单位提取（参考）].REGEXEXTRACT("❾.*?❾🔞"),
"；",
[🧪🤍初级数量-单位提取（参考）].REGEXEXTRACT("❾.*?❾🔞"),
"；",
[🧪🤍初级数量-单位提取（参考）].REGEXEXTRACT("❾.*?❾🔞"),
"；",
[🧪🤍初级数量-单位提取（参考）].REGEXEXTRACT("❾.*?❾🔞"),
"；",
[🧪🤍初级数量-单位提取（参考）].REGEXEXTRACT("❾.*?❾🔞"),
"；",
[🧪🤍初级数量-单位提取（参考）].REGEXEXTRACT("❾.*?❾🔞")
))


)



.REGEXREPLACE("FALSE","")

.REGEXREPLACE("[❶-❾]("&[🧠进阶单位]&"|"&[🧠高阶单位]&")[❶-❾]🔞；?","")

.REGEXREPLACE("[;；,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====

🧪🤍初级数量-数字提取（1-6）
IF(ISBLANK([🍬商品信息（原始版）]),""，

CONCATENATE(

IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(1)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❶1❶🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(1)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❶$0❶🔞"))

，


IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(2)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❶1❶🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(2)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❶$0❶🔞"))

,

IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(3)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❶1❶🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(3)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❶$0❶🔞"))


,


IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(4)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❶1❶🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(4)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❶$0❶🔞"))



,


IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(5)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❶1❶🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(5)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❶$0❶🔞"))


,



IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(6)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❶1❶🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(6)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❶$0❶🔞"))


,CHAR(10)，

IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(1)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❷1❷🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(1)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❷$0❷🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(2)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❷1❷🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(2)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❷$0❷🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(3)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❷1❷🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(3)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❷$0❷🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(4)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❷1❷🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(4)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❷$0❷🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(5)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❷1❷🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(5)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❷$0❷🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(6)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❷1❷🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(6)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❷$0❷🔞"))



,CHAR(10),




IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(1)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❸1❸🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(1)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❸$0❸🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(2)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❸1❸🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(2)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❸$0❸🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(3)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❸1❸🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(3)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❸$0❸🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(4)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❸1❸🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(4)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❸$0❸🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(5)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❸1❸🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(5)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❸$0❸🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(6)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❸1❸🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(6)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❸$0❸🔞"))


,CHAR(10),


IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(1)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❹1❹🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(1)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❹$0❹🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(2)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❹1❹🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(2)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❹$0❹🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(3)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❹1❹🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(3)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❹$0❹🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(4)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❹1❹🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(4)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❹$0❹🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(5)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❹1❹🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(5)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❹$0❹🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(6)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❹1❹🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(6)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❹$0❹🔞"))


,CHAR(10),




IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(1)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❺1❺🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(1)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❺$0❺🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(2)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❺1❺🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(2)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❺$0❺🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(3)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❺1❺🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(3)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❺$0❺🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(4)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❺1❺🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(4)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❺$0❺🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(5)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❺1❺🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(5)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❺$0❺🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(6)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❺1❺🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(6)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❺$0❺🔞"))


,CHAR(10),

IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(1)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❻1❻🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(1)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❻$0❻🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(2)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❻1❻🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(2)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❻$0❻🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(3)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❻1❻🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(3)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❻$0❻🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(4)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❻1❻🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(4)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❻$0❻🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(5)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❻1❻🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(5)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❻$0❻🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(6)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❻1❻🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(6)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❻$0❻🔞"))



))

.REGEXREPLACE("；?\d+(?:\.\d+)?("&[🧠基础单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&"|"&[🧠终极单位]&")；?","")


.REGEXREPLACE("("&[🧠单位合集]&")","")

.REGEXREPLACE("([❶-❾])"&[🧠乘法符号],"$1")
.REGEXREPLACE([🧠乘法符号]&"([❶-❾])","$1")

.REGEXREPLACE("\*\d+(?:\.\d+)?","")


.REGEXREPLACE("([❶-❾])([❶-❾]🔞)","${1}1${2}")
.REGEXREPLACE("([❶-❾]🔞)([❶-❾])","$1；$2")

.REGEXREPLACE("[;；,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====

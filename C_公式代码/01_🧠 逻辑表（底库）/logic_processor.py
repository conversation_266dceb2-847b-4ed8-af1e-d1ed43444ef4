from .logic_table_model import LogicTableModel

class LogicProcessor:
    def __init__(self):
        self.model = LogicTableModel()
        self._init_rules()  # 初始化基础规则

    def _init_rules(self):
        """初始化信息获取表的字段规则"""
        # 源信息
        self.model.add_rule(
            field_type="源信息",
            field_name="原始商品名称",
            formula="直接提取",
            depend_fields=[],
            apply_table="信息获取（基建）"
        )
        
        # 第一原生字段
        self.model.add_rule(
            field_type="第一原生字段",
            field_name="清洗后商品名称",
            formula="strip(原始商品名称)",
            depend_fields=["信息获取（基建）.原始商品名称"],
            apply_table="信息获取（基建）"
        )
        
        # 关联字段
        self.model.add_rule(
            field_type="关联字段",
            field_name="品牌ID",
            formula="从品牌识别表获取",
            depend_fields=["品牌识别（基建）.品牌ID"],
            apply_table="信息获取（基建）"
        )
        
        # 第二原生字段
        self.model.add_rule(
            field_type="第二原生字段",
            field_name="品牌评分",
            formula="关联查询品牌评分",
            depend_fields=["信息获取（基建）.品牌ID"],
            apply_table="信息获取（基建）"
        )

    def check_deps(self, table_name, field_name):
        """检查字段依赖是否就绪"""
        rules = self.model.get_rules(table_name)
        rule = next((r for r in rules if r[self.model.fields["字段名称"]] == field_name), None)
        
        if not rule:
            return True, "规则不存在"
            
        missing = [d for d in rule[self.model.fields["依赖字段"]]]  # 实际项目中需检查真实状态
        return (False, f"缺失依赖: {missing}") if missing else (True, "依赖就绪")

💻💚高阶单价（7-9）
CONCATENATE(

IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❼.*?❼🔞").REGEXMATCH("礼包")
，"",


IF(
[💻💚数量（最终）-【高阶数量】颗粒度]
.REGEXEXTRACTALL("❼(.*?)❼🔞")
.NTH(1)
.VALUE()
=1
，""，


CONCATENATE("❼"，
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(1)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【高阶数量】颗粒度]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(1)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
，"/"，

[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(1)
.REGEXEXTRACT("❼.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❼🔞")，"Ⓜ️❼🔞")

））
，"；"，

IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❼.*?❼🔞").REGEXMATCH("礼包")
，"",


IF(
[💻💚数量（最终）-【高阶数量】颗粒度]
.REGEXEXTRACTALL("❼(.*?)❼🔞")
.NTH(2)
.VALUE()
=1
，""，


CONCATENATE("❼"，
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(2)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【高阶数量】颗粒度]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(2)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
，"/"，

[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(2)
.REGEXEXTRACT("❼.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❼🔞")，"Ⓜ️❼🔞")


））

，"；"，

IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❼.*?❼🔞").REGEXMATCH("礼包")
，"",


IF(
[💻💚数量（最终）-【高阶数量】颗粒度]
.REGEXEXTRACTALL("❼(.*?)❼🔞")
.NTH(3)
.VALUE()
=1
，""，


CONCATENATE("❼"，
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(3)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【高阶数量】颗粒度]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(3)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
，"/"，

[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(3)
.REGEXEXTRACT("❼.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❼🔞")，"Ⓜ️❼🔞")


））

，"；"，

IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❼.*?❼🔞").REGEXMATCH("礼包")
，"",



IF(
[💻💚数量（最终）-【高阶数量】颗粒度]
.REGEXEXTRACTALL("❼(.*?)❼🔞")
.NTH(4)
.VALUE()
=1
，""，


CONCATENATE("❼"，
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(4)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【高阶数量】颗粒度]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(4)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
，"/"，

[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(4)
.REGEXEXTRACT("❼.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❼🔞")，"Ⓜ️❼🔞")


））

，"；"，

IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❼.*?❼🔞").REGEXMATCH("礼包")
，"",



IF(
[💻💚数量（最终）-【高阶数量】颗粒度]
.REGEXEXTRACTALL("❼(.*?)❼🔞")
.NTH(5)
.VALUE()
=1
，""，



CONCATENATE("❼"，
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(5)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【高阶数量】颗粒度]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(5)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
，"/"，

[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(5)
.REGEXEXTRACT("❼.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❼🔞")，"Ⓜ️❼🔞")


））

，"；"，

IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❼.*?❼🔞").REGEXMATCH("礼包")
，"",



IF(
[💻💚数量（最终）-【高阶数量】颗粒度]
.REGEXEXTRACTALL("❼(.*?)❼🔞")
.NTH(6)
.VALUE()
=1
，""，


CONCATENATE("❼"，
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(6)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【高阶数量】颗粒度]
.REGEXEXTRACTALL("❼(.*?)❼🔞")
.NTH(6)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
，"/"，


[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(6)
.REGEXEXTRACT("❼.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❼🔞")，"Ⓜ️❼🔞")
））



，CHAR(10)，



IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❽.*?❽🔞").REGEXMATCH("礼包")
,"",
IF(
[💻💚数量（最终）-【高阶数量】颗粒度]
.REGEXEXTRACTALL("❽(.*?)❽🔞")
.NTH(1)
.VALUE()
=1
,"",
CONCATENATE("❽",
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(1)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【高阶数量】颗粒度]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(1)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
,"/",
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(1)
.REGEXEXTRACT("❽.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❽🔞"), "Ⓜ️❽🔞")
)
),
"；",
IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❽.*?❽🔞").REGEXMATCH("礼包")
,"",
IF(
[💻💚数量（最终）-【高阶数量】颗粒度]
.REGEXEXTRACTALL("❽(.*?)❽🔞")
.NTH(2)
.VALUE()
=1
,"",
CONCATENATE("❽",
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(2)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【高阶数量】颗粒度]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(2)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
,"/",
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(2)
.REGEXEXTRACT("❽.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❽🔞"), "Ⓜ️❽🔞")
)
),
"；",
IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❽.*?❽🔞").REGEXMATCH("礼包")
,"",
IF(
[💻💚数量（最终）-【高阶数量】颗粒度]
.REGEXEXTRACTALL("❽(.*?)❽🔞")
.NTH(3)
.VALUE()
=1
,"",
CONCATENATE("❽",
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(3)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【高阶数量】颗粒度]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(3)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
,"/",
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(3)
.REGEXEXTRACT("❽.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❽🔞"), "Ⓜ️❽🔞")
)
),
"；",
IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❽.*?❽🔞").REGEXMATCH("礼包")
,"",
IF(
[💻💚数量（最终）-【高阶数量】颗粒度]
.REGEXEXTRACTALL("❽(.*?)❽🔞")
.NTH(4)
.VALUE()
=1
,"",
CONCATENATE("❽",
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(4)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【高阶数量】颗粒度]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(4)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
,"/",
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(4)
.REGEXEXTRACT("❽.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❽🔞"), "Ⓜ️❽🔞")
)
),
"；",
IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❽.*?❽🔞").REGEXMATCH("礼包")
,"",
IF(
[💻💚数量（最终）-【高阶数量】颗粒度]
.REGEXEXTRACTALL("❽(.*?)❽🔞")
.NTH(5)
.VALUE()
=1
,"",
CONCATENATE("❽",
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(5)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【高阶数量】颗粒度]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(5)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
,"/",
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(5)
.REGEXEXTRACT("❽.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❽🔞"), "Ⓜ️❽🔞")
)
),
"；",
IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❽.*?❽🔞").REGEXMATCH("礼包")
,"",
IF(
[💻💚数量（最终）-【高阶数量】颗粒度]
.REGEXEXTRACTALL("❽(.*?)❽🔞")
.NTH(6)
.VALUE()
=1
,"",
CONCATENATE("❽",
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(6)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【高阶数量】颗粒度]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(6)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
,"/",
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(6)
.REGEXEXTRACT("❽.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❽🔞"), "Ⓜ️❽🔞")
)
)



，CHAR(10)，



IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❾.*?❾🔞").REGEXMATCH("礼包")
,"",
IF(
[💻💚数量（最终）-【高阶数量】颗粒度]
.REGEXEXTRACTALL("❾(.*?)❾🔞")
.NTH(1)
.VALUE()
=1
,"",
CONCATENATE("❾",
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(1)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【高阶数量】颗粒度]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(1)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
,"/",
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(1)
.REGEXEXTRACT("❾.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❾🔞"), "Ⓜ️❾🔞")
)
),
"；",
IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❾.*?❾🔞").REGEXMATCH("礼包")
,"",
IF(
[💻💚数量（最终）-【高阶数量】颗粒度]
.REGEXEXTRACTALL("❾(.*?)❾🔞")
.NTH(2)
.VALUE()
=1
,"",
CONCATENATE("❾",
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(2)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【高阶数量】颗粒度]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(2)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
,"/",
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(2)
.REGEXEXTRACT("❾.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❾🔞"), "Ⓜ️❾🔞")
)
),
"；",
IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❾.*?❾🔞").REGEXMATCH("礼包")
,"",
IF(
[💻💚数量（最终）-【高阶数量】颗粒度]
.REGEXEXTRACTALL("❾(.*?)❾🔞")
.NTH(3)
.VALUE()
=1
,"",
CONCATENATE("❾",
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(3)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【高阶数量】颗粒度]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(3)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
,"/",
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(3)
.REGEXEXTRACT("❾.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❾🔞"), "Ⓜ️❾🔞")
)
),
"；",
IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❾.*?❾🔞").REGEXMATCH("礼包")
,"",
IF(
[💻💚数量（最终）-【高阶数量】颗粒度]
.REGEXEXTRACTALL("❾(.*?)❾🔞")
.NTH(4)
.VALUE()
=1
,"",
CONCATENATE("❾",
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(4)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【高阶数量】颗粒度]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(4)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
,"/",
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(4)
.REGEXEXTRACT("❾.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❾🔞"), "Ⓜ️❾🔞")
)
),
"；",
IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❾.*?❾🔞").REGEXMATCH("礼包")
,"",
IF(
[💻💚数量（最终）-【高阶数量】颗粒度]
.REGEXEXTRACTALL("❾(.*?)❾🔞")
.NTH(5)
.VALUE()
=1
,"",
CONCATENATE("❾",
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(5)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【高阶数量】颗粒度]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(5)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
,"/",
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(5)
.REGEXEXTRACT("❾.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❾🔞"), "Ⓜ️❾🔞")
)
),
"；",
IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❾.*?❾🔞").REGEXMATCH("礼包")
,"",
IF(
[💻💚数量（最终）-【高阶数量】颗粒度]
.REGEXEXTRACTALL("❾(.*?)❾🔞")
.NTH(6)
.VALUE()
=1
,"",
CONCATENATE("❾",
(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(6)
.REGEXEXTRACT("\d+(?:\.\d+)?")
/
[💻💚数量（最终）-【高阶数量】颗粒度]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(6)
.REGEXEXTRACT("\d+(?:\.\d+)?")).ROUNDDOWN(2)
,"/",
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(6)
.REGEXEXTRACT("❾.*?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")❾🔞"), "Ⓜ️❾🔞")
)
)


)



.REGEXREPLACE("\d+(?:\.\d+)?","¥$0")
.REGEXREPLACE("[❼-❾]¥0/?(?:"&[🧠单位合集]&")?Ⓜ️[❼-❾]🔞","")
.REGEXREPLACE("[❼-❾]¥\d+(?:\.\d+)?/Ⓜ️[❼-❾]🔞","")

.REGEXREPLACE("；+","；")
.REGEXREPLACE("^；","")
.REGEXREPLACE("
；","")


.REGEXREPLACE("[;；,，+➕～\\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
💻💚高阶单价
CONCATENATE(
[💻💚高阶单价（1-3）]
，CHAR(10)，
[💻💚高阶单价（4-6）]
，CHAR(10)，
[💻💚高阶单价（7-9）]

)

.REGEXREPLACE("(\..*?)0+(/)","$1$2")
.REGEXREPLACE("\.\/","/")

.REGEXREPLACE("[;；,，+➕～\\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
💻🤍😀终极数量-数字提取 （完整版）勿删！
IF(ISBLANK([🍬商品信息（原始版）]),""，

CONCATENATE(
IF(ISBLANK(
[🧪🤍😀终极数量] .REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(1))
，"❶1❶🔞"，
[🧪🤍😀终极数量]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(1)
)

,"；"，
IF(ISBLANK(
[🧪🤍😀终极数量].REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(2))
，"❶1❶🔞"，
[🧪🤍😀终极数量]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(2)
)

,"；"，
IF(ISBLANK(
[🧪🤍😀终极数量].REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(3))
，"❶1❶🔞"，
[🧪🤍😀终极数量]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(3)
)

,"；"，
IF(ISBLANK(
[🧪🤍😀终极数量].REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(4))
，"❶1❶🔞"，
[🧪🤍😀终极数量]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(4)
)

,"；"，
IF(ISBLANK(
[🧪🤍😀终极数量].REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(5))
，"❶1❶🔞"，
[🧪🤍😀终极数量]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(5)
)

,"；"，
IF(ISBLANK(
[🧪🤍😀终极数量].REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(6))
，"❶1❶🔞"，
[🧪🤍😀终极数量]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(6)
)

，CHAR(10)，


IF(ISBLANK(
[🧪🤍😀终极数量].REGEXEXTRACTALL("❷.*?❷🔞").NTH(1))
,"❷1❷🔞",
[🧪🤍😀终极数量].REGEXEXTRACTALL("❷.*?❷🔞").NTH(1)
)
,"；",
IF(ISBLANK(
[🧪🤍😀终极数量].REGEXEXTRACTALL("❷.*?❷🔞").NTH(2))
,"❷1❷🔞",
[🧪🤍😀终极数量].REGEXEXTRACTALL("❷.*?❷🔞").NTH(2)
)
,"；",
IF(ISBLANK(
[🧪🤍😀终极数量].REGEXEXTRACTALL("❷.*?❷🔞").NTH(3))
,"❷1❷🔞",
[🧪🤍😀终极数量].REGEXEXTRACTALL("❷.*?❷🔞").NTH(3)
)
,"；",
IF(ISBLANK(
[🧪🤍😀终极数量].REGEXEXTRACTALL("❷.*?❷🔞").NTH(4))
,"❷1❷🔞",
[🧪🤍😀终极数量].REGEXEXTRACTALL("❷.*?❷🔞").NTH(4)
)
,"；",
IF(ISBLANK(
[🧪🤍😀终极数量].REGEXEXTRACTALL("❷.*?❷🔞").NTH(5))
,"❷1❷🔞",
[🧪🤍😀终极数量].REGEXEXTRACTALL("❷.*?❷🔞").NTH(5)
)
,"；",
IF(ISBLANK(
[🧪🤍😀终极数量].REGEXEXTRACTALL("❷.*?❷🔞").NTH(6))
,"❷1❷🔞",
[🧪🤍😀终极数量].REGEXEXTRACTALL("❷.*?❷🔞").NTH(6)
)



，CHAR(10)，



IF(ISBLANK(
[🧪🤍😀终极数量].REGEXEXTRACTALL("❸.*?❸🔞").NTH(1))
,"❸1❸🔞",
[🧪🤍😀终极数量].REGEXEXTRACTALL("❸.*?❸🔞").NTH(1)
)
,"；",
IF(ISBLANK(
[🧪🤍😀终极数量].REGEXEXTRACTALL("❸.*?❸🔞").NTH(2))
,"❸1❸🔞",
[🧪🤍😀终极数量].REGEXEXTRACTALL("❸.*?❸🔞").NTH(2)
)
,"；",
IF(ISBLANK(
[🧪🤍😀终极数量].REGEXEXTRACTALL("❸.*?❸🔞").NTH(3))
,"❸1❸🔞",
[🧪🤍😀终极数量].REGEXEXTRACTALL("❸.*?❸🔞").NTH(3)
)
,"；",
IF(ISBLANK(
[🧪🤍😀终极数量].REGEXEXTRACTALL("❸.*?❸🔞").NTH(4))
,"❸1❸🔞",
[🧪🤍😀终极数量].REGEXEXTRACTALL("❸.*?❸🔞").NTH(4)
)
,"；",
IF(ISBLANK(
[🧪🤍😀终极数量].REGEXEXTRACTALL("❸.*?❸🔞").NTH(5))
,"❸1❸🔞",
[🧪🤍😀终极数量].REGEXEXTRACTALL("❸.*?❸🔞").NTH(5)
)
,"；",
IF(ISBLANK(
[🧪🤍😀终极数量].REGEXEXTRACTALL("❸.*?❸🔞").NTH(6))
,"❸1❸🔞",
[🧪🤍😀终极数量].REGEXEXTRACTALL("❸.*?❸🔞").NTH(6)
)


，CHAR(10)，


IF(ISBLANK(
[🧪🤍😀终极数量].REGEXEXTRACTALL("❹.*?❹🔞").NTH(1))
,"❹1❹🔞",
[🧪🤍😀终极数量].REGEXEXTRACTALL("❹.*?❹🔞").NTH(1)
)
,"；",
IF(ISBLANK(
[🧪🤍😀终极数量].REGEXEXTRACTALL("❹.*?❹🔞").NTH(2))
,"❹1❹🔞",
[🧪🤍😀终极数量].REGEXEXTRACTALL("❹.*?❹🔞").NTH(2)
)
,"；",
IF(ISBLANK(
[🧪🤍😀终极数量].REGEXEXTRACTALL("❹.*?❹🔞").NTH(3))
,"❹1❹🔞",
[🧪🤍😀终极数量].REGEXEXTRACTALL("❹.*?❹🔞").NTH(3)
)
,"；",
IF(ISBLANK(
[🧪🤍😀终极数量].REGEXEXTRACTALL("❹.*?❹🔞").NTH(4))
,"❹1❹🔞",
[🧪🤍😀终极数量].REGEXEXTRACTALL("❹.*?❹🔞").NTH(4)
)
,"；",
IF(ISBLANK(
[🧪🤍😀终极数量].REGEXEXTRACTALL("❹.*?❹🔞").NTH(5))
,"❹1❹🔞",
[🧪🤍😀终极数量].REGEXEXTRACTALL("❹.*?❹🔞").NTH(5)
)
,"；",
IF(ISBLANK(
[🧪🤍😀终极数量].REGEXEXTRACTALL("❹.*?❹🔞").NTH(6))
,"❹1❹🔞",
[🧪🤍😀终极数量].REGEXEXTRACTALL("❹.*?❹🔞").NTH(6)
)



，CHAR(10)，


IF(ISBLANK(
[🧪🤍😀终极数量].REGEXEXTRACTALL("❺.*?❺🔞").NTH(1))
,"❺1❺🔞",
[🧪🤍😀终极数量].REGEXEXTRACTALL("❺.*?❺🔞").NTH(1)
)
,"；",
IF(ISBLANK(
[🧪🤍😀终极数量].REGEXEXTRACTALL("❺.*?❺🔞").NTH(2))
,"❺1❺🔞",
[🧪🤍😀终极数量].REGEXEXTRACTALL("❺.*?❺🔞").NTH(2)
)
,"；",
IF(ISBLANK(
[🧪🤍😀终极数量].REGEXEXTRACTALL("❺.*?❺🔞").NTH(3))
,"❺1❺🔞",
[🧪🤍😀终极数量].REGEXEXTRACTALL("❺.*?❺🔞").NTH(3)
)
,"；",
IF(ISBLANK(
[🧪🤍😀终极数量].REGEXEXTRACTALL("❺.*?❺🔞").NTH(4))
,"❺1❺🔞",
[🧪🤍😀终极数量].REGEXEXTRACTALL("❺.*?❺🔞").NTH(4)
)
,"；",
IF(ISBLANK(
[🧪🤍😀终极数量].REGEXEXTRACTALL("❺.*?❺🔞").NTH(5))
,"❺1❺🔞",
[🧪🤍😀终极数量].REGEXEXTRACTALL("❺.*?❺🔞").NTH(5)
)
,"；",
IF(ISBLANK(
[🧪🤍😀终极数量].REGEXEXTRACTALL("❺.*?❺🔞").NTH(6))
,"❺1❺🔞",
[🧪🤍😀终极数量].REGEXEXTRACTALL("❺.*?❺🔞").NTH(6)
)




，CHAR(10)，

IF(ISBLANK(
[🧪🤍😀终极数量].REGEXEXTRACTALL("❻.*?❻🔞").NTH(1))
,"❻1❻🔞",
[🧪🤍😀终极数量].REGEXEXTRACTALL("❻.*?❻🔞").NTH(1)
)
,"；",
IF(ISBLANK(
[🧪🤍😀终极数量].REGEXEXTRACTALL("❻.*?❻🔞").NTH(2))
,"❻1❻🔞",
[🧪🤍😀终极数量].REGEXEXTRACTALL("❻.*?❻🔞").NTH(2)
)
,"；",
IF(ISBLANK(
[🧪🤍😀终极数量].REGEXEXTRACTALL("❻.*?❻🔞").NTH(3))
,"❻1❻🔞",
[🧪🤍😀终极数量].REGEXEXTRACTALL("❻.*?❻🔞").NTH(3)
)
,"；",
IF(ISBLANK(
[🧪🤍😀终极数量].REGEXEXTRACTALL("❻.*?❻🔞").NTH(4))
,"❻1❻🔞",
[🧪🤍😀终极数量].REGEXEXTRACTALL("❻.*?❻🔞").NTH(4)
)
,"；",
IF(ISBLANK(
[🧪🤍😀终极数量].REGEXEXTRACTALL("❻.*?❻🔞").NTH(5))
,"❻1❻🔞",
[🧪🤍😀终极数量].REGEXEXTRACTALL("❻.*?❻🔞").NTH(5)
)
,"；",
IF(ISBLANK(
[🧪🤍😀终极数量].REGEXEXTRACTALL("❻.*?❻🔞").NTH(6))
,"❻1❻🔞",
[🧪🤍😀终极数量].REGEXEXTRACTALL("❻.*?❻🔞").NTH(6)
)



,CHAR(10),



IF(ISBLANK(
[🧪🤍😀终极数量].REGEXEXTRACTALL("❼.*?❼🔞").NTH(1))
,"❼1❼🔞",
[🧪🤍😀终极数量].REGEXEXTRACTALL("❼.*?❼🔞").NTH(1)
)
,"；",
IF(ISBLANK(
[🧪🤍😀终极数量].REGEXEXTRACTALL("❼.*?❼🔞").NTH(2))
,"❼1❼🔞",
[🧪🤍😀终极数量].REGEXEXTRACTALL("❼.*?❼🔞").NTH(2)
)
,"；",
IF(ISBLANK(
[🧪🤍😀终极数量].REGEXEXTRACTALL("❼.*?❼🔞").NTH(3))
,"❼1❼🔞",
[🧪🤍😀终极数量].REGEXEXTRACTALL("❼.*?❼🔞").NTH(3)
)
,"；",
IF(ISBLANK(
[🧪🤍😀终极数量].REGEXEXTRACTALL("❼.*?❼🔞").NTH(4))
,"❼1❼🔞",
[🧪🤍😀终极数量].REGEXEXTRACTALL("❼.*?❼🔞").NTH(4)
)
,"；",
IF(ISBLANK(
[🧪🤍😀终极数量].REGEXEXTRACTALL("❼.*?❼🔞").NTH(5))
,"❼1❼🔞",
[🧪🤍😀终极数量].REGEXEXTRACTALL("❼.*?❼🔞").NTH(5)
)
,"；",
IF(ISBLANK(
[🧪🤍😀终极数量].REGEXEXTRACTALL("❼.*?❼🔞").NTH(6))
,"❼1❼🔞",
[🧪🤍😀终极数量].REGEXEXTRACTALL("❼.*?❼🔞").NTH(6)
)



,CHAR(10),



IF(ISBLANK(
[🧪🤍😀终极数量].REGEXEXTRACTALL("❽.*?❽🔞").NTH(1))
,"❽1❽🔞",
[🧪🤍😀终极数量].REGEXEXTRACTALL("❽.*?❽🔞").NTH(1)
)
,"；",
IF(ISBLANK(
[🧪🤍😀终极数量].REGEXEXTRACTALL("❽.*?❽🔞").NTH(2))
,"❽1❽🔞",
[🧪🤍😀终极数量].REGEXEXTRACTALL("❽.*?❽🔞").NTH(2)
)
,"；",
IF(ISBLANK(
[🧪🤍😀终极数量].REGEXEXTRACTALL("❽.*?❽🔞").NTH(3))
,"❽1❽🔞",
[🧪🤍😀终极数量].REGEXEXTRACTALL("❽.*?❽🔞").NTH(3)
)
,"；",
IF(ISBLANK(
[🧪🤍😀终极数量].REGEXEXTRACTALL("❽.*?❽🔞").NTH(4))
,"❽1❽🔞",
[🧪🤍😀终极数量].REGEXEXTRACTALL("❽.*?❽🔞").NTH(4)
)
,"；",
IF(ISBLANK(
[🧪🤍😀终极数量].REGEXEXTRACTALL("❽.*?❽🔞").NTH(5))
,"❽1❽🔞",
[🧪🤍😀终极数量].REGEXEXTRACTALL("❽.*?❽🔞").NTH(5)
)
,"；",
IF(ISBLANK(
[🧪🤍😀终极数量].REGEXEXTRACTALL("❽.*?❽🔞").NTH(6))
,"❽1❽🔞",
[🧪🤍😀终极数量].REGEXEXTRACTALL("❽.*?❽🔞").NTH(6)
)



,CHAR(10),


IF(ISBLANK(
[🧪🤍😀终极数量].REGEXEXTRACTALL("❾.*?❾🔞").NTH(1))
,"❾1❾🔞",
[🧪🤍😀终极数量].REGEXEXTRACTALL("❾.*?❾🔞").NTH(1)
)
,"；",
IF(ISBLANK(
[🧪🤍😀终极数量].REGEXEXTRACTALL("❾.*?❾🔞").NTH(2))
,"❾1❾🔞",
[🧪🤍😀终极数量].REGEXEXTRACTALL("❾.*?❾🔞").NTH(2)
)
,"；",
IF(ISBLANK(
[🧪🤍😀终极数量].REGEXEXTRACTALL("❾.*?❾🔞").NTH(3))
,"❾1❾🔞",
[🧪🤍😀终极数量].REGEXEXTRACTALL("❾.*?❾🔞").NTH(3)
)
,"；",
IF(ISBLANK(
[🧪🤍😀终极数量].REGEXEXTRACTALL("❾.*?❾🔞").NTH(4))
,"❾1❾🔞",
[🧪🤍😀终极数量].REGEXEXTRACTALL("❾.*?❾🔞").NTH(4)
)
,"；",
IF(ISBLANK(
[🧪🤍😀终极数量].REGEXEXTRACTALL("❾.*?❾🔞").NTH(5))
,"❾1❾🔞",
[🧪🤍😀终极数量].REGEXEXTRACTALL("❾.*?❾🔞").NTH(5)
)
,"；",
IF(ISBLANK(
[🧪🤍😀终极数量].REGEXEXTRACTALL("❾.*?❾🔞").NTH(6))
,"❾1❾🔞",
[🧪🤍😀终极数量].REGEXEXTRACTALL("❾.*?❾🔞").NTH(6)
)


))
=====
💻💚终极单价（1-3）
CONCATENATE(

IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❶.*?❶🔞").REGEXMATCH("礼包")
,"",

IF(
OR(
[💻💚初级单价]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(1).ISBLANK().NOT()
，

[💻💚进阶单价]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(1).ISBLANK().NOT()

，

[💻💚高阶单价]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(1).ISBLANK().NOT()

）
,"",

IF(
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(1).VALUE()=1
,"",

CONCATENATE("❶",
TEXT(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(1)
.REGEXEXTRACT("\d+(?:.\d+)?")
/
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(1)
.REGEXEXTRACT("\d+(?:.\d+)?"), "¥0.0"),
"/件Ⓜ️❶🔞")

)
))

,"；",


IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❶.*?❶🔞").REGEXMATCH("礼包")
,"",


IF(
OR(
[💻💚初级单价]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(2).ISBLANK().NOT()
，

[💻💚进阶单价]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(2).ISBLANK().NOT()
，

[💻💚高阶单价]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(2).ISBLANK().NOT()

）
,"",

IF(
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(2).VALUE()=1
,"",

CONCATENATE("❶",
TEXT(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(2)
.REGEXEXTRACT("\d+(?:.\d+)?")
/
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶.?❶🔞")
.NTH(2)
.REGEXEXTRACT("\d+(?:.\d+)?"), "¥0.0"),
"/件Ⓜ️❶🔞")

)
))

,"；",


IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❶.*?❶🔞").REGEXMATCH("礼包")
,"",


IF(
OR(
[💻💚初级单价]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(3).ISBLANK().NOT()
，

[💻💚进阶单价]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(3).ISBLANK().NOT()

，

[💻💚高阶单价]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(3).ISBLANK().NOT()

）
,"",

IF(
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(3).VALUE()=1
,"",

CONCATENATE("❶",
TEXT(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(3)
.REGEXEXTRACT("\d+(?:.\d+)?")
/
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(3)
.REGEXEXTRACT("\d+(?:.\d+)?"), "¥0.0"),
"/件Ⓜ️❶🔞")

)))


，CHAR(10)，

IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❶.*?❶🔞").REGEXMATCH("礼包")
,"",

IF(
OR(
[💻💚初级单价]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(1).ISBLANK().NOT()
，

[💻💚进阶单价]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(1).ISBLANK().NOT()

，

[💻💚高阶单价]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(1).ISBLANK().NOT()

）
,"",

IF(
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(1).VALUE()=1
,"",

CONCATENATE("❶",
TEXT(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(1)
.REGEXEXTRACT("\d+(?:.\d+)?")
/
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(1)
.REGEXEXTRACT("\d+(?:.\d+)?"), "¥0.0"),
"/件Ⓜ️❶🔞")

)
))

,"；",


IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❶.*?❶🔞").REGEXMATCH("礼包")
,"",


IF(
OR(
[💻💚初级单价]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(2).ISBLANK().NOT()
，

[💻💚进阶单价]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(2).ISBLANK().NOT()

，

[💻💚高阶单价]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(2).ISBLANK().NOT()

）
,"",

IF(
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(2).VALUE()=1
,"",

CONCATENATE("❶",
TEXT(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(2)
.REGEXEXTRACT("\d+(?:.\d+)?")
/
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶.?❶🔞")
.NTH(2)
.REGEXEXTRACT("\d+(?:.\d+)?"), "¥0.0"),
"/件Ⓜ️❶🔞")

)
))

,"；",


IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❶.*?❶🔞").REGEXMATCH("礼包")
,"",


IF(
OR(
[💻💚初级单价]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(3).ISBLANK().NOT()
，

[💻💚进阶单价]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(3).ISBLANK().NOT()

，

[💻💚高阶单价]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(3).ISBLANK().NOT()


）
,"",

IF(
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(3).VALUE()=1
,"",

CONCATENATE("❶",
TEXT(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(3)
.REGEXEXTRACT("\d+(?:.\d+)?")
/
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(3)
.REGEXEXTRACT("\d+(?:.\d+)?"), "¥0.0"),
"/件Ⓜ️❶🔞")

)))



,CHAR(10),
IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❷.*?❷🔞").REGEXMATCH("礼包")
,"",

IF(
OR(
[💻💚初级单价]
.REGEXEXTRACTALL("❷(.*?)❷🔞")
.NTH(1).ISBLANK().NOT()
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❷(.*?)❷🔞")
.NTH(1).ISBLANK().NOT()

,
[💻💚高阶单价]
.REGEXEXTRACTALL("❷(.*?)❷🔞")
.NTH(1).ISBLANK().NOT()


)
,"",

IF(
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❷(.*?)❷🔞")
.NTH(1).VALUE()=1
,"",

CONCATENATE("❷",
TEXT(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(1)
.REGEXEXTRACT("\d+(?:.\d+)?")
/
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(1)
.REGEXEXTRACT("\d+(?:.\d+)?"), "¥0.0"),
"/件Ⓜ️❷🔞")

)
))

,"；",


IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❷.*?❷🔞").REGEXMATCH("礼包")
,"",


IF(
OR(
[💻💚初级单价]
.REGEXEXTRACTALL("❷(.*?)❷🔞")
.NTH(2).ISBLANK().NOT()
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❷(.*?)❷🔞")
.NTH(2).ISBLANK().NOT()

,
[💻💚高阶单价]
.REGEXEXTRACTALL("❷(.*?)❷🔞")
.NTH(2).ISBLANK().NOT()

)
,"",

IF(
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❷(.*?)❷🔞")
.NTH(2).VALUE()=1
,"",

CONCATENATE("❷",
TEXT(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(2)
.REGEXEXTRACT("\d+(?:.\d+)?")
/
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❷.?❷🔞")
.NTH(2)
.REGEXEXTRACT("\d+(?:.\d+)?"), "¥0.0"),
"/件Ⓜ️❷🔞")

)
))

,"；",


IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❷.*?❷🔞").REGEXMATCH("礼包")
,"",


IF(
OR(
[💻💚初级单价]
.REGEXEXTRACTALL("❷(.*?)❷🔞")
.NTH(3).ISBLANK().NOT()
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❷(.*?)❷🔞")
.NTH(3).ISBLANK().NOT()

,
[💻💚高阶单价]
.REGEXEXTRACTALL("❷(.*?)❷🔞")
.NTH(3).ISBLANK().NOT()

)
,"",

IF(
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❷(.*?)❷🔞")
.NTH(3).VALUE()=1
,"",

CONCATENATE("❷",
TEXT(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(3)
.REGEXEXTRACT("\d+(?:.\d+)?")
/
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(3)
.REGEXEXTRACT("\d+(?:.\d+)?"), "¥0.0"),
"/件Ⓜ️❷🔞")

)))



,CHAR(10),
IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❸.*?❸🔞").REGEXMATCH("礼包")
,"",

IF(
OR(
[💻💚初级单价]
.REGEXEXTRACTALL("❸(.*?)❸🔞")
.NTH(1).ISBLANK().NOT()
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❸(.*?)❸🔞")
.NTH(1).ISBLANK().NOT()

,
[💻💚高阶单价]
.REGEXEXTRACTALL("❸(.*?)❸🔞")
.NTH(1).ISBLANK().NOT()


)
,"",

IF(
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❸(.*?)❸🔞")
.NTH(1).VALUE()=1
,"",

CONCATENATE("❸",
TEXT(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(1)
.REGEXEXTRACT("\d+(?:.\d+)?")
/
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(1)
.REGEXEXTRACT("\d+(?:.\d+)?"), "¥0.0"),
"/件Ⓜ️❸🔞")

)
))

,"；",


IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❸.*?❸🔞").REGEXMATCH("礼包")
,"",


IF(
OR(
[💻💚初级单价]
.REGEXEXTRACTALL("❸(.*?)❸🔞")
.NTH(2).ISBLANK().NOT()
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❸(.*?)❸🔞")
.NTH(2).ISBLANK().NOT()

,
[💻💚高阶单价]
.REGEXEXTRACTALL("❸(.*?)❸🔞")
.NTH(2).ISBLANK().NOT()


)
,"",

IF(
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❸(.*?)❸🔞")
.NTH(2).VALUE()=1
,"",

CONCATENATE("❸",
TEXT(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(2)
.REGEXEXTRACT("\d+(?:.\d+)?")
/
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❸.?❸🔞")
.NTH(2)
.REGEXEXTRACT("\d+(?:.\d+)?"), "¥0.0"),
"/件Ⓜ️❸🔞")

)
))

,"；",


IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❸.*?❸🔞").REGEXMATCH("礼包")
,"",


IF(
OR(
[💻💚初级单价]
.REGEXEXTRACTALL("❸(.*?)❸🔞")
.NTH(3).ISBLANK().NOT()
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❸(.*?)❸🔞")
.NTH(3).ISBLANK().NOT()

,
[💻💚高阶单价]
.REGEXEXTRACTALL("❸(.*?)❸🔞")
.NTH(3).ISBLANK().NOT()


)
,"",

IF(
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❸(.*?)❸🔞")
.NTH(3).VALUE()=1
,"",

CONCATENATE("❸",
TEXT(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(3)
.REGEXEXTRACT("\d+(?:.\d+)?")
/
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(3)
.REGEXEXTRACT("\d+(?:.\d+)?"), "¥0.0"),
"/件Ⓜ️❸🔞")

)))


)

.REGEXREPLACE("[❶-❾]¥0/?(?:"&[🧠单位合集]&")?Ⓜ️[❶-❾]🔞","")
.REGEXREPLACE("[❶-❾]¥\d+(?:\.\d+)?/Ⓜ️[❶-❾]🔞","")

.REGEXREPLACE("；+","；")
.REGEXREPLACE("^；","")
.REGEXREPLACE("
；","")

.REGEXREPLACE("[;；,，+➕～\\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
💻💚终极单价（4-6）
CONCATENATE(

IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❹.*?❹🔞").REGEXMATCH("礼包")
,"",

IF(
OR(
[💻💚初级单价]
.REGEXEXTRACTALL("❹(.*?)❹🔞")
.NTH(1).ISBLANK().NOT()
,

[💻💚进阶单价]
.REGEXEXTRACTALL("❹(.*?)❹🔞")
.NTH(1).ISBLANK().NOT()

,

[💻💚高阶单价]
.REGEXEXTRACTALL("❹(.*?)❹🔞")
.NTH(1).ISBLANK().NOT()

)
,"",

IF(
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❹(.*?)❹🔞")
.NTH(1).VALUE()=1
,"",

CONCATENATE("❹",
TEXT(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(1)
.REGEXEXTRACT("\d+(?:.\d+)?")
/
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(1)
.REGEXEXTRACT("\d+(?:.\d+)?"), "¥0.0"),
"/件Ⓜ️❹🔞")

)
))

,"；",


IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❹.*?❹🔞").REGEXMATCH("礼包")
,"",


IF(
OR(
[💻💚初级单价]
.REGEXEXTRACTALL("❹(.*?)❹🔞")
.NTH(2).ISBLANK().NOT()
,

[💻💚进阶单价]
.REGEXEXTRACTALL("❹(.*?)❹🔞")
.NTH(2).ISBLANK().NOT()
,

[💻💚高阶单价]
.REGEXEXTRACTALL("❹(.*?)❹🔞")
.NTH(2).ISBLANK().NOT()

)
,"",

IF(
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❹(.*?)❹🔞")
.NTH(2).VALUE()=1
,"",

CONCATENATE("❹",
TEXT(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(2)
.REGEXEXTRACT("\d+(?:.\d+)?")
/
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(2)
.REGEXEXTRACT("\d+(?:.\d+)?"), "¥0.0"),
"/件Ⓜ️❹🔞")

)
))

,"；",


IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❹.*?❹🔞").REGEXMATCH("礼包")
,"",


IF(
OR(
[💻💚初级单价]
.REGEXEXTRACTALL("❹(.*?)❹🔞")
.NTH(3).ISBLANK().NOT()
,

[💻💚进阶单价]
.REGEXEXTRACTALL("❹(.*?)❹🔞")
.NTH(3).ISBLANK().NOT()

,

[💻💚高阶单价]
.REGEXEXTRACTALL("❹(.*?)❹🔞")
.NTH(3).ISBLANK().NOT()

)
,"",

IF(
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❹(.*?)❹🔞")
.NTH(3).VALUE()=1
,"",

CONCATENATE("❹",
TEXT(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(3)
.REGEXEXTRACT("\d+(?:.\d+)?")
/
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(3)
.REGEXEXTRACT("\d+(?:.\d+)?"), "¥0.0"),
"/件Ⓜ️❹🔞")

)))


,CHAR(10),

IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❹.*?❹🔞").REGEXMATCH("礼包")
,"",

IF(
OR(
[💻💚初级单价]
.REGEXEXTRACTALL("❹(.*?)❹🔞")
.NTH(1).ISBLANK().NOT()
,

[💻💚进阶单价]
.REGEXEXTRACTALL("❹(.*?)❹🔞")
.NTH(1).ISBLANK().NOT()

,

[💻💚高阶单价]
.REGEXEXTRACTALL("❹(.*?)❹🔞")
.NTH(1).ISBLANK().NOT()

)
,"",

IF(
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❹(.*?)❹🔞")
.NTH(1).VALUE()=1
,"",

CONCATENATE("❹",
TEXT(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(1)
.REGEXEXTRACT("\d+(?:.\d+)?")
/
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(1)
.REGEXEXTRACT("\d+(?:.\d+)?"), "¥0.0"),
"/件Ⓜ️❹🔞")

)
))

,"；",


IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❹.*?❹🔞").REGEXMATCH("礼包")
,"",


IF(
OR(
[💻💚初级单价]
.REGEXEXTRACTALL("❹(.*?)❹🔞")
.NTH(2).ISBLANK().NOT()
,

[💻💚进阶单价]
.REGEXEXTRACTALL("❹(.*?)❹🔞")
.NTH(2).ISBLANK().NOT()

,

[💻💚高阶单价]
.REGEXEXTRACTALL("❹(.*?)❹🔞")
.NTH(2).ISBLANK().NOT()

)
,"",

IF(
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❹(.*?)❹🔞")
.NTH(2).VALUE()=1
,"",

CONCATENATE("❹",
TEXT(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(2)
.REGEXEXTRACT("\d+(?:.\d+)?")
/
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(2)
.REGEXEXTRACT("\d+(?:.\d+)?"), "¥0.0"),
"/件Ⓜ️❹🔞")

)
))

,"；",


IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❹.*?❹🔞").REGEXMATCH("礼包")
,"",


IF(
OR(
[💻💚初级单价]
.REGEXEXTRACTALL("❹(.*?)❹🔞")
.NTH(3).ISBLANK().NOT()
,

[💻💚进阶单价]
.REGEXEXTRACTALL("❹(.*?)❹🔞")
.NTH(3).ISBLANK().NOT()

,

[💻💚高阶单价]
.REGEXEXTRACTALL("❹(.*?)❹🔞")
.NTH(3).ISBLANK().NOT()


)
,"",

IF(
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❹(.*?)❹🔞")
.NTH(3).VALUE()=1
,"",

CONCATENATE("❹",
TEXT(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(3)
.REGEXEXTRACT("\d+(?:.\d+)?")
/
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(3)
.REGEXEXTRACT("\d+(?:.\d+)?"), "¥0.0"),
"/件Ⓜ️❹🔞")

)))


,CHAR(10),
IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❺.*?❺🔞").REGEXMATCH("礼包")
,"",

IF(
OR(
[💻💚初级单价]
.REGEXEXTRACTALL("❺(.*?)❺🔞")
.NTH(1).ISBLANK().NOT()
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❺(.*?)❺🔞")
.NTH(1).ISBLANK().NOT()

,
[💻💚高阶单价]
.REGEXEXTRACTALL("❺(.*?)❺🔞")
.NTH(1).ISBLANK().NOT()


)
,"",

IF(
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❺(.*?)❺🔞")
.NTH(1).VALUE()=1
,"",

CONCATENATE("❺",
TEXT(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(1)
.REGEXEXTRACT("\d+(?:.\d+)?")
/
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(1)
.REGEXEXTRACT("\d+(?:.\d+)?"), "¥0.0"),
"/件Ⓜ️❺🔞")

)
))

,"；",


IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❺.*?❺🔞").REGEXMATCH("礼包")
,"",


IF(
OR(
[💻💚初级单价]
.REGEXEXTRACTALL("❺(.*?)❺🔞")
.NTH(2).ISBLANK().NOT()
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❺(.*?)❺🔞")
.NTH(2).ISBLANK().NOT()

,
[💻💚高阶单价]
.REGEXEXTRACTALL("❺(.*?)❺🔞")
.NTH(2).ISBLANK().NOT()

)
,"",

IF(
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❺(.*?)❺🔞")
.NTH(2).VALUE()=1
,"",

CONCATENATE("❺",
TEXT(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(2)
.REGEXEXTRACT("\d+(?:.\d+)?")
/
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(2)
.REGEXEXTRACT("\d+(?:.\d+)?"), "¥0.0"),
"/件Ⓜ️❺🔞")

)
))

,"；",


IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❺.*?❺🔞").REGEXMATCH("礼包")
,"",


IF(
OR(
[💻💚初级单价]
.REGEXEXTRACTALL("❺(.*?)❺🔞")
.NTH(3).ISBLANK().NOT()
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❺(.*?)❺🔞")
.NTH(3).ISBLANK().NOT()

,
[💻💚高阶单价]
.REGEXEXTRACTALL("❺(.*?)❺🔞")
.NTH(3).ISBLANK().NOT()

)
,"",

IF(
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❺(.*?)❺🔞")
.NTH(3).VALUE()=1
,"",

CONCATENATE("❺",
TEXT(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(3)
.REGEXEXTRACT("\d+(?:.\d+)?")
/
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(3)
.REGEXEXTRACT("\d+(?:.\d+)?"), "¥0.0"),
"/件Ⓜ️❺🔞")

)))


,CHAR(10),
IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❻.*?❻🔞").REGEXMATCH("礼包")
,"",

IF(
OR(
[💻💚初级单价]
.REGEXEXTRACTALL("❻(.*?)❻🔞")
.NTH(1).ISBLANK().NOT()
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❻(.*?)❻🔞")
.NTH(1).ISBLANK().NOT()

,
[💻💚高阶单价]
.REGEXEXTRACTALL("❻(.*?)❻🔞")
.NTH(1).ISBLANK().NOT()


)
,"",

IF(
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❻(.*?)❻🔞")
.NTH(1).VALUE()=1
,"",

CONCATENATE("❻",
TEXT(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(1)
.REGEXEXTRACT("\d+(?:.\d+)?")
/
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(1)
.REGEXEXTRACT("\d+(?:.\d+)?"), "¥0.0"),
"/件Ⓜ️❻🔞")

)
))

,"；",


IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❻.*?❻🔞").REGEXMATCH("礼包")
,"",


IF(
OR(
[💻💚初级单价]
.REGEXEXTRACTALL("❻(.*?)❻🔞")
.NTH(2).ISBLANK().NOT()
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❻(.*?)❻🔞")
.NTH(2).ISBLANK().NOT()

,
[💻💚高阶单价]
.REGEXEXTRACTALL("❻(.*?)❻🔞")
.NTH(2).ISBLANK().NOT()

)
,"",

IF(
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❻(.*?)❻🔞")
.NTH(2).VALUE()=1
,"",

CONCATENATE("❻",
TEXT(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(2)
.REGEXEXTRACT("\d+(?:.\d+)?")
/
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(2)
.REGEXEXTRACT("\d+(?:.\d+)?"), "¥0.0"),
"/件Ⓜ️❻🔞")

)
))

,"；",


IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❻.*?❻🔞").REGEXMATCH("礼包")
,"",


IF(
OR(
[💻💚初级单价]
.REGEXEXTRACTALL("❻(.*?)❻🔞")
.NTH(3).ISBLANK().NOT()
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❻(.*?)❻🔞")
.NTH(3).ISBLANK().NOT()

,
[💻💚高阶单价]
.REGEXEXTRACTALL("❻(.*?)❻🔞")
.NTH(3).ISBLANK().NOT()

)
,"",

IF(
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❻(.*?)❻🔞")
.NTH(3).VALUE()=1
,"",

CONCATENATE("❻",
TEXT(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(3)
.REGEXEXTRACT("\d+(?:.\d+)?")
/
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(3)
.REGEXEXTRACT("\d+(?:.\d+)?"), "¥0.0"),
"/件Ⓜ️❻🔞")

)))

)

.REGEXREPLACE("[❶-❾]¥0/?(?:"&[🧠单位合集]&")?Ⓜ️[❶-❾]🔞","")
.REGEXREPLACE("[❶-❾]¥\d+(?:\.\d+)?/Ⓜ️[❶-❾]🔞","")

.REGEXREPLACE("；+","；")
.REGEXREPLACE("^；","")
.REGEXREPLACE("
；","")

.REGEXREPLACE("[;；,，+➕～\\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
💻💚终极单价（7-9）
CONCATENATE(

IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❼.*?❼🔞").REGEXMATCH("礼包")
,"",

IF(
OR(
[💻💚初级单价]
.REGEXEXTRACTALL("❼(.*?)❼🔞")
.NTH(1).ISBLANK().NOT()
,

[💻💚进阶单价]
.REGEXEXTRACTALL("❼(.*?)❼🔞")
.NTH(1).ISBLANK().NOT()

,

[💻💚高阶单价]
.REGEXEXTRACTALL("❼(.*?)❼🔞")
.NTH(1).ISBLANK().NOT()

)
,"",

IF(
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❼(.*?)❼🔞")
.NTH(1).VALUE()=1
,"",

CONCATENATE("❼",
TEXT(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(1)
.REGEXEXTRACT("\d+(?:.\d+)?")
/
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(1)
.REGEXEXTRACT("\d+(?:.\d+)?"), "¥0.0"),
"/件Ⓜ️❼🔞")

)
))

,"；",


IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❼.*?❼🔞").REGEXMATCH("礼包")
,"",


IF(
OR(
[💻💚初级单价]
.REGEXEXTRACTALL("❼(.*?)❼🔞")
.NTH(2).ISBLANK().NOT()
,

[💻💚进阶单价]
.REGEXEXTRACTALL("❼(.*?)❼🔞")
.NTH(2).ISBLANK().NOT()
,

[💻💚高阶单价]
.REGEXEXTRACTALL("❼(.*?)❼🔞")
.NTH(2).ISBLANK().NOT()

)
,"",

IF(
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❼(.*?)❼🔞")
.NTH(2).VALUE()=1
,"",

CONCATENATE("❼",
TEXT(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(2)
.REGEXEXTRACT("\d+(?:.\d+)?")
/
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(2)
.REGEXEXTRACT("\d+(?:.\d+)?"), "¥0.0"),
"/件Ⓜ️❼🔞")

)
))

,"；",


IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❼.*?❼🔞").REGEXMATCH("礼包")
,"",


IF(
OR(
[💻💚初级单价]
.REGEXEXTRACTALL("❼(.*?)❼🔞")
.NTH(3).ISBLANK().NOT()
,

[💻💚进阶单价]
.REGEXEXTRACTALL("❼(.*?)❼🔞")
.NTH(3).ISBLANK().NOT()

,

[💻💚高阶单价]
.REGEXEXTRACTALL("❼(.*?)❼🔞")
.NTH(3).ISBLANK().NOT()

)
,"",

IF(
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❼(.*?)❼🔞")
.NTH(3).VALUE()=1
,"",

CONCATENATE("❼",
TEXT(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(3)
.REGEXEXTRACT("\d+(?:.\d+)?")
/
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(3)
.REGEXEXTRACT("\d+(?:.\d+)?"), "¥0.0"),
"/件Ⓜ️❼🔞")

)))


,CHAR(10),

IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❼.*?❼🔞").REGEXMATCH("礼包")
,"",

IF(
OR(
[💻💚初级单价]
.REGEXEXTRACTALL("❼(.*?)❼🔞")
.NTH(1).ISBLANK().NOT()
,

[💻💚进阶单价]
.REGEXEXTRACTALL("❼(.*?)❼🔞")
.NTH(1).ISBLANK().NOT()

,

[💻💚高阶单价]
.REGEXEXTRACTALL("❼(.*?)❼🔞")
.NTH(1).ISBLANK().NOT()

)
,"",

IF(
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❼(.*?)❼🔞")
.NTH(1).VALUE()=1
,"",

CONCATENATE("❼",
TEXT(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(1)
.REGEXEXTRACT("\d+(?:.\d+)?")
/
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(1)
.REGEXEXTRACT("\d+(?:.\d+)?"), "¥0.0"),
"/件Ⓜ️❼🔞")

)
))

,"；",


IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❼.*?❼🔞").REGEXMATCH("礼包")
,"",


IF(
OR(
[💻💚初级单价]
.REGEXEXTRACTALL("❼(.*?)❼🔞")
.NTH(2).ISBLANK().NOT()
,

[💻💚进阶单价]
.REGEXEXTRACTALL("❼(.*?)❼🔞")
.NTH(2).ISBLANK().NOT()

,

[💻💚高阶单价]
.REGEXEXTRACTALL("❼(.*?)❼🔞")
.NTH(2).ISBLANK().NOT()

)
,"",

IF(
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❼(.*?)❼🔞")
.NTH(2).VALUE()=1
,"",

CONCATENATE("❼",
TEXT(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(2)
.REGEXEXTRACT("\d+(?:.\d+)?")
/
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(2)
.REGEXEXTRACT("\d+(?:.\d+)?"), "¥0.0"),
"/件Ⓜ️❼🔞")

)
))

,"；",


IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❼.*?❼🔞").REGEXMATCH("礼包")
,"",


IF(
OR(
[💻💚初级单价]
.REGEXEXTRACTALL("❼(.*?)❼🔞")
.NTH(3).ISBLANK().NOT()
,

[💻💚进阶单价]
.REGEXEXTRACTALL("❼(.*?)❼🔞")
.NTH(3).ISBLANK().NOT()

,

[💻💚高阶单价]
.REGEXEXTRACTALL("❼(.*?)❼🔞")
.NTH(3).ISBLANK().NOT()


)
,"",

IF(
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❼(.*?)❼🔞")
.NTH(3).VALUE()=1
,"",

CONCATENATE("❼",
TEXT(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(3)
.REGEXEXTRACT("\d+(?:.\d+)?")
/
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(3)
.REGEXEXTRACT("\d+(?:.\d+)?"), "¥0.0"),
"/件Ⓜ️❼🔞")

)))


,CHAR(10),
IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❽.*?❽🔞").REGEXMATCH("礼包")
,"",

IF(
OR(
[💻💚初级单价]
.REGEXEXTRACTALL("❽(.*?)❽🔞")
.NTH(1).ISBLANK().NOT()
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❽(.*?)❽🔞")
.NTH(1).ISBLANK().NOT()

,
[💻💚高阶单价]
.REGEXEXTRACTALL("❽(.*?)❽🔞")
.NTH(1).ISBLANK().NOT()


)
,"",

IF(
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❽(.*?)❽🔞")
.NTH(1).VALUE()=1
,"",

CONCATENATE("❽",
TEXT(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(1)
.REGEXEXTRACT("\d+(?:.\d+)?")
/
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(1)
.REGEXEXTRACT("\d+(?:.\d+)?"), "¥0.0"),
"/件Ⓜ️❽🔞")

)
))

,"；",


IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❽.*?❽🔞").REGEXMATCH("礼包")
,"",


IF(
OR(
[💻💚初级单价]
.REGEXEXTRACTALL("❽(.*?)❽🔞")
.NTH(2).ISBLANK().NOT()
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❽(.*?)❽🔞")
.NTH(2).ISBLANK().NOT()

,
[💻💚高阶单价]
.REGEXEXTRACTALL("❽(.*?)❽🔞")
.NTH(2).ISBLANK().NOT()

)
,"",

IF(
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❽(.*?)❽🔞")
.NTH(2).VALUE()=1
,"",

CONCATENATE("❽",
TEXT(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(2)
.REGEXEXTRACT("\d+(?:.\d+)?")
/
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(2)
.REGEXEXTRACT("\d+(?:.\d+)?"), "¥0.0"),
"/件Ⓜ️❽🔞")

)
))

,"；",


IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❽.*?❽🔞").REGEXMATCH("礼包")
,"",


IF(
OR(
[💻💚初级单价]
.REGEXEXTRACTALL("❽(.*?)❽🔞")
.NTH(3).ISBLANK().NOT()
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❽(.*?)❽🔞")
.NTH(3).ISBLANK().NOT()

,
[💻💚高阶单价]
.REGEXEXTRACTALL("❽(.*?)❽🔞")
.NTH(3).ISBLANK().NOT()

)
,"",

IF(
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❽(.*?)❽🔞")
.NTH(3).VALUE()=1
,"",

CONCATENATE("❽",
TEXT(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(3)
.REGEXEXTRACT("\d+(?:.\d+)?")
/
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(3)
.REGEXEXTRACT("\d+(?:.\d+)?"), "¥0.0"),
"/件Ⓜ️❽🔞")

)))


,CHAR(10),
IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❾.*?❾🔞").REGEXMATCH("礼包")
,"",

IF(
OR(
[💻💚初级单价]
.REGEXEXTRACTALL("❾(.*?)❾🔞")
.NTH(1).ISBLANK().NOT()
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❾(.*?)❾🔞")
.NTH(1).ISBLANK().NOT()

,
[💻💚高阶单价]
.REGEXEXTRACTALL("❾(.*?)❾🔞")
.NTH(1).ISBLANK().NOT()


)
,"",

IF(
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❾(.*?)❾🔞")
.NTH(1).VALUE()=1
,"",

CONCATENATE("❾",
TEXT(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(1)
.REGEXEXTRACT("\d+(?:.\d+)?")
/
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(1)
.REGEXEXTRACT("\d+(?:.\d+)?"), "¥0.0"),
"/件Ⓜ️❾🔞")

)
))

,"；",


IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❾.*?❾🔞").REGEXMATCH("礼包")
,"",


IF(
OR(
[💻💚初级单价]
.REGEXEXTRACTALL("❾(.*?)❾🔞")
.NTH(2).ISBLANK().NOT()
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❾(.*?)❾🔞")
.NTH(2).ISBLANK().NOT()

,
[💻💚高阶单价]
.REGEXEXTRACTALL("❾(.*?)❾🔞")
.NTH(2).ISBLANK().NOT()

)
,"",

IF(
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❾(.*?)❾🔞")
.NTH(2).VALUE()=1
,"",

CONCATENATE("❾",
TEXT(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(2)
.REGEXEXTRACT("\d+(?:.\d+)?")
/
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(2)
.REGEXEXTRACT("\d+(?:.\d+)?"), "¥0.0"),
"/件Ⓜ️❾🔞")

)
))

,"；",


IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACTALL("❾.*?❾🔞").REGEXMATCH("礼包")
,"",


IF(
OR(
[💻💚初级单价]
.REGEXEXTRACTALL("❾(.*?)❾🔞")
.NTH(3).ISBLANK().NOT()
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❾(.*?)❾🔞")
.NTH(3).ISBLANK().NOT()

,
[💻💚高阶单价]
.REGEXEXTRACTALL("❾(.*?)❾🔞")
.NTH(3).ISBLANK().NOT()

)
,"",

IF(
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❾(.*?)❾🔞")
.NTH(3).VALUE()=1
,"",

CONCATENATE("❾",
TEXT(
[💻🤍到手价格-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(3)
.REGEXEXTRACT("\d+(?:.\d+)?")
/
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(3)
.REGEXEXTRACT("\d+(?:.\d+)?"), "¥0.0"),
"/件Ⓜ️❾🔞")

)))

)

.REGEXREPLACE("[❶-❾]¥0/?(?:"&[🧠单位合集]&")?Ⓜ️[❶-❾]🔞","")
.REGEXREPLACE("[❶-❾]¥\d+(?:\.\d+)?/Ⓜ️[❶-❾]🔞","")

.REGEXREPLACE("；+","；")
.REGEXREPLACE("^；","")
.REGEXREPLACE("
；","")

.REGEXREPLACE("[;；,，+➕～\\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
💻💚终极单价
CONCATENATE(
[💻💚终极单价（1-3）]
，CHAR(10)，
[💻💚终极单价（4-6）]
，CHAR(10)，
[💻💚终极单价（7-9）]
)

.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
💻💚单价（纯信息合并）（1-3）
CONCATENATE(
[💻💚基础单价]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(1)
,CHAR(10)，
[💻💚初级单价]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(1)

,CHAR(10)，
[💻💚进阶单价]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(1)

,CHAR(10)，
[💻💚高阶单价]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(1)

,CHAR(10)，
[💻💚终极单价]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(1)

,CHAR(10)

,"🔥",

[💻💚基础单价]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(2)
,CHAR(10)，
[💻💚初级单价]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(2)

,CHAR(10)，
[💻💚进阶单价]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(2)

,CHAR(10)，
[💻💚高阶单价]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(2)

,CHAR(10)，
[💻💚终极单价]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(2)



,CHAR(10)

,"🔥",

[💻💚基础单价]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(3)
,CHAR(10)，
[💻💚初级单价]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(3)

,CHAR(10)，
[💻💚进阶单价]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(3)

,CHAR(10)，
[💻💚高阶单价]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(3)

,CHAR(10)，
[💻💚终极单价]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(3)



,CHAR(10)

,"🔥",

[💻💚基础单价]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(4)
,CHAR(10)，
[💻💚初级单价]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(4)

,CHAR(10)，
[💻💚进阶单价]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(4)

,CHAR(10)，
[💻💚高阶单价]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(4)

,CHAR(10)，
[💻💚终极单价]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(4)



,CHAR(10)

,"🔥",

[💻💚基础单价]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(5)
,CHAR(10)，
[💻💚初级单价]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(5)

,CHAR(10)，
[💻💚进阶单价]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(5)

,CHAR(10)，
[💻💚高阶单价]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(5)

,CHAR(10)，
[💻💚终极单价]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(5)



,CHAR(10)

,"🔥",

[💻💚基础单价]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(6)
,CHAR(10)，
[💻💚初级单价]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(6)

,CHAR(10)，
[💻💚进阶单价]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(6)

,CHAR(10)，
[💻💚高阶单价]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(6)

,CHAR(10)，
[💻💚终极单价]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(6)


,CHAR(10)

,"㊙️㊙️㊙️",


[💻💚基础单价]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(1)
,CHAR(10)，
[💻💚初级单价]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(1)

,CHAR(10)，
[💻💚进阶单价]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(1)

,CHAR(10)，
[💻💚高阶单价]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(1)

,CHAR(10)，
[💻💚终极单价]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(1)

,CHAR(10)

,"🔥",

[💻💚基础单价]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(2)
,CHAR(10)，
[💻💚初级单价]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(2)

,CHAR(10)，
[💻💚进阶单价]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(2)

,CHAR(10)，
[💻💚高阶单价]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(2)

,CHAR(10)，
[💻💚终极单价]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(2)



,CHAR(10)

,"🔥",

[💻💚基础单价]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(3)
,CHAR(10)，
[💻💚初级单价]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(3)

,CHAR(10)，
[💻💚进阶单价]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(3)

,CHAR(10)，
[💻💚高阶单价]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(3)

,CHAR(10)，
[💻💚终极单价]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(3)



,CHAR(10)

,"🔥",

[💻💚基础单价]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(4)
,CHAR(10)，
[💻💚初级单价]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(4)

,CHAR(10)，
[💻💚进阶单价]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(4)

,CHAR(10)，
[💻💚高阶单价]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(4)

,CHAR(10)，
[💻💚终极单价]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(4)



,CHAR(10)

,"🔥",

[💻💚基础单价]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(5)
,CHAR(10)，
[💻💚初级单价]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(5)

,CHAR(10)，
[💻💚进阶单价]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(5)

,CHAR(10)，
[💻💚高阶单价]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(5)

,CHAR(10)，
[💻💚终极单价]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(5)



,CHAR(10)

,"🔥",

[💻💚基础单价]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(6)
,CHAR(10)，
[💻💚初级单价]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(6)

,CHAR(10)，
[💻💚进阶单价]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(6)

,CHAR(10)，
[💻💚高阶单价]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(6)

,CHAR(10)，
[💻💚终极单价]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(6)


,CHAR(10)

,"㊙️㊙️㊙️",


[💻💚基础单价]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(1)
,CHAR(10)，
[💻💚初级单价]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(1)

,CHAR(10)，
[💻💚进阶单价]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(1)

,CHAR(10)，
[💻💚高阶单价]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(1)

,CHAR(10)，
[💻💚终极单价]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(1)

,CHAR(10)

,"🔥",

[💻💚基础单价]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(2)
,CHAR(10)，
[💻💚初级单价]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(2)

,CHAR(10)，
[💻💚进阶单价]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(2)

,CHAR(10)，
[💻💚高阶单价]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(2)

,CHAR(10)，
[💻💚终极单价]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(2)



,CHAR(10)

,"🔥",

[💻💚基础单价]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(3)
,CHAR(10)，
[💻💚初级单价]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(3)

,CHAR(10)，
[💻💚进阶单价]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(3)

,CHAR(10)，
[💻💚高阶单价]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(3)

,CHAR(10)，
[💻💚终极单价]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(3)



,CHAR(10)

,"🔥",

[💻💚基础单价]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(4)
,CHAR(10)，
[💻💚初级单价]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(4)

,CHAR(10)，
[💻💚进阶单价]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(4)

,CHAR(10)，
[💻💚高阶单价]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(4)

,CHAR(10)，
[💻💚终极单价]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(4)



,CHAR(10)

,"🔥",

[💻💚基础单价]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(5)
,CHAR(10)，
[💻💚初级单价]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(5)

,CHAR(10)，
[💻💚进阶单价]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(5)

,CHAR(10)，
[💻💚高阶单价]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(5)

,CHAR(10)，
[💻💚终极单价]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(5)



,CHAR(10)

,"🔥",

[💻💚基础单价]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(6)
,CHAR(10)，
[💻💚初级单价]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(6)

,CHAR(10)，
[💻💚进阶单价]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(6)

,CHAR(10)，
[💻💚高阶单价]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(6)

,CHAR(10)，
[💻💚终极单价]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(6)


)
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
.REGEXREPLACE("🔥","")
.REGEXREPLACE("(㊙️\s*)+","㊙️")
.REGEXREPLACE("㊙️"&CHAR(10)&"㊙️","")

.REGEXREPLACE("㊙️","〰️〰️〰️"&CHAR(10))
.REGEXREPLACE("^(\s*〰️\s*)+","")

.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
💻💚单价（纯信息合并）（4-6）
CONCATENATE(

[💻💚基础单价]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(1)
,CHAR(10)，
[💻💚初级单价]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(1)

,CHAR(10)，
[💻💚进阶单价]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(1)

,CHAR(10)，
[💻💚高阶单价]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(1)

,CHAR(10)，
[💻💚终极单价]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(1)

,CHAR(10)

,"🔥",

[💻💚基础单价]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(2)
,CHAR(10)，
[💻💚初级单价]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(2)

,CHAR(10)，
[💻💚进阶单价]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(2)

,CHAR(10)，
[💻💚高阶单价]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(2)

,CHAR(10)，
[💻💚终极单价]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(2)



,CHAR(10)

,"🔥",

[💻💚基础单价]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(3)
,CHAR(10)，
[💻💚初级单价]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(3)

,CHAR(10)，
[💻💚进阶单价]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(3)

,CHAR(10)，
[💻💚高阶单价]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(3)

,CHAR(10)，
[💻💚终极单价]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(3)



,CHAR(10)

,"🔥",

[💻💚基础单价]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(4)
,CHAR(10)，
[💻💚初级单价]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(4)

,CHAR(10)，
[💻💚进阶单价]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(4)

,CHAR(10)，
[💻💚高阶单价]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(4)

,CHAR(10)，
[💻💚终极单价]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(4)



,CHAR(10)

,"🔥",

[💻💚基础单价]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(5)
,CHAR(10)，
[💻💚初级单价]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(5)

,CHAR(10)，
[💻💚进阶单价]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(5)

,CHAR(10)，
[💻💚高阶单价]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(5)

,CHAR(10)，
[💻💚终极单价]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(5)



,CHAR(10)

,"🔥",

[💻💚基础单价]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(6)
,CHAR(10)，
[💻💚初级单价]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(6)

,CHAR(10)，
[💻💚进阶单价]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(6)

,CHAR(10)，
[💻💚高阶单价]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(6)

,CHAR(10)，
[💻💚终极单价]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(6)



,CHAR(10)

,"㊙️㊙️㊙️",


[💻💚基础单价]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(1)
,CHAR(10)，
[💻💚初级单价]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(1)

,CHAR(10)，
[💻💚进阶单价]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(1)

,CHAR(10)，
[💻💚高阶单价]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(1)

,CHAR(10)，
[💻💚终极单价]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(1)

,CHAR(10)

,"🔥",

[💻💚基础单价]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(2)
,CHAR(10)，
[💻💚初级单价]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(2)

,CHAR(10)，
[💻💚进阶单价]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(2)

,CHAR(10)，
[💻💚高阶单价]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(2)

,CHAR(10)，
[💻💚终极单价]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(2)



,CHAR(10)

,"🔥",

[💻💚基础单价]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(3)
,CHAR(10)，
[💻💚初级单价]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(3)

,CHAR(10)，
[💻💚进阶单价]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(3)

,CHAR(10)，
[💻💚高阶单价]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(3)

,CHAR(10)，
[💻💚终极单价]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(3)



,CHAR(10)

,"🔥",

[💻💚基础单价]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(4)
,CHAR(10)，
[💻💚初级单价]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(4)

,CHAR(10)，
[💻💚进阶单价]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(4)

,CHAR(10)，
[💻💚高阶单价]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(4)

,CHAR(10)，
[💻💚终极单价]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(4)



,CHAR(10)

,"🔥",

[💻💚基础单价]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(5)
,CHAR(10)，
[💻💚初级单价]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(5)

,CHAR(10)，
[💻💚进阶单价]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(5)

,CHAR(10)，
[💻💚高阶单价]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(5)

,CHAR(10)，
[💻💚终极单价]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(5)



,CHAR(10)

,"🔥",

[💻💚基础单价]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(6)
,CHAR(10)，
[💻💚初级单价]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(6)

,CHAR(10)，
[💻💚进阶单价]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(6)

,CHAR(10)，
[💻💚高阶单价]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(6)

,CHAR(10)，
[💻💚终极单价]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(6)



,CHAR(10)

,"㊙️㊙️㊙️",


[💻💚基础单价]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(1)
,CHAR(10)，
[💻💚初级单价]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(1)

,CHAR(10)，
[💻💚进阶单价]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(1)

,CHAR(10)，
[💻💚高阶单价]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(1)

,CHAR(10)，
[💻💚终极单价]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(1)

,CHAR(10)

,"🔥",

[💻💚基础单价]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(2)
,CHAR(10)，
[💻💚初级单价]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(2)

,CHAR(10)，
[💻💚进阶单价]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(2)

,CHAR(10)，
[💻💚高阶单价]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(2)

,CHAR(10)，
[💻💚终极单价]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(2)



,CHAR(10)

,"🔥",

[💻💚基础单价]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(3)
,CHAR(10)，
[💻💚初级单价]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(3)

,CHAR(10)，
[💻💚进阶单价]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(3)

,CHAR(10)，
[💻💚高阶单价]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(3)

,CHAR(10)，
[💻💚终极单价]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(3)



,CHAR(10)

,"🔥",

[💻💚基础单价]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(4)
,CHAR(10)，
[💻💚初级单价]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(4)

,CHAR(10)，
[💻💚进阶单价]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(4)

,CHAR(10)，
[💻💚高阶单价]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(4)

,CHAR(10)，
[💻💚终极单价]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(4)



,CHAR(10)

,"🔥",

[💻💚基础单价]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(5)
,CHAR(10)，
[💻💚初级单价]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(5)

,CHAR(10)，
[💻💚进阶单价]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(5)

,CHAR(10)，
[💻💚高阶单价]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(5)

,CHAR(10)，
[💻💚终极单价]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(5)



,CHAR(10)

,"🔥",

[💻💚基础单价]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(6)
,CHAR(10)，
[💻💚初级单价]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(6)

,CHAR(10)，
[💻💚进阶单价]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(6)

,CHAR(10)，
[💻💚高阶单价]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(6)

,CHAR(10)，
[💻💚终极单价]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(6)




)
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
.REGEXREPLACE("🔥","")
.REGEXREPLACE("(㊙️\s*)+","㊙️")
.REGEXREPLACE("㊙️"&CHAR(10)&"㊙️","")

.REGEXREPLACE("㊙️","〰️〰️〰️"&CHAR(10))
.REGEXREPLACE("^(\s*〰️\s*)+","")

.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
💻💚单价（纯信息合并）（7-9）
CONCATENATE(

[💻💚基础单价]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(1)
,CHAR(10)，
[💻💚初级单价]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(1)

,CHAR(10)，
[💻💚进阶单价]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(1)

,CHAR(10)，
[💻💚高阶单价]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(1)

,CHAR(10)，
[💻💚终极单价]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(1)

,CHAR(10)

,"🔥",

[💻💚基础单价]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(2)
,CHAR(10)，
[💻💚初级单价]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(2)

,CHAR(10)，
[💻💚进阶单价]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(2)

,CHAR(10)，
[💻💚高阶单价]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(2)

,CHAR(10)，
[💻💚终极单价]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(2)



,CHAR(10)

,"🔥",

[💻💚基础单价]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(3)
,CHAR(10)，
[💻💚初级单价]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(3)

,CHAR(10)，
[💻💚进阶单价]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(3)

,CHAR(10)，
[💻💚高阶单价]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(3)

,CHAR(10)，
[💻💚终极单价]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(3)



,CHAR(10)

,"🔥",

[💻💚基础单价]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(4)
,CHAR(10)，
[💻💚初级单价]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(4)

,CHAR(10)，
[💻💚进阶单价]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(4)

,CHAR(10)，
[💻💚高阶单价]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(4)

,CHAR(10)，
[💻💚终极单价]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(4)



,CHAR(10)

,"🔥",

[💻💚基础单价]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(5)
,CHAR(10)，
[💻💚初级单价]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(5)

,CHAR(10)，
[💻💚进阶单价]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(5)

,CHAR(10)，
[💻💚高阶单价]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(5)

,CHAR(10)，
[💻💚终极单价]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(5)



,CHAR(10)

,"🔥",

[💻💚基础单价]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(6)
,CHAR(10)，
[💻💚初级单价]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(6)

,CHAR(10)，
[💻💚进阶单价]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(6)

,CHAR(10)，
[💻💚高阶单价]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(6)

,CHAR(10)，
[💻💚终极单价]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(6)



,CHAR(10)

,"㊙️㊙️㊙️",


[💻💚基础单价]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(1)
,CHAR(10)，
[💻💚初级单价]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(1)

,CHAR(10)，
[💻💚进阶单价]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(1)

,CHAR(10)，
[💻💚高阶单价]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(1)

,CHAR(10)，
[💻💚终极单价]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(1)

,CHAR(10)

,"🔥",

[💻💚基础单价]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(2)
,CHAR(10)，
[💻💚初级单价]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(2)

,CHAR(10)，
[💻💚进阶单价]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(2)

,CHAR(10)，
[💻💚高阶单价]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(2)

,CHAR(10)，
[💻💚终极单价]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(2)



,CHAR(10)

,"🔥",

[💻💚基础单价]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(3)
,CHAR(10)，
[💻💚初级单价]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(3)

,CHAR(10)，
[💻💚进阶单价]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(3)

,CHAR(10)，
[💻💚高阶单价]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(3)

,CHAR(10)，
[💻💚终极单价]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(3)



,CHAR(10)

,"🔥",

[💻💚基础单价]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(4)
,CHAR(10)，
[💻💚初级单价]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(4)

,CHAR(10)，
[💻💚进阶单价]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(4)

,CHAR(10)，
[💻💚高阶单价]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(4)

,CHAR(10)，
[💻💚终极单价]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(4)



,CHAR(10)

,"🔥",

[💻💚基础单价]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(5)
,CHAR(10)，
[💻💚初级单价]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(5)

,CHAR(10)，
[💻💚进阶单价]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(5)

,CHAR(10)，
[💻💚高阶单价]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(5)

,CHAR(10)，
[💻💚终极单价]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(5)



,CHAR(10)

,"🔥",

[💻💚基础单价]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(6)
,CHAR(10)，
[💻💚初级单价]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(6)

,CHAR(10)，
[💻💚进阶单价]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(6)

,CHAR(10)，
[💻💚高阶单价]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(6)

,CHAR(10)，
[💻💚终极单价]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(6)



,CHAR(10)

,"㊙️㊙️㊙️",


[💻💚基础单价]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(1)
,CHAR(10)，
[💻💚初级单价]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(1)

,CHAR(10)，
[💻💚进阶单价]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(1)

,CHAR(10)，
[💻💚高阶单价]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(1)

,CHAR(10)，
[💻💚终极单价]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(1)

,CHAR(10)

,"🔥",

[💻💚基础单价]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(2)
,CHAR(10)，
[💻💚初级单价]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(2)

,CHAR(10)，
[💻💚进阶单价]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(2)

,CHAR(10)，
[💻💚高阶单价]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(2)

,CHAR(10)，
[💻💚终极单价]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(2)



,CHAR(10)

,"🔥",

[💻💚基础单价]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(3)
,CHAR(10)，
[💻💚初级单价]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(3)

,CHAR(10)，
[💻💚进阶单价]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(3)

,CHAR(10)，
[💻💚高阶单价]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(3)

,CHAR(10)，
[💻💚终极单价]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(3)



,CHAR(10)

,"🔥",

[💻💚基础单价]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(4)
,CHAR(10)，
[💻💚初级单价]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(4)

,CHAR(10)，
[💻💚进阶单价]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(4)

,CHAR(10)，
[💻💚高阶单价]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(4)

,CHAR(10)，
[💻💚终极单价]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(4)



,CHAR(10)

,"🔥",

[💻💚基础单价]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(5)
,CHAR(10)，
[💻💚初级单价]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(5)

,CHAR(10)，
[💻💚进阶单价]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(5)

,CHAR(10)，
[💻💚高阶单价]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(5)

,CHAR(10)，
[💻💚终极单价]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(5)



,CHAR(10)

,"🔥",

[💻💚基础单价]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(6)
,CHAR(10)，
[💻💚初级单价]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(6)

,CHAR(10)，
[💻💚进阶单价]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(6)

,CHAR(10)，
[💻💚高阶单价]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(6)

,CHAR(10)，
[💻💚终极单价]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(6)



)
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
.REGEXREPLACE("🔥","")
.REGEXREPLACE("(㊙️\s*)+","㊙️")
.REGEXREPLACE("㊙️"&CHAR(10)&"㊙️","")

.REGEXREPLACE("㊙️","〰️〰️〰️"&CHAR(10))
.REGEXREPLACE("^(\s*〰️\s*)+","")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
💻💚单价（纯信息合并）
CONCATENATE(
[💻💚单价（纯信息合并）（1-3）]
，CHAR(10)，
[💻💚单价（纯信息合并）（4-6）]
，CHAR(10)，
[💻💚单价（纯信息合并）（7-9）])

.REGEXREPLACE("(\s*〰️\s*)+$","")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
💻💚计算单价合并（初步）
CONCATENATE(
IF(
[💻💚高阶单价]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(1)
.ISBLANK().NOT()
，
[💻💚高阶单价]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(1)
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(1)
)
,"；",

IF(
[🧠高阶单位]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(2)
.ISBLANK().NOT()
，
[💻💚高阶单价]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(2)
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(2)
)

,"；",

IF(
[💻💚高阶单价]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(3)
.ISBLANK().NOT()
，
[💻💚高阶单价]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(3)
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(3)
)
,"；",

IF(
[💻💚高阶单价]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(4)
.ISBLANK().NOT()
，
[💻💚高阶单价]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(4)
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(4)
)

,"；",

IF(
[💻💚高阶单价]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(5)
.ISBLANK().NOT()
，
[💻💚高阶单价]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(5)
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(5)
)


,"；",

IF(
[💻💚高阶单价]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(6)
.ISBLANK().NOT()
，
[💻💚高阶单价]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(6)
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(6)
)



,CHAR(10),

IF(
[💻💚高阶单价]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(1)
.ISBLANK().NOT()
，
[💻💚高阶单价]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(1)
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(1)
)
,"；",

IF(
[💻💚高阶单价]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(2)
.ISBLANK().NOT()
，
[💻💚高阶单价]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(2)
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(2)
)

,"；",

IF(
[💻💚高阶单价]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(3)
.ISBLANK().NOT()
，
[💻💚高阶单价]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(3)
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(3)
)
,"；",

IF(
[💻💚高阶单价]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(4)
.ISBLANK().NOT()
，
[💻💚高阶单价]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(4)
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(4)
)

,"；",

IF(
[💻💚高阶单价]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(5)
.ISBLANK().NOT()
，
[💻💚高阶单价]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(5)
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(5)
)


,"；",

IF(
[💻💚高阶单价]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(6)
.ISBLANK().NOT()
，
[💻💚高阶单价]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(6)
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(6)
)




,CHAR(10),

IF(
[💻💚高阶单价]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(1)
.ISBLANK().NOT()
，
[💻💚高阶单价]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(1)
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(1)
)
,"；",

IF(
[💻💚高阶单价]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(2)
.ISBLANK().NOT()
，
[💻💚高阶单价]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(2)
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(2)
)

,"；",

IF(
[💻💚高阶单价]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(3)
.ISBLANK().NOT()
，
[💻💚高阶单价]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(3)
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(3)
)
,"；",

IF(
[💻💚高阶单价]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(4)
.ISBLANK().NOT()
，
[💻💚高阶单价]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(4)
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(4)
)

,"；",

IF(
[💻💚高阶单价]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(5)
.ISBLANK().NOT()
，
[💻💚高阶单价]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(5)
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(5)
)


,"；",

IF(
[💻💚高阶单价]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(6)
.ISBLANK().NOT()
，
[💻💚高阶单价]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(6)
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(6)
)




,CHAR(10),

IF(
[💻💚高阶单价]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(1)
.ISBLANK().NOT()
，
[💻💚高阶单价]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(1)
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(1)
)
,"；",

IF(
[💻💚高阶单价]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(2)
.ISBLANK().NOT()
，
[💻💚高阶单价]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(2)
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(2)
)

,"；",

IF(
[💻💚高阶单价]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(3)
.ISBLANK().NOT()
，
[💻💚高阶单价]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(3)
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(3)
)
,"；",

IF(
[💻💚高阶单价]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(4)
.ISBLANK().NOT()
，
[💻💚高阶单价]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(4)
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(4)
)

,"；",

IF(
[💻💚高阶单价]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(5)
.ISBLANK().NOT()
，
[💻💚高阶单价]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(5)
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(5)
)


,"；",

IF(
[💻💚高阶单价]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(6)
.ISBLANK().NOT()
，
[💻💚高阶单价]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(6)
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(6)
)





,CHAR(10),

IF(
[💻💚高阶单价]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(1)
.ISBLANK().NOT()
，
[💻💚高阶单价]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(1)
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(1)
)
,"；",

IF(
[💻💚高阶单价]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(2)
.ISBLANK().NOT()
，
[💻💚高阶单价]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(2)
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(2)
)

,"；",

IF(
[💻💚高阶单价]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(3)
.ISBLANK().NOT()
，
[💻💚高阶单价]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(3)
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(3)
)
,"；",

IF(
[💻💚高阶单价]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(4)
.ISBLANK().NOT()
，
[💻💚高阶单价]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(4)
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(4)
)

,"；",

IF(
[🧠高阶单位]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(5)
.ISBLANK().NOT()
，
[💻💚高阶单价]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(5)
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(5)
)


,"；",

IF(
[💻💚高阶单价]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(6)
.ISBLANK().NOT()
，
[💻💚高阶单价]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(6)
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(6)
)




,CHAR(10),

IF(
[💻💚高阶单价]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(1)
.ISBLANK().NOT()
，
[💻💚高阶单价]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(1)
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(1)
)
,"；",

IF(
[💻💚高阶单价]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(2)
.ISBLANK().NOT()
，
[💻💚高阶单价]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(2)
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(2)
)

,"；",

IF(
[💻💚高阶单价]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(3)
.ISBLANK().NOT()
，
[💻💚高阶单价]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(3)
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(3)
)
,"；",

IF(
[💻💚高阶单价]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(4)
.ISBLANK().NOT()
，
[💻💚高阶单价]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(4)
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(4)
)

,"；",

IF(
[💻💚高阶单价]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(5)
.ISBLANK().NOT()
，
[💻💚高阶单价]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(5)
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(5)
)


,"；",

IF(
[💻💚高阶单价]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(6)
.ISBLANK().NOT()
，
[💻💚高阶单价]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(6)
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(6)
)






,CHAR(10),

IF(
[💻💚高阶单价]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(1)
.ISBLANK().NOT()
，
[💻💚高阶单价]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(1)
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(1)
)
,"；",

IF(
[💻💚高阶单价]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(2)
.ISBLANK().NOT()
，
[💻💚高阶单价]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(2)
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(2)
)

,"；",

IF(
[💻💚高阶单价]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(3)
.ISBLANK().NOT()
，
[💻💚高阶单价]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(3)
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(3)
)
,"；",

IF(
[💻💚高阶单价]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(4)
.ISBLANK().NOT()
，
[💻💚高阶单价]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(4)
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(4)
)

,"；",

IF(
[💻💚高阶单价]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(5)
.ISBLANK().NOT()
，
[💻💚高阶单价]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(5)
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(5)
)


,"；",

IF(
[💻💚高阶单价]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(6)
.ISBLANK().NOT()
，
[💻💚高阶单价]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(6)
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(6)
)








,CHAR(10),

IF(
[💻💚高阶单价]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(1)
.ISBLANK().NOT()
，
[💻💚高阶单价]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(1)
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(1)
)
,"；",

IF(
[💻💚高阶单价]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(2)
.ISBLANK().NOT()
，
[💻💚高阶单价]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(2)
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(2)
)

,"；",

IF(
[💻💚高阶单价]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(3)
.ISBLANK().NOT()
，
[💻💚高阶单价]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(3)
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(3)
)
,"；",

IF(
[💻💚高阶单价]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(4)
.ISBLANK().NOT()
，
[💻💚高阶单价]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(4)
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(4)
)

,"；",

IF(
[💻💚高阶单价]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(5)
.ISBLANK().NOT()
，
[💻💚高阶单价]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(5)
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(5)
)


,"；",

IF(
[💻💚高阶单价]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(6)
.ISBLANK().NOT()
，
[💻💚高阶单价]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(6)
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(6)
)





,CHAR(10),

IF(
[💻💚高阶单价]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(1)
.ISBLANK().NOT()
，
[💻💚高阶单价]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(1)
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(1)
)
,"；",

IF(
[💻💚高阶单价]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(2)
.ISBLANK().NOT()
，
[💻💚高阶单价]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(2)
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(2)
)

,"；",

IF(
[💻💚高阶单价]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(3)
.ISBLANK().NOT()
，
[💻💚高阶单价]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(3)
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(3)
)
,"；",

IF(
[💻💚高阶单价]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(4)
.ISBLANK().NOT()
，
[💻💚高阶单价]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(4)
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(4)
)

,"；",

IF(
[💻💚高阶单价]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(5)
.ISBLANK().NOT()
，
[💻💚高阶单价]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(5)
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(5)
)


,"；",

IF(
[💻💚高阶单价]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(6)
.ISBLANK().NOT()
，
[💻💚高阶单价]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(6)
,
[💻💚进阶单价]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(6)
)




)

.REGEXREPLACE("；+","；")
.REGEXREPLACE("^；","")
.REGEXREPLACE("
；","")

.REGEXREPLACE("[,，；;+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
💻💚计算单价合并（进阶）
CONCATENATE([💻💚计算单价合并（初步）],"
",[💻💚终极单价]
)

.REGEXREPLACE("(\..*?)0+(/)","$1$2")
.REGEXREPLACE("\.\/","/")

.REGEXREPLACE("[;；,，+➕～\\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
💻💚计算单价合并（高级）
CONCATENATE(
IF(ISBLANK(
[💻💚初级单价]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(1)

),
[💻💚计算单价合并（进阶）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(1)
,
[💻💚初级单价]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(1)
)
,"；",

IF(ISBLANK(
[💻💚初级单价]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(2)

),
[💻💚计算单价合并（进阶）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(2)
,
[💻💚初级单价]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(2)
)

,"；",

IF(ISBLANK(
[💻💚初级单价]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(3)

),
[💻💚计算单价合并（进阶）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(3)
,
[💻💚初级单价]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(3)
)

,"；",

IF(ISBLANK(
[💻💚初级单价]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(4)

),
[💻💚计算单价合并（进阶）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(4)
,
[💻💚初级单价]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(4)
)


,"；",

IF(ISBLANK(
[💻💚初级单价]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(5)

),
[💻💚计算单价合并（进阶）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(5)
,
[💻💚初级单价]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(5)
)



,"；",

IF(ISBLANK(
[💻💚初级单价]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(6)

),
[💻💚计算单价合并（进阶）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(6)
,
[💻💚初级单价]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(6)
)




，CHAR(10)，




IF(ISBLANK(
[💻💚初级单价]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(1)
),
[💻💚计算单价合并（进阶）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(1)
,
[💻💚初级单价]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(1)
)
,"；",
IF(ISBLANK(
[💻💚初级单价]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(2)
),
[💻💚计算单价合并（进阶）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(2)
,
[💻💚初级单价]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(2)
)
,"；",
IF(ISBLANK(
[💻💚初级单价]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(3)
),
[💻💚计算单价合并（进阶）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(3)
,
[💻💚初级单价]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(3)
)
,"；",
IF(ISBLANK(
[💻💚初级单价]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(4)
),
[💻💚计算单价合并（进阶）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(4)
,
[💻💚初级单价]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(4)
)
,"；",
IF(ISBLANK(
[💻💚初级单价]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(5)
),
[💻💚计算单价合并（进阶）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(5)
,
[💻💚初级单价]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(5)
)
,"；",
IF(ISBLANK(
[💻💚初级单价]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(6)
),
[💻💚计算单价合并（进阶）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(6)
,
[💻💚初级单价]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(6)
)


,"
",

IF(ISBLANK(
[💻💚初级单价]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(1)
),
[💻💚计算单价合并（进阶）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(1)
,
[💻💚初级单价]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(1)
)
,"；",
IF(ISBLANK(
[💻💚初级单价]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(2)
),
[💻💚计算单价合并（进阶）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(2)
,
[💻💚初级单价]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(2)
)
,"；",
IF(ISBLANK(
[💻💚初级单价]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(3)
),
[💻💚计算单价合并（进阶）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(3)
,
[💻💚初级单价]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(3)
)
,"；",
IF(ISBLANK(
[💻💚初级单价]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(4)
),
[💻💚计算单价合并（进阶）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(4)
,
[💻💚初级单价]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(4)
)
,"；",
IF(ISBLANK(
[💻💚初级单价]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(5)
),
[💻💚计算单价合并（进阶）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(5)
,
[💻💚初级单价]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(5)
)
,"；",
IF(ISBLANK(
[💻💚初级单价]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(6)
),
[💻💚计算单价合并（进阶）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(6)
,
[💻💚初级单价]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(6)
)





，CHAR(10)，




IF(ISBLANK(
[💻💚初级单价]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(1)
),
[💻💚计算单价合并（进阶）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(1)
,
[💻💚初级单价]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(1)
)
,"；",
IF(ISBLANK(
[💻💚初级单价]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(2)
),
[💻💚计算单价合并（进阶）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(2)
,
[💻💚初级单价]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(2)
)
,"；",
IF(ISBLANK(
[💻💚初级单价]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(3)
),
[💻💚计算单价合并（进阶）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(3)
,
[💻💚初级单价]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(3)
)
,"；",
IF(ISBLANK(
[💻💚初级单价]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(4)
),
[💻💚计算单价合并（进阶）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(4)
,
[💻💚初级单价]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(4)
)
,"；",
IF(ISBLANK(
[💻💚初级单价]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(5)
),
[💻💚计算单价合并（进阶）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(5)
,
[💻💚初级单价]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(5)
)
,"；",
IF(ISBLANK(
[💻💚初级单价]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(6)
),
[💻💚计算单价合并（进阶）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(6)
,
[💻💚初级单价]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(6)
)



，CHAR(10)，




IF(ISBLANK(
[💻💚初级单价]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(1)
),
[💻💚计算单价合并（进阶）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(1)
,
[💻💚初级单价]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(1)
)
,"；",
IF(ISBLANK(
[💻💚初级单价]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(2)
),
[💻💚计算单价合并（进阶）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(2)
,
[💻💚初级单价]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(2)
)
,"；",
IF(ISBLANK(
[💻💚初级单价]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(3)
),
[💻💚计算单价合并（进阶）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(3)
,
[💻💚初级单价]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(3)
)
,"；",
IF(ISBLANK(
[💻💚初级单价]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(4)
),
[💻💚计算单价合并（进阶）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(4)
,
[💻💚初级单价]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(4)
)
,"；",
IF(ISBLANK(
[💻💚初级单价]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(5)
),
[💻💚计算单价合并（进阶）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(5)
,
[💻💚初级单价]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(5)
)
,"；",
IF(ISBLANK(
[💻💚初级单价]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(6)
),
[💻💚计算单价合并（进阶）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(6)
,
[💻💚初级单价]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(6)
)


，CHAR(10)，

IF(ISBLANK(
[💻💚初级单价]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(1)
),
[💻💚计算单价合并（进阶）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(1)
,
[💻💚初级单价]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(1)
)
,"；",
IF(ISBLANK(
[💻💚初级单价]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(2)
),
[💻💚计算单价合并（进阶）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(2)
,
[💻💚初级单价]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(2)
)
,"；",
IF(ISBLANK(
[💻💚初级单价]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(3)
),
[💻💚计算单价合并（进阶）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(3)
,
[💻💚初级单价]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(3)
)
,"；",
IF(ISBLANK(
[💻💚初级单价]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(4)
),
[💻💚计算单价合并（进阶）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(4)
,
[💻💚初级单价]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(4)
)
,"；",
IF(ISBLANK(
[💻💚初级单价]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(5)
),
[💻💚计算单价合并（进阶）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(5)
,
[💻💚初级单价]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(5)
)
,"；",
IF(ISBLANK(
[💻💚初级单价]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(6)
),
[💻💚计算单价合并（进阶）]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(6)
,
[💻💚初级单价]
.REGEXEXTRACTALL("❻.*?❻🔞").NTH(6)
)



,CHAR(10),



IF(ISBLANK(
[💻💚初级单价]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(1)
),
[💻💚计算单价合并（进阶）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(1)
,
[💻💚初级单价]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(1)
)
,"；",
IF(ISBLANK(
[💻💚初级单价]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(2)
),
[💻💚计算单价合并（进阶）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(2)
,
[💻💚初级单价]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(2)
)
,"；",
IF(ISBLANK(
[💻💚初级单价]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(3)
),
[💻💚计算单价合并（进阶）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(3)
,
[💻💚初级单价]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(3)
)
,"；",
IF(ISBLANK(
[💻💚初级单价]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(4)
),
[💻💚计算单价合并（进阶）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(4)
,
[💻💚初级单价]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(4)
)
,"；",
IF(ISBLANK(
[💻💚初级单价]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(5)
),
[💻💚计算单价合并（进阶）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(5)
,
[💻💚初级单价]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(5)
)
,"；",
IF(ISBLANK(
[💻💚初级单价]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(6)
),
[💻💚计算单价合并（进阶）]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(6)
,
[💻💚初级单价]
.REGEXEXTRACTALL("❼.*?❼🔞").NTH(6)
)


,CHAR(10),


IF(ISBLANK(
[💻💚初级单价]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(1)
),
[💻💚计算单价合并（进阶）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(1)
,
[💻💚初级单价]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(1)
)
,"；",
IF(ISBLANK(
[💻💚初级单价]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(2)
),
[💻💚计算单价合并（进阶）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(2)
,
[💻💚初级单价]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(2)
)
,"；",
IF(ISBLANK(
[💻💚初级单价]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(3)
),
[💻💚计算单价合并（进阶）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(3)
,
[💻💚初级单价]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(3)
)
,"；",
IF(ISBLANK(
[💻💚初级单价]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(4)
),
[💻💚计算单价合并（进阶）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(4)
,
[💻💚初级单价]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(4)
)
,"；",
IF(ISBLANK(
[💻💚初级单价]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(5)
),
[💻💚计算单价合并（进阶）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(5)
,
[💻💚初级单价]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(5)
)
,"；",
IF(ISBLANK(
[💻💚初级单价]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(6)
),
[💻💚计算单价合并（进阶）]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(6)
,
[💻💚初级单价]
.REGEXEXTRACTALL("❽.*?❽🔞").NTH(6)
)



,CHAR(10),



IF(ISBLANK(
[💻💚初级单价]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(1)
),
[💻💚计算单价合并（进阶）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(1)
,
[💻💚初级单价]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(1)
)
,"；",
IF(ISBLANK(
[💻💚初级单价]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(2)
),
[💻💚计算单价合并（进阶）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(2)
,
[💻💚初级单价]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(2)
)
,"；",
IF(ISBLANK(
[💻💚初级单价]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(3)
),
[💻💚计算单价合并（进阶）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(3)
,
[💻💚初级单价]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(3)
)
,"；",
IF(ISBLANK(
[💻💚初级单价]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(4)
),
[💻💚计算单价合并（进阶）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(4)
,
[💻💚初级单价]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(4)
)
,"；",
IF(ISBLANK(
[💻💚初级单价]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(5)
),
[💻💚计算单价合并（进阶）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(5)
,
[💻💚初级单价]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(5)
)
,"；",
IF(ISBLANK(
[💻💚初级单价]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(6)
),
[💻💚计算单价合并（进阶）]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(6)
,
[💻💚初级单价]
.REGEXEXTRACTALL("❾.*?❾🔞").NTH(6)
)




)

.REGEXREPLACE("；+","；")
.REGEXREPLACE("^；","")
.REGEXREPLACE("
；","")

.REGEXREPLACE("[,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
💻💚计算单价（终版）
CONCATENATE(
IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACTALL("❶.*❶🔞").REGEXMATCH("猫砂|主粮")
，
[💻💚基础单价].REGEXEXTRACTALL("❶.*❶🔞")


,
[💻💚计算单价合并（高级）].REGEXEXTRACTALL("❶.*❶🔞"))



，CHAR(10)，



IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACTALL("❷.*❷🔞").REGEXMATCH("猫砂|主粮")
，
[💻💚基础单价].REGEXEXTRACTALL("❷.*❷🔞")

，
[💻💚计算单价合并（高级）].REGEXEXTRACTALL("❷.*❷🔞"))



，CHAR(10)，




IF(
OR(ISBLANK(
[💻💚计算单价合并（高级）].REGEXEXTRACTALL("❸.*❸🔞"))，
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACTALL("❸.*❸🔞").REGEXMATCH("猫砂|主粮"))
，
[💻💚基础单价].REGEXEXTRACTALL("❸.*❸🔞")
，
[💻💚计算单价合并（高级）].REGEXEXTRACTALL("❸.*❸🔞"))



，CHAR(10)，





IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACTALL("❹.*❹🔞").REGEXMATCH("猫砂|主粮")
，
[💻💚基础单价].REGEXEXTRACTALL("❹.*❹🔞")

，
[💻💚计算单价合并（高级）].REGEXEXTRACTALL("❹.*❹🔞"))



，CHAR(10)，




IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACTALL("❺.*❺🔞").REGEXMATCH("猫砂|主粮")
，[💻💚基础单价].REGEXEXTRACTALL("❺.*❺🔞")
，
[💻💚计算单价合并（高级）].REGEXEXTRACTALL("❺.*❺🔞"))



，CHAR(10)，




IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACTALL("❻.*❻🔞").REGEXMATCH("猫砂|主粮")
，
[💻💚基础单价].REGEXEXTRACTALL("❻.*❻🔞")

，
[💻💚计算单价合并（高级）].REGEXEXTRACTALL("❻.*❻🔞"))


,CHAR(10),




IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACTALL("❼.*❼🔞").REGEXMATCH("猫砂|主粮")
,
[💻💚基础单价].REGEXEXTRACTALL("❼.*❼🔞")

，[💻💚计算单价合并（高级）].REGEXEXTRACTALL("❼.*❼🔞"))





,CHAR(10),





IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACTALL("❽.*❽🔞").REGEXMATCH("猫砂|主粮")
,
[💻💚基础单价].REGEXEXTRACTALL("❽.*❽🔞")

，

[💻💚计算单价合并（高级）].REGEXEXTRACTALL("❽.*❽🔞"))


,CHAR(10),




IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACTALL("❾.*❾🔞").REGEXMATCH("猫砂|主粮")
，
[💻💚基础单价].REGEXEXTRACTALL("❾.*❾🔞")

，
[💻💚计算单价合并（高级）].REGEXEXTRACTALL("❾.*❾🔞"))



）

.REGEXREPLACE("折?💰","¥")
.REGEXREPLACE("[❶-❾]🔞","Ⓜ️$0")
.REGEXREPLACE("(Ⓜ️\s*)+","Ⓜ️")
.REGEXREPLACE("[❶-❾].*?c?mⓂ️[❶-❾]🔞","")

.REGEXREPLACE("([❶-❾]🔞).*?([❶-❾])；?","$1"&CHAR(10)&"$2")
.REGEXREPLACE("[,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
💻💚计算单价（低价提取）
CONCATENATE(
IF(
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(1)
.ISBLANK()
,""
,
MIN(
IF(
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(1)
.ISBLANK()
,
"999999"
,
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(1)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(2)
.ISBLANK()
,
"999999"
,
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(2)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(3)
.ISBLANK()
,
"999999"
,
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(3)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(4)
.ISBLANK()
,
"999999"
,
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(4)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(5)
.ISBLANK()
,
"999999"
,
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(5)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
)
.REGEXREPLACE("\d+(?:\.\d+)?",[💻💚计算单价（终版）].REGEXEXTRACT("[❶].*?¥")&"$0"&[💻💚计算单价（终版）].REGEXEXTRACT("\/.*?[❶]🔞"))


)


,CHAR(10),
IF(
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(1)
.ISBLANK()
,""
,
MIN(
IF(
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(1)
.ISBLANK()
,
"999999"
,
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(1)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(2)
.ISBLANK()
,
"999999"
,
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(2)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(3)
.ISBLANK()
,
"999999"
,
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(3)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(4)
.ISBLANK()
,
"999999"
,
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(4)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(5)
.ISBLANK()
,
"999999"
,
[💻💚计算单价（终版）]
.REGEXEXTRACTALL(".*?❷🔞")
.NTH(5)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
)
.REGEXREPLACE("\d+(?:\.\d+)?",[💻💚计算单价（终版）].REGEXEXTRACT("[❷].*?¥")&"$0"&[💻💚计算单价（终版）].REGEXEXTRACT("\/.*?[❷]🔞"))

)
,CHAR(10),




IF(
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(1)
.ISBLANK()
,
""
,
MIN(
IF(
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(1)
.ISBLANK()
,
"999999"
,
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(1)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(2)
.ISBLANK()
,
"999999"
,
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(2)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(3)
.ISBLANK()
,
"999999"
,
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(3)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(4)
.ISBLANK()
,
"999999"
,
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(4)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(5)
.ISBLANK()
,
"999999"
,
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(5)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
)
.REGEXREPLACE("\d+(?:\.\d+)?",[💻💚计算单价（终版）].REGEXEXTRACT("[❸].*?¥")&"$0"&[💻💚计算单价（终版）].REGEXEXTRACT("\/.*?[❸]🔞"))





)
,CHAR(10),


IF(
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(1)
.ISBLANK()
,
""
,
MIN(
IF(
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(1)
.ISBLANK()
,
"999999"
,
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(1)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(2)
.ISBLANK()
,
"999999"
,
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(2)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(3)
.ISBLANK()
,
"999999"
,
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(3)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(4)
.ISBLANK()
,
"999999"
,
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(4)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(5)
.ISBLANK()
,
"999999"
,
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(5)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
)
.REGEXREPLACE("\d+(?:\.\d+)?",[💻💚计算单价（终版）].REGEXEXTRACT("[❹].*?¥")&"$0"&[💻💚计算单价（终版）].REGEXEXTRACT("\/.*?[❹]🔞"))

)


,CHAR(10),


IF(
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(1)
.ISBLANK()
,
""
,
MIN(
IF(
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(1)
.ISBLANK()
,
"999999"
,
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(1)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(2)
.ISBLANK()
,
"999999"
,
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(2)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(3)
.ISBLANK()
,
"999999"
,
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(3)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(4)
.ISBLANK()
,
"999999"
,
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(4)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(5)
.ISBLANK()
,
"999999"
,
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(5)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
)
.REGEXREPLACE("\d+(?:\.\d+)?",[💻💚计算单价（终版）].REGEXEXTRACT("[❺].*?¥")&"$0"&[💻💚计算单价（终版）].REGEXEXTRACT("\/.*?[❺]🔞"))

)


,CHAR(10),
IF(
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(1)
.ISBLANK()
,
""
,
MIN(
IF(
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(1)
.ISBLANK()
,
"999999"
,
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(1)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(2)
.ISBLANK()
,
"999999"
,
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(2)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(3)
.ISBLANK()
,
"999999"
,
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(3)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(4)
.ISBLANK()
,
"999999"
,
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(4)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(5)
.ISBLANK()
,
"999999"
,
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(5)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
)
.REGEXREPLACE("\d+(?:\.\d+)?",[💻💚计算单价（终版）].REGEXEXTRACT("[❻].*?¥")&"$0"&[💻💚计算单价（终版）].REGEXEXTRACT("\/.*?[❻]🔞"))
)
,CHAR(10),
IF(
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(1)
.ISBLANK()
,
""
,
MIN(
IF(
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(1)
.ISBLANK()
,
"999999"
,
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(1)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(2)
.ISBLANK()
,
"999999"
,
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(2)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(3)
.ISBLANK()
,
"999999"
,
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(3)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(4)
.ISBLANK()
,
"999999"
,
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(4)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
)
.REGEXREPLACE("\d+(?:\.\d+)?",[💻💚计算单价（终版）].REGEXEXTRACT("[❼].*?¥")&"$0"&[💻💚计算单价（终版）].REGEXEXTRACT("\/.*?[❼]🔞"))
)


,CHAR(10),
IF(
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(1)
.ISBLANK()
,
""
,
MIN(
IF(
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(1)
.ISBLANK()
,
"999999"
,
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(1)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(2)
.ISBLANK()
,
"999999"
,
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(2)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(3)
.ISBLANK()
,
"999999"
,
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(3)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(4)
.ISBLANK()
,
"999999"
,
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(4)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
)
.REGEXREPLACE("\d+(?:\.\d+)?",[💻💚计算单价（终版）].REGEXEXTRACT("[❽].*?¥")&"$0"&[💻💚计算单价（终版）].REGEXEXTRACT("\/.*?[❽]🔞"))


)
,CHAR(10),


IF(
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(1)
.ISBLANK()
,
""
,
MIN(
IF(
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(1)
.ISBLANK()
,
"999999"
,
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(1)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(2)
.ISBLANK()
,
"999999"
,
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(2)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(3)
.ISBLANK()
,
"999999"
,
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(3)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(4)
.ISBLANK()
,
"999999"
,
[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(4)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
)
.REGEXREPLACE("\d+(?:\.\d+)?",[💻💚计算单价（终版）].REGEXEXTRACT("[❾].*?¥")&"$0"&[💻💚计算单价（终版）].REGEXEXTRACT("\/.*?[❾]🔞"))

)
)


.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
💻💜文本单价（终版）
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[💻编号（最终）]).[🔪💜折算价格提取].LISTCOMBINE()

.REGEXEXTRACTALL("(.*?/\s*(?:\d+(?:\.\d+)?)?(?:"&[🧠单位合集]&").*)")
.ARRAYJOIN(CHAR(10))

.REGEXREPLACE("\s*💰\s*","¥")
.REGEXREPLACE("(.*?)¥(\d+(?:\.\d+)?[,，]折¥.*)","$1💰$2")
.REGEXREPLACE("([❶-❾]🔞).*?([❶-❾])；?","$1"&CHAR(10)&"$2")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
💻💜文本单价（低价提取）
CONCATENATE(
IF(
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(1)
.ISBLANK()
,""
,
MIN(
IF(
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(1)
.ISBLANK()
,
"999999"
,
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(1)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(2)
.ISBLANK()
,
"999999"
,
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(2)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(3)
.ISBLANK()
,
"999999"
,
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(3)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(4)
.ISBLANK()
,
"999999"
,
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(4)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(5)
.ISBLANK()
,
"999999"
,
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(5)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
)
.REGEXREPLACE("\d+(?:\.\d+)?",[💻💜文本单价（终版）].REGEXEXTRACT("[❶].*?¥")&"$0"&[💻💜文本单价（终版）].REGEXEXTRACT("\/.*?[❶]🔞"))


)


,CHAR(10),
IF(
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(1)
.ISBLANK()
,""
,
MIN(
IF(
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(1)
.ISBLANK()
,
"999999"
,
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(1)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(2)
.ISBLANK()
,
"999999"
,
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(2)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(3)
.ISBLANK()
,
"999999"
,
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(3)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(4)
.ISBLANK()
,
"999999"
,
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(4)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(5)
.ISBLANK()
,
"999999"
,
[💻💜文本单价（终版）]
.REGEXEXTRACTALL(".*?❷🔞")
.NTH(5)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
)
.REGEXREPLACE("\d+(?:\.\d+)?",[💻💜文本单价（终版）].REGEXEXTRACT("[❷].*?¥")&"$0"&[💻💜文本单价（终版）].REGEXEXTRACT("\/.*?[❷]🔞"))

)
,CHAR(10),




IF(
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(1)
.ISBLANK()
,
""
,
MIN(
IF(
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(1)
.ISBLANK()
,
"999999"
,
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(1)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(2)
.ISBLANK()
,
"999999"
,
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(2)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(3)
.ISBLANK()
,
"999999"
,
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(3)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(4)
.ISBLANK()
,
"999999"
,
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(4)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(5)
.ISBLANK()
,
"999999"
,
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(5)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
)
.REGEXREPLACE("\d+(?:\.\d+)?",[💻💜文本单价（终版）].REGEXEXTRACT("[❸].*?¥")&"$0"&[💻💜文本单价（终版）].REGEXEXTRACT("\/.*?[❸]🔞"))





)
,CHAR(10),


IF(
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(1)
.ISBLANK()
,
""
,
MIN(
IF(
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(1)
.ISBLANK()
,
"999999"
,
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(1)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(2)
.ISBLANK()
,
"999999"
,
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(2)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(3)
.ISBLANK()
,
"999999"
,
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(3)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(4)
.ISBLANK()
,
"999999"
,
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(4)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(5)
.ISBLANK()
,
"999999"
,
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(5)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
)
.REGEXREPLACE("\d+(?:\.\d+)?",[💻💜文本单价（终版）].REGEXEXTRACT("[❹].*?¥")&"$0"&[💻💜文本单价（终版）].REGEXEXTRACT("\/.*?[❹]🔞"))

)


,CHAR(10),


IF(
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(1)
.ISBLANK()
,
""
,
MIN(
IF(
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(1)
.ISBLANK()
,
"999999"
,
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(1)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(2)
.ISBLANK()
,
"999999"
,
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(2)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(3)
.ISBLANK()
,
"999999"
,
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(3)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(4)
.ISBLANK()
,
"999999"
,
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(4)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(5)
.ISBLANK()
,
"999999"
,
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(5)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
)
.REGEXREPLACE("\d+(?:\.\d+)?",[💻💜文本单价（终版）].REGEXEXTRACT("[❺].*?¥")&"$0"&[💻💜文本单价（终版）].REGEXEXTRACT("\/.*?[❺]🔞"))

)


,CHAR(10),
IF(
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(1)
.ISBLANK()
,
""
,
MIN(
IF(
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(1)
.ISBLANK()
,
"999999"
,
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(1)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(2)
.ISBLANK()
,
"999999"
,
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(2)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(3)
.ISBLANK()
,
"999999"
,
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(3)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(4)
.ISBLANK()
,
"999999"
,
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(4)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(5)
.ISBLANK()
,
"999999"
,
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(5)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
)
.REGEXREPLACE("\d+(?:\.\d+)?",[💻💜文本单价（终版）].REGEXEXTRACT("[❻].*?¥")&"$0"&[💻💜文本单价（终版）].REGEXEXTRACT("\/.*?[❻]🔞"))
)
,CHAR(10),
IF(
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(1)
.ISBLANK()
,
""
,
MIN(
IF(
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(1)
.ISBLANK()
,
"999999"
,
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(1)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(2)
.ISBLANK()
,
"999999"
,
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(2)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(3)
.ISBLANK()
,
"999999"
,
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(3)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(4)
.ISBLANK()
,
"999999"
,
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(4)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
)
.REGEXREPLACE("\d+(?:\.\d+)?",[💻💜文本单价（终版）].REGEXEXTRACT("[❼].*?¥")&"$0"&[💻💜文本单价（终版）].REGEXEXTRACT("\/.*?[❼]🔞"))
)


,CHAR(10),
IF(
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(1)
.ISBLANK()
,
""
,
MIN(
IF(
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(1)
.ISBLANK()
,
"999999"
,
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(1)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(2)
.ISBLANK()
,
"999999"
,
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(2)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(3)
.ISBLANK()
,
"999999"
,
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(3)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(4)
.ISBLANK()
,
"999999"
,
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(4)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
)
.REGEXREPLACE("\d+(?:\.\d+)?",[💻💜文本单价（终版）].REGEXEXTRACT("[❽].*?¥")&"$0"&[💻💜文本单价（终版）].REGEXEXTRACT("\/.*?[❽]🔞"))


)
,CHAR(10),


IF(
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(1)
.ISBLANK()
,
""
,
MIN(
IF(
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(1)
.ISBLANK()
,
"999999"
,
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(1)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(2)
.ISBLANK()
,
"999999"
,
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(2)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(3)
.ISBLANK()
,
"999999"
,
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(3)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
,
IF(
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(4)
.ISBLANK()
,
"999999"
,
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(4)
.REGEXEXTRACTALL("¥(\d+(?:\.\d+)?)")
.VALUE())
)
.REGEXREPLACE("\d+(?:\.\d+)?",[💻💜文本单价（终版）].REGEXEXTRACT("[❾].*?¥")&"$0"&[💻💜文本单价（终版）].REGEXEXTRACT("\/.*?[❾]🔞"))

)
)


.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
💻💚文本&计算单价合并（终版）
CONCATENATE(

IF(
[🛍️🤍品类-二级分类（原始值）-优化版]
.REGEXEXTRACTALL("❶.*❶🔞")
.REGEXMATCH("猫砂|主粮")

,[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❶.*❶🔞")

,
IF(
OR(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACTALL("❶.*?🔞").ISBLANK()，
[💻💚数量（最终）-【初级数量】颗粒度].REGEXEXTRACTALL("❶(.*?)❶🔞").ISBLANK(),
[💻💚数量（最终）-【初级数量】颗粒度].REGEXEXTRACTALL("❶(.*?)❶🔞").UNIQUE().VALUE()=1,[💻💚数量（最终）-【初级数量】颗粒度].REGEXEXTRACTALL("❶(.*?)❶🔞").UNIQUE().VALUE()=0)

,[💻💜文本单价（终版）].REGEXEXTRACTALL("❶.*❶🔞")

,
[💻💚计算单价（终版）].REGEXEXTRACTALL("❶.*❶🔞")))



，CHAR(10)，


IF(
[🛍️🤍品类-二级分类（原始值）-优化版]
.REGEXEXTRACTALL("❷.*❷🔞")
.REGEXMATCH("猫砂|主粮")

,[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❷.*❷🔞")
,
IF(
OR(
ISBLANK([🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACTALL("❷.*?🔞"))，
ISBLANK([💻💚数量（最终）-【初级数量】颗粒度].REGEXEXTRACTALL("❷(.*?)❷🔞")),
[💻💚数量（最终）-【初级数量】颗粒度].REGEXEXTRACTALL("❷(.*?)❷🔞").UNIQUE().VALUE()=1,[💻💚数量（最终）-【初级数量】颗粒度].REGEXEXTRACTALL("❷(.*?)❷🔞").UNIQUE().VALUE()=0)

,[💻💜文本单价（终版）].REGEXEXTRACTALL("❷.*❷🔞")
，
[💻💚计算单价（终版）].REGEXEXTRACTALL("❷.*❷🔞")))



，CHAR(10)，


IF(
[🛍️🤍品类-二级分类（原始值）-优化版]
.REGEXEXTRACTALL("❸.*❸🔞")
.REGEXMATCH("猫砂|主粮")

,[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❸.*❸🔞")
,

IF(
OR(
ISBLANK([🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACTALL("❸.*?🔞"))，
ISBLANK([💻💚数量（最终）-【初级数量】颗粒度].REGEXEXTRACTALL("❸(.*?)❸🔞")),
[💻💚数量（最终）-【初级数量】颗粒度].REGEXEXTRACTALL("❸(.*?)❸🔞").UNIQUE().VALUE()=1,[💻💚数量（最终）-【初级数量】颗粒度].REGEXEXTRACTALL("❸(.*?)❸🔞").UNIQUE().VALUE()=0)

,[💻💜文本单价（终版）].REGEXEXTRACTALL("❸.*❸🔞")
，
[💻💚计算单价（终版）].REGEXEXTRACTALL("❸.*❸🔞")))



，CHAR(10)，


IF(
[🛍️🤍品类-二级分类（原始值）-优化版]
.REGEXEXTRACTALL("❹.*❹🔞")
.REGEXMATCH("猫砂|主粮")

,[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❹.*❹🔞")
,

IF(
OR(
ISBLANK([🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACTALL("❹.*?🔞"))，
ISBLANK([💻💚数量（最终）-【初级数量】颗粒度].REGEXEXTRACTALL("❹(.*?)❹🔞")),
[💻💚数量（最终）-【初级数量】颗粒度].REGEXEXTRACTALL("❹(.*?)❹🔞").UNIQUE().VALUE()=1,[💻💚数量（最终）-【初级数量】颗粒度].REGEXEXTRACTALL("❹(.*?)❹🔞").UNIQUE().VALUE()=0)

,[💻💜文本单价（终版）].REGEXEXTRACTALL("❹.*❹🔞")

，
[💻💚计算单价（终版）].REGEXEXTRACTALL("❹.*❹🔞")))



，CHAR(10)，



IF(
[🛍️🤍品类-二级分类（原始值）-优化版]
.REGEXEXTRACTALL("❺.*❺🔞")
.REGEXMATCH("猫砂|主粮")

,[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❺.*❺🔞")
,

IF(
OR(
ISBLANK([🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACTALL("❺.*?🔞"))，
ISBLANK([💻💚数量（最终）-【初级数量】颗粒度].REGEXEXTRACTALL("❺(.*?)❺🔞")),
[💻💚数量（最终）-【初级数量】颗粒度].REGEXEXTRACTALL("❺(.*?)❺🔞").UNIQUE().VALUE()=1,[💻💚数量（最终）-【初级数量】颗粒度].REGEXEXTRACTALL("❺(.*?)❺🔞").UNIQUE().VALUE()=0)
,
[💻💜文本单价（终版）].REGEXEXTRACTALL("❺.*❺🔞")
，
[💻💚计算单价（终版）].REGEXEXTRACTALL("❺.*❺🔞")))



，CHAR(10)，




IF(
[🛍️🤍品类-二级分类（原始值）-优化版]
.REGEXEXTRACTALL("❻.*❻🔞")
.REGEXMATCH("猫砂|主粮")

,[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❻.*❻🔞")
,


IF(
OR(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACTALL("❻.*?❻🔞").ISBLANK()，
[💻💚数量（最终）-【初级数量】颗粒度].REGEXEXTRACTALL("❻(.*?)❻🔞").ISBLANK(),
[💻💚数量（最终）-【初级数量】颗粒度].REGEXEXTRACTALL("❻(.*?)❻🔞").UNIQUE().VALUE()=1,[💻💚数量（最终）-【初级数量】颗粒度].REGEXEXTRACTALL("❻(.*?)❻🔞").UNIQUE().VALUE()=0)

，[💻💜文本单价（终版）].REGEXEXTRACTALL("❻.*❻🔞")
，
[💻💚计算单价（终版）].REGEXEXTRACTALL("❻.*❻🔞")))






,CHAR(10),


IF(
[🛍️🤍品类-二级分类（原始值）-优化版]
.REGEXEXTRACTALL("❼.*❼🔞")
.REGEXMATCH("猫砂|主粮")

,[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❼.*❼🔞")
,

IF(
OR(
ISBLANK([🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACTALL("❼.*?🔞")),
ISBLANK([💻💚数量（最终）-【初级数量】颗粒度].REGEXEXTRACTALL("❼(.*?)❼🔞")),
[💻💚数量（最终）-【初级数量】颗粒度].REGEXEXTRACTALL("❼(.*?)❼🔞").UNIQUE().VALUE()=1,
[💻💚数量（最终）-【初级数量】颗粒度].REGEXEXTRACTALL("❼(.*?)❼🔞").UNIQUE().VALUE()=0)

，[💻💜文本单价（终版）].REGEXEXTRACTALL("❼.*❼🔞")

，[💻💚计算单价（终版）].REGEXEXTRACTALL("❼.*❼🔞")))





,CHAR(10),

IF(
[🛍️🤍品类-二级分类（原始值）-优化版]
.REGEXEXTRACTALL("❽.*❽🔞")
.REGEXMATCH("猫砂|主粮")

,[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❽.*❽🔞")
,


IF(
OR(
ISBLANK([🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACTALL("❽.*?🔞")),
ISBLANK([💻💚数量（最终）-【初级数量】颗粒度].REGEXEXTRACTALL("❽(.*?)❽🔞")),
[💻💚数量（最终）-【初级数量】颗粒度].REGEXEXTRACTALL("❽(.*?)❽🔞").UNIQUE().VALUE()=1,
[💻💚数量（最终）-【初级数量】颗粒度].REGEXEXTRACTALL("❽(.*?)❽🔞").UNIQUE().VALUE()=0)

，
[💻💜文本单价（终版）].REGEXEXTRACTALL("❽.*❽🔞")
，

[💻💚计算单价（终版）].REGEXEXTRACTALL("❽.*❽🔞")))




,CHAR(10),



IF(
[🛍️🤍品类-二级分类（原始值）-优化版]
.REGEXEXTRACTALL("❾.*❾🔞")
.REGEXMATCH("猫砂|主粮")

,[💻💚计算单价（终版）]
.REGEXEXTRACTALL("❾.*❾🔞")
,

IF(
OR(
ISBLANK([🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACTALL("❾.*?🔞")),
ISBLANK([💻💚数量（最终）-【初级数量】颗粒度].REGEXEXTRACTALL("❾(.*?)❾🔞")),
[💻💚数量（最终）-【初级数量】颗粒度].REGEXEXTRACTALL("❾(.*?)❾🔞").UNIQUE().VALUE()=1,
[💻💚数量（最终）-【初级数量】颗粒度].REGEXEXTRACTALL("❾(.*?)❾🔞").UNIQUE().VALUE()=0)

，
[💻💜文本单价（终版）].REGEXEXTRACTALL("❾.*❾🔞")
，
[💻💚计算单价（终版）].REGEXEXTRACTALL("❾.*❾🔞")))



）
.REGEXREPLACE("Ⓜ️","")
.REGEXREPLACE("(折)?💰","¥")
.REGEXREPLACE("[❶-❾].*?c?m[❶-❾]🔞","")

.REGEXREPLACE("([❶-❾]🔞).*?([❶-❾])；?","$1"&CHAR(10)&"$2")
.REGEXREPLACE("[,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====

💻💚单价（信息重组专用版）
CONCATENATE(

IF(
[🛍️🤍品类-二级分类（原始值）-优化版]
.REGEXEXTRACTALL("❶.*❶🔞")
.REGEXMATCH("猫砂|主粮")

,[💻💚计算单价（低价提取）]
.REGEXEXTRACTALL("❶.*❶🔞")
,


IF(
OR(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACTALL("❶.*?🔞").ISBLANK()，
[💻💚数量（最终）-【初级数量】颗粒度].REGEXEXTRACTALL("❶(.*?)❶🔞").ISBLANK(),
[💻💚数量（最终）-【初级数量】颗粒度].REGEXEXTRACTALL("❶(.*?)❶🔞").UNIQUE().VALUE()=1,[💻💚数量（最终）-【初级数量】颗粒度].REGEXEXTRACTALL("❶(.*?)❶🔞").UNIQUE().VALUE()=0)

,[💻💜文本单价（低价提取）].REGEXEXTRACTALL("❶.*❶🔞")

,
[💻💚计算单价（低价提取）].REGEXEXTRACTALL("❶.*❶🔞")))



，CHAR(10)，



IF(
[🛍️🤍品类-二级分类（原始值）-优化版]
.REGEXEXTRACTALL("❷.*❷🔞")
.REGEXMATCH("猫砂|主粮")

,[💻💚计算单价（低价提取）]
.REGEXEXTRACTALL("❷.*❷🔞")
,


IF(
OR(
ISBLANK([🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACTALL("❷.*?🔞"))，
ISBLANK([💻💚数量（最终）-【初级数量】颗粒度].REGEXEXTRACTALL("❷(.*?)❷🔞")),
[💻💚数量（最终）-【初级数量】颗粒度].REGEXEXTRACTALL("❷(.*?)❷🔞").UNIQUE().VALUE()=1,[💻💚数量（最终）-【初级数量】颗粒度].REGEXEXTRACTALL("❷(.*?)❷🔞").UNIQUE().VALUE()=0)

,[💻💜文本单价（低价提取）].REGEXEXTRACTALL("❷.*❷🔞")
，
[💻💚计算单价（低价提取）].REGEXEXTRACTALL("❷.*❷🔞")))



，CHAR(10)，



IF(
[🛍️🤍品类-二级分类（原始值）-优化版]
.REGEXEXTRACTALL("❸.*❸🔞")
.REGEXMATCH("猫砂|主粮")

,[💻💚计算单价（低价提取）]
.REGEXEXTRACTALL("❸.*❸🔞")
,


IF(
OR(
ISBLANK([🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACTALL("❸.*?🔞"))，
ISBLANK([💻💚数量（最终）-【初级数量】颗粒度].REGEXEXTRACTALL("❸(.*?)❸🔞")),
[💻💚数量（最终）-【初级数量】颗粒度].REGEXEXTRACTALL("❸(.*?)❸🔞").UNIQUE().VALUE()=1,[💻💚数量（最终）-【初级数量】颗粒度].REGEXEXTRACTALL("❸(.*?)❸🔞").UNIQUE().VALUE()=0)

,[💻💜文本单价（低价提取）].REGEXEXTRACTALL("❸.*❸🔞")
，
[💻💚计算单价（低价提取）].REGEXEXTRACTALL("❸.*❸🔞")))



，CHAR(10)，




IF(
[🛍️🤍品类-二级分类（原始值）-优化版]
.REGEXEXTRACTALL("❹.*❹🔞")
.REGEXMATCH("猫砂|主粮")

,[💻💚计算单价（低价提取）]
.REGEXEXTRACTALL("❹.*❹🔞")
,


IF(
OR(
ISBLANK([🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACTALL("❹.*?🔞"))，
ISBLANK([💻💚数量（最终）-【初级数量】颗粒度].REGEXEXTRACTALL("❹(.*?)❹🔞")),
[💻💚数量（最终）-【初级数量】颗粒度].REGEXEXTRACTALL("❹(.*?)❹🔞").UNIQUE().VALUE()=1,[💻💚数量（最终）-【初级数量】颗粒度].REGEXEXTRACTALL("❹(.*?)❹🔞").UNIQUE().VALUE()=0)

,[💻💜文本单价（低价提取）].REGEXEXTRACTALL("❹.*❹🔞")

，
[💻💚计算单价（低价提取）].REGEXEXTRACTALL("❹.*❹🔞")))



，CHAR(10)，


IF(
[🛍️🤍品类-二级分类（原始值）-优化版]
.REGEXEXTRACTALL("❺.*❺🔞")
.REGEXMATCH("猫砂|主粮")

,[💻💚计算单价（低价提取）]
.REGEXEXTRACTALL("❺.*❺🔞")
,



IF(
OR(
ISBLANK([🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACTALL("❺.*?🔞"))，
ISBLANK([💻💚数量（最终）-【初级数量】颗粒度].REGEXEXTRACTALL("❺(.*?)❺🔞")),
[💻💚数量（最终）-【初级数量】颗粒度].REGEXEXTRACTALL("❺(.*?)❺🔞").UNIQUE().VALUE()=1,[💻💚数量（最终）-【初级数量】颗粒度].REGEXEXTRACTALL("❺(.*?)❺🔞").UNIQUE().VALUE()=0)
,
[💻💜文本单价（低价提取）].REGEXEXTRACTALL("❺.*❺🔞")
，
[💻💚计算单价（低价提取）].REGEXEXTRACTALL("❺.*❺🔞")))



，CHAR(10)，



IF(
[🛍️🤍品类-二级分类（原始值）-优化版]
.REGEXEXTRACTALL("❻.*❻🔞")
.REGEXMATCH("猫砂|主粮")

,[💻💚计算单价（低价提取）]
.REGEXEXTRACTALL("❻.*❻🔞")
,


IF(
OR(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACTALL("❻.*?❻🔞").ISBLANK()，
[💻💚数量（最终）-【初级数量】颗粒度].REGEXEXTRACTALL("❻(.*?)❻🔞").ISBLANK(),
[💻💚数量（最终）-【初级数量】颗粒度].REGEXEXTRACTALL("❻(.*?)❻🔞").UNIQUE().VALUE()=1,[💻💚数量（最终）-【初级数量】颗粒度].REGEXEXTRACTALL("❻(.*?)❻🔞").UNIQUE().VALUE()=0)

，[💻💜文本单价（低价提取）].REGEXEXTRACTALL("❻.*❻🔞")
，
[💻💚计算单价（低价提取）].REGEXEXTRACTALL("❻.*❻🔞")))






,CHAR(10),



IF(
[🛍️🤍品类-二级分类（原始值）-优化版]
.REGEXEXTRACTALL("❼.*❼🔞")
.REGEXMATCH("猫砂|主粮")

,[💻💚计算单价（低价提取）]
.REGEXEXTRACTALL("❼.*❼🔞")
,


IF(
OR(
ISBLANK([🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACTALL("❼.*?🔞")),
ISBLANK([💻💚数量（最终）-【初级数量】颗粒度].REGEXEXTRACTALL("❼(.*?)❼🔞")),
[💻💚数量（最终）-【初级数量】颗粒度].REGEXEXTRACTALL("❼(.*?)❼🔞").UNIQUE().VALUE()=1,
[💻💚数量（最终）-【初级数量】颗粒度].REGEXEXTRACTALL("❼(.*?)❼🔞").UNIQUE().VALUE()=0)

，[💻💜文本单价（低价提取）].REGEXEXTRACTALL("❼.*❼🔞")

，[💻💚计算单价（低价提取）].REGEXEXTRACTALL("❼.*❼🔞")))





,CHAR(10),


IF(
[🛍️🤍品类-二级分类（原始值）-优化版]
.REGEXEXTRACTALL("❽.*❽🔞")
.REGEXMATCH("猫砂|主粮")

,[💻💚计算单价（低价提取）]
.REGEXEXTRACTALL("❽.*❽🔞")
,


IF(
OR(
ISBLANK([🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACTALL("❽.*?🔞")),
ISBLANK([💻💚数量（最终）-【初级数量】颗粒度].REGEXEXTRACTALL("❽(.*?)❽🔞")),
[💻💚数量（最终）-【初级数量】颗粒度].REGEXEXTRACTALL("❽(.*?)❽🔞").UNIQUE().VALUE()=1,
[💻💚数量（最终）-【初级数量】颗粒度].REGEXEXTRACTALL("❽(.*?)❽🔞").UNIQUE().VALUE()=0)

，
[💻💜文本单价（低价提取）].REGEXEXTRACTALL("❽.*❽🔞")
，

[💻💚计算单价（低价提取）].REGEXEXTRACTALL("❽.*❽🔞")))




,CHAR(10),


IF(
[🛍️🤍品类-二级分类（原始值）-优化版]
.REGEXEXTRACTALL("❾.*❾🔞")
.REGEXMATCH("猫砂|主粮")

,[💻💚计算单价（低价提取）]
.REGEXEXTRACTALL("❾.*❾🔞")
,

IF(
OR(
ISBLANK([🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACTALL("❾.*?🔞")),
ISBLANK([💻💚数量（最终）-【初级数量】颗粒度].REGEXEXTRACTALL("❾(.*?)❾🔞")),
[💻💚数量（最终）-【初级数量】颗粒度].REGEXEXTRACTALL("❾(.*?)❾🔞").UNIQUE().VALUE()=1,
[💻💚数量（最终）-【初级数量】颗粒度].REGEXEXTRACTALL("❾(.*?)❾🔞").UNIQUE().VALUE()=0)

，
[💻💜文本单价（低价提取）].REGEXEXTRACTALL("❾.*❾🔞")
，
[💻💚计算单价（低价提取）].REGEXEXTRACTALL("❾.*❾🔞")))



）

.REGEXREPLACE("Ⓜ️","")
.REGEXREPLACE("([❶-❾])(?:到手)?¥","$1低至¥")
.REGEXREPLACE("[❶-❾].*?c?m[❶-❾]🔞","")
.REGEXREPLACE("¥","💰")

.REGEXREPLACE("([❶-❾]🔞).*?([❶-❾])；?","$1"&CHAR(10)&"$2")
.REGEXREPLACE("[,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
💻🖤单价（字段维护专用）
CONCATENATE(
IF(
[💻💚单价（信息重组专用版）]
.REGEXMATCH("❶")
.NOT()
，
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❶.*❶🔞")

,
[💻💚单价（信息重组专用版）]
.REGEXEXTRACTALL("❶.*❶🔞"))


,CHAR(10),

IF(
[💻💚单价（信息重组专用版）]
.REGEXMATCH("❷")
.NOT()
,
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❷.*❷🔞")
,
[💻💚单价（信息重组专用版）]
.REGEXEXTRACTALL("❷.*❷🔞"))


,CHAR(10),

IF(
[💻💚单价（信息重组专用版）]
.REGEXMATCH("❸")
.NOT()
,
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❸.*❸🔞")
,
[💻💚单价（信息重组专用版）]
.REGEXEXTRACTALL("❸.*❸🔞"))



,CHAR(10),

IF(
[💻💚单价（信息重组专用版）]
.REGEXMATCH("❹")
.NOT()
,
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❹.*❹🔞")
,
[💻💚单价（信息重组专用版）]
.REGEXEXTRACTALL("❹.*❹🔞"))


,CHAR(10),

IF(
[💻💚单价（信息重组专用版）]
.REGEXMATCH("❺")
.NOT()
,
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❺.*❺🔞")
,
[💻💚单价（信息重组专用版）]
.REGEXEXTRACTALL("❺.*❺🔞"))


,CHAR(10),

IF(
[💻💚单价（信息重组专用版）]
.REGEXMATCH("❻")
.NOT()
,
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❻.*❻🔞")
,
[💻💚单价（信息重组专用版）]
.REGEXEXTRACTALL("❻.*❻🔞"))


,CHAR(10),

IF(
[💻💚单价（信息重组专用版）]
.REGEXMATCH("❼")
.NOT()
,
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❼.*❼🔞")
,
[💻💚单价（信息重组专用版）]
.REGEXEXTRACTALL("❼.*❼🔞"))


,CHAR(10),

IF(
[💻💚单价（信息重组专用版）]
.REGEXMATCH("❽")
.NOT()
,
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❽.*❽🔞")
,
[💻💚单价（信息重组专用版）]
.REGEXEXTRACTALL("❽.*❽🔞"))


,CHAR(10),

IF(
[💻💚单价（信息重组专用版）]
.REGEXMATCH("❾")
.NOT()
,
[💻💜文本单价（终版）]
.REGEXEXTRACTALL("❾.*❾🔞")
,
[💻💚单价（信息重组专用版）]
.REGEXEXTRACTALL("❾.*❾🔞"))



)


.REGEXREPLACE("(低至|折)?💰","约¥")
.REGEXREPLACE("([❶-❾]🔞).*?([❶-❾])；?","$1"&CHAR(10)&"$2")

.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
💻💚数量（最终）-初级数量*进阶数量
IF(ISBLANK([🍬商品信息（原始版）]),""，

CONCATENATE(
IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACT("❶.*?❶🔞").REGEXMATCH("礼包"),
"",

CONCATENATE(
CONCATENATE(
"❶",
[💻🤍进阶数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(1)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(1)
,"❶🔞"
）
,"；"，
CONCATENATE(
"❶",
[💻🤍进阶数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(2)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(2)
,"❶🔞"
）

,"；"，
CONCATENATE(
"❶",
[💻🤍进阶数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(3)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(3)
,"❶🔞"
）


,"；"，
CONCATENATE(
"❶",
[💻🤍进阶数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(4)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(4)
,"❶🔞"
）



,"；"，
CONCATENATE(
"❶",
[💻🤍进阶数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(5)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(5)
,"❶🔞"
）



,"；"，
CONCATENATE(
"❶",
[💻🤍进阶数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(6)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！]
.REGEXEXTRACTALL("❶(.*?)❶🔞")
.NTH(6)
,"❶🔞"
）

）
）


，CHAR(10)，



IF(
[🧪🤍规格类型（原始值）].REGEXEXTRACT("❷.*?❷🔞").REGEXMATCH("礼包"),
"",
CONCATENATE(
CONCATENATE(
"❷",
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(1)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(1),
"❷🔞"
),
"；",
CONCATENATE(
"❷",
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(2)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(2),
"❷🔞"
),
"；",
CONCATENATE(
"❷",
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(3)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(3),
"❷🔞"
),
"；",
CONCATENATE(
"❷",
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(4)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(4),
"❷🔞"
),
"；",
CONCATENATE(
"❷",
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(5)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(5),
"❷🔞"
),
"；",
CONCATENATE(
"❷",
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(6)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❷(.*?)❷🔞").NTH(6),
"❷🔞"
)
)
)


，CHAR(10)，




IF(
[🧪🤍规格类型（原始值）].REGEXEXTRACT("❸.*?❸🔞").REGEXMATCH("礼包"),
"",
CONCATENATE(
CONCATENATE(
"❸",
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(1)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(1),
"❸🔞"
),
"；",
CONCATENATE(
"❸",
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(2)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(2),
"❸🔞"
),
"；",
CONCATENATE(
"❸",
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(3)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(3),
"❸🔞"
),
"；",
CONCATENATE(
"❸",
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(4)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(4),
"❸🔞"
),
"；",
CONCATENATE(
"❸",
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(5)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(5),
"❸🔞"
),
"；",
CONCATENATE(
"❸",
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(6)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❸(.*?)❸🔞").NTH(6),
"❸🔞"
)
)
)




，CHAR(10)，




IF(
[🧪🤍规格类型（原始值）].REGEXEXTRACT("❹.*?❹🔞").REGEXMATCH("礼包"),
"",
CONCATENATE(
CONCATENATE(
"❹",
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❹(.*?)❹🔞").NTH(1)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❹(.*?)❹🔞").NTH(1),
"❹🔞"
),
"；",
CONCATENATE(
"❹",
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❹(.*?)❹🔞").NTH(2)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❹(.*?)❹🔞").NTH(2),
"❹🔞"
),
"；",
CONCATENATE(
"❹",
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❹(.*?)❹🔞").NTH(3)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❹(.*?)❹🔞").NTH(3),
"❹🔞"
),
"；",
CONCATENATE(
"❹",
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❹(.*?)❹🔞").NTH(4)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❹(.*?)❹🔞").NTH(4),
"❹🔞"
),
"；",
CONCATENATE(
"❹",
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❹(.*?)❹🔞").NTH(5)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❹(.*?)❹🔞").NTH(5),
"❹🔞"
),
"；",
CONCATENATE(
"❹",
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❹(.*?)❹🔞").NTH(6)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❹(.*?)❹🔞").NTH(6),
"❹🔞"
)
)
)



，CHAR(10)，



IF(
[🧪🤍规格类型（原始值）].REGEXEXTRACT("❺.*?❺🔞").REGEXMATCH("礼包"),
"",
CONCATENATE(
CONCATENATE(
"❺",
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❺(.*?)❺🔞").NTH(1)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❺(.*?)❺🔞").NTH(1),
"❺🔞"
),
"；",
CONCATENATE(
"❺",
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❺(.*?)❺🔞").NTH(2)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❺(.*?)❺🔞").NTH(2),
"❺🔞"
),
"；",
CONCATENATE(
"❺",
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❺(.*?)❺🔞").NTH(3)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❺(.*?)❺🔞").NTH(3),
"❺🔞"
),
"；",
CONCATENATE(
"❺",
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❺(.*?)❺🔞").NTH(4)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❺(.*?)❺🔞").NTH(4),
"❺🔞"
),
"；",
CONCATENATE(
"❺",
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❺(.*?)❺🔞").NTH(5)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❺(.*?)❺🔞").NTH(5),
"❺🔞"
),
"；",
CONCATENATE(
"❺",
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❺(.*?)❺🔞").NTH(6)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❺(.*?)❺🔞").NTH(6),
"❺🔞"
)
)
)

，CHAR(10)，


IF(
[🧪🤍规格类型（原始值）].REGEXEXTRACT("❻.*?❻🔞").REGEXMATCH("礼包"),
"",
CONCATENATE(
CONCATENATE(
"❻",
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(1)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(1),
"❻🔞"
),
"；",
CONCATENATE(
"❻",
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(2)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(2),
"❻🔞"
),
"；",
CONCATENATE(
"❻",
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(3)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(3),
"❻🔞"
),
"；",
CONCATENATE(
"❻",
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(4)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(4),
"❻🔞"
),
"；",
CONCATENATE(
"❻",
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(5)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(5),
"❻🔞"
),
"；",
CONCATENATE(
"❻",
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(6)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❻(.*?)❻🔞").NTH(6),
"❻🔞"
)
)
)


,CHAR(10),
IF(
[🧪🤍规格类型（原始值）].REGEXEXTRACT("❼.*?❼🔞").REGEXMATCH("礼包"),
"",
CONCATENATE(
CONCATENATE(
"❼",
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(1)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(1),
"❼🔞"
),
"；",
CONCATENATE(
"❼",
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(2)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(2),
"❼🔞"
),
"；",
CONCATENATE(
"❼",
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(3)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(3),
"❼🔞"
),
"；",
CONCATENATE(
"❼",
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(4)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(4),
"❼🔞"
),
"；",
CONCATENATE(
"❼",
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(5)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(5),
"❼🔞"
),
"；",
CONCATENATE(
"❼",
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(6)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❼(.*?)❼🔞").NTH(6),
"❼🔞"
)
)
)




,CHAR(10),
IF(
[🧪🤍规格类型（原始值）].REGEXEXTRACT("❽.*?❽🔞").REGEXMATCH("礼包"),
"",
CONCATENATE(
CONCATENATE(
"❽",
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(1)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(1),
"❽🔞"
),
"；",
CONCATENATE(
"❽",
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(2)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(2),
"❽🔞"
),
"；",
CONCATENATE(
"❽",
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(3)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(3),
"❽🔞"
),
"；",
CONCATENATE(
"❽",
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(4)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(4),
"❽🔞"
),
"；",
CONCATENATE(
"❽",
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(5)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(5),
"❽🔞"
),
"；",
CONCATENATE(
"❽",
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(6)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❽(.*?)❽🔞").NTH(6),
"❽🔞"
)
)
)


,CHAR(10),
IF(
[🧪🤍规格类型（原始值）].REGEXEXTRACT("❾.*?❾🔞").REGEXMATCH("礼包"),
"",
CONCATENATE(
CONCATENATE(
"❾",
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❾(.*?)❾🔞").NTH(1)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❾(.*?)❾🔞").NTH(1),
"❾🔞"
),
"；",
CONCATENATE(
"❾",
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❾(.*?)❾🔞").NTH(2)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❾(.*?)❾🔞").NTH(2),
"❾🔞"
),
"；",
CONCATENATE(
"❾",
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❾(.*?)❾🔞").NTH(3)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❾(.*?)❾🔞").NTH(3),
"❾🔞"
),
"；",
CONCATENATE(
"❾",
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❾(.*?)❾🔞").NTH(4)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❾(.*?)❾🔞").NTH(4),
"❾🔞"
),
"；",
CONCATENATE(
"❾",
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❾(.*?)❾🔞").NTH(5)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❾(.*?)❾🔞").NTH(5),
"❾🔞"
),
"；",
CONCATENATE(
"❾",
[💻🤍进阶数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❾(.*?)❾🔞").NTH(6)
*
[💻🤍😀终极数量-数字提取 （完整版）勿删！].REGEXEXTRACTALL("❾(.*?)❾🔞").NTH(6),
"❾🔞"
)
)
)

））
.REGEXREPLACE("[❶-❾]0[❶-❾]🔞；?","")

.REGEXREPLACE("[,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====

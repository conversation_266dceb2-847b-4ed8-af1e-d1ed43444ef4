🎫编号（最终）
TEXT([🎫  优惠券管理（底库）].COUNTIF(CurrentValue.[🎫编号（参考勿删！）]<[🎫编号（参考勿删！）])+1,"👀000000000")
=====
🎫编号（参考勿删！）
自增数字
=====
🍬商品信息（原始版）
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🎫编号（最终）]).[🍬商品信息（原始版）].LISTCOMBINE()
=====
🍬商品信息ID
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🎫编号（最终）]).[🍬商品信息ID].LISTCOMBINE()
=====
📁🩵商品信息（清洗版）
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🎫编号（最终）]).[📁🩵商品信息（清洗版）].LISTCOMBINE()
=====
📁🩵下单口令/链接提取
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🎫编号（最终）]).[📁🩵下单口令/链接提取（原始）].LISTCOMBINE()
=====
📁🩵信息类型（唯一值）
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🎫编号（最终）]).[📁🩵信息类型（唯一值）].LISTCOMBINE()
=====
📁选品定位符
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🎫编号（最终）]).[📁选品定位符].LISTCOMBINE()
=====
📁源信息发布时间(最终) -精确到天
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🎫编号（最终）]).[📁源信息发布时间(最终) -精确到天].LISTCOMBINE()
=====
📁所属平台
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🎫编号（最终）]).[📁所属平台].LISTCOMBINE()
=====
🔪💚领券文案 （信息重组专用版）
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[🎫编号（最终）]).[🔪💚领券文案 （信息重组专用版）].LISTCOMBINE()
=====
🎫🩵优惠券面值
IF(
[📁🩵信息类型（唯一值）]
.REGEXMATCH("🎫")
，
[📁🩵商品信息（清洗版）]
.REGEXEXTRACTALL([🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠优惠券-逻辑]!="").[🧠优惠券-逻辑].LISTCOMBINE().UNIQUE()).ARRAYJOIN(CHAR(10))
，"")

.TRIM()
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🎫🩵优惠券类型
IF(
[📁🩵信息类型（唯一值）]
.REGEXMATCH("🎫")
，
[📁🩵商品信息（清洗版）]
.REGEXEXTRACTALL("(.*?"&[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠优惠券-逻辑]!="").[🧠优惠券-逻辑]&".*)").ARRAYJOIN(CHAR(10))
，"")

.REGEXREPLACE(",.*","")
.REGEXEXTRACTALL(".*?("&[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠优惠券类型-逻辑]!="").[🧠优惠券类型-逻辑]&").*")
.REGEXREPLACE("^券","消费券")

.TRIM()
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====

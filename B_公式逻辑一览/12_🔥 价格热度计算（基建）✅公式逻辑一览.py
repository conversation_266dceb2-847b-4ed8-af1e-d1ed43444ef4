🔥编号（最终）
TEXT([🔥 价格热度计算（基建）]
.COUNTIF(CurrentValue.[🔥编号（参考勿删！）]<[🔥编号（参考勿删！）])+1

，"👀000000000"）
=====
🔥编号（参考勿删！）
自增数字
=====
🍬商品信息（原始版）
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🔥编号（最终）]).[🍬商品信息（原始版）].LISTCOMBINE()
=====
🍬商品信息ID
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🔥编号（最终）]).[🍬商品信息ID].LISTCOMBINE()
=====
🧠价格优势-逻辑
IF([🍬商品信息（原始版）].ISBLANK(),""，
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠价格优势-逻辑]!="").[🧠价格优势-逻辑].LISTCOMBINE().UNIQUE())
=====
📁源信息发布时间(最终)
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🔥编号（最终）]).[📁源信息发布时间(最终)]
=====
📁源信息新鲜度(天)
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🔥编号（最终）]).[📁源信息新鲜度(天)]
=====
📁信息类型
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🔥编号（最终）]).[📁🩵信息类型（唯一值）]
=====
📁⚫️SKU是否重复
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🔥编号（最终）]).[📁⚫️SKU是否重复]
=====
📁🟠SKU｜所属平台-品牌-2级品类-规格-口味-使用对象-成长期
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🔥编号（最终）]).[📁🟠SKU｜所属平台-品牌-2级品类-规格-口味-使用对象-成长期]
=====
🛍️🤍一级品类（唯一值）
[🛍️ ｜✍️ 品类｜口味｜制作工艺 ｜ 使用对象 ｜ 成长期（基建）].FILTER(CurrentValue.[🛍️编号（最终）]=[🔥编号（最终）]).[🛍️🤍😀一级品类（唯一值）].LISTCOMBINE()
=====
🛍️🤍二级品类（唯一值）
[🛍️ ｜✍️ 品类｜口味｜制作工艺 ｜ 使用对象 ｜ 成长期（基建）].FILTER(CurrentValue.[🛍️编号（最终）]=[🔥编号（最终）]).[🛍️🤍😀二级品类（唯一值）].LISTCOMBINE()
=====
🛍️💚口味（唯一值）
[🛍️ ｜✍️ 品类｜口味｜制作工艺 ｜ 使用对象 ｜ 成长期（基建）].FILTER(CurrentValue.[🛍️编号（最终）]=[🔥编号（最终）]).[🛍️💜口味（唯一值）].LISTCOMBINE()
=====
🛍️💜适用宠物成长期（唯一值）
[🛍️ ｜✍️ 品类｜口味｜制作工艺 ｜ 使用对象 ｜ 成长期（基建）].FILTER(CurrentValue.[🛍️编号（最终）]=[🔥编号（最终）]).[🛍️💜适用宠物成长期（唯一值）].LISTCOMBINE()
=====
🛍️💜使用对象(唯一值)
[🛍️ ｜✍️ 品类｜口味｜制作工艺 ｜ 使用对象 ｜ 成长期（基建）].FILTER(CurrentValue.[🛍️编号（最终）]=[🔥编号（最终）]).[🛍️💜使用对象(唯一值)].LISTCOMBINE()
=====
🧪🤍规格类型（原始值）
[🧪 规格解构（基建）].FILTER(CurrentValue.[🧪编号（最终）]=[🔥编号（最终）]).[🧪🤍规格类型（原始值）].LISTCOMBINE()
=====
🧪规格类型（唯一值）
[🧪 规格解构（基建）].FILTER(CurrentValue.[🧪编号（最终）]=[🔥编号（最终）]).[🧪规格类型（唯一值）].LISTCOMBINE()
=====
💰💚到手价格（终版）
[🧪 规格解构（基建）].FILTER(CurrentValue.[🧪编号（最终）]=[🔥编号（最终）]).[💰到手价格（终版）].LISTCOMBINE()
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.VALUE()
=====
Ⓜ️🤍‼️品牌（标准格式）
[Ⓜ️ ｜ ✍️ 品牌识别（基建）].FILTER(CurrentValue.[Ⓜ️编号（最终）]=[🔥编号（最终）]).[Ⓜ️🤍‼️品牌（标准格式）].LISTCOMBINE()
=====
🔥🟠sku-到手价（Average）
IF([📁🟠SKU｜所属平台-品牌-2级品类-规格-口味-使用对象-成长期].ISBLANK(),""，
[🔥 价格热度计算（基建）].FILTER(CurrentValue.[📁🟠SKU｜所属平台-品牌-2级品类-规格-口味-使用对象-成长期]=[📁🟠SKU｜所属平台-品牌-2级品类-规格-口味-使用对象-成长期]).[💰💚到手价格（终版）].LISTCOMBINE().AVERAGE().TEXT("0.00").VALUE())
=====
🔥🟠sku-到手价（Max）
IF([📁🟠SKU｜所属平台-品牌-2级品类-规格-口味-使用对象-成长期].ISBLANK(),""，
[🔥 价格热度计算（基建）].FILTER(CurrentValue.[📁🟠SKU｜所属平台-品牌-2级品类-规格-口味-使用对象-成长期]=[📁🟠SKU｜所属平台-品牌-2级品类-规格-口味-使用对象-成长期]).[💰💚到手价格（终版）].LISTCOMBINE().MAX())
=====
🔥🟠sku-到手价（Min）
IF([📁🟠SKU｜所属平台-品牌-2级品类-规格-口味-使用对象-成长期].ISBLANK(),""，
[🔥 价格热度计算（基建）].FILTER(CurrentValue.[📁🟠SKU｜所属平台-品牌-2级品类-规格-口味-使用对象-成长期]=[📁🟠SKU｜所属平台-品牌-2级品类-规格-口味-使用对象-成长期]).[💰💚到手价格（终版）].LISTCOMBINE().Min())
=====
🔥🟠相同SKU数量
[🔥 价格热度计算（基建）].FILTER(CurrentValue.[📁🟠SKU｜所属平台-品牌-2级品类-规格-口味-使用对象-成长期]=[📁🟠SKU｜所属平台-品牌-2级品类-规格-口味-使用对象-成长期]).[📁🟠SKU｜所属平台-品牌-2级品类-规格-口味-使用对象-成长期].LISTCOMBINE().COUNTA()
=====
🔥🟠价格范围
IF(
OR(
[🍬商品信息（原始版）]
.ISBLANK()
，
[🔥🟠sku-到手价（Average）]
.ISBLANK()
,
[🔥🟠sku-到手价（Max）]
.ISBLANK()
,
[🔥🟠sku-到手价（Min）]
.ISBLANK()
)
,"",
IF(
[🔥🟠sku-到手价（Max）]=[🔥🟠sku-到手价（Min）]，[🔥🟠sku-到手价（Max）]
，
CONCATENATE([🔥🟠sku-到手价（Min）]，"-"，[🔥🟠sku-到手价（Max）])))
=====
🔥🟠低价热度
IF(
OR(
[🍬商品信息（原始版）]
.ISBLANK()
，
[🔥🟠sku-到手价（Average）]
.ISBLANK()
,
[🔥🟠sku-到手价（Max）]
.ISBLANK()
,
[🔥🟠sku-到手价（Min）]
.ISBLANK()
)
,"",
IF(
[💰💚到手价格（终版）]=[🔥🟠sku-到手价（Max）],0,

IF(
[💰💚到手价格（终版）]=[🔥🟠sku-到手价（Min）]，1,

TEXT(([🔥🟠sku-到手价（Max）]-[💰💚到手价格（终版）]）/（[🔥🟠sku-到手价（Max）]-[🔥🟠sku-到手价（Min）]),"0.00"))))
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.VALUE()
=====
🔥🟠价格优势（数学计算）（唯一值）
IF(
OR(
[🍬商品信息（原始版）]
.ISBLANK()
，
[🔥🟠sku-到手价（Average）]
.ISBLANK()
,
[🔥🟠sku-到手价（Max）]
.ISBLANK()
,
[🔥🟠sku-到手价（Min）]
.ISBLANK()

，

[🔥 价格热度计算（基建）].FILTER(CurrentValue.[📁🟠SKU｜所属平台-品牌-2级品类-规格-口味-使用对象-成长期]=[📁🟠SKU｜所属平台-品牌-2级品类-规格-口味-使用对象-成长期]).[📁🟠SKU｜所属平台-品牌-2级品类-规格-口味-使用对象-成长期].LISTCOMBINE().COUNTA()<6

)
,"",

IF(
AND(
[🔥🟠低价热度]>=0.8
，
[🔥 价格热度计算（基建）].FILTER(CurrentValue.[📁🟠SKU｜所属平台-品牌-2级品类-规格-口味-使用对象-成长期]=[📁🟠SKU｜所属平台-品牌-2级品类-规格-口味-使用对象-成长期]).[📁🟠SKU｜所属平台-品牌-2级品类-规格-口味-使用对象-成长期].LISTCOMBINE().COUNTA()>=6
）
,"✨有豪车✨"
,
IF(
AND(
[🔥🟠低价热度]>=0.4
，
[🔥 价格热度计算（基建）].FILTER(CurrentValue.[📁🟠SKU｜所属平台-品牌-2级品类-规格-口味-使用对象-成长期]=[📁🟠SKU｜所属平台-品牌-2级品类-规格-口味-使用对象-成长期]).[📁🟠SKU｜所属平台-品牌-2级品类-规格-口味-使用对象-成长期].LISTCOMBINE().COUNTA()>=6
)
,"✨有好价✨"

,"")))
=====
🔥💜价格优势（信息提取）（唯一值）
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🔥编号（最终）]).[📁💜商品信息（终版分割版）]

.REGEXEXTRACTALL([🧠价格优势-逻辑]).ARRAYJOIN(CHAR(10))
.REGEXREPLACE("好价和豪车(之间|边缘)","")


.REGEXREPLACE(".*?好车.*","好价")
.REGEXREPLACE(".*?(白菜价|低价|史低|最低|新低|豪🚗|豪车).*","豪车")

.REGEXEXTRACTALL("羊毛|豪车|好价"）
.UNIQUE()

.REGEXREPLACE("(羊毛|豪车|好价)","✨有$1✨")

.REGEXREPLACE("[,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🔥💜价格优势（逻辑匹配）（唯一值）
IF(
[🧪 规格解构（基建）]
.FILTER(CurrentValue.[🧪编号（最终）]=[🔥编号（最终）])
.[🧪规格类型（唯一值）]
.LISTCOMBINE()
.REGEXMATCH("试吃"),

"✨有羊毛✨"，
"")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🔥价格优势（终版）（唯一值）
CONCATENATE([🔥💜价格优势（信息提取）（唯一值）]，[🔥🟠价格优势（数学计算）（唯一值）]，[🔥💜价格优势（逻辑匹配）（唯一值）])
.REGEXEXTRACTALL("✨.*?✨")
.UNIQUE()
.ARRAYJOIN("")
.REGEXREPLACE("(✨有豪车✨)✨有羊毛✨","$1")
.REGEXREPLACE("(✨有豪车✨|✨有好价✨|✨有羊毛✨)+","$1")
=====

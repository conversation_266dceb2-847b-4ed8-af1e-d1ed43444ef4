♦︎编号（最终）
TEXT([♦︎ 品牌库（底库）]
.COUNTIF(CurrentValue.[♦︎编号（参考勿删！）]<[♦︎编号（参考勿删！）])+1
，"👀0000"）
=====
♦︎编号（参考勿删！）
自动生成
=====
♦︎✍️品牌名称-中文
手动输入
=====
♦︎✍️品牌名称-英文
手动输入
=====
♦︎✍️品牌百科（手动）
手动输入
=====
♦︎✍️品牌名称-昵称/缩写（若有）
手动输入
=====
♦︎✍️品牌介绍
手动输入
=====
♦︎✍️百科链接/官网链接
手动输入
=====
♦︎✍️AI返回的百科链接是否有效
手动输入
=====
♦︎✍️旗舰店店铺名称
手动输入
=====
♦︎✍️所有产品链接-grid视图
手动输入
=====
♦︎所有产品链接-list视图
IF(
[♦︎✍️所有产品链接-grid视图]
.REGEXMATCH(".taobao.com")，
[♦︎✍️所有产品链接-grid视图]，

IF(
[♦︎✍️所有产品链接-grid视图]
.REGEXMATCH("grid")，

[♦︎✍️所有产品链接-grid视图]
.REGEXREPLACE("grid","list")
,

[♦︎✍️所有产品链接-grid视图]
.REGEXREPLACE("(https://.*)","$0&viewType=list")
））
.REGEXREPLACE("&keyword=[%A-Fa-f0-9]+","")
.HYPERLINK()
=====
♦︎✍️备注
手动输入
=====
♦︎✍️信息抓取优先级
手动输入
=====
♦︎✍️商标™️
手动输入
=====
♦︎信息同步进度（最终）
IF(
[♦︎品牌名称（标准版）]
.ISBLANK()
，""，

IF(
[♦︎✍️品牌百科（手动）]
.ISBLANK()
，"待同步…"
,
IF(
AND(
[♦︎✍️品牌百科（手动）]
.ISBLANK()
.NOT()
，

[♦︎✍️商标™️]
.ISBLANK()
.NOT()

）

，"已同步✅"
，"同步中⭕️"
)))
=====
♦︎重复性判断
IF(
ISBLANK([♦︎品牌名称（标准版）]),
"",
IF(
[♦︎ 品牌库（底库）].COUNTIF(
((CurrentValue.[♦︎品牌入库时间] < [♦︎品牌入库时间]) 
+
(CurrentValue.[♦︎编号（最终）].REGEXREPLACE("👀","").VALUE()<[♦︎编号（最终）].REGEXREPLACE("👀","").VALUE()))
*
((CurrentValue.[♦︎✍️品牌名称-中文] = [♦︎✍️品牌名称-中文]) * (LEN([♦︎✍️品牌名称-中文]) > 0)
+
(CurrentValue.[♦︎✍️品牌名称-英文] = [♦︎✍️品牌名称-英文]) * (LEN([♦︎✍️品牌名称-英文]) > 0) 
+
(CurrentValue.[♦︎品牌名称（标准版）] = [♦︎品牌名称（标准版）])) > 0
),
"🚫重复品牌",
"🆕新品牌"
))
=====
♦︎Ai Prompt
IF(AND(ISBLANK([♦︎✍️品牌名称-中文]),ISBLANK([♦︎✍️品牌名称-英文])),"",

CONCATENATE(
if(ISBLANK([♦︎✍️品牌名称-中文])，CONCATENATE([♦︎✍️品牌名称-英文]，"（宠物品牌）的品牌中文名称（若有)"),CONCATENATE([♦︎✍️品牌名称-中文]，"【宠物品牌】的品牌英文名称（若有)")),
"｜品牌昵称（若有）｜品牌发源地 ｜品牌介绍｜主卖产品类型 ｜品牌梯度｜平均客单价 ｜百科链接
分别是什么？分段显示。
不要根据一般宠物品牌的特点和市场常见情况进行胡编乱造！"))


=====
♦︎Ai 品牌百科（最终）
[♦︎✍️品牌百科（手动）]
.REGEXREPLACE("\*+","")
.REGEXREPLACE("#+","")

.REGEXREPLACE("\*+","")
=====
♦︎Ai 品牌百科（纯净版）
if(ISBLANK([♦︎Ai 品牌百科（最终）]),"",
CONCATENATE(
SUBSTITUTE(SUBSTITUTE(SUBSTITUTE(SUBSTITUTE([♦︎Ai 品牌百科（最终）], "###",""),"**","")，"-**","")，"：", ""),"🐈"))
=====
♦︎品牌梯度
IF(
[♦︎Ai 品牌百科（最终）]
.ISBLANK()
,"",

IF(
[♦︎Ai 品牌百科（最终）]
.REGEXMATCH("🚫")
,"大众",

CONCATENATE("🐶"，
[♦︎Ai 品牌百科（纯净版）]
.REGEXEXTRACT("品牌梯度(?:\r\n|\n|\r)?([\s\S]+?)平均客单价"))))

.REGEXREPLACE(" ","")
.REGEXREPLACE("🐶"&CHAR(10),"🐶")

.REGEXREPLACE(".*?(暂?未找到相关信息|暂?无公开信息|暂?无明确信息)[\s\S]*","大众")


.REGEXREPLACE(".*?(高中[档端]档?次?|中高[档端]档?次?|[中高]{2}端梯度|[中高]{2}端性价比)[\s\S]*","中高🔥端")
.REGEXREPLACE(".*?(一线知名|高档次|高端)[\s\S]*","高端")
.REGEXREPLACE(".*?(不同档次|不同消费层次|多个价格段|中低端到中端|至)[\s\S]*","多元定位")
.REGEXREPLACE(".*?(中[等端档]+|性价比|亲民|普通消费市场)[\s\S]*","大众")


.REGEXREPLACE("🔥","")
.REGEXEXTRACTALL("高端|中高端|大众|多元定位")
.UNIQUE()
=====
♦︎品牌名称（标准版）
IF(
OR(
[♦︎✍️品牌名称-中文]
.REGEXREPLACE("(^\s*$\r?\n?","")
.REGEXREPLACE("[\u200B-\u200D\uFE00-\uFE0F\u202A-\u202E\u2060-\u206F]|CHAR(8203)|CHAR(8288)","")
.SPLIT(CHAR(10))
.COUNTA()>1
,
[♦︎✍️品牌名称-英文]
.REGEXREPLACE("[\u200B-\u200D\uFE00-\uFE0F\u202A-\u202E\u2060-\u206F]|CHAR(8203)|CHAR(8288)","")
.REGEXREPLACE("^\s*$\r?\n?", "")
.SPLIT(CHAR(10))
.COUNTA()>1
)
,"",

IF(
AND(
[♦︎✍️品牌名称-中文]
.ISBLANK()
，
[♦︎✍️品牌名称-英文]
.ISBLANK()
)
,"",

IF(
[♦︎✍️品牌名称-中文]
.ISBLANK()
,
CONCATENATE([♦︎✍️品牌名称-英文],"【"，[♦︎✍️品牌名称-昵称/缩写（若有）],"】")


,

IF(
[♦︎✍️品牌名称-英文]
.ISBLANK()
,


CONCATENATE([♦︎✍️品牌名称-中文],"【"，[♦︎✍️品牌名称-昵称/缩写（若有）],"】")

，
CONCATENATE([♦︎✍️品牌名称-中文]," / "，[♦︎✍️品牌名称-英文],"【"，[♦︎✍️品牌名称-昵称/缩写（若有）],"】")))))


.REGEXREPLACE("(【】)\s*+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
♦︎品牌发源地（国家）
IF(
[♦︎Ai 品牌百科（最终）]
.ISBLANK()
,"",

[♦︎Ai 品牌百科（纯净版）]
.REGEXEXTRACT("发源地(?:\r\n|\n|\r)?([\s\S]+?)品牌介绍"))

.REGEXEXTRACTALL("♎️
|上海|北京|天津|天津|重庆|成都|四川|广东|新疆|江苏|浙江|河北|河南|山西|山东|安徽|♎️
|苏州|邢台|广州|深圳|杭州|厦门|♎️
|加利福尼亚|伍斯特|俄勒冈|纽约|♎️
|中国|美国|英国|法国|德国|日本|意大利|加拿大|芬兰|新西兰|瑞典|新加坡|台湾|西班牙|荷兰|韩国|菲律宾|挪威").ARRAYJOIN()
.UNIQUE()

.REGEXREPLACE("上海|北京|天津|天津|重庆|成都|四川|广东|新疆|江苏|浙江|河北|河南|山西|山东|安徽|苏州|邢台|广州|深圳|杭州|厦门","中国")

.SPLIT(",").UNIQUE()

=====
♦︎品牌归属
IF(
OR(
[♦︎品牌名称（标准版）]
.ISBLANK()
,

[♦︎Ai 品牌百科（最终）]
.ISBLANK()
，

[♦︎Ai 品牌百科（最终）]
.REGEXMATCH("🚫")
)，""，

IF(
[♦︎品牌发源地（国家）]
.REGEXMATCH("美国|英国|法国|德国|日本|意大利|加拿大|芬兰|新西兰|瑞典|新加坡|台湾|西班牙|荷兰|韩国|菲律宾|挪威|比利时")，"🇺🇸进口品牌","🇨🇳国产品牌")）

=====
♦︎品牌介绍（文本部分）
if(ISBLANK([♦︎Ai 品牌百科（最终）]),""，
[♦︎Ai 品牌百科（纯净版）]
.REGEXEXTRACT("品牌介绍(?:\r\n|\n|\r)?([\s\S]+?)主卖产品类型")）
=====
♦︎主卖产品类型（文本部分）
 if(ISBLANK([♦︎Ai 品牌百科（最终）]),""，
 [♦︎Ai 品牌百科（纯净版）]
  .REGEXEXTRACT("主卖产品类型(?:\r\n|\n|\r)?([\s\S]+?)品牌梯度")）
=====
♦︎平均客单价（文本部分）
if(ISBLANK([♦︎Ai 品牌百科（最终）]),""，
[♦︎Ai 品牌百科（纯净版）]
  .REGEXEXTRACT("平均客单价(?:\r\n|\n|\r)?([\s\S]+?)百科链接")）
=====
♦︎百科（文本部分）
if(
  ISBLANK([♦︎Ai 品牌百科（最终）]),
  "",
  [♦︎Ai 品牌百科（纯净版）]
    .REGEXEXTRACT("百科链接(?:\r\n|\n|\r)?([\s\S]+?)🐈"))
    .CONCATENATE("🐈")
    .REGEXREPLACE("旗舰店链接(?:\r\n|\n|\r)?([\s\S]+?)🐈"，"")
=====
♦︎旗舰店链接（文本部分）
if(
  ISBLANK([♦︎Ai 品牌百科（最终）]),
  "",
  [♦︎Ai 品牌百科（纯净版）]
    .REGEXEXTRACT("旗舰店链接(?:\r\n|\n|\r)?([\s\S]+?)🐈")
)
=====
♦︎旗舰店店铺名称
[♦︎✍️旗舰店店铺名称]
.TEXT("")
=====
♦︎点击搜索天猫旗舰店
 IF([♦︎品牌归属].REGEXMATCH("进口"),
CONCATENATE("https://s.taobao.com/search?finalPage=1&fromTmallRedirect=true&q="，[♦︎✍️品牌名称-英文]，"&tab=shop"）
.HYPERLINK()，

CONCATENATE("https://s.taobao.com/search?finalPage=1&fromTmallRedirect=true&q="，[♦︎编号（最终）]，"&tab=shop"）
.HYPERLINK()）
=====
♦︎百科链接
 [♦︎百科（文本部分）]
  .REGEXEXTRACT("(https?:\/\/[a-zA-Z0-9.-]+(?:\/[a-zA-Z0-9%&_\/]+)?(?:\?[a-zA-Z0-9%&_=-]+)?)")
  .ARRAYJOIN("👀")
  .REGEXREPLACE("👀","")
  .HYPERLINK()

=====
♦︎店铺类型
IF(ISBLANK([♦︎✍️旗舰店店铺名称])，"🙅暂无对应官旗"，
IF(
[♦︎✍️所有产品链接-grid视图]
.REGEXMATCH(".taobao.com")，
"🍑淘宝店铺","🐱天猫店铺"））
=====
♦︎品牌入库时间
自动生成
=====
♦︎品牌更新时间
自动生成
=====

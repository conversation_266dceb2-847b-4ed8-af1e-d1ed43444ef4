"""
公式转换器
将表格公式语法转换为Python代码执行
"""

import re
import json
from typing import Any, Dict, List, Optional, Union, Callable
import logging
from datetime import datetime

class FormulaConverter:
    """公式转换器"""
    
    def __init__(self):
        self.logger = logging.getLogger('FormulaConverter')
        
        # 函数映射表：表格函数 -> Python函数
        self.function_mapping = {
            'ISBLANK': self._is_blank,
            'TRIM': self._trim,
            'CONCATENATE': self._concatenate,
            'IF': self._if,
            'AND': self._and,
            'OR': self._or,
            'NOT': self._not,
            'REGEXMATCH': self._regex_match,
            'REGEXREPLACE': self._regex_replace,
            'REGEXEXTRACT': self._regex_extract,
            'REGEXEXTRACTALL': self._regex_extract_all,
            'SPLIT': self._split,
            'NTH': self._nth,
            'LISTCOMBINE': self._list_combine,
            'UNIQUE': self._unique,
            'FILTER': self._filter,
            'COUNTIF': self._count_if,
            'VALUE': self._value,
            'TEXT': self._text,
            'ROUND': self._round,
            'ROUNDDOWN': self._round_down,
            'CHAR': self._char,
            'ARRAYJOIN': self._array_join,
            'CONTAINTEXT': self._contain_text
        }
        
        # 操作符映射
        self.operator_mapping = {
            '&': '+',  # 字符串连接
            '=': '==',  # 等于比较
            '<>': '!=',  # 不等于比较
        }
    
    def convert_formula(self, formula: str, context: Dict[str, Any] = None) -> Any:
        """
        转换并执行表格公式
        
        Args:
            formula: 表格公式字符串
            context: 上下文数据，包含字段值和表格数据
            
        Returns:
            公式执行结果
        """
        if not formula or not formula.strip():
            return ""
        
        try:
            # 预处理公式
            processed_formula = self._preprocess_formula(formula)
            
            # 解析并执行公式
            result = self._execute_formula(processed_formula, context or {})
            
            return result
            
        except Exception as e:
            self.logger.error(f"公式执行失败: {formula[:100]}... 错误: {e}")
            return ""
    
    def _preprocess_formula(self, formula: str) -> str:
        """预处理公式"""
        # 移除注释和多余空格
        formula = re.sub(r'㊙️.*?(?=\n|$)', '', formula)
        formula = re.sub(r'\s+', ' ', formula).strip()
        
        # 处理字段引用 [字段名] -> get_field('字段名')
        formula = re.sub(r'\[([^\]]+)\]', r"get_field('\1')", formula)
        
        # 处理表格引用 [表名].FILTER(...) -> filter_table('表名', ...)
        formula = re.sub(r'get_field\(\'([^\']+)\'\)\.FILTER', r"filter_table('\1'", formula)
        
        return formula
    
    def _execute_formula(self, formula: str, context: Dict[str, Any]) -> Any:
        """执行公式"""
        # 创建执行环境
        exec_env = {
            'get_field': lambda field_name: context.get(field_name, ""),
            'filter_table': lambda table_name, condition: self._filter_table(table_name, condition, context),
            'CHAR': self._char,
            **self.function_mapping
        }
        
        try:
            # 简单的公式执行（实际项目中需要更复杂的解析器）
            if formula.startswith('IF('):
                return self._parse_if_statement(formula, context)
            elif formula.startswith('CONCATENATE('):
                return self._parse_concatenate(formula, context)
            elif formula.startswith('REGEXREPLACE('):
                return self._parse_regex_replace(formula, context)
            else:
                # 直接返回字段值或常量
                if formula.startswith("get_field("):
                    field_name = re.search(r"get_field\('([^']+)'\)", formula)
                    if field_name:
                        return context.get(field_name.group(1), "")
                return formula
                
        except Exception as e:
            self.logger.error(f"公式执行错误: {e}")
            return ""
    
    def _parse_if_statement(self, formula: str, context: Dict[str, Any]) -> Any:
        """解析IF语句"""
        # 简化的IF解析，实际需要更复杂的语法分析
        match = re.match(r'IF\((.+?),\s*"([^"]*)",\s*"([^"]*)"\)', formula)
        if match:
            condition, true_value, false_value = match.groups()
            
            # 评估条件
            if self._evaluate_condition(condition, context):
                return true_value
            else:
                return false_value
        
        return ""
    
    def _parse_concatenate(self, formula: str, context: Dict[str, Any]) -> str:
        """解析CONCATENATE函数"""
        # 提取参数
        content = formula[12:-1]  # 移除 CONCATENATE( 和 )
        
        # 简单的参数分割（实际需要更复杂的解析）
        parts = []
        current_part = ""
        in_quotes = False
        paren_count = 0
        
        for char in content:
            if char == '"' and paren_count == 0:
                in_quotes = not in_quotes
            elif char == '(' and not in_quotes:
                paren_count += 1
            elif char == ')' and not in_quotes:
                paren_count -= 1
            elif char == ',' and not in_quotes and paren_count == 0:
                parts.append(current_part.strip())
                current_part = ""
                continue
            
            current_part += char
        
        if current_part.strip():
            parts.append(current_part.strip())
        
        # 处理每个部分
        result_parts = []
        for part in parts:
            if part.startswith('"') and part.endswith('"'):
                result_parts.append(part[1:-1])  # 移除引号
            elif part.startswith("get_field("):
                field_name = re.search(r"get_field\('([^']+)'\)", part)
                if field_name:
                    result_parts.append(str(context.get(field_name.group(1), "")))
            else:
                result_parts.append(str(part))
        
        return ''.join(result_parts)
    
    def _parse_regex_replace(self, formula: str, context: Dict[str, Any]) -> str:
        """解析REGEXREPLACE函数"""
        # 简化的正则替换解析
        match = re.match(r'(.+?)\.REGEXREPLACE\("([^"]+)",\s*"([^"]*)"\)', formula)
        if match:
            source_expr, pattern, replacement = match.groups()
            
            # 获取源文本
            if source_expr.startswith("get_field("):
                field_name = re.search(r"get_field\('([^']+)'\)", source_expr)
                if field_name:
                    source_text = str(context.get(field_name.group(1), ""))
                else:
                    source_text = ""
            else:
                source_text = str(source_expr)
            
            # 执行正则替换
            try:
                result = re.sub(pattern, replacement, source_text)
                return result
            except re.error as e:
                self.logger.error(f"正则表达式错误: {pattern}, {e}")
                return source_text
        
        return ""
    
    def _evaluate_condition(self, condition: str, context: Dict[str, Any]) -> bool:
        """评估条件表达式"""
        # 简化的条件评估
        if '.ISBLANK()' in condition:
            field_expr = condition.replace('.ISBLANK()', '')
            if field_expr.startswith("get_field("):
                field_name = re.search(r"get_field\('([^']+)'\)", field_expr)
                if field_name:
                    value = context.get(field_name.group(1), "")
                    return self._is_blank(value)
        
        return False
    
    def _filter_table(self, table_name: str, condition: str, context: Dict[str, Any]) -> List[Dict]:
        """过滤表格数据"""
        # 这里需要实际的表格数据访问逻辑
        # 暂时返回空列表
        return []
    
    # 表格函数实现
    def _is_blank(self, value: Any) -> bool:
        """检查是否为空"""
        if value is None:
            return True
        if isinstance(value, str):
            return not value.strip()
        return not value
    
    def _trim(self, value: Any) -> str:
        """去除首尾空格"""
        return str(value).strip() if value is not None else ""
    
    def _concatenate(self, *args) -> str:
        """连接字符串"""
        return ''.join(str(arg) for arg in args if arg is not None)
    
    def _if(self, condition: bool, true_value: Any, false_value: Any) -> Any:
        """条件判断"""
        return true_value if condition else false_value
    
    def _and(self, *conditions) -> bool:
        """逻辑与"""
        return all(bool(cond) for cond in conditions)
    
    def _or(self, *conditions) -> bool:
        """逻辑或"""
        return any(bool(cond) for cond in conditions)
    
    def _not(self, condition: bool) -> bool:
        """逻辑非"""
        return not bool(condition)
    
    def _regex_match(self, text: str, pattern: str) -> bool:
        """正则匹配"""
        try:
            return bool(re.search(pattern, str(text)))
        except re.error:
            return False
    
    def _regex_replace(self, text: str, pattern: str, replacement: str) -> str:
        """正则替换"""
        try:
            return re.sub(pattern, replacement, str(text))
        except re.error:
            return str(text)
    
    def _regex_extract(self, text: str, pattern: str) -> str:
        """正则提取"""
        try:
            match = re.search(pattern, str(text))
            return match.group(1) if match and match.groups() else (match.group(0) if match else "")
        except re.error:
            return ""
    
    def _regex_extract_all(self, text: str, pattern: str) -> List[str]:
        """正则提取所有"""
        try:
            return re.findall(pattern, str(text))
        except re.error:
            return []
    
    def _split(self, text: str, delimiter: str) -> List[str]:
        """分割字符串"""
        return str(text).split(delimiter) if text else []
    
    def _nth(self, lst: List[Any], index: int) -> Any:
        """获取列表第N个元素"""
        try:
            return lst[index - 1] if isinstance(lst, list) and 0 < index <= len(lst) else ""
        except (IndexError, TypeError):
            return ""
    
    def _list_combine(self, lst: List[Any]) -> str:
        """合并列表为字符串"""
        if isinstance(lst, list):
            return ''.join(str(item) for item in lst if item is not None)
        return str(lst) if lst is not None else ""
    
    def _unique(self, lst: List[Any]) -> List[Any]:
        """去重"""
        if isinstance(lst, list):
            seen = set()
            result = []
            for item in lst:
                if item not in seen:
                    seen.add(item)
                    result.append(item)
            return result
        return [lst] if lst is not None else []
    
    def _filter(self, lst: List[Dict], condition: Callable) -> List[Dict]:
        """过滤列表"""
        if isinstance(lst, list):
            return [item for item in lst if condition(item)]
        return []
    
    def _count_if(self, lst: List[Any], condition: Callable) -> int:
        """条件计数"""
        if isinstance(lst, list):
            return sum(1 for item in lst if condition(item))
        return 0
    
    def _value(self, text: str) -> float:
        """转换为数值"""
        try:
            return float(str(text))
        except (ValueError, TypeError):
            return 0.0
    
    def _text(self, value: Any, format_str: str = "") -> str:
        """格式化为文本"""
        if format_str:
            try:
                return format_str.format(value)
            except:
                pass
        return str(value) if value is not None else ""
    
    def _round(self, value: float, digits: int = 0) -> float:
        """四舍五入"""
        try:
            return round(float(value), digits)
        except (ValueError, TypeError):
            return 0.0
    
    def _round_down(self, value: float, digits: int = 0) -> float:
        """向下取整"""
        try:
            import math
            multiplier = 10 ** digits
            return math.floor(float(value) * multiplier) / multiplier
        except (ValueError, TypeError):
            return 0.0
    
    def _char(self, code: int) -> str:
        """字符编码转字符"""
        try:
            return chr(int(code))
        except (ValueError, TypeError):
            return ""
    
    def _array_join(self, lst: List[Any], delimiter: str = "") -> str:
        """数组连接"""
        if isinstance(lst, list):
            return delimiter.join(str(item) for item in lst if item is not None)
        return str(lst) if lst is not None else ""
    
    def _contain_text(self, text: str, search_text: str) -> bool:
        """包含文本"""
        return search_text in str(text)

# 测试代码
if __name__ == "__main__":
    converter = FormulaConverter()
    
    # 测试简单公式
    context = {
        "商品名称": "爱肯拿 农场盛宴 猫粮",
        "价格": "128.5"
    }
    
    # 测试IF函数
    formula1 = 'IF([商品名称].ISBLANK(), "", "有商品名称")'
    result1 = converter.convert_formula(formula1, context)
    print(f"公式1结果: {result1}")
    
    # 测试CONCATENATE函数
    formula2 = 'CONCATENATE([商品名称], " - ", [价格], "元")'
    result2 = converter.convert_formula(formula2, context)
    print(f"公式2结果: {result2}")
    
    # 测试REGEXREPLACE函数
    formula3 = '[商品名称].REGEXREPLACE("农场", "牧场")'
    result3 = converter.convert_formula(formula3, context)
    print(f"公式3结果: {result3}")

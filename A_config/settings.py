# --------------------------
# 项目全局配置文件
"""
作用：统一管理所有规则（数据库连接、字段标记、集合映射等）
配置文件里的 FIELD_MARKERS 和 ICON_TO_COLLECTION 就像你给代码 “制定的规则手册”
代码会严格按照手册里的映射关系执行，相当于 “系统承认了这些关联”。

核心结论：
1)配置文件里的映射关系不是 “自动生效” 的，而是代码会主动读取这些配置并执行（通过 import 和字典取值）；
2)一旦配置写好，所有导入配置的代码都会 “承认” 并遵循这些关系，相当于 “系统认可了你的自定义规则”；
3）后续修改配置，代码会自动读取新规则，无需修改代码本身，这就是 “一次配置，全局生效” 的核心逻辑。
4）简单说：你定义规则，代码遵守规则，配置文件就是规则的 “唯一来源”。
"""
# 维护说明：所有规则修改只改这里，其他地方自动同步
# --------------------------


# --------------------------
# 1. MongoDB数据库配置
# 用途：记录数据库的连接信息（地址、端口、账号等）
# --------------------------
MONGO_CONFIG = {
    "host": "*************",  # 数据库地址（你的服务器IP）
    "port": 30017,            # 数据库端口（你的服务器端口）
    "db_name": "petgo_prod",  # 数据库名称
    "user": "petgo_prod",     # 登录用户名
    "pwd": "@qweDec23p3",     # 登录密码
    "auth_source": "petgo_prod"  # 认证数据库（和db_name一致）
}


# --------------------------
# 2. 字段标记常量
# 用途：定义每个表格的专属图标（标签），所有字段命名必须用这些图标
# 格式："表格简称": "专属图标"
# --------------------------
FIELD_MARKERS = {
    "逻辑表（底库）": "🧠",               # 逻辑表的专属图标
    "信息获取（基建）": "📁｜✍️",             # 信息获取表的专属图标
    "优惠券管理（底库）": "🎫",              # 优惠券管理表的专属图标
    "品牌识别（基建）": "Ⓜ️｜✍️",            # 品牌识别表的专属图标
    "品类｜口味｜制作工艺 ｜ 使用对象 ｜ 成长期（基建）": "🛍️｜✍️",         # 品类表的专属图标
    "sku解构(基建)": "🔪｜✍️",   # sku解构表的专属图标
    "sku重组-网站专用（基建）": "🐤",             # 网站专用sku重组表的专属图标
    "sku重组-社群专用 (基建)": "🐽",           # 社群专用sku重组表的专属图标
    "规格解构（基建）": "🧪",                # 规格解构表的专属图标
    "价格解构（基建）": "💰",               # 价格解构表的专属图标
    "单价计算（基建）": "💻",          # 单价计算表的专属图标
    "价格热度计算（基建）": "🔥",                # 价格热度计算表的专属图标
    "中转表（工具）": "🤖｜✍️",         # 中转表的专属图标
    "源信息": ["🍬", "🥝"]           # 原始数据的标记图标（爬虫获取的信息）
}


# --------------------------
# 3. 图标与MongoDB集合名的映射
# 用途：定义每个图标对应的数据库集合名（相当于“图标→存储位置”的对照表）
# 格式："图标": "集合名"（集合名是数据库中实际存储数据的地方）
# --------------------------
ICON_TO_COLLECTION = {
    "🧠": "logic_table",                  # 逻辑表在数据库中的集合名
    "📁｜✍️": "info_table",                # 信息获取表在数据库中的集合名
    "🎫": "coupon_table",                 # 优惠券管理表在数据库中的集合名
    "Ⓜ️｜✍️": "brand_table",               # 品牌识别表在数据库中的集合名
    "🛍️｜✍️": "category_table",            # 品类表在数据库中的集合名
    "🔪｜✍️": "sku_deconstruct_table",      # sku解构表在数据库中的集合名
    "🐤": "sku_recombine_web_table",       # 网站专用sku重组表在数据库中的集合名
    "🐽": "sku_recombine_group_table",     # 社群专用sku重组表在数据库中的集合名
    "🧪": "spec_deconstruct_table",        # 规格解构表在数据库中的集合名
    "💰": "price_deconstruct_table",       # 价格解构表在数据库中的集合名
    "💻": "unit_price_table",              # 单价计算表在数据库中的集合名
    "🔥": "price_heat_table",              # 价格热度计算表在数据库中的集合名
    "🤖｜✍️": "transfer_table"              # 中转表在数据库中的集合名
}


"""
=============================
配置文件维护手册（修改规则说明）
=============================

1. 更换数据库地址/端口/账号
--------------------------
场景：数据库从本地迁移到服务器，或账号密码变更
操作：
- 找到 MONGO_CONFIG 中的对应字段修改：
  - 改地址：修改 "host" 的值（如从 localhost 改为 *************）
  - 改端口：修改 "port" 的值（如从 27017 改为 30017）
  - 改账号：修改 "user"、"pwd"、"auth_source" 的值
效果：整个项目的所有代码会自动使用新配置，无需修改其他文件


2. 新增表格（如“促销活动表”）
--------------------------
场景：需要添加一个新的表格来管理促销信息
操作：
1. 给新表格选一个专属图标（如 🎁）
2. 在 FIELD_MARKERS 中添加一行：
   "promotion": "🎁"  # 左边是表格简称，右边是图标
3. 在 ICON_TO_COLLECTION 中添加一行：
   "🎁": "promotion_table"  # 左边是图标，右边是数据库集合名
效果：新表格会自动遵循项目的命名规则，与其他表格保持一致


3. 修改表格的数据库集合名
--------------------------
场景：想把 info_table 改成 info_main_table（更贴合业务）
操作：
- 在 ICON_TO_COLLECTION 中找到 "📁｜✍️": "info_table"
- 把右边的值改成 "info_main_table"
效果：所有用到信息获取表的代码（如 info_table_model.py）会自动读取新集合名，无需修改模型文件


4. 修改表格的专属图标
--------------------------
场景：想把品牌识别表的图标从 Ⓜ️｜✍️ 改成 🏷️｜✍️（更直观）
操作：
1. 在 FIELD_MARKERS 中找到 "brand": "Ⓜ️｜✍️"，改成 "brand": "🏷️｜✍️"
2. 在 ICON_TO_COLLECTION 中找到 "Ⓜ️｜✍️": "brand_table"，改成 "🏷️｜✍️": "brand_table"
效果：整个项目中，品牌识别表的所有字段会自动使用新图标，保持统一
"""
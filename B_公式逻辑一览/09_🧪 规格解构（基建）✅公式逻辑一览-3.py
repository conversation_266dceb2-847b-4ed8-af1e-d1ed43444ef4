🧪🤍初级数量-数字提取（7-9）
IF(ISBLANK([🍬商品信息（原始版）]),""，

CONCATENATE(

IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(1)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❼1❼🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(1)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❼$0❼🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(2)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❼1❼🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(2)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❼$0❼🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(3)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❼1❼🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(3)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❼$0❼🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(4)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❼1❼🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(4)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❼$0❼🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(5)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❼1❼🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(5)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❼$0❼🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(6)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❼1❼🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(6)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❼$0❼🔞"))


,CHAR(10),

IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(1)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❽1❽🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(1)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❽$0❽🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(2)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❽1❽🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(2)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❽$0❽🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(3)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❽1❽🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(3)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❽$0❽🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(4)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❽1❽🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(4)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❽$0❽🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(5)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❽1❽🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(5)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❽$0❽🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(6)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❽1❽🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(6)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❽$0❽🔞"))





,CHAR(10),

IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(1)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❾1❾🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(1)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❾$0❾🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(2)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❾1❾🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(2)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❾$0❾🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(3)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❾1❾🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(3)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❾$0❾🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(4)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❾1❾🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(4)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❾$0❾🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(5)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❾1❾🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(5)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❾$0❾🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(6)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❾1❾🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(6)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❾$0❾🔞"))


))

.REGEXREPLACE("；?\d+(?:\.\d+)?("&[🧠基础单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&"|"&[🧠终极单位]&")；?","")

.REGEXREPLACE("("&[🧠单位合集]&")","")

.REGEXREPLACE("([❶-❾])"&[🧠乘法符号],"$1")
.REGEXREPLACE([🧠乘法符号]&"([❶-❾])","$1")

.REGEXREPLACE("\*\d+(?:\.\d+)?","")


.REGEXREPLACE("([❶-❾])([❶-❾]🔞)","${1}1${2}")
.REGEXREPLACE("([❶-❾]🔞)([❶-❾])","$1；$2")

.REGEXREPLACE("[;；,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🧪🤍初级数量-数字提取（终版）
CONCATENATE(
[🧪🤍初级数量-数字提取（1-6）]
，CHAR(10)，
[🧪🤍初级数量-数字提取（7-9）]

)

.REGEXREPLACE("[,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🧪🤍初级数量（清洗版+终版）
CONCATENATE(
IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACT("❶.*?❶🔞")
.REGEXMATCH("礼包")
,"",
CONCATENATE(
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❶.*🔞")
.SPLIT("；")
.NTH(1)
,
[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❶.*🔞")
.SPLIT("；")
.NTH(1)
，
"；"
，
CONCATENATE(
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❶.*🔞")
.SPLIT("；")
.NTH(2)
,
[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❶.*🔞")
.SPLIT("；")
.NTH(2)
，
"；"
，
CONCATENATE(
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❶.*🔞")
.SPLIT("；")
.NTH(3)
,
[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❶.*🔞")
.SPLIT("；")
.NTH(3)
，
"；"
，
CONCATENATE(
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❶.*🔞")
.SPLIT("；")
.NTH(4)
,
[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❶.*🔞")
.SPLIT("；")
.NTH(4)
，
"；"
，
CONCATENATE(
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❶.*🔞")
.SPLIT("；")
.NTH(5)
,
[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❶.*🔞")
.SPLIT("；")
.NTH(5)
，
"；"
，
CONCATENATE(
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❶.*🔞")
.SPLIT("；")
.NTH(6)
,
[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❶.*🔞")
.SPLIT("；")
.NTH(6)
)))))))


，CHAR(10)，


IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACT("❷.*?❷🔞")
.REGEXMATCH("礼包"),
"",
CONCATENATE(
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❷.*🔞")
.SPLIT("；")
.NTH(1),
[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❷.*🔞")
.SPLIT("；")
.NTH(1),
"；",
CONCATENATE(
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❷.*🔞")
.SPLIT("；")
.NTH(2),
[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❷.*🔞")
.SPLIT("；")
.NTH(2),
"；",
CONCATENATE(
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❷.*🔞")
.SPLIT("；")
.NTH(3),
[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❷.*🔞")
.SPLIT("；")
.NTH(3),
"；",
CONCATENATE(
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❷.*🔞")
.SPLIT("；")
.NTH(4),
[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❷.*🔞")
.SPLIT("；")
.NTH(4),
"；",
CONCATENATE(
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❷.*🔞")
.SPLIT("；")
.NTH(5),
[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❷.*🔞")
.SPLIT("；")
.NTH(5),
"；",
CONCATENATE(
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❷.*🔞")
.SPLIT("；")
.NTH(6),
[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❷.*🔞")
.SPLIT("；")
.NTH(6)
)))))))


，CHAR(10)，


IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACT("❸.*?❸🔞")
.REGEXMATCH("礼包"),
"",
CONCATENATE(
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❸.*🔞")
.SPLIT("；")
.NTH(1),
[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❸.*🔞")
.SPLIT("；")
.NTH(1),
"；",
CONCATENATE(
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❸.*🔞")
.SPLIT("；")
.NTH(2),
[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❸.*🔞")
.SPLIT("；")
.NTH(2),
"；",
CONCATENATE(
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❸.*🔞")
.SPLIT("；")
.NTH(3),
[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❸.*🔞")
.SPLIT("；")
.NTH(3),
"；",
CONCATENATE(
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❸.*🔞")
.SPLIT("；")
.NTH(4),
[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❸.*🔞")
.SPLIT("；")
.NTH(4),
"；",
CONCATENATE(
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❸.*🔞")
.SPLIT("；")
.NTH(5),
[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❸.*🔞")
.SPLIT("；")
.NTH(5),
"；",
CONCATENATE(
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❸.*🔞")
.SPLIT("；")
.NTH(6),
[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❸.*🔞")
.SPLIT("；")
.NTH(6)
)))))))


，CHAR(10)，




IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACT("❹.*?❹🔞")
.REGEXMATCH("礼包"),
"",
CONCATENATE(
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❹.*🔞")
.SPLIT("；")
.NTH(1),
[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❹.*🔞")
.SPLIT("；")
.NTH(1),
"；",
CONCATENATE(
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❹.*🔞")
.SPLIT("；")
.NTH(2),
[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❹.*🔞")
.SPLIT("；")
.NTH(2),
"；",
CONCATENATE(
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❹.*🔞")
.SPLIT("；")
.NTH(3),
[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❹.*🔞")
.SPLIT("；")
.NTH(3),
"；",
CONCATENATE(
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❹.*🔞")
.SPLIT("；")
.NTH(4),
[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❹.*🔞")
.SPLIT("；")
.NTH(4),
"；",
CONCATENATE(
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❹.*🔞")
.SPLIT("；")
.NTH(5),
[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❹.*🔞")
.SPLIT("；")
.NTH(5),
"；",
CONCATENATE(
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❹.*🔞")
.SPLIT("；")
.NTH(6),
[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❹.*🔞")
.SPLIT("；")
.NTH(6)
)))))))



，CHAR(10)，



IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACT("❺.*?❺🔞")
.REGEXMATCH("礼包"),
"",
CONCATENATE(
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❺.*🔞")
.SPLIT("；")
.NTH(1),
[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❺.*🔞")
.SPLIT("；")
.NTH(1),
"；",
CONCATENATE(
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❺.*🔞")
.SPLIT("；")
.NTH(2),
[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❺.*🔞")
.SPLIT("；")
.NTH(2),
"；",
CONCATENATE(
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❺.*🔞")
.SPLIT("；")
.NTH(3),
[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❺.*🔞")
.SPLIT("；")
.NTH(3),
"；",
CONCATENATE(
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❺.*🔞")
.SPLIT("；")
.NTH(4),
[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❺.*🔞")
.SPLIT("；")
.NTH(4),
"；",
CONCATENATE(
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❺.*🔞")
.SPLIT("；")
.NTH(5),
[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❺.*🔞")
.SPLIT("；")
.NTH(5),
"；",
CONCATENATE(
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❺.*🔞")
.SPLIT("；")
.NTH(6),
[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❺.*🔞")
.SPLIT("；")
.NTH(6)
)))))))




，CHAR(10)，




IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACT("❻.*?❻🔞")
.REGEXMATCH("礼包"),
"",
CONCATENATE(
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❻.*🔞")
.SPLIT("；")
.NTH(1),
[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❻.*🔞")
.SPLIT("；")
.NTH(1),
"；",
CONCATENATE(
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❻.*🔞")
.SPLIT("；")
.NTH(2),
[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❻.*🔞")
.SPLIT("；")
.NTH(2),
"；",
CONCATENATE(
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❻.*🔞")
.SPLIT("；")
.NTH(3),
[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❻.*🔞")
.SPLIT("；")
.NTH(3),
"；",
CONCATENATE(
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❻.*🔞")
.SPLIT("；")
.NTH(4),
[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❻.*🔞")
.SPLIT("；")
.NTH(4),
"；",
CONCATENATE(
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❻.*🔞")
.SPLIT("；")
.NTH(5),
[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❻.*🔞")
.SPLIT("；")
.NTH(5),
"；",
CONCATENATE(
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❻.*🔞")
.SPLIT("；")
.NTH(6),
[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❻.*🔞")
.SPLIT("；")
.NTH(6)
)))))))


,CHAR(10),




IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACT("❼.*?❼🔞")
.REGEXMATCH("礼包"),
"",
CONCATENATE(
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❼.*🔞")
.SPLIT("；")
.NTH(1),
[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❼.*🔞")
.SPLIT("；")
.NTH(1),
"；",
CONCATENATE(
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❼.*🔞")
.SPLIT("；")
.NTH(2),
[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❼.*🔞")
.SPLIT("；")
.NTH(2),
"；",
CONCATENATE(
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❼.*🔞")
.SPLIT("；")
.NTH(3),
[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❼.*🔞")
.SPLIT("；")
.NTH(3),
"；",
CONCATENATE(
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❼.*🔞")
.SPLIT("；")
.NTH(4),
[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❼.*🔞")
.SPLIT("；")
.NTH(4),
"；",
CONCATENATE(
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❼.*🔞")
.SPLIT("；")
.NTH(5),
[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❼.*🔞")
.SPLIT("；")
.NTH(5),
"；",
CONCATENATE(
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❼.*🔞")
.SPLIT("；")
.NTH(6),
[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❼.*🔞")
.SPLIT("；")
.NTH(6)
)))))))


,CHAR(10),




IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACT("❽.*?❽🔞")
.REGEXMATCH("礼包"),
"",
CONCATENATE(
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❽.*🔞")
.SPLIT("；")
.NTH(1),
[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❽.*🔞")
.SPLIT("；")
.NTH(1),
"；",
CONCATENATE(
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❽.*🔞")
.SPLIT("；")
.NTH(2),
[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❽.*🔞")
.SPLIT("；")
.NTH(2),
"；",
CONCATENATE(
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❽.*🔞")
.SPLIT("；")
.NTH(3),
[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❽.*🔞")
.SPLIT("；")
.NTH(3),
"；",
CONCATENATE(
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❽.*🔞")
.SPLIT("；")
.NTH(4),
[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❽.*🔞")
.SPLIT("；")
.NTH(4),
"；",
CONCATENATE(
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❽.*🔞")
.SPLIT("；")
.NTH(5),
[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❽.*🔞")
.SPLIT("；")
.NTH(5),
"；",
CONCATENATE(
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❽.*🔞")
.SPLIT("；")
.NTH(6),
[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❽.*🔞")
.SPLIT("；")
.NTH(6)
)))))))


,CHAR(10),




IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACT("❾.*?❾🔞")
.REGEXMATCH("礼包"),
"",
CONCATENATE(
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❾.*🔞")
.SPLIT("；")
.NTH(1),
[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❾.*🔞")
.SPLIT("；")
.NTH(1),
"；",
CONCATENATE(
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❾.*🔞")
.SPLIT("；")
.NTH(2),
[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❾.*🔞")
.SPLIT("；")
.NTH(2),
"；",
CONCATENATE(
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❾.*🔞")
.SPLIT("；")
.NTH(3),
[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❾.*🔞")
.SPLIT("；")
.NTH(3),
"；",
CONCATENATE(
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❾.*🔞")
.SPLIT("；")
.NTH(4),
[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❾.*🔞")
.SPLIT("；")
.NTH(4),
"；",
CONCATENATE(
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❾.*🔞")
.SPLIT("；")
.NTH(5),
[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❾.*🔞")
.SPLIT("；")
.NTH(5),
"；",
CONCATENATE(
[🧪🤍初级数量-数字提取（终版）]
.REGEXEXTRACTALL("❾.*🔞")
.SPLIT("；")
.NTH(6),
[🧪🤍初级数量-单位提取（终版）]
.REGEXEXTRACTALL("❾.*🔞")
.SPLIT("；")
.NTH(6)
)))))))


)



.REGEXREPLACE("；+","；")
.REGEXREPLACE("^；","")
.REGEXREPLACE("
；","")

.REGEXREPLACE("[❶-❾]🔞[❶-❾]","")
.REGEXREPLACE("[❶-❾](?:"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")[❶-❾]🔞；?","")
.REGEXREPLACE("[❶-❾]\d+(?:\.\d+)?[❶-❾]🔞；?","")

.REGEXREPLACE("[;；,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🧪🤍进阶数量（原始版）
CONCATENATE(
[🧪🔥🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❶.*❶🔞")
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ARRAYJOIN("；")
.REGEXREPLACE([🧠规格提取参考-逻辑], "❶$0❶🔞")


,CHAR(10),



[🧪🔥🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❷.*❷🔞")
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ARRAYJOIN("；")
.REGEXREPLACE([🧠规格提取参考-逻辑], "❷$0❷🔞")



,CHAR(10),


[🧪🔥🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❸.*❸🔞")
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ARRAYJOIN("；")
.REGEXREPLACE([🧠规格提取参考-逻辑], "❸$0❸🔞")


,CHAR(10),



[🧪🔥🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❹.*❹🔞")
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ARRAYJOIN("；")
.REGEXREPLACE([🧠规格提取参考-逻辑], "❹$0❹🔞")



,CHAR(10),



[🧪🔥🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❺.*❺🔞")
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ARRAYJOIN("；")
.REGEXREPLACE([🧠规格提取参考-逻辑], "❺$0❺🔞")



,CHAR(10),


[🧪🔥🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❻.*❻🔞")
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ARRAYJOIN("；")
.REGEXREPLACE([🧠规格提取参考-逻辑], "❻$0❻🔞")



,CHAR(10),


[🧪🔥🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❼.*❼🔞")
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ARRAYJOIN("；")
.REGEXREPLACE([🧠规格提取参考-逻辑], "❼$0❼🔞")



,CHAR(10),



[🧪🔥🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❽.*❽🔞")
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ARRAYJOIN("；")
.REGEXREPLACE([🧠规格提取参考-逻辑], "❽$0❽🔞")



，CHAR(10)，


[🧪🔥🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❾.*❾🔞")
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ARRAYJOIN("；")
.REGEXREPLACE([🧠规格提取参考-逻辑], "❾$0❾🔞")


)


.REGEXREPLACE("🎁","")
.REGEXREPLACE("[；*]?\*?\d+(?:\.\d+)?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠高阶单位]&"|"&[🧠终极单位]&")；?","")

.REGEXREPLACE("(\d+"&[🧠进阶单位]&")(?:\*.*?)*([❶-❾]🔞)","$1$2")



.REGEXREPLACE("([❶-❾])"&[🧠乘法符号],"$1")
.REGEXREPLACE([🧠乘法符号]&"([❶-❾])","$1")


.REGEXREPLACE("[❶-❾][❶-❾]🔞；?","")
.REGEXREPLACE("[;；,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")

.REGEXREPLACE("[❶-❾][❶-❾]🔞；?","")
.REGEXREPLACE("[;；,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🧪🤍进阶数量-单位提取（参考）
[🧪🤍进阶数量（原始版）]
.REGEXREPLACE("\d+(?:\.\d+)?"，"")
.REGEXREPLACE("(❶❶|❷❷|❸❸|❹❹|❺❺|❻❻|❼❼|❽❽|❾❾)🔞；?","")
.REGEXREPLACE("^；","")


.REGEXREPLACE("[;；,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🧪🤍进阶数量-单位提取（终版）
CONCATENATE(

IF([🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACTALL("❶.*❶🔞").ISBLANK(),

CONCATENATE(
IF([🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❶.*❶🔞").REGEXMATCH("主粮|肉干|冻干")
，"❶袋❶🔞；❶袋❶🔞；❶袋❶🔞；❶袋❶🔞；❶袋❶🔞；❶袋❶🔞"
，

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❶.*❶🔞").REGEXMATCH("猫砂|餐包")
，"❶包❶🔞；❶包❶🔞；❶包❶🔞；❶包❶🔞；❶包❶🔞；❶包❶🔞"
，

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❶.*❶🔞").REGEXMATCH("驱虫药|猫条|狗条")
，"❶支❶🔞；❶支❶🔞；❶支❶🔞；❶支❶🔞；❶支❶🔞；❶支❶🔞"
，
IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❶.*❶🔞").REGEXMATCH("罐头")
，"❶罐❶🔞；❶罐❶🔞；❶罐❶🔞；❶罐❶🔞；❶罐❶🔞；❶罐❶🔞"
，

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❶.*❶🔞").REGEXMATCH("餐盒")
，"❶盒❶🔞；❶盒❶🔞；❶盒❶🔞；❶盒❶🔞；❶盒❶🔞；❶盒❶🔞"
，

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❶.*❶🔞").REGEXMATCH("除臭剂|消毒剂")
，"❶瓶❶🔞；❶瓶❶🔞；❶瓶❶🔞；❶瓶❶🔞；❶瓶❶🔞；❶瓶❶🔞"
，""

)))))))
，
CONCATENATE(
[🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACT("❶.*?❶🔞")
,"；"，
[🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACT("❶.*?❶🔞")
,"；"，

[🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACT("❶.*?❶🔞")
,"；"，

[🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACT("❶.*?❶🔞")
,"；"，

[🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACT("❶.*?❶🔞")
,"；"，

[🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACT("❶.*?❶🔞")


))

，CHAR(10)，



IF([🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACTALL("❷.*❷🔞").ISBLANK(),

CONCATENATE(
IF([🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❷.*❷🔞").REGEXMATCH("主粮|肉干|冻干")
,"❷袋❷🔞；❷袋❷🔞；❷袋❷🔞；❷袋❷🔞；❷袋❷🔞；❷袋❷🔞"
,

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❷.*❷🔞").REGEXMATCH("猫砂|餐包")
,"❷包❷🔞；❷包❷🔞；❷包❷🔞；❷包❷🔞；❷包❷🔞；❷包❷🔞"
,

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❷.*❷🔞").REGEXMATCH("驱虫药|猫条|狗条")
,"❷支❷🔞；❷支❷🔞；❷支❷🔞；❷支❷🔞；❷支❷🔞；❷支❷🔞"
,
IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❷.*❷🔞").REGEXMATCH("罐头")
,"❷罐❷🔞；❷罐❷🔞；❷罐❷🔞；❷罐❷🔞；❷罐❷🔞；❷罐❷🔞"
,

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❷.*❷🔞").REGEXMATCH("餐盒")
,"❷盒❷🔞；❷盒❷🔞；❷盒❷🔞；❷盒❷🔞；❷盒❷🔞；❷盒❷🔞"
,

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❷.*❷🔞").REGEXMATCH("除臭剂|消毒剂")
,"❷瓶❷🔞；❷瓶❷🔞；❷瓶❷🔞；❷瓶❷🔞；❷瓶❷🔞；❷瓶❷🔞"
,""

)))))))
,
CONCATENATE(
[🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACT("❷.*?❷🔞")
,"；"，
[🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACT("❷.*?❷🔞")
,"；"，

[🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACT("❷.*?❷🔞")
,"；"，

[🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACT("❷.*?❷🔞")
,"；"，

[🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACT("❷.*?❷🔞")
,"；"，

[🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACT("❷.*?❷🔞")


))






，CHAR(10)，




IF([🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACTALL("❸.*❸🔞").ISBLANK(),

CONCATENATE(
IF([🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❸.*❸🔞").REGEXMATCH("主粮|肉干|冻干")
,"❸袋❸🔞；❸袋❸🔞；❸袋❸🔞；❸袋❸🔞；❸袋❸🔞；❸袋❸🔞"
,

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❸.*❸🔞").REGEXMATCH("猫砂|餐包")
,"❸包❸🔞；❸包❸🔞；❸包❸🔞；❸包❸🔞；❸包❸🔞；❸包❸🔞"
,

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❸.*❸🔞").REGEXMATCH("驱虫药|猫条|狗条")
,"❸支❸🔞；❸支❸🔞；❸支❸🔞；❸支❸🔞；❸支❸🔞；❸支❸🔞"
,
IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❸.*❸🔞").REGEXMATCH("罐头")
,"❸罐❸🔞；❸罐❸🔞；❸罐❸🔞；❸罐❸🔞；❸罐❸🔞；❸罐❸🔞"
,

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❸.*❸🔞").REGEXMATCH("餐盒")
,"❸盒❸🔞；❸盒❸🔞；❸盒❸🔞；❸盒❸🔞；❸盒❸🔞；❸盒❸🔞"
,

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❸.*❸🔞").REGEXMATCH("除臭剂|消毒剂")
,"❸瓶❸🔞；❸瓶❸🔞；❸瓶❸🔞；❸瓶❸🔞；❸瓶❸🔞；❸瓶❸🔞"
,""

)))))))
,
CONCATENATE(
[🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACT("❸.*?❸🔞")
,"；"，
[🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACT("❸.*?❸🔞")
,"；"，

[🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACT("❸.*?❸🔞")
,"；"，

[🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACT("❸.*?❸🔞")
,"；"，

[🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACT("❸.*?❸🔞")
,"；"，

[🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACT("❸.*?❸🔞")


))




，CHAR(10)，



IF([🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACTALL("❹.*❹🔞").ISBLANK(),

CONCATENATE(
IF([🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❹.*❹🔞").REGEXMATCH("主粮|肉干|冻干")
,"❹袋❹🔞；❹袋❹🔞；❹袋❹🔞；❹袋❹🔞；❹袋❹🔞；❹袋❹🔞"
,

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❹.*❹🔞").REGEXMATCH("猫砂|餐包")
,"❹包❹🔞；❹包❹🔞；❹包❹🔞；❹包❹🔞；❹包❹🔞；❹包❹🔞"
,

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❹.*❹🔞").REGEXMATCH("驱虫药|猫条|狗条")
,"❹支❹🔞；❹支❹🔞；❹支❹🔞；❹支❹🔞；❹支❹🔞；❹支❹🔞"
,
IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❹.*❹🔞").REGEXMATCH("罐头")
,"❹罐❹🔞；❹罐❹🔞；❹罐❹🔞；❹罐❹🔞；❹罐❹🔞；❹罐❹🔞"
,

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❹.*❹🔞").REGEXMATCH("餐盒")
,"❹盒❹🔞；❹盒❹🔞；❹盒❹🔞；❹盒❹🔞；❹盒❹🔞；❹盒❹🔞"
,

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❹.*❹🔞").REGEXMATCH("除臭剂|消毒剂")
,"❹瓶❹🔞；❹瓶❹🔞；❹瓶❹🔞；❹瓶❹🔞；❹瓶❹🔞；❹瓶❹🔞"
,""

)))))))
,
CONCATENATE(
[🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACT("❹.*?❹🔞")
,"；"，
[🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACT("❹.*?❹🔞")
,"；"，

[🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACT("❹.*?❹🔞")
,"；"，

[🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACT("❹.*?❹🔞")
,"；"，

[🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACT("❹.*?❹🔞")
,"；"，

[🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACT("❹.*?❹🔞")


))





，CHAR(10)，




IF([🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACTALL("❺.*❺🔞").ISBLANK(),

CONCATENATE(
IF([🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❺.*❺🔞").REGEXMATCH("主粮|肉干|冻干")
,"❺袋❺🔞；❺袋❺🔞；❺袋❺🔞；❺袋❺🔞；❺袋❺🔞；❺袋❺🔞"
,

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❺.*❺🔞").REGEXMATCH("猫砂|餐包")
,"❺包❺🔞；❺包❺🔞；❺包❺🔞；❺包❺🔞；❺包❺🔞；❺包❺🔞"
,

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❺.*❺🔞").REGEXMATCH("驱虫药|猫条|狗条")
,"❺支❺🔞；❺支❺🔞；❺支❺🔞；❺支❺🔞；❺支❺🔞；❺支❺🔞"
,
IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❺.*❺🔞").REGEXMATCH("罐头")
,"❺罐❺🔞；❺罐❺🔞；❺罐❺🔞；❺罐❺🔞；❺罐❺🔞；❺罐❺🔞"
,

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❺.*❺🔞").REGEXMATCH("餐盒")
,"❺盒❺🔞；❺盒❺🔞；❺盒❺🔞；❺盒❺🔞；❺盒❺🔞；❺盒❺🔞"
,

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❺.*❺🔞").REGEXMATCH("除臭剂|消毒剂")
,"❺瓶❺🔞；❺瓶❺🔞；❺瓶❺🔞；❺瓶❺🔞；❺瓶❺🔞；❺瓶❺🔞"
,""

)))))))
,
CONCATENATE(
[🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACT("❺.*?❺🔞")
,"；"，
[🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACT("❺.*?❺🔞")
,"；"，

[🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACT("❺.*?❺🔞")
,"；"，

[🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACT("❺.*?❺🔞")
,"；"，

[🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACT("❺.*?❺🔞")
,"；"，

[🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACT("❺.*?❺🔞")


))





，CHAR(10)，




IF([🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACTALL("❻.*❻🔞").ISBLANK(),

CONCATENATE(
IF([🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❻.*❻🔞").REGEXMATCH("主粮|肉干|冻干")
,"❻袋❻🔞；❻袋❻🔞；❻袋❻🔞；❻袋❻🔞；❻袋❻🔞；❻袋❻🔞"
,

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❻.*❻🔞").REGEXMATCH("猫砂|餐包")
,"❻包❻🔞；❻包❻🔞；❻包❻🔞；❻包❻🔞；❻包❻🔞；❻包❻🔞"
,

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❻.*❻🔞").REGEXMATCH("驱虫药|猫条|狗条")
,"❻支❻🔞；❻支❻🔞；❻支❻🔞；❻支❻🔞；❻支❻🔞；❻支❻🔞"
,
IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❻.*❻🔞").REGEXMATCH("罐头")
,"❻罐❻🔞；❻罐❻🔞；❻罐❻🔞；❻罐❻🔞；❻罐❻🔞；❻罐❻🔞"
,

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❻.*❻🔞").REGEXMATCH("餐盒")
,"❻盒❻🔞；❻盒❻🔞；❻盒❻🔞；❻盒❻🔞；❻盒❻🔞；❻盒❻🔞"
,

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❻.*❻🔞").REGEXMATCH("除臭剂|消毒剂")
,"❻瓶❻🔞；❻瓶❻🔞；❻瓶❻🔞；❻瓶❻🔞；❻瓶❻🔞；❻瓶❻🔞"
,""

)))))))
,
CONCATENATE(
[🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACT("❻.*?❻🔞")
,"；"，
[🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACT("❻.*?❻🔞")
,"；"，

[🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACT("❻.*?❻🔞")
,"；"，

[🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACT("❻.*?❻🔞")
,"；"，

[🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACT("❻.*?❻🔞")
,"；"，

[🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACT("❻.*?❻🔞")


))




，CHAR(10)，




IF([🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACTALL("❼.*❼🔞").ISBLANK(),

CONCATENATE(
IF([🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❼.*❼🔞").REGEXMATCH("主粮|肉干|冻干")
,"❼袋❼🔞；❼袋❼🔞；❼袋❼🔞；❼袋❼🔞；❼袋❼🔞；❼袋❼🔞"
,

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❼.*❼🔞").REGEXMATCH("猫砂|餐包")
,"❼包❼🔞；❼包❼🔞；❼包❼🔞；❼包❼🔞；❼包❼🔞；❼包❼🔞"
,

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❼.*❼🔞").REGEXMATCH("驱虫药|猫条|狗条")
,"❼支❼🔞；❼支❼🔞；❼支❼🔞；❼支❼🔞；❼支❼🔞；❼支❼🔞"
,
IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❼.*❼🔞").REGEXMATCH("罐头")
,"❼罐❼🔞；❼罐❼🔞；❼罐❼🔞；❼罐❼🔞；❼罐❼🔞；❼罐❼🔞"
,

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❼.*❼🔞").REGEXMATCH("餐盒")
,"❼盒❼🔞；❼盒❼🔞；❼盒❼🔞；❼盒❼🔞；❼盒❼🔞；❼盒❼🔞"
,

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❼.*❼🔞").REGEXMATCH("除臭剂|消毒剂")
,"❼瓶❼🔞；❼瓶❼🔞；❼瓶❼🔞；❼瓶❼🔞；❼瓶❼🔞；❼瓶❼🔞"
,""

)))))))
,
CONCATENATE(
[🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACT("❼.*?❼🔞")
,"；"，
[🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACT("❼.*?❼🔞")
,"；"，

[🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACT("❼.*?❼🔞")
,"；"，

[🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACT("❼.*?❼🔞")
,"；"，

[🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACT("❼.*?❼🔞")
,"；"，

[🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACT("❼.*?❼🔞")


))




，CHAR(10)，





IF([🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACTALL("❽.*❽🔞").ISBLANK(),

CONCATENATE(
IF([🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❽.*❽🔞").REGEXMATCH("主粮|肉干|冻干")
,"❽袋❽🔞；❽袋❽🔞；❽袋❽🔞；❽袋❽🔞；❽袋❽🔞；❽袋❽🔞"
,

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❽.*❽🔞").REGEXMATCH("猫砂|餐包")
,"❽包❽🔞；❽包❽🔞；❽包❽🔞；❽包❽🔞；❽包❽🔞；❽包❽🔞"
,

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❽.*❽🔞").REGEXMATCH("驱虫药|猫条|狗条")
,"❽支❽🔞；❽支❽🔞；❽支❽🔞；❽支❽🔞；❽支❽🔞；❽支❽🔞"
,
IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❽.*❽🔞").REGEXMATCH("罐头")
,"❽罐❽🔞；❽罐❽🔞；❽罐❽🔞；❽罐❽🔞；❽罐❽🔞；❽罐❽🔞"
,

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❽.*❽🔞").REGEXMATCH("餐盒")
,"❽盒❽🔞；❽盒❽🔞；❽盒❽🔞；❽盒❽🔞；❽盒❽🔞；❽盒❽🔞"
,

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❽.*❽🔞").REGEXMATCH("除臭剂|消毒剂")
,"❽瓶❽🔞；❽瓶❽🔞；❽瓶❽🔞；❽瓶❽🔞；❽瓶❽🔞；❽瓶❽🔞"
,""

)))))))
,
CONCATENATE(
[🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACT("❽.*?❽🔞")
,"；"，
[🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACT("❽.*?❽🔞")
,"；"，

[🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACT("❽.*?❽🔞")
,"；"，

[🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACT("❽.*?❽🔞")
,"；"，

[🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACT("❽.*?❽🔞")
,"；"，

[🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACT("❽.*?❽🔞")


))


,CHAR(10),






IF([🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACTALL("❾.*❾🔞").ISBLANK(),

CONCATENATE(
IF([🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❾.*❾🔞").REGEXMATCH("主粮|肉干|冻干")
,"❾袋❾🔞；❾袋❾🔞；❾袋❾🔞；❾袋❾🔞；❾袋❾🔞；❾袋❾🔞"
,

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❾.*❾🔞").REGEXMATCH("猫砂|餐包")
,"❾包❾🔞；❾包❾🔞；❾包❾🔞；❾包❾🔞；❾包❾🔞；❾包❾🔞"
,

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❾.*❾🔞").REGEXMATCH("驱虫药|猫条|狗条")
,"❾支❾🔞；❾支❾🔞；❾支❾🔞；❾支❾🔞；❾支❾🔞；❾支❾🔞"
,
IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❾.*❾🔞").REGEXMATCH("罐头")
,"❾罐❾🔞；❾罐❾🔞；❾罐❾🔞；❾罐❾🔞；❾罐❾🔞；❾罐❾🔞"
,

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❾.*❾🔞").REGEXMATCH("餐盒")
,"❾盒❾🔞；❾盒❾🔞；❾盒❾🔞；❾盒❾🔞；❾盒❾🔞；❾盒❾🔞"
,

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❾.*❾🔞").REGEXMATCH("除臭剂|消毒剂")
,"❾瓶❾🔞；❾瓶❾🔞；❾瓶❾🔞；❾瓶❾🔞；❾瓶❾🔞；❾瓶❾🔞"
,""

)))))))
,
CONCATENATE(
[🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACT("❾.*?❾🔞")
,"；"，
[🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACT("❾.*?❾🔞")
,"；"，

[🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACT("❾.*?❾🔞")
,"；"，

[🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACT("❾.*?❾🔞")
,"；"，

[🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACT("❾.*?❾🔞")
,"；"，

[🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACT("❾.*?❾🔞")


))





)




.REGEXREPLACE("FALSE","")
.REGEXREPLACE("[❶-❾]("&[🧠初级单位]&"|"&[🧠高阶单位]&")[❶-❾]🔞；?","")


.REGEXREPLACE("[;；,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🧪🤍进阶数量-数字提取（1-6）
IF(ISBLANK([🍬商品信息（原始版）]),""，

CONCATENATE(

IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(1)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❶1❶🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(1)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❶$0❶🔞"))

，


IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(2)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❶1❶🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(2)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❶$0❶🔞"))

,

IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(3)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❶1❶🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(3)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❶$0❶🔞"))


,


IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(4)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❶1❶🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(4)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❶$0❶🔞"))



,


IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(5)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❶1❶🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(5)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❶$0❶🔞"))


,



IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(6)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❶1❶🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(6)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❶$0❶🔞"))


,CHAR(10)，

IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(1)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❷1❷🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(1)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❷$0❷🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(2)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❷1❷🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(2)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❷$0❷🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(3)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❷1❷🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(3)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❷$0❷🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(4)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❷1❷🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(4)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❷$0❷🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(5)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❷1❷🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(5)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❷$0❷🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(6)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❷1❷🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(6)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❷$0❷🔞"))



,CHAR(10),




IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(1)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❸1❸🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(1)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❸$0❸🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(2)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❸1❸🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(2)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❸$0❸🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(3)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❸1❸🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(3)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❸$0❸🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(4)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❸1❸🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(4)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❸$0❸🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(5)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❸1❸🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(5)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❸$0❸🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(6)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❸1❸🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(6)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❸$0❸🔞"))


,CHAR(10),


IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(1)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❹1❹🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(1)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❹$0❹🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(2)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❹1❹🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(2)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❹$0❹🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(3)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❹1❹🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(3)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❹$0❹🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(4)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❹1❹🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(4)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❹$0❹🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(5)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❹1❹🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(5)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❹$0❹🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(6)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❹1❹🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(6)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❹$0❹🔞"))


,CHAR(10),




IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(1)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❺1❺🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(1)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❺$0❺🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(2)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❺1❺🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(2)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❺$0❺🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(3)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❺1❺🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(3)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❺$0❺🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(4)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❺1❺🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(4)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❺$0❺🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(5)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❺1❺🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(5)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❺$0❺🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(6)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❺1❺🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(6)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❺$0❺🔞"))


,CHAR(10),

IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(1)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❻1❻🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(1)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❻$0❻🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(2)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❻1❻🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(2)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❻$0❻🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(3)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❻1❻🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(3)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❻$0❻🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(4)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❻1❻🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(4)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❻$0❻🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(5)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❻1❻🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(5)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❻$0❻🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(6)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❻1❻🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(6)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❻$0❻🔞"))



))

.REGEXREPLACE("；?\d+(?:\.\d+)?(?:"&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠高阶单位]&"|"&[🧠终极单位]&")；?","")
.REGEXREPLACE("("&[🧠单位合集]&")","")

.REGEXREPLACE("([❶-❾])"&[🧠乘法符号],"$1")
.REGEXREPLACE([🧠乘法符号]&"([❶-❾])","$1")

.REGEXREPLACE("\*\d+(?:\.\d+)?","")

.REGEXREPLACE("([❶-❾])([❶-❾]🔞)","${1}1${2}")
.REGEXREPLACE("([❶-❾]🔞)([❶-❾])","$1；$2")

.REGEXREPLACE("[;；,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🧪🤍进阶数量-数字提取（7-9）
IF(ISBLANK([🍬商品信息（原始版）]),""，

CONCATENATE(

IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(1)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❼1❼🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(1)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❼$0❼🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(2)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❼1❼🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(2)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❼$0❼🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(3)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❼1❼🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(3)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❼$0❼🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(4)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❼1❼🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(4)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❼$0❼🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(5)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❼1❼🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(5)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❼$0❼🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(6)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❼1❼🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(6)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❼$0❼🔞"))


,CHAR(10),

IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(1)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❽1❽🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(1)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❽$0❽🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(2)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❽1❽🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(2)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❽$0❽🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(3)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❽1❽🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(3)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❽$0❽🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(4)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❽1❽🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(4)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❽$0❽🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(5)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❽1❽🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(5)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❽$0❽🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(6)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❽1❽🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(6)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❽$0❽🔞"))





,CHAR(10),

IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(1)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❾1❾🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(1)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❾$0❾🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(2)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❾1❾🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(2)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❾$0❾🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(3)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❾1❾🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(3)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❾$0❾🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(4)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❾1❾🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(4)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❾$0❾🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(5)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❾1❾🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(5)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❾$0❾🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(6)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❾1❾🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(6)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❾$0❾🔞"))


))

.REGEXREPLACE("；?\d+(?:\.\d+)?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠高阶单位]&"|"&[🧠终极单位]&")；?","")

.REGEXREPLACE("("&[🧠单位合集]&")","")

.REGEXREPLACE("([❶-❾])"&[🧠乘法符号],"$1")
.REGEXREPLACE([🧠乘法符号]&"([❶-❾])","$1")

.REGEXREPLACE("\*\d+(?:\.\d+)?","")

.REGEXREPLACE("([❶-❾])([❶-❾]🔞)","${1}1${2}")
.REGEXREPLACE("([❶-❾]🔞)([❶-❾])","$1；$2")

.REGEXREPLACE("[;；,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🧪🤍进阶数量-数字提取（终版）
CONCATENATE(
[🧪🤍进阶数量-数字提取（1-6）]
，CHAR(10)，
[🧪🤍进阶数量-数字提取（7-9）]
)

.REGEXREPLACE("[,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🧪🤍进阶数量（清洗版+终版）
CONCATENATE(
IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACT("❶.*?❶🔞")
.REGEXMATCH("礼包")
,"",
CONCATENATE(
[🧪🤍进阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❶.*🔞")
.SPLIT("；")
.NTH(1)
,
[🧪🤍进阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❶.*🔞")
.SPLIT("；")
.NTH(1)
,"；"
,
CONCATENATE(
[🧪🤍进阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❶.*🔞")
.SPLIT("；")
.NTH(2)
,
[🧪🤍进阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❶.*🔞")
.SPLIT("；")
.NTH(2)
,"；"
,
CONCATENATE(
[🧪🤍进阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❶.*🔞")
.SPLIT("；")
.NTH(3)
,
[🧪🤍进阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❶.*🔞")
.SPLIT("；")
.NTH(3)
,"；"
,
CONCATENATE(
[🧪🤍进阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❶.*🔞")
.SPLIT("；")
.NTH(4)
,
[🧪🤍进阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❶.*🔞")
.SPLIT("；")
.NTH(4)
,"；"
,
CONCATENATE(
[🧪🤍进阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❶.*🔞")
.SPLIT("；")
.NTH(5)
,
[🧪🤍进阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❶.*🔞")
.SPLIT("；")
.NTH(5)
,"；"
,
CONCATENATE(
[🧪🤍进阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❶.*🔞")
.SPLIT("；")
.NTH(6)
,
[🧪🤍进阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❶.*🔞")
.SPLIT("；")
.NTH(6)
)))))))


，CHAR(10)，


IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACT("❷.*?❷🔞")
.REGEXMATCH("礼包")
,"",
CONCATENATE(
[🧪🤍进阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❷.*🔞")
.SPLIT("；")
.NTH(1)
,
[🧪🤍进阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❷.*🔞")
.SPLIT("；")
.NTH(1)
,"；"
,
CONCATENATE(
[🧪🤍进阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❷.*🔞")
.SPLIT("；")
.NTH(2)
,
[🧪🤍进阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❷.*🔞")
.SPLIT("；")
.NTH(2)
,"；"
,
CONCATENATE(
[🧪🤍进阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❷.*🔞")
.SPLIT("；")
.NTH(3)
,
[🧪🤍进阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❷.*🔞")
.SPLIT("；")
.NTH(3)
,"；"
,
CONCATENATE(
[🧪🤍进阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❷.*🔞")
.SPLIT("；")
.NTH(4)
,
[🧪🤍进阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❷.*🔞")
.SPLIT("；")
.NTH(4)
,"；"
,
CONCATENATE(
[🧪🤍进阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❷.*🔞")
.SPLIT("；")
.NTH(5)
,
[🧪🤍进阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❷.*🔞")
.SPLIT("；")
.NTH(5)
,"；"
,
CONCATENATE(
[🧪🤍进阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❷.*🔞")
.SPLIT("；")
.NTH(6)
,
[🧪🤍进阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❷.*🔞")
.SPLIT("；")
.NTH(6)
)))))))


，CHAR(10)，



IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACT("❸.*?❸🔞")
.REGEXMATCH("礼包")
,"",
CONCATENATE(
[🧪🤍进阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❸.*🔞")
.SPLIT("；")
.NTH(1)
,
[🧪🤍进阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❸.*🔞")
.SPLIT("；")
.NTH(1)
,"；"
,
CONCATENATE(
[🧪🤍进阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❸.*🔞")
.SPLIT("；")
.NTH(2)
,
[🧪🤍进阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❸.*🔞")
.SPLIT("；")
.NTH(2)
,"；"
,
CONCATENATE(
[🧪🤍进阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❸.*🔞")
.SPLIT("；")
.NTH(3)
,
[🧪🤍进阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❸.*🔞")
.SPLIT("；")
.NTH(3)
,"；"
,
CONCATENATE(
[🧪🤍进阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❸.*🔞")
.SPLIT("；")
.NTH(4)
,
[🧪🤍进阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❸.*🔞")
.SPLIT("；")
.NTH(4)
,"；"
,
CONCATENATE(
[🧪🤍进阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❸.*🔞")
.SPLIT("；")
.NTH(5)
,
[🧪🤍进阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❸.*🔞")
.SPLIT("；")
.NTH(5)
,"；"
,
CONCATENATE(
[🧪🤍进阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❸.*🔞")
.SPLIT("；")
.NTH(6)
,
[🧪🤍进阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❸.*🔞")
.SPLIT("；")
.NTH(6)
)))))))


，CHAR(10)，


IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACT("❹.*?❹🔞")
.REGEXMATCH("礼包")
,"",
CONCATENATE(
[🧪🤍进阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❹.*🔞")
.SPLIT("；")
.NTH(1)
,
[🧪🤍进阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❹.*🔞")
.SPLIT("；")
.NTH(1)
,"；"
,
CONCATENATE(
[🧪🤍进阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❹.*🔞")
.SPLIT("；")
.NTH(2)
,
[🧪🤍进阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❹.*🔞")
.SPLIT("；")
.NTH(2)
,"；"
,
CONCATENATE(
[🧪🤍进阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❹.*🔞")
.SPLIT("；")
.NTH(3)
,
[🧪🤍进阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❹.*🔞")
.SPLIT("；")
.NTH(3)
,"；"
,
CONCATENATE(
[🧪🤍进阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❹.*🔞")
.SPLIT("；")
.NTH(4)
,
[🧪🤍进阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❹.*🔞")
.SPLIT("；")
.NTH(4)
,"；"
,
CONCATENATE(
[🧪🤍进阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❹.*🔞")
.SPLIT("；")
.NTH(5)
,
[🧪🤍进阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❹.*🔞")
.SPLIT("；")
.NTH(5)
,"；"
,
CONCATENATE(
[🧪🤍进阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❹.*🔞")
.SPLIT("；")
.NTH(6)
,
[🧪🤍进阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❹.*🔞")
.SPLIT("；")
.NTH(6)
)))))))


，CHAR(10)，



IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACT("❺.*?❺🔞")
.REGEXMATCH("礼包")
,"",
CONCATENATE(
[🧪🤍进阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❺.*🔞")
.SPLIT("；")
.NTH(1)
,
[🧪🤍进阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❺.*🔞")
.SPLIT("；")
.NTH(1)
,"；"
,
CONCATENATE(
[🧪🤍进阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❺.*🔞")
.SPLIT("；")
.NTH(2)
,
[🧪🤍进阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❺.*🔞")
.SPLIT("；")
.NTH(2)
,"；"
,
CONCATENATE(
[🧪🤍进阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❺.*🔞")
.SPLIT("；")
.NTH(3)
,
[🧪🤍进阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❺.*🔞")
.SPLIT("；")
.NTH(3)
,"；"
,
CONCATENATE(
[🧪🤍进阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❺.*🔞")
.SPLIT("；")
.NTH(4)
,
[🧪🤍进阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❺.*🔞")
.SPLIT("；")
.NTH(4)
,"；"
,
CONCATENATE(
[🧪🤍进阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❺.*🔞")
.SPLIT("；")
.NTH(5)
,
[🧪🤍进阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❺.*🔞")
.SPLIT("；")
.NTH(5)
,"；"
,
CONCATENATE(
[🧪🤍进阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❺.*🔞")
.SPLIT("；")
.NTH(6)
,
[🧪🤍进阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❺.*🔞")
.SPLIT("；")
.NTH(6)
)))))))


，CHAR(10)，



IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACT("❻.*?❻🔞")
.REGEXMATCH("礼包")
,"",
CONCATENATE(
[🧪🤍进阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❻.*🔞")
.SPLIT("；")
.NTH(1)
,
[🧪🤍进阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❻.*🔞")
.SPLIT("；")
.NTH(1)
,"；"
,
CONCATENATE(
[🧪🤍进阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❻.*🔞")
.SPLIT("；")
.NTH(2)
,
[🧪🤍进阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❻.*🔞")
.SPLIT("；")
.NTH(2)
,"；"
,
CONCATENATE(
[🧪🤍进阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❻.*🔞")
.SPLIT("；")
.NTH(3)
,
[🧪🤍进阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❻.*🔞")
.SPLIT("；")
.NTH(3)
,"；"
,
CONCATENATE(
[🧪🤍进阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❻.*🔞")
.SPLIT("；")
.NTH(4)
,
[🧪🤍进阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❻.*🔞")
.SPLIT("；")
.NTH(4)
,"；"
,
CONCATENATE(
[🧪🤍进阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❻.*🔞")
.SPLIT("；")
.NTH(5)
,
[🧪🤍进阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❻.*🔞")
.SPLIT("；")
.NTH(5)
,"；"
,
CONCATENATE(
[🧪🤍进阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❻.*🔞")
.SPLIT("；")
.NTH(6)
,
[🧪🤍进阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❻.*🔞")
.SPLIT("；")
.NTH(6)
)))))))

，CHAR(10)，


IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACT("❼.*?❼🔞")
.REGEXMATCH("礼包")
,"",
CONCATENATE(
[🧪🤍进阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❼.*🔞")
.SPLIT("；")
.NTH(1)
,
[🧪🤍进阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❼.*🔞")
.SPLIT("；")
.NTH(1)
,"；"
,
CONCATENATE(
[🧪🤍进阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❼.*🔞")
.SPLIT("；")
.NTH(2)
,
[🧪🤍进阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❼.*🔞")
.SPLIT("；")
.NTH(2)
,"；"
,
CONCATENATE(
[🧪🤍进阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❼.*🔞")
.SPLIT("；")
.NTH(3)
,
[🧪🤍进阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❼.*🔞")
.SPLIT("；")
.NTH(3)
,"；"
,
CONCATENATE(
[🧪🤍进阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❼.*🔞")
.SPLIT("；")
.NTH(4)
,
[🧪🤍进阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❼.*🔞")
.SPLIT("；")
.NTH(4)
,"；"
,
CONCATENATE(
[🧪🤍进阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❼.*🔞")
.SPLIT("；")
.NTH(5)
,
[🧪🤍进阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❼.*🔞")
.SPLIT("；")
.NTH(5)
,"；"
,
CONCATENATE(
[🧪🤍进阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❼.*🔞")
.SPLIT("；")
.NTH(6)
,
[🧪🤍进阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❼.*🔞")
.SPLIT("；")
.NTH(6)
)))))))


，CHAR(10)，


IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACT("❽.*?❽🔞")
.REGEXMATCH("礼包")
,"",
CONCATENATE(
[🧪🤍进阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❽.*🔞")
.SPLIT("；")
.NTH(1)
,
[🧪🤍进阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❽.*🔞")
.SPLIT("；")
.NTH(1)
,"；"
,
CONCATENATE(
[🧪🤍进阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❽.*🔞")
.SPLIT("；")
.NTH(2)
,
[🧪🤍进阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❽.*🔞")
.SPLIT("；")
.NTH(2)
,"；"
,
CONCATENATE(
[🧪🤍进阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❽.*🔞")
.SPLIT("；")
.NTH(3)
,
[🧪🤍进阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❽.*🔞")
.SPLIT("；")
.NTH(3)
,"；"
,
CONCATENATE(
[🧪🤍进阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❽.*🔞")
.SPLIT("；")
.NTH(4)
,
[🧪🤍进阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❽.*🔞")
.SPLIT("；")
.NTH(4)
,"；"
,
CONCATENATE(
[🧪🤍进阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❽.*🔞")
.SPLIT("；")
.NTH(5)
,
[🧪🤍进阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❽.*🔞")
.SPLIT("；")
.NTH(5)
,"；"
,
CONCATENATE(
[🧪🤍进阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❽.*🔞")
.SPLIT("；")
.NTH(6)
,
[🧪🤍进阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❽.*🔞")
.SPLIT("；")
.NTH(6)
)))))))



，CHAR(10)，
IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACT("❾.*?❾🔞")
.REGEXMATCH("礼包")
,"",
CONCATENATE(
[🧪🤍进阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❾.*🔞")
.SPLIT("；")
.NTH(1)
,
[🧪🤍进阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❾.*🔞")
.SPLIT("；")
.NTH(1)
,"；"
,
CONCATENATE(
[🧪🤍进阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❾.*🔞")
.SPLIT("；")
.NTH(2)
,
[🧪🤍进阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❾.*🔞")
.SPLIT("；")
.NTH(2)
,"；"
,
CONCATENATE(
[🧪🤍进阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❾.*🔞")
.SPLIT("；")
.NTH(3)
,
[🧪🤍进阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❾.*🔞")
.SPLIT("；")
.NTH(3)
,"；"
,
CONCATENATE(
[🧪🤍进阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❾.*🔞")
.SPLIT("；")
.NTH(4)
,
[🧪🤍进阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❾.*🔞")
.SPLIT("；")
.NTH(4)
,"；"
,
CONCATENATE(
[🧪🤍进阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❾.*🔞")
.SPLIT("；")
.NTH(5)
,
[🧪🤍进阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❾.*🔞")
.SPLIT("；")
.NTH(5)
,"；"
,
CONCATENATE(
[🧪🤍进阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❾.*🔞")
.SPLIT("；")
.NTH(6)
,
[🧪🤍进阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❾.*🔞")
.SPLIT("；")
.NTH(6)
)))))))

)


.REGEXREPLACE("[❶-❾]🔞[❶-❾]","")
.REGEXREPLACE("[❶-❾](?:"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")[❶-❾]🔞；?","")
.REGEXREPLACE("[❶-❾]\d+(?:\.\d+)?[❶-❾]🔞；?","")


.REGEXREPLACE("；+","；")
.REGEXREPLACE("^；","")
.REGEXREPLACE("
；","")


.REGEXREPLACE("[;；,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🧪🤍高阶数量（原始版）
CONCATENATE(
[🧪🔥🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❶.*❶🔞")
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ARRAYJOIN("；")
.REGEXREPLACE([🧠规格提取参考-逻辑], "❶$0❶🔞")


,CHAR(10),



[🧪🔥🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❷.*❷🔞")
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ARRAYJOIN("；")
.REGEXREPLACE([🧠规格提取参考-逻辑], "❷$0❷🔞")



,CHAR(10),


[🧪🔥🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❸.*❸🔞")
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ARRAYJOIN("；")
.REGEXREPLACE([🧠规格提取参考-逻辑], "❸$0❸🔞")


,CHAR(10),



[🧪🔥🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❹.*❹🔞")
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ARRAYJOIN("；")
.REGEXREPLACE([🧠规格提取参考-逻辑], "❹$0❹🔞")



,CHAR(10),



[🧪🔥🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❺.*❺🔞")
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ARRAYJOIN("；")
.REGEXREPLACE([🧠规格提取参考-逻辑], "❺$0❺🔞")



,CHAR(10),


[🧪🔥🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❻.*❻🔞")
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ARRAYJOIN("；")
.REGEXREPLACE([🧠规格提取参考-逻辑], "❻$0❻🔞")



,CHAR(10),


[🧪🔥🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❼.*❼🔞")
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ARRAYJOIN("；")
.REGEXREPLACE([🧠规格提取参考-逻辑], "❼$0❼🔞")



,CHAR(10),



[🧪🔥🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❽.*❽🔞")
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ARRAYJOIN("；")
.REGEXREPLACE([🧠规格提取参考-逻辑], "❽$0❽🔞")



，CHAR(10)，


[🧪🔥🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❾.*❾🔞")
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ARRAYJOIN("；")
.REGEXREPLACE([🧠规格提取参考-逻辑], "❾$0❾🔞")


)


.REGEXREPLACE("🎁","")
.REGEXREPLACE("[；*]?\*?\d+(?:\.\d+)?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠终极单位]&")；?","")

.REGEXREPLACE("(\d+"&[🧠高阶单位]&")(?:\*.*?)*([❶-❾]🔞)","$1$2")



.REGEXREPLACE("([❶-❾])"&[🧠乘法符号],"$1")
.REGEXREPLACE([🧠乘法符号]&"([❶-❾])","$1")


.REGEXREPLACE("[❶-❾][❶-❾]🔞；?","")
.REGEXREPLACE("[;；,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")

.REGEXREPLACE("[❶-❾][❶-❾]🔞；?","")
.REGEXREPLACE("[;；,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🧪🤍高阶数量-单位提取（参考）
[🧪🤍高阶数量（原始版）]
.REGEXREPLACE("\d+(?:\.\d+)?"，"")
.REGEXREPLACE("(❶❶|❷❷|❸❸|❹❹|❺❺|❻❻|❼❼|❽❽|❾❾)🔞；?","")
.REGEXREPLACE("^；","")


.REGEXREPLACE("[;；,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🧪🤍高阶数量-单位提取（终版）
CONCATENATE(

IF([🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACTALL("❶.*❶🔞").ISBLANK(),

CONCATENATE(
IF([🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❶.*❶🔞").REGEXMATCH("主粮|肉干|冻干")
，"❶袋❶🔞；❶袋❶🔞；❶袋❶🔞；❶袋❶🔞；❶袋❶🔞；❶袋❶🔞"
，

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❶.*❶🔞").REGEXMATCH("猫砂|餐包")
，"❶包❶🔞；❶包❶🔞；❶包❶🔞；❶包❶🔞；❶包❶🔞；❶包❶🔞"
，

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❶.*❶🔞").REGEXMATCH("驱虫药|猫条|狗条")
，"❶支❶🔞；❶支❶🔞；❶支❶🔞；❶支❶🔞；❶支❶🔞；❶支❶🔞"
，
IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❶.*❶🔞").REGEXMATCH("罐头")
，"❶罐❶🔞；❶罐❶🔞；❶罐❶🔞；❶罐❶🔞；❶罐❶🔞；❶罐❶🔞"
，

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❶.*❶🔞").REGEXMATCH("餐盒")
，"❶盒❶🔞；❶盒❶🔞；❶盒❶🔞；❶盒❶🔞；❶盒❶🔞；❶盒❶🔞"
，

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❶.*❶🔞").REGEXMATCH("除臭剂|消毒剂")
，"❶瓶❶🔞；❶瓶❶🔞；❶瓶❶🔞；❶瓶❶🔞；❶瓶❶🔞；❶瓶❶🔞"
，""

)))))))
，
CONCATENATE(
[🧪🤍高阶数量-单位提取（参考）].REGEXEXTRACT("❶.*?❶🔞")
,"；"，
[🧪🤍高阶数量-单位提取（参考）].REGEXEXTRACT("❶.*?❶🔞")
,"；"，

[🧪🤍高阶数量-单位提取（参考）].REGEXEXTRACT("❶.*?❶🔞")
,"；"，

[🧪🤍高阶数量-单位提取（参考）].REGEXEXTRACT("❶.*?❶🔞")
,"；"，

[🧪🤍高阶数量-单位提取（参考）].REGEXEXTRACT("❶.*?❶🔞")
,"；"，

[🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACT("❶.*?❶🔞")


))

，CHAR(10)，



IF([🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACTALL("❷.*❷🔞").ISBLANK(),

CONCATENATE(
IF([🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❷.*❷🔞").REGEXMATCH("主粮|肉干|冻干")
,"❷袋❷🔞；❷袋❷🔞；❷袋❷🔞；❷袋❷🔞；❷袋❷🔞；❷袋❷🔞"
,

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❷.*❷🔞").REGEXMATCH("猫砂|餐包")
,"❷包❷🔞；❷包❷🔞；❷包❷🔞；❷包❷🔞；❷包❷🔞；❷包❷🔞"
,

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❷.*❷🔞").REGEXMATCH("驱虫药|猫条|狗条")
,"❷支❷🔞；❷支❷🔞；❷支❷🔞；❷支❷🔞；❷支❷🔞；❷支❷🔞"
,
IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❷.*❷🔞").REGEXMATCH("罐头")
,"❷罐❷🔞；❷罐❷🔞；❷罐❷🔞；❷罐❷🔞；❷罐❷🔞；❷罐❷🔞"
,

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❷.*❷🔞").REGEXMATCH("餐盒")
,"❷盒❷🔞；❷盒❷🔞；❷盒❷🔞；❷盒❷🔞；❷盒❷🔞；❷盒❷🔞"
,

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❷.*❷🔞").REGEXMATCH("除臭剂|消毒剂")
,"❷瓶❷🔞；❷瓶❷🔞；❷瓶❷🔞；❷瓶❷🔞；❷瓶❷🔞；❷瓶❷🔞"
,""

)))))))
,
CONCATENATE(
[🧪🤍高阶数量-单位提取（参考）].REGEXEXTRACT("❷.*?❷🔞")
,"；"，
[🧪🤍高阶数量-单位提取（参考）].REGEXEXTRACT("❷.*?❷🔞")
,"；"，

[🧪🤍高阶数量-单位提取（参考）].REGEXEXTRACT("❷.*?❷🔞")
,"；"，

[🧪🤍高阶数量-单位提取（参考）].REGEXEXTRACT("❷.*?❷🔞")
,"；"，

[🧪🤍高阶数量-单位提取（参考）].REGEXEXTRACT("❷.*?❷🔞")
,"；"，

[🧪🤍高阶数量-单位提取（参考）].REGEXEXTRACT("❷.*?❷🔞")


))






，CHAR(10)，




IF([🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACTALL("❸.*❸🔞").ISBLANK(),

CONCATENATE(
IF([🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❸.*❸🔞").REGEXMATCH("主粮|肉干|冻干")
,"❸袋❸🔞；❸袋❸🔞；❸袋❸🔞；❸袋❸🔞；❸袋❸🔞；❸袋❸🔞"
,

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❸.*❸🔞").REGEXMATCH("猫砂|餐包")
,"❸包❸🔞；❸包❸🔞；❸包❸🔞；❸包❸🔞；❸包❸🔞；❸包❸🔞"
,

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❸.*❸🔞").REGEXMATCH("驱虫药|猫条|狗条")
,"❸支❸🔞；❸支❸🔞；❸支❸🔞；❸支❸🔞；❸支❸🔞；❸支❸🔞"
,
IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❸.*❸🔞").REGEXMATCH("罐头")
,"❸罐❸🔞；❸罐❸🔞；❸罐❸🔞；❸罐❸🔞；❸罐❸🔞；❸罐❸🔞"
,

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❸.*❸🔞").REGEXMATCH("餐盒")
,"❸盒❸🔞；❸盒❸🔞；❸盒❸🔞；❸盒❸🔞；❸盒❸🔞；❸盒❸🔞"
,

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❸.*❸🔞").REGEXMATCH("除臭剂|消毒剂")
,"❸瓶❸🔞；❸瓶❸🔞；❸瓶❸🔞；❸瓶❸🔞；❸瓶❸🔞；❸瓶❸🔞"
,""

)))))))
,
CONCATENATE(
[🧪🤍高阶数量-单位提取（参考）].REGEXEXTRACT("❸.*?❸🔞")
,"；"，
[🧪🤍高阶数量-单位提取（参考）].REGEXEXTRACT("❸.*?❸🔞")
,"；"，

[🧪🤍高阶数量-单位提取（参考）].REGEXEXTRACT("❸.*?❸🔞")
,"；"，

[🧪🤍高阶数量-单位提取（参考）].REGEXEXTRACT("❸.*?❸🔞")
,"；"，

[🧪🤍高阶数量-单位提取（参考）].REGEXEXTRACT("❸.*?❸🔞")
,"；"，

[🧪🤍高阶数量-单位提取（参考）].REGEXEXTRACT("❸.*?❸🔞")


))




，CHAR(10)，



IF([🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACTALL("❹.*❹🔞").ISBLANK(),

CONCATENATE(
IF([🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❹.*❹🔞").REGEXMATCH("主粮|肉干|冻干")
,"❹袋❹🔞；❹袋❹🔞；❹袋❹🔞；❹袋❹🔞；❹袋❹🔞；❹袋❹🔞"
,

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❹.*❹🔞").REGEXMATCH("猫砂|餐包")
,"❹包❹🔞；❹包❹🔞；❹包❹🔞；❹包❹🔞；❹包❹🔞；❹包❹🔞"
,

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❹.*❹🔞").REGEXMATCH("驱虫药|猫条|狗条")
,"❹支❹🔞；❹支❹🔞；❹支❹🔞；❹支❹🔞；❹支❹🔞；❹支❹🔞"
,
IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❹.*❹🔞").REGEXMATCH("罐头")
,"❹罐❹🔞；❹罐❹🔞；❹罐❹🔞；❹罐❹🔞；❹罐❹🔞；❹罐❹🔞"
,

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❹.*❹🔞").REGEXMATCH("餐盒")
,"❹盒❹🔞；❹盒❹🔞；❹盒❹🔞；❹盒❹🔞；❹盒❹🔞；❹盒❹🔞"
,

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❹.*❹🔞").REGEXMATCH("除臭剂|消毒剂")
,"❹瓶❹🔞；❹瓶❹🔞；❹瓶❹🔞；❹瓶❹🔞；❹瓶❹🔞；❹瓶❹🔞"
,""

)))))))
,
CONCATENATE(
[🧪🤍高阶数量-单位提取（参考）].REGEXEXTRACT("❹.*?❹🔞")
,"；"，
[🧪🤍高阶数量-单位提取（参考）].REGEXEXTRACT("❹.*?❹🔞")
,"；"，

[🧪🤍高阶数量-单位提取（参考）].REGEXEXTRACT("❹.*?❹🔞")
,"；"，

[🧪🤍高阶数量-单位提取（参考）].REGEXEXTRACT("❹.*?❹🔞")
,"；"，

[🧪🤍高阶数量-单位提取（参考）].REGEXEXTRACT("❹.*?❹🔞")
,"；"，

[🧪🤍高阶数量-单位提取（参考）].REGEXEXTRACT("❹.*?❹🔞")


))





，CHAR(10)，




IF([🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACTALL("❺.*❺🔞").ISBLANK(),

CONCATENATE(
IF([🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❺.*❺🔞").REGEXMATCH("主粮|肉干|冻干")
,"❺袋❺🔞；❺袋❺🔞；❺袋❺🔞；❺袋❺🔞；❺袋❺🔞；❺袋❺🔞"
,

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❺.*❺🔞").REGEXMATCH("猫砂|餐包")
,"❺包❺🔞；❺包❺🔞；❺包❺🔞；❺包❺🔞；❺包❺🔞；❺包❺🔞"
,

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❺.*❺🔞").REGEXMATCH("驱虫药|猫条|狗条")
,"❺支❺🔞；❺支❺🔞；❺支❺🔞；❺支❺🔞；❺支❺🔞；❺支❺🔞"
,
IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❺.*❺🔞").REGEXMATCH("罐头")
,"❺罐❺🔞；❺罐❺🔞；❺罐❺🔞；❺罐❺🔞；❺罐❺🔞；❺罐❺🔞"
,

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❺.*❺🔞").REGEXMATCH("餐盒")
,"❺盒❺🔞；❺盒❺🔞；❺盒❺🔞；❺盒❺🔞；❺盒❺🔞；❺盒❺🔞"
,

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❺.*❺🔞").REGEXMATCH("除臭剂|消毒剂")
,"❺瓶❺🔞；❺瓶❺🔞；❺瓶❺🔞；❺瓶❺🔞；❺瓶❺🔞；❺瓶❺🔞"
,""

)))))))
,
CONCATENATE(
[🧪🤍高阶数量-单位提取（参考）].REGEXEXTRACT("❺.*?❺🔞")
,"；"，
[🧪🤍高阶数量-单位提取（参考）].REGEXEXTRACT("❺.*?❺🔞")
,"；"，

[🧪🤍高阶数量-单位提取（参考）].REGEXEXTRACT("❺.*?❺🔞")
,"；"，

[🧪🤍高阶数量-单位提取（参考）].REGEXEXTRACT("❺.*?❺🔞")
,"；"，

[🧪🤍高阶数量-单位提取（参考）].REGEXEXTRACT("❺.*?❺🔞")
,"；"，

[🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACT("❺.*?❺🔞")


))





，CHAR(10)，




IF([🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACTALL("❻.*❻🔞").ISBLANK(),

CONCATENATE(
IF([🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❻.*❻🔞").REGEXMATCH("主粮|肉干|冻干")
,"❻袋❻🔞；❻袋❻🔞；❻袋❻🔞；❻袋❻🔞；❻袋❻🔞；❻袋❻🔞"
,

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❻.*❻🔞").REGEXMATCH("猫砂|餐包")
,"❻包❻🔞；❻包❻🔞；❻包❻🔞；❻包❻🔞；❻包❻🔞；❻包❻🔞"
,

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❻.*❻🔞").REGEXMATCH("驱虫药|猫条|狗条")
,"❻支❻🔞；❻支❻🔞；❻支❻🔞；❻支❻🔞；❻支❻🔞；❻支❻🔞"
,
IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❻.*❻🔞").REGEXMATCH("罐头")
,"❻罐❻🔞；❻罐❻🔞；❻罐❻🔞；❻罐❻🔞；❻罐❻🔞；❻罐❻🔞"
,

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❻.*❻🔞").REGEXMATCH("餐盒")
,"❻盒❻🔞；❻盒❻🔞；❻盒❻🔞；❻盒❻🔞；❻盒❻🔞；❻盒❻🔞"
,

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❻.*❻🔞").REGEXMATCH("除臭剂|消毒剂")
,"❻瓶❻🔞；❻瓶❻🔞；❻瓶❻🔞；❻瓶❻🔞；❻瓶❻🔞；❻瓶❻🔞"
,""

)))))))
,
CONCATENATE(
[🧪🤍高阶数量-单位提取（参考）].REGEXEXTRACT("❻.*?❻🔞")
,"；"，
[🧪🤍高阶数量-单位提取（参考）].REGEXEXTRACT("❻.*?❻🔞")
,"；"，

[🧪🤍高阶数量-单位提取（参考）].REGEXEXTRACT("❻.*?❻🔞")
,"；"，

[🧪🤍高阶数量-单位提取（参考）].REGEXEXTRACT("❻.*?❻🔞")
,"；"，

[🧪🤍高阶数量-单位提取（参考）].REGEXEXTRACT("❻.*?❻🔞")
,"；"，

[🧪🤍高阶数量-单位提取（参考）].REGEXEXTRACT("❻.*?❻🔞")


))




，CHAR(10)，




IF([🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACTALL("❼.*❼🔞").ISBLANK(),

CONCATENATE(
IF([🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❼.*❼🔞").REGEXMATCH("主粮|肉干|冻干")
,"❼袋❼🔞；❼袋❼🔞；❼袋❼🔞；❼袋❼🔞；❼袋❼🔞；❼袋❼🔞"
,

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❼.*❼🔞").REGEXMATCH("猫砂|餐包")
,"❼包❼🔞；❼包❼🔞；❼包❼🔞；❼包❼🔞；❼包❼🔞；❼包❼🔞"
,

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❼.*❼🔞").REGEXMATCH("驱虫药|猫条|狗条")
,"❼支❼🔞；❼支❼🔞；❼支❼🔞；❼支❼🔞；❼支❼🔞；❼支❼🔞"
,
IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❼.*❼🔞").REGEXMATCH("罐头")
,"❼罐❼🔞；❼罐❼🔞；❼罐❼🔞；❼罐❼🔞；❼罐❼🔞；❼罐❼🔞"
,

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❼.*❼🔞").REGEXMATCH("餐盒")
,"❼盒❼🔞；❼盒❼🔞；❼盒❼🔞；❼盒❼🔞；❼盒❼🔞；❼盒❼🔞"
,

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❼.*❼🔞").REGEXMATCH("除臭剂|消毒剂")
,"❼瓶❼🔞；❼瓶❼🔞；❼瓶❼🔞；❼瓶❼🔞；❼瓶❼🔞；❼瓶❼🔞"
,""

)))))))
,
CONCATENATE(
[🧪🤍高阶数量-单位提取（参考）].REGEXEXTRACT("❼.*?❼🔞")
,"；"，
[🧪🤍高阶数量-单位提取（参考）].REGEXEXTRACT("❼.*?❼🔞")
,"；"，

[🧪🤍高阶数量-单位提取（参考）].REGEXEXTRACT("❼.*?❼🔞")
,"；"，

[🧪🤍高阶数量-单位提取（参考）].REGEXEXTRACT("❼.*?❼🔞")
,"；"，

[🧪🤍高阶数量-单位提取（参考）].REGEXEXTRACT("❼.*?❼🔞")
,"；"，

[🧪🤍高阶数量-单位提取（参考）].REGEXEXTRACT("❼.*?❼🔞")


))




，CHAR(10)，





IF([🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACTALL("❽.*❽🔞").ISBLANK(),

CONCATENATE(
IF([🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❽.*❽🔞").REGEXMATCH("主粮|肉干|冻干")
,"❽袋❽🔞；❽袋❽🔞；❽袋❽🔞；❽袋❽🔞；❽袋❽🔞；❽袋❽🔞"
,

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❽.*❽🔞").REGEXMATCH("猫砂|餐包")
,"❽包❽🔞；❽包❽🔞；❽包❽🔞；❽包❽🔞；❽包❽🔞；❽包❽🔞"
,

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❽.*❽🔞").REGEXMATCH("驱虫药|猫条|狗条")
,"❽支❽🔞；❽支❽🔞；❽支❽🔞；❽支❽🔞；❽支❽🔞；❽支❽🔞"
,
IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❽.*❽🔞").REGEXMATCH("罐头")
,"❽罐❽🔞；❽罐❽🔞；❽罐❽🔞；❽罐❽🔞；❽罐❽🔞；❽罐❽🔞"
,

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❽.*❽🔞").REGEXMATCH("餐盒")
,"❽盒❽🔞；❽盒❽🔞；❽盒❽🔞；❽盒❽🔞；❽盒❽🔞；❽盒❽🔞"
,

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❽.*❽🔞").REGEXMATCH("除臭剂|消毒剂")
,"❽瓶❽🔞；❽瓶❽🔞；❽瓶❽🔞；❽瓶❽🔞；❽瓶❽🔞；❽瓶❽🔞"
,""

)))))))
,
CONCATENATE(
[🧪🤍高阶数量-单位提取（参考）].REGEXEXTRACT("❽.*?❽🔞")
,"；"，
[🧪🤍高阶数量-单位提取（参考）].REGEXEXTRACT("❽.*?❽🔞")
,"；"，

[🧪🤍高阶数量-单位提取（参考）].REGEXEXTRACT("❽.*?❽🔞")
,"；"，

[🧪🤍高阶数量-单位提取（参考）].REGEXEXTRACT("❽.*?❽🔞")
,"；"，

[🧪🤍高阶数量-单位提取（参考）].REGEXEXTRACT("❽.*?❽🔞")
,"；"，

[🧪🤍高阶数量-单位提取（参考）].REGEXEXTRACT("❽.*?❽🔞")


))


,CHAR(10),






IF([🧪🤍进阶数量-单位提取（参考）].REGEXEXTRACTALL("❾.*❾🔞").ISBLANK(),

CONCATENATE(
IF([🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❾.*❾🔞").REGEXMATCH("主粮|肉干|冻干")
,"❾袋❾🔞；❾袋❾🔞；❾袋❾🔞；❾袋❾🔞；❾袋❾🔞；❾袋❾🔞"
,

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❾.*❾🔞").REGEXMATCH("猫砂|餐包")
,"❾包❾🔞；❾包❾🔞；❾包❾🔞；❾包❾🔞；❾包❾🔞；❾包❾🔞"
,

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❾.*❾🔞").REGEXMATCH("驱虫药|猫条|狗条")
,"❾支❾🔞；❾支❾🔞；❾支❾🔞；❾支❾🔞；❾支❾🔞；❾支❾🔞"
,
IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❾.*❾🔞").REGEXMATCH("罐头")
,"❾罐❾🔞；❾罐❾🔞；❾罐❾🔞；❾罐❾🔞；❾罐❾🔞；❾罐❾🔞"
,

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❾.*❾🔞").REGEXMATCH("餐盒")
,"❾盒❾🔞；❾盒❾🔞；❾盒❾🔞；❾盒❾🔞；❾盒❾🔞；❾盒❾🔞"
,

IF(
[🛍️🤍品类-二级分类（原始值）-优化版].REGEXEXTRACT("❾.*❾🔞").REGEXMATCH("除臭剂|消毒剂")
,"❾瓶❾🔞；❾瓶❾🔞；❾瓶❾🔞；❾瓶❾🔞；❾瓶❾🔞；❾瓶❾🔞"
,""

)))))))
,
CONCATENATE(
[🧪🤍高阶数量-单位提取（参考）].REGEXEXTRACT("❾.*?❾🔞")
,"；"，
[🧪🤍高阶数量-单位提取（参考）].REGEXEXTRACT("❾.*?❾🔞")
,"；"，

[🧪🤍高阶数量-单位提取（参考）].REGEXEXTRACT("❾.*?❾🔞")
,"；"，

[🧪🤍高阶数量-单位提取（参考）].REGEXEXTRACT("❾.*?❾🔞")
,"；"，

[🧪🤍高阶数量-单位提取（参考）].REGEXEXTRACT("❾.*?❾🔞")
,"；"，

[🧪🤍高阶数量-单位提取（参考）].REGEXEXTRACT("❾.*?❾🔞")


))





)




.REGEXREPLACE("FALSE","")
.REGEXREPLACE("[❶-❾]("&[🧠初级单位]&"|"&[🧠进阶单位]&")[❶-❾]🔞；?","")


.REGEXREPLACE("[;；,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🧪🤍高阶数量-数字提取（1-6）
IF(ISBLANK([🍬商品信息（原始版）]),""，

CONCATENATE(

IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(1)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❶1❶🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(1)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❶$0❶🔞"))

，


IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(2)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❶1❶🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(2)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❶$0❶🔞"))

,

IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(3)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❶1❶🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(3)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❶$0❶🔞"))


,


IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(4)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❶1❶🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(4)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❶$0❶🔞"))



,


IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(5)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❶1❶🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(5)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❶$0❶🔞"))


,



IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(6)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❶1❶🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(6)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❶$0❶🔞"))


,CHAR(10)，

IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(1)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❷1❷🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(1)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❷$0❷🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(2)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❷1❷🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(2)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❷$0❷🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(3)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❷1❷🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(3)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❷$0❷🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(4)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❷1❷🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(4)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❷$0❷🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(5)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❷1❷🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(5)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❷$0❷🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(6)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❷1❷🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(6)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❷$0❷🔞"))



,CHAR(10),




IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(1)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❸1❸🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(1)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❸$0❸🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(2)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❸1❸🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(2)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❸$0❸🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(3)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❸1❸🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(3)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❸$0❸🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(4)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❸1❸🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(4)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❸$0❸🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(5)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❸1❸🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(5)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❸$0❸🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(6)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❸1❸🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(6)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❸$0❸🔞"))


,CHAR(10),


IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(1)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❹1❹🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(1)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❹$0❹🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(2)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❹1❹🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(2)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❹$0❹🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(3)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❹1❹🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(3)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❹$0❹🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(4)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❹1❹🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(4)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❹$0❹🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(5)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❹1❹🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(5)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❹$0❹🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(6)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❹1❹🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(6)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❹$0❹🔞"))


,CHAR(10),




IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(1)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❺1❺🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(1)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❺$0❺🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(2)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❺1❺🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(2)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❺$0❺🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(3)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❺1❺🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(3)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❺$0❺🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(4)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❺1❺🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(4)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❺$0❺🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(5)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❺1❺🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(5)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❺$0❺🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(6)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❺1❺🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(6)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❺$0❺🔞"))


,CHAR(10),

IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(1)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❻1❻🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(1)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❻$0❻🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(2)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❻1❻🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(2)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❻$0❻🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(3)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❻1❻🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(3)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❻$0❻🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(4)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❻1❻🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(4)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❻$0❻🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(5)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❻1❻🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(5)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❻$0❻🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(6)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❻1❻🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(6)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❻$0❻🔞"))



))

.REGEXREPLACE("；?\d+(?:\.\d+)?(?:"&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠终极单位]&")；?","")
.REGEXREPLACE("("&[🧠单位合集]&")","")

.REGEXREPLACE("([❶-❾])"&[🧠乘法符号],"$1")
.REGEXREPLACE([🧠乘法符号]&"([❶-❾])","$1")

.REGEXREPLACE("\*\d+(?:\.\d+)?","")

.REGEXREPLACE("([❶-❾])([❶-❾]🔞)","${1}1${2}")
.REGEXREPLACE("([❶-❾]🔞)([❶-❾])","$1；$2")

.REGEXREPLACE("[;；,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🧪🤍高阶数量-数字提取（7-9）
IF(ISBLANK([🍬商品信息（原始版）]),""，

CONCATENATE(

IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(1)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❼1❼🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(1)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❼$0❼🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(2)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❼1❼🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(2)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❼$0❼🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(3)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❼1❼🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(3)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❼$0❼🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(4)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❼1❼🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(4)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❼$0❼🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(5)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❼1❼🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(5)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❼$0❼🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(6)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❼1❼🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(6)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❼$0❼🔞"))


,CHAR(10),

IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(1)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❽1❽🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(1)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❽$0❽🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(2)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❽1❽🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(2)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❽$0❽🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(3)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❽1❽🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(3)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❽$0❽🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(4)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❽1❽🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(4)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❽$0❽🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(5)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❽1❽🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(5)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❽$0❽🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(6)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❽1❽🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(6)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❽$0❽🔞"))





,CHAR(10),

IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(1)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❾1❾🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(1)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❾$0❾🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(2)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❾1❾🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(2)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❾$0❾🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(3)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❾1❾🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(3)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❾$0❾🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(4)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❾1❾🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(4)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❾$0❾🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(5)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❾1❾🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(5)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❾$0❾🔞")),

,IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(6)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ISBLANK()

,
"❾1❾🔞"
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(6)
.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.REGEXREPLACE([🧠规格提取参考-逻辑], "❾$0❾🔞"))


))

.REGEXREPLACE("；?\d+(?:\.\d+)?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠终极单位]&")；?","")

.REGEXREPLACE("("&[🧠单位合集]&")","")

.REGEXREPLACE("([❶-❾])"&[🧠乘法符号],"$1")
.REGEXREPLACE([🧠乘法符号]&"([❶-❾])","$1")

.REGEXREPLACE("\*\d+(?:\.\d+)?","")

.REGEXREPLACE("([❶-❾])([❶-❾]🔞)","${1}1${2}")
.REGEXREPLACE("([❶-❾]🔞)([❶-❾])","$1；$2")

.REGEXREPLACE("[;；,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🧪🤍高阶数量-数字提取（终版）
CONCATENATE(
[🧪🤍高阶数量-数字提取（1-6）]
，CHAR(10)，
[🧪🤍高阶数量-数字提取（7-9）]
)

.REGEXREPLACE("[,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🧪🤍高阶数量（清洗版+终版）
CONCATENATE(
IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACT("❶.*?❶🔞")
.REGEXMATCH("礼包")
,"",
CONCATENATE(
[🧪🤍高阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❶.*🔞")
.SPLIT("；")
.NTH(1)
,
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❶.*🔞")
.SPLIT("；")
.NTH(1)
,"；"
,
CONCATENATE(
[🧪🤍高阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❶.*🔞")
.SPLIT("；")
.NTH(2)
,
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❶.*🔞")
.SPLIT("；")
.NTH(2)
,"；"
,
CONCATENATE(
[🧪🤍高阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❶.*🔞")
.SPLIT("；")
.NTH(3)
,
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❶.*🔞")
.SPLIT("；")
.NTH(3)
,"；"
,
CONCATENATE(
[🧪🤍高阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❶.*🔞")
.SPLIT("；")
.NTH(4)
,
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❶.*🔞")
.SPLIT("；")
.NTH(4)
,"；"
,
CONCATENATE(
[🧪🤍高阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❶.*🔞")
.SPLIT("；")
.NTH(5)
,
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❶.*🔞")
.SPLIT("；")
.NTH(5)
,"；"
,
CONCATENATE(
[🧪🤍高阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❶.*🔞")
.SPLIT("；")
.NTH(6)
,
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❶.*🔞")
.SPLIT("；")
.NTH(6)
)))))))


，CHAR(10)，


IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACT("❷.*?❷🔞")
.REGEXMATCH("礼包")
,"",
CONCATENATE(
[🧪🤍高阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❷.*🔞")
.SPLIT("；")
.NTH(1)
,
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❷.*🔞")
.SPLIT("；")
.NTH(1)
,"；"
,
CONCATENATE(
[🧪🤍高阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❷.*🔞")
.SPLIT("；")
.NTH(2)
,
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❷.*🔞")
.SPLIT("；")
.NTH(2)
,"；"
,
CONCATENATE(
[🧪🤍高阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❷.*🔞")
.SPLIT("；")
.NTH(3)
,
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❷.*🔞")
.SPLIT("；")
.NTH(3)
,"；"
,
CONCATENATE(
[🧪🤍高阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❷.*🔞")
.SPLIT("；")
.NTH(4)
,
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❷.*🔞")
.SPLIT("；")
.NTH(4)
,"；"
,
CONCATENATE(
[🧪🤍高阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❷.*🔞")
.SPLIT("；")
.NTH(5)
,
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❷.*🔞")
.SPLIT("；")
.NTH(5)
,"；"
,
CONCATENATE(
[🧪🤍高阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❷.*🔞")
.SPLIT("；")
.NTH(6)
,
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❷.*🔞")
.SPLIT("；")
.NTH(6)
)))))))


，CHAR(10)，



IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACT("❸.*?❸🔞")
.REGEXMATCH("礼包")
,"",
CONCATENATE(
[🧪🤍高阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❸.*🔞")
.SPLIT("；")
.NTH(1)
,
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❸.*🔞")
.SPLIT("；")
.NTH(1)
,"；"
,
CONCATENATE(
[🧪🤍高阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❸.*🔞")
.SPLIT("；")
.NTH(2)
,
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❸.*🔞")
.SPLIT("；")
.NTH(2)
,"；"
,
CONCATENATE(
[🧪🤍高阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❸.*🔞")
.SPLIT("；")
.NTH(3)
,
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❸.*🔞")
.SPLIT("；")
.NTH(3)
,"；"
,
CONCATENATE(
[🧪🤍高阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❸.*🔞")
.SPLIT("；")
.NTH(4)
,
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❸.*🔞")
.SPLIT("；")
.NTH(4)
,"；"
,
CONCATENATE(
[🧪🤍高阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❸.*🔞")
.SPLIT("；")
.NTH(5)
,
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❸.*🔞")
.SPLIT("；")
.NTH(5)
,"；"
,
CONCATENATE(
[🧪🤍高阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❸.*🔞")
.SPLIT("；")
.NTH(6)
,
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❸.*🔞")
.SPLIT("；")
.NTH(6)
)))))))


，CHAR(10)，


IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACT("❹.*?❹🔞")
.REGEXMATCH("礼包")
,"",
CONCATENATE(
[🧪🤍高阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❹.*🔞")
.SPLIT("；")
.NTH(1)
,
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❹.*🔞")
.SPLIT("；")
.NTH(1)
,"；"
,
CONCATENATE(
[🧪🤍高阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❹.*🔞")
.SPLIT("；")
.NTH(2)
,
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❹.*🔞")
.SPLIT("；")
.NTH(2)
,"；"
,
CONCATENATE(
[🧪🤍高阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❹.*🔞")
.SPLIT("；")
.NTH(3)
,
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❹.*🔞")
.SPLIT("；")
.NTH(3)
,"；"
,
CONCATENATE(
[🧪🤍高阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❹.*🔞")
.SPLIT("；")
.NTH(4)
,
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❹.*🔞")
.SPLIT("；")
.NTH(4)
,"；"
,
CONCATENATE(
[🧪🤍高阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❹.*🔞")
.SPLIT("；")
.NTH(5)
,
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❹.*🔞")
.SPLIT("；")
.NTH(5)
,"；"
,
CONCATENATE(
[🧪🤍高阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❹.*🔞")
.SPLIT("；")
.NTH(6)
,
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❹.*🔞")
.SPLIT("；")
.NTH(6)
)))))))


，CHAR(10)，



IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACT("❺.*?❺🔞")
.REGEXMATCH("礼包")
,"",
CONCATENATE(
[🧪🤍高阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❺.*🔞")
.SPLIT("；")
.NTH(1)
,
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❺.*🔞")
.SPLIT("；")
.NTH(1)
,"；"
,
CONCATENATE(
[🧪🤍高阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❺.*🔞")
.SPLIT("；")
.NTH(2)
,
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❺.*🔞")
.SPLIT("；")
.NTH(2)
,"；"
,
CONCATENATE(
[🧪🤍高阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❺.*🔞")
.SPLIT("；")
.NTH(3)
,
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❺.*🔞")
.SPLIT("；")
.NTH(3)
,"；"
,
CONCATENATE(
[🧪🤍高阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❺.*🔞")
.SPLIT("；")
.NTH(4)
,
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❺.*🔞")
.SPLIT("；")
.NTH(4)
,"；"
,
CONCATENATE(
[🧪🤍高阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❺.*🔞")
.SPLIT("；")
.NTH(5)
,
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❺.*🔞")
.SPLIT("；")
.NTH(5)
,"；"
,
CONCATENATE(
[🧪🤍高阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❺.*🔞")
.SPLIT("；")
.NTH(6)
,
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❺.*🔞")
.SPLIT("；")
.NTH(6)
)))))))


，CHAR(10)，



IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACT("❻.*?❻🔞")
.REGEXMATCH("礼包")
,"",
CONCATENATE(
[🧪🤍高阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❻.*🔞")
.SPLIT("；")
.NTH(1)
,
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❻.*🔞")
.SPLIT("；")
.NTH(1)
,"；"
,
CONCATENATE(
[🧪🤍高阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❻.*🔞")
.SPLIT("；")
.NTH(2)
,
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❻.*🔞")
.SPLIT("；")
.NTH(2)
,"；"
,
CONCATENATE(
[🧪🤍高阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❻.*🔞")
.SPLIT("；")
.NTH(3)
,
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❻.*🔞")
.SPLIT("；")
.NTH(3)
,"；"
,
CONCATENATE(
[🧪🤍高阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❻.*🔞")
.SPLIT("；")
.NTH(4)
,
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❻.*🔞")
.SPLIT("；")
.NTH(4)
,"；"
,
CONCATENATE(
[🧪🤍高阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❻.*🔞")
.SPLIT("；")
.NTH(5)
,
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❻.*🔞")
.SPLIT("；")
.NTH(5)
,"；"
,
CONCATENATE(
[🧪🤍高阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❻.*🔞")
.SPLIT("；")
.NTH(6)
,
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❻.*🔞")
.SPLIT("；")
.NTH(6)
)))))))

，CHAR(10)，


IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACT("❼.*?❼🔞")
.REGEXMATCH("礼包")
,"",
CONCATENATE(
[🧪🤍高阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❼.*🔞")
.SPLIT("；")
.NTH(1)
,
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❼.*🔞")
.SPLIT("；")
.NTH(1)
,"；"
,
CONCATENATE(
[🧪🤍高阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❼.*🔞")
.SPLIT("；")
.NTH(2)
,
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❼.*🔞")
.SPLIT("；")
.NTH(2)
,"；"
,
CONCATENATE(
[🧪🤍高阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❼.*🔞")
.SPLIT("；")
.NTH(3)
,
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❼.*🔞")
.SPLIT("；")
.NTH(3)
,"；"
,
CONCATENATE(
[🧪🤍高阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❼.*🔞")
.SPLIT("；")
.NTH(4)
,
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❼.*🔞")
.SPLIT("；")
.NTH(4)
,"；"
,
CONCATENATE(
[🧪🤍高阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❼.*🔞")
.SPLIT("；")
.NTH(5)
,
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❼.*🔞")
.SPLIT("；")
.NTH(5)
,"；"
,
CONCATENATE(
[🧪🤍高阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❼.*🔞")
.SPLIT("；")
.NTH(6)
,
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❼.*🔞")
.SPLIT("；")
.NTH(6)
)))))))


，CHAR(10)，


IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACT("❽.*?❽🔞")
.REGEXMATCH("礼包")
,"",
CONCATENATE(
[🧪🤍高阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❽.*🔞")
.SPLIT("；")
.NTH(1)
,
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❽.*🔞")
.SPLIT("；")
.NTH(1)
,"；"
,
CONCATENATE(
[🧪🤍高阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❽.*🔞")
.SPLIT("；")
.NTH(2)
,
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❽.*🔞")
.SPLIT("；")
.NTH(2)
,"；"
,
CONCATENATE(
[🧪🤍高阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❽.*🔞")
.SPLIT("；")
.NTH(3)
,
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❽.*🔞")
.SPLIT("；")
.NTH(3)
,"；"
,
CONCATENATE(
[🧪🤍高阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❽.*🔞")
.SPLIT("；")
.NTH(4)
,
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❽.*🔞")
.SPLIT("；")
.NTH(4)
,"；"
,
CONCATENATE(
[🧪🤍高阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❽.*🔞")
.SPLIT("；")
.NTH(5)
,
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❽.*🔞")
.SPLIT("；")
.NTH(5)
,"；"
,
CONCATENATE(
[🧪🤍高阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❽.*🔞")
.SPLIT("；")
.NTH(6)
,
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❽.*🔞")
.SPLIT("；")
.NTH(6)
)))))))



，CHAR(10)，
IF(
[🧪🤍规格类型（原始值）]
.REGEXEXTRACT("❾.*?❾🔞")
.REGEXMATCH("礼包")
,"",
CONCATENATE(
[🧪🤍高阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❾.*🔞")
.SPLIT("；")
.NTH(1)
,
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❾.*🔞")
.SPLIT("；")
.NTH(1)
,"；"
,
CONCATENATE(
[🧪🤍高阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❾.*🔞")
.SPLIT("；")
.NTH(2)
,
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❾.*🔞")
.SPLIT("；")
.NTH(2)
,"；"
,
CONCATENATE(
[🧪🤍高阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❾.*🔞")
.SPLIT("；")
.NTH(3)
,
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❾.*🔞")
.SPLIT("；")
.NTH(3)
,"；"
,
CONCATENATE(
[🧪🤍高阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❾.*🔞")
.SPLIT("；")
.NTH(4)
,
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❾.*🔞")
.SPLIT("；")
.NTH(4)
,"；"
,
CONCATENATE(
[🧪🤍高阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❾.*🔞")
.SPLIT("；")
.NTH(5)
,
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❾.*🔞")
.SPLIT("；")
.NTH(5)
,"；"
,
CONCATENATE(
[🧪🤍高阶数量-数字提取（终版）]
.REGEXEXTRACTALL("❾.*🔞")
.SPLIT("；")
.NTH(6)
,
[🧪🤍高阶数量-单位提取（终版）]
.REGEXEXTRACTALL("❾.*🔞")
.SPLIT("；")
.NTH(6)
)))))))

)


.REGEXREPLACE("[❶-❾]🔞[❶-❾]","")
.REGEXREPLACE("[❶-❾](?:"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")[❶-❾]🔞；?","")
.REGEXREPLACE("[❶-❾]\d+(?:\.\d+)?[❶-❾]🔞；?","")


.REGEXREPLACE("；+","；")
.REGEXREPLACE("^；","")
.REGEXREPLACE("
；","")


.REGEXREPLACE("[;；,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====

"""
本地缓存管理器
解决NAS存储的稳定性问题，提供本地缓存和离线工作能力
"""

import os
import json
import shutil
import hashlib
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime
import logging

class 本地缓存管理器:
    """本地缓存管理器"""
    
    def __init__(self, nas_root: str = None, local_cache_root: str = None):
        self.logger = logging.getLogger('本地缓存管理器')
        
        # NAS根目录（当前项目目录）
        self.nas_root = Path(nas_root) if nas_root else Path(__file__).parent.parent
        
        # 本地缓存目录（用户主目录下）
        if local_cache_root:
            self.local_cache_root = Path(local_cache_root)
        else:
            home_dir = Path.home()
            self.local_cache_root = home_dir / ".nars_pet_business_cache"
        
        # 确保缓存目录存在
        self.local_cache_root.mkdir(parents=True, exist_ok=True)
        
        # 缓存配置
        self.cache_config_file = self.local_cache_root / "cache_config.json"
        self.sync_log_file = self.local_cache_root / "sync_log.json"
        
        # 需要缓存的关键目录
        self.critical_dirs = [
            "A_main",
            "A_连接MongoDB", 
            "A_config",
            "B_公式逻辑一览",
            "C_公式代码"
        ]
        
        # 需要缓存的关键文件
        self.critical_files = [
            "README_系统实现总结.md",
            ".vscode/settings.json",
            ".vscode/launch.json"
        ]
        
        self.load_cache_config()
    
    def load_cache_config(self):
        """加载缓存配置"""
        if self.cache_config_file.exists():
            try:
                with open(self.cache_config_file, 'r', encoding='utf-8') as f:
                    self.cache_config = json.load(f)
            except Exception as e:
                self.logger.warning(f"加载缓存配置失败: {e}")
                self.cache_config = {}
        else:
            self.cache_config = {}
    
    def save_cache_config(self):
        """保存缓存配置"""
        try:
            with open(self.cache_config_file, 'w', encoding='utf-8') as f:
                json.dump(self.cache_config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"保存缓存配置失败: {e}")
    
    def get_file_hash(self, file_path: Path) -> str:
        """获取文件哈希值"""
        try:
            with open(file_path, 'rb') as f:
                return hashlib.md5(f.read()).hexdigest()
        except Exception:
            return ""
    
    def is_nas_available(self) -> bool:
        """检查NAS是否可用"""
        try:
            # 检查关键文件是否存在
            test_file = self.nas_root / "A_main" / "multi_round_scheduler.py"
            return test_file.exists()
        except Exception:
            return False
    
    def sync_to_local(self, force: bool = False) -> bool:
        """同步NAS数据到本地缓存"""
        if not self.is_nas_available():
            self.logger.warning("NAS不可用，无法同步")
            return False
        
        self.logger.info("开始同步NAS数据到本地缓存...")
        
        sync_log = {
            "sync_time": datetime.now().isoformat(),
            "synced_files": [],
            "failed_files": [],
            "total_size": 0
        }
        
        try:
            # 同步关键目录
            for dir_name in self.critical_dirs:
                nas_dir = self.nas_root / dir_name
                local_dir = self.local_cache_root / dir_name
                
                if nas_dir.exists():
                    self._sync_directory(nas_dir, local_dir, sync_log, force)
            
            # 同步关键文件
            for file_path in self.critical_files:
                nas_file = self.nas_root / file_path
                local_file = self.local_cache_root / file_path
                
                if nas_file.exists():
                    self._sync_file(nas_file, local_file, sync_log, force)
            
            # 保存同步日志
            with open(self.sync_log_file, 'w', encoding='utf-8') as f:
                json.dump(sync_log, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"同步完成: {len(sync_log['synced_files'])} 个文件, "
                           f"总大小: {sync_log['total_size']/1024/1024:.2f}MB")
            
            return True
            
        except Exception as e:
            self.logger.error(f"同步失败: {e}")
            return False
    
    def _sync_directory(self, nas_dir: Path, local_dir: Path, sync_log: Dict, force: bool):
        """同步目录"""
        local_dir.mkdir(parents=True, exist_ok=True)
        
        for item in nas_dir.rglob("*"):
            if item.is_file():
                # 计算相对路径
                rel_path = item.relative_to(nas_dir)
                local_file = local_dir / rel_path
                
                self._sync_file(item, local_file, sync_log, force)
    
    def _sync_file(self, nas_file: Path, local_file: Path, sync_log: Dict, force: bool):
        """同步单个文件"""
        try:
            # 检查是否需要同步
            need_sync = force
            
            if not need_sync:
                if not local_file.exists():
                    need_sync = True
                else:
                    nas_hash = self.get_file_hash(nas_file)
                    local_hash = self.get_file_hash(local_file)
                    need_sync = nas_hash != local_hash
            
            if need_sync:
                # 确保目标目录存在
                local_file.parent.mkdir(parents=True, exist_ok=True)
                
                # 复制文件
                shutil.copy2(nas_file, local_file)
                
                # 记录同步信息
                file_size = nas_file.stat().st_size
                sync_log["synced_files"].append({
                    "file": str(nas_file.relative_to(self.nas_root)),
                    "size": file_size,
                    "hash": self.get_file_hash(local_file)
                })
                sync_log["total_size"] += file_size
                
        except Exception as e:
            sync_log["failed_files"].append({
                "file": str(nas_file),
                "error": str(e)
            })
            self.logger.warning(f"同步文件失败 {nas_file}: {e}")
    
    def sync_from_local(self) -> bool:
        """从本地缓存同步回NAS（如果NAS可用）"""
        if not self.is_nas_available():
            self.logger.warning("NAS不可用，无法回写")
            return False
        
        self.logger.info("开始从本地缓存同步到NAS...")
        
        try:
            # 同步关键目录
            for dir_name in self.critical_dirs:
                local_dir = self.local_cache_root / dir_name
                nas_dir = self.nas_root / dir_name
                
                if local_dir.exists():
                    self._sync_directory(local_dir, nas_dir, {}, True)
            
            # 同步关键文件
            for file_path in self.critical_files:
                local_file = self.local_cache_root / file_path
                nas_file = self.nas_root / file_path
                
                if local_file.exists():
                    self._sync_file(local_file, nas_file, {}, True)
            
            self.logger.info("回写同步完成")
            return True
            
        except Exception as e:
            self.logger.error(f"回写同步失败: {e}")
            return False
    
    def get_working_directory(self) -> Path:
        """获取当前工作目录（优先使用NAS，不可用时使用本地缓存）"""
        if self.is_nas_available():
            self.logger.info("使用NAS目录")
            return self.nas_root
        else:
            self.logger.info("NAS不可用，使用本地缓存目录")
            return self.local_cache_root
    
    def setup_offline_environment(self):
        """设置离线工作环境"""
        working_dir = self.get_working_directory()
        
        # 更新Python路径
        import sys
        
        # 移除旧的路径
        paths_to_remove = [p for p in sys.path if "Nars-代码管理" in p]
        for path in paths_to_remove:
            sys.path.remove(path)
        
        # 添加新的路径
        new_paths = [
            str(working_dir),
            str(working_dir / "A_main"),
            str(working_dir / "A_连接MongoDB"),
            str(working_dir / "C_公式代码")
        ]
        
        for path in new_paths:
            if path not in sys.path:
                sys.path.insert(0, path)
        
        self.logger.info(f"离线环境已设置，工作目录: {working_dir}")
        return working_dir
    
    def create_portable_package(self, output_path: str = None) -> str:
        """创建便携包（用于完全离线使用）"""
        if not output_path:
            output_path = str(Path.home() / "Desktop" / "nars_pet_business_portable.zip")
        
        import zipfile
        
        self.logger.info("创建便携包...")
        
        try:
            with zipfile.ZipFile(output_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # 添加关键目录
                for dir_name in self.critical_dirs:
                    source_dir = self.get_working_directory() / dir_name
                    if source_dir.exists():
                        for file_path in source_dir.rglob("*"):
                            if file_path.is_file():
                                arcname = file_path.relative_to(self.get_working_directory())
                                zipf.write(file_path, arcname)
                
                # 添加关键文件
                for file_path in self.critical_files:
                    source_file = self.get_working_directory() / file_path
                    if source_file.exists():
                        zipf.write(source_file, file_path)
                
                # 添加启动脚本
                startup_script = """#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import sys
import os
from pathlib import Path

# 设置工作目录
work_dir = Path(__file__).parent
os.chdir(work_dir)

# 设置Python路径
sys.path.insert(0, str(work_dir))
sys.path.insert(0, str(work_dir / "A_main"))
sys.path.insert(0, str(work_dir / "A_连接MongoDB"))
sys.path.insert(0, str(work_dir / "C_公式代码"))

# 启动主程序
if __name__ == "__main__":
    from A_main.final_demo import demo_complete_system
    demo_complete_system()
"""
                zipf.writestr("启动程序.py", startup_script)
            
            self.logger.info(f"便携包已创建: {output_path}")
            return output_path
            
        except Exception as e:
            self.logger.error(f"创建便携包失败: {e}")
            return ""
    
    def get_cache_status(self) -> Dict[str, Any]:
        """获取缓存状态"""
        status = {
            "nas_available": self.is_nas_available(),
            "local_cache_path": str(self.local_cache_root),
            "cache_size": 0,
            "cached_files": 0,
            "last_sync": None
        }
        
        # 计算缓存大小
        if self.local_cache_root.exists():
            for file_path in self.local_cache_root.rglob("*"):
                if file_path.is_file():
                    status["cache_size"] += file_path.stat().st_size
                    status["cached_files"] += 1
        
        # 获取最后同步时间
        if self.sync_log_file.exists():
            try:
                with open(self.sync_log_file, 'r', encoding='utf-8') as f:
                    sync_log = json.load(f)
                    status["last_sync"] = sync_log.get("sync_time")
            except Exception:
                pass
        
        return status

# 全局实例
缓存管理器 = 本地缓存管理器()

# 便捷函数
def 检查NAS状态() -> bool:
    """检查NAS是否可用"""
    return 缓存管理器.is_nas_available()

def 同步到本地() -> bool:
    """同步NAS数据到本地"""
    return 缓存管理器.sync_to_local()

def 获取工作目录() -> Path:
    """获取当前工作目录"""
    return 缓存管理器.get_working_directory()

def 设置离线环境() -> Path:
    """设置离线工作环境"""
    return 缓存管理器.setup_offline_environment()

# 测试代码
if __name__ == "__main__":
    manager = 本地缓存管理器()
    
    print("🗄️ 本地缓存管理器测试")
    print("=" * 50)
    
    # 检查状态
    status = manager.get_cache_status()
    print(f"NAS可用: {status['nas_available']}")
    print(f"缓存路径: {status['local_cache_path']}")
    print(f"缓存大小: {status['cache_size']/1024/1024:.2f}MB")
    print(f"缓存文件: {status['cached_files']} 个")
    print(f"最后同步: {status['last_sync']}")
    print()
    
    # 测试同步
    if status['nas_available']:
        print("🔄 开始同步测试...")
        success = manager.sync_to_local()
        print(f"同步结果: {'成功' if success else '失败'}")
    else:
        print("⚠️ NAS不可用，跳过同步测试")
    
    # 设置工作环境
    work_dir = manager.setup_offline_environment()
    print(f"🏠 工作目录: {work_dir}")
    
    print("\n✅ 测试完成")

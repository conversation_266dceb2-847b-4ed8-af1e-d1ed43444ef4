{"version": "0.2.0", "configurations": [{"name": "Python: 当前文件", "type": "python", "request": "launch", "program": "${file}", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}:${workspaceFolder}/A_main:${workspaceFolder}/A_连接MongoDB:${workspaceFolder}/C_公式代码"}}, {"name": "Python: 多轮处理器", "type": "python", "request": "launch", "program": "${workspaceFolder}/A_main/multi_round_scheduler.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}:${workspaceFolder}/A_main:${workspaceFolder}/A_连接MongoDB:${workspaceFolder}/C_公式代码"}}, {"name": "Python: 完整演示", "type": "python", "request": "launch", "program": "${workspaceFolder}/A_main/final_demo.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}:${workspaceFolder}/A_main:${workspaceFolder}/A_连接MongoDB:${workspaceFolder}/C_公式代码"}}, {"name": "Python: 架构适配器测试", "type": "python", "request": "launch", "program": "${workspaceFolder}/A_main/架构适配器.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}:${workspaceFolder}/A_main:${workspaceFolder}/A_连接MongoDB:${workspaceFolder}/C_公式代码"}}]}
🧪编号（最终）
TEXT([🧪 规格解构（基建）]
.COUNTIF(CurrentValue.[🧪编号（参考勿删！）]<[🧪编号（参考勿删！）])+1

，"👀000000000"）
=====
🧪编号（参考勿删！）
自增数字
=====
🍬商品信息（原始版）
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🧪编号（最终）]).[🍬商品信息（原始版）].LISTCOMBINE()
=====
🍬商品信息ID
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🧪编号（最终）]).[🍬商品信息ID].LISTCOMBINE()
=====
🧠规格提取参考-逻辑
IF([🍬商品信息（原始版）].ISBLANK(),""，
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠规格提取-逻辑]!="").[🧠规格提取-逻辑].LISTCOMBINE().UNIQUE())
=====
🧠所拍规格参考-逻辑
IF([🍬商品信息（原始版）].ISBLANK(),""，
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠所拍规格-逻辑]!="").[🧠所拍规格-逻辑].LISTCOMBINE().UNIQUE())
=====
🧠需转义符号参考-逻辑
IF([🍬商品信息（原始版）].ISBLANK(),""，
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠需转义符号-逻辑]!="").[🧠需转义符号-逻辑].LISTCOMBINE().UNIQUE())
=====
🧠乘法符号
IF([🍬商品信息（原始版）].ISBLANK(),""，
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠乘法符号]!="").[🧠乘法符号].LISTCOMBINE().UNIQUE())
=====
🧠加法符号
IF([🍬商品信息（原始版）].ISBLANK(),""，
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠加法符号]!="").[🧠加法符号].LISTCOMBINE().UNIQUE())
=====
🧠基础单位
IF([🍬商品信息（原始版）].ISBLANK(),""，
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠基础单位]!="").[🧠基础单位].LISTCOMBINE().UNIQUE())
=====
🧠初级单位
IF([🍬商品信息（原始版）].ISBLANK(),""，
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠初级单位]!="").[🧠初级单位].LISTCOMBINE().UNIQUE())
=====
🧠进阶单位
IF([🍬商品信息（原始版）].ISBLANK(),""，
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠进阶单位]!="").[🧠进阶单位].LISTCOMBINE().UNIQUE())
=====
🧠高阶单位
IF([🍬商品信息（原始版）].ISBLANK(),""，
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠高阶单位]!="").[🧠高阶单位].LISTCOMBINE().UNIQUE())
=====
🧠终极单位
IF([🍬商品信息（原始版）].ISBLANK(),""，
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠终极单位]!="").[🧠终极单位].LISTCOMBINE().UNIQUE())
=====
🧠单位合集
IF([🍬商品信息（原始版）].ISBLANK(),""，
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠单位合集]!="").[🧠单位合集].LISTCOMBINE().UNIQUE())
=====
📁💜商品信息（终版分割版）
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🧪编号（最终）]).[📁💜商品信息（终版分割版）].LISTCOMBINE()
=====
📁🤍商品信息（纯净分割版）
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🧪编号（最终）]).[📁🤍商品信息（纯净分割版）].LISTCOMBINE()
=====
📁🤍😀商品标题（纯净分割版）
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🧪编号（最终）]).[📁🤍😀商品信息（纯净分割版） -商品名称&价格专享版].LISTCOMBINE()
=====
📁💜sku数量
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🧪编号（最终）]).[📁🩵链接/口令数量（原始版）].LISTCOMBINE()
=====
📁源信息发布时间(最终)
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🧪编号（最终）]).[📁源信息发布时间(最终)]
=====
📁选品定位符
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🧪编号（最终）]).[📁选品定位符].LISTCOMBINE()
=====
🛍️🤍品类-一级分类（原始值）
[🛍️ ｜✍️ 品类｜口味｜制作工艺 ｜ 使用对象 ｜ 成长期（基建）].FILTER(CurrentValue.[🛍️编号（最终）]=[🧪编号（最终）]).[🛍️🤍😀品类-一级品类（原始值）].LISTCOMBINE()
=====
🛍️🤍品类-二级分类（原始值）-优化版
[🛍️ ｜✍️ 品类｜口味｜制作工艺 ｜ 使用对象 ｜ 成长期（基建）].FILTER(CurrentValue.[🛍️编号（最终）]=[🧪编号（最终）]).[🛍️🤍😀品类-二级分类（原始值）-优化版].LISTCOMBINE()
=====
💰到手价格（终版）
[💰 价格解构（基建）]
.FILTER(CurrentValue.[💰编号（最终）]=[🧪编号（最终）]).[💰🤎到手价格（终版）]
=====
💰🤍😀商品价格-逻辑 勿删！！
[💰 价格解构（基建）].FILTER(CurrentValue.[💰编号（最终）]=[🧪编号（最终）]).[🧠商品价格-逻辑].LISTCOMBINE()
=====
💰规格&价格数量对应关系
[💰 价格解构（基建）].FILTER(CurrentValue.[💰编号（最终）]=[🧪编号（最终）]).[💰🩶规格&价格数量对应关系]
=====
💰🤍😀付款类型
[💰 价格解构（基建）].FILTER(CurrentValue.[💰编号（最终）]=[🧪编号（最终）]).[💰🤍😀付款类型]
=====
Ⓜ️品牌参考-逻辑 勿删！！
[Ⓜ️ ｜ ✍️ 品牌识别（基建）].FILTER(CurrentValue.[Ⓜ️编号（最终）]=[🧪编号（最终）]).[🧠品牌-逻辑].LISTCOMBINE()
=====
🔪🖤sku数量（勿删）
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[🧪编号（最终）]).[🔪🖤SKU数量]
=====
🔪🤍😀商品名称（原始版）
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[🧪编号（最终）]).[🔪🤍😀商品名称（原始版）].LISTCOMBINE()
=====
🔪🤍😀拍凑份数(原始版)
[🔪 ｜ ✍️ sku解构(基建)]
.FILTER(CurrentValue.[🔪编号（最终）]=[🧪编号（最终）])
.[🔪🤍😀拍凑份数(原始版)]

.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🔪🤍😀下单文案
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[🧪编号（最终）]).[🔪🧡下单文案]
=====
🔪💚商品价格（信息重组专用版）
[🔪 ｜ ✍️ sku解构(基建)]
.FILTER(CurrentValue.[🔪编号（最终）]=[🧪编号（最终）])
.[🔪💚商品价格（信息重组专用版）]
=====
🔪💚商品信息（发车重组版）
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[🧪编号（最终）]).[🐽💚商品信息（重组版）].LISTCOMBINE()
=====
🔪💚赠送信息（信息重组专用版）
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[🧪编号（最终）]).[🔪💚赠送信息（信息重组专用版）]
=====
🧪商品名称（规格类型提取专用版）
IF([🍬商品信息（原始版）].ISBLANK(),""，

[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[🧪编号（最终）]).[🔪🤍😀商品名称（规格类型提取专用版）-参考])

.REGEXREPLACE("[🐓🍽]","")
.REGEXREPLACE("🔟","10")

.REGEXREPLACE("([❶-❾])[,，。]","$1")
.REGEXREPLACE("[❶-❾][,。，]?\s?[❶-❾]🔞","")

.REGEXREPLACE("❶🔞\s?❶",CHAR(10))
.REGEXREPLACE("❷🔞\s?❷",CHAR(10))
.REGEXREPLACE("❸🔞\s?❸",CHAR(10))
.REGEXREPLACE("❹🔞\s?❹",CHAR(10))
.REGEXREPLACE("❺🔞\s?❺",CHAR(10))
.REGEXREPLACE("❻🔞\s?❻",CHAR(10))
.REGEXREPLACE("❼🔞\s?❼",CHAR(10))
.REGEXREPLACE("❽🔞\s?❽",CHAR(10))
.REGEXREPLACE("❾🔞\s?❾",CHAR(10))

.REGEXREPLACE("❶🔞\s?"&CHAR(10)&"❶",CHAR(10))
.REGEXREPLACE("❷🔞\s?"&CHAR(10)&"❷",CHAR(10))
.REGEXREPLACE("❸🔞\s?"&CHAR(10)&"❸",CHAR(10))
.REGEXREPLACE("❹🔞\s?"&CHAR(10)&"❹",CHAR(10))
.REGEXREPLACE("❺🔞\s?"&CHAR(10)&"❺",CHAR(10))
.REGEXREPLACE("❻🔞\s?"&CHAR(10)&"❻",CHAR(10))
.REGEXREPLACE("❼🔞\s?"&CHAR(10)&"❼",CHAR(10))
.REGEXREPLACE("❽🔞\s?"&CHAR(10)&"❽",CHAR(10))
.REGEXREPLACE("❾🔞\s?"&CHAR(10)&"❾",CHAR(10))

.REGEXREPLACE([🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠赠送信息-逻辑]!="").[🧠赠送信息-逻辑],"")


.REGEXREPLACE("[+﹢🞡🞢＋🞣🞤🞥🞦🞧➕](试吃)","加试🍚吃")

.REGEXREPLACE("([❶-❾])([\s\S]*?)*"&CHAR(10)&"([❶-❾])","$1$2$1🔞"&CHAR(10)&"$3")
.REGEXREPLACE("([❶-❾]🔞)([❶-❾]🔞)","$1")
.REGEXREPLACE(CHAR(10)&"([❶-❾]🔞)+$","$1")

.REGEXREPLACE("("&[Ⓜ️品牌参考-逻辑 勿删！！]&"\d+)\+(\d+)","$1加$4")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🧪🤍规格类型（原始值）
IF(
OR（
[🍬商品信息（原始版）]
.ISBLANK()
，
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🧪编号（最终）]).[📁🩵信息类型（唯一值）]
.REGEXMATCH("🛒")
.NOT())

,"",

CONCATENATE(
IF(
[💰规格&价格数量对应关系]
.REGEXEXTRACTALL("❶[\s\S]*?❶🔞")
.ISBLANK()
,"",

IF(

OR(
AND(
[🛍️🤍品类-二级分类（原始值）-优化版]
.REGEXEXTRACTALL("❶[\s\S]*?❶🔞")
.REGEXMATCH("玩具")
,

[🧪商品名称（规格类型提取专用版）]
.REGEXEXTRACTALL("❶[\s\S]*?❶🔞")
.REGEXMATCH([🧠加法符号])
)
，

[💰规格&价格数量对应关系]
.REGEXEXTRACTALL("❶[\s\S]*?❶🔞")
.REGEXMATCH("♠️|🎴")
)
，
"❶🟠礼包❶🔞",


IF(
OR(
[🧪商品名称（规格类型提取专用版）]
.REGEXEXTRACTALL("❶[\s\S]*?❶🔞")
.REGEXMATCH("试吃")
，
AND(
[🛍️🤍品类-二级分类（原始值）-优化版]
.REGEXEXTRACTALL("❶.*?❶🔞")
.REGEXMATCH("主粮")
,

[🧪🤍基础规格-单位提取]
.REGEXEXTRACTALL("❶.*?❶🔞")
.REGEXMATCH("斤"),

[🧪🤍基础规格-数字提取]
.REGEXEXTRACTALL("❶(.*?)❶🔞").VALUE()
<1
)
),
"❶🔵试吃❶🔞",
"❶🟣正装❶🔞"
)
))

，CHAR(10)，

IF(
[💰规格&价格数量对应关系]
.REGEXEXTRACTALL("❷")
.ISBLANK()
,"",

IF(

OR(
AND(
[🛍️🤍品类-二级分类（原始值）-优化版]
.REGEXEXTRACTALL("❷[\s\S]*?❷🔞")
.REGEXMATCH("玩具")
,

[🧪商品名称（规格类型提取专用版）]
.REGEXEXTRACTALL("❷[\s\S]*?❷🔞")
.REGEXMATCH([🧠加法符号])
)
，


[💰规格&价格数量对应关系]
.REGEXEXTRACTALL("❷[\s\S]*?❷🔞")
.REGEXMATCH("♠️|🎴")
)

，
"❷🟠礼包❷🔞",

IF(
OR(
[🧪商品名称（规格类型提取专用版）]
.REGEXEXTRACTALL("❷[\s\S]*?❷🔞")
.REGEXMATCH("试吃")
,
AND(
[🛍️🤍品类-二级分类（原始值）-优化版]
.REGEXEXTRACTALL("❷.*?❷🔞")
.REGEXMATCH("主粮")
,
[🧪🤍基础规格-单位提取]
.REGEXEXTRACTALL("❷.*?❷🔞")
.REGEXMATCH("斤"),
[🧪🤍基础规格-数字提取]
.REGEXEXTRACTALL("❷(.*?)❷🔞").VALUE()
<1
)
),
"❷🔵试吃❷🔞",
"❷🟣正装❷🔞"
)
))

，CHAR(10)，

IF(
[💰规格&价格数量对应关系]
.REGEXEXTRACTALL("❸")
.ISBLANK()
,"",
IF(


OR(
AND(
[🛍️🤍品类-二级分类（原始值）-优化版]
.REGEXEXTRACTALL("❸[\s\S]*?❸🔞")
.REGEXMATCH("玩具")
,

[🧪商品名称（规格类型提取专用版）]
.REGEXEXTRACTALL("❸[\s\S]*?❸🔞")
.REGEXMATCH([🧠加法符号])
)
，



[💰规格&价格数量对应关系]
.REGEXEXTRACTALL("❸[\s\S]*?❸🔞")
.REGEXMATCH("♠️|🎴")
)
，
"❸🟠礼包❸🔞",

IF(
OR(
[🧪商品名称（规格类型提取专用版）]
.REGEXEXTRACTALL("❸[\s\S]*?❸🔞")
.REGEXMATCH("试吃")
,
AND(
[🛍️🤍品类-二级分类（原始值）-优化版]
.REGEXEXTRACTALL("❸.*?❸🔞")
.REGEXMATCH("主粮")
,
[🧪🤍基础规格-单位提取]
.REGEXEXTRACTALL("❸.*?❸🔞")
.REGEXMATCH("斤"),
[🧪🤍基础规格-数字提取]
.REGEXEXTRACTALL("❸(.*?)❸🔞").VALUE()
<1
)
),
"❸🔵试吃❸🔞",
"❸🟣正装❸🔞"
)
))

，CHAR(10)，

IF(
[💰规格&价格数量对应关系]
.REGEXEXTRACTALL("❹")
.ISBLANK()
,"",
IF(

OR(
AND(
[🛍️🤍品类-二级分类（原始值）-优化版]
.REGEXEXTRACTALL("❹[\s\S]*?❹🔞")
.REGEXMATCH("玩具")
,

[🧪商品名称（规格类型提取专用版）]
.REGEXEXTRACTALL("❹[\s\S]*?❹🔞")
.REGEXMATCH([🧠加法符号])
)
，



[💰规格&价格数量对应关系]
.REGEXEXTRACTALL("❹[\s\S]*?❹🔞")
.REGEXMATCH("♠️|🎴")
)
，
"❹🟠礼包❹🔞",

IF(
OR(
[🧪商品名称（规格类型提取专用版）]
.REGEXEXTRACTALL("❹[\s\S]*?❹🔞")
.REGEXMATCH("试吃")
,
AND(
[🛍️🤍品类-二级分类（原始值）-优化版]
.REGEXEXTRACTALL("❹.*?❹🔞")
.REGEXMATCH("主粮")
,
[🧪🤍基础规格-单位提取]
.REGEXEXTRACTALL("❹.*?❹🔞")
.REGEXMATCH("斤"),
[🧪🤍基础规格-数字提取]
.REGEXEXTRACTALL("❹(.*?)❹🔞").VALUE()
<1
)
),
"❹🔵试吃❹🔞",
"❹🟣正装❹🔞"
)
))

，CHAR(10)，

IF(
[💰规格&价格数量对应关系]
.REGEXEXTRACTALL("❺")
.ISBLANK()
,"",

IF(


OR(
AND(
[🛍️🤍品类-二级分类（原始值）-优化版]
.REGEXEXTRACTALL("❺[\s\S]*?❺🔞")
.REGEXMATCH("玩具")
,

[🧪商品名称（规格类型提取专用版）]
.REGEXEXTRACTALL("❺[\s\S]*?❺🔞")
.REGEXMATCH([🧠加法符号])
)
，


[💰规格&价格数量对应关系]
.REGEXEXTRACTALL("❺[\s\S]*?❺🔞")
.REGEXMATCH("♠️|🎴")
)


，
"❺🟠礼包❺🔞",


IF(
OR(
[🧪商品名称（规格类型提取专用版）]
.REGEXEXTRACTALL("❺[\s\S]*?❺🔞")
.REGEXMATCH("试吃")
,
AND(
[🛍️🤍品类-二级分类（原始值）-优化版]
.REGEXEXTRACTALL("❺.*?❺🔞")
.REGEXMATCH("主粮")
,
[🧪🤍基础规格-单位提取]
.REGEXEXTRACTALL("❺.*?❺🔞")
.REGEXMATCH("斤"),
[🧪🤍基础规格-数字提取]
.REGEXEXTRACTALL("❺(.*?)❺🔞").VALUE()
<1
)
),
"❺🔵试吃❺🔞",
"❺🟣正装❺🔞"
)
))


，CHAR(10)，

IF(
[💰规格&价格数量对应关系]
.REGEXEXTRACTALL("❻")
.ISBLANK()
,"",

IF(



OR(
AND(
[🛍️🤍品类-二级分类（原始值）-优化版]
.REGEXEXTRACTALL("❻[\s\S]*?❻🔞")
.REGEXMATCH("玩具")
,

[🧪商品名称（规格类型提取专用版）]
.REGEXEXTRACTALL("❻[\s\S]*?❻🔞")
.REGEXMATCH([🧠加法符号])
)
，


[💰规格&价格数量对应关系]
.REGEXEXTRACTALL("❻[\s\S]*?❻🔞")
.REGEXMATCH("♠️|🎴")
)
，
"❻🟠礼包❻🔞",

IF(
OR(
[🧪商品名称（规格类型提取专用版）]
.REGEXEXTRACTALL("❻[\s\S]*?❻🔞")
.REGEXMATCH("试吃")
,
AND(
[🛍️🤍品类-二级分类（原始值）-优化版]
.REGEXEXTRACTALL("❻.*?❻🔞")
.REGEXMATCH("主粮")
,
[🧪🤍基础规格-单位提取]
.REGEXEXTRACTALL("❻.*?❻🔞")
.REGEXMATCH("斤"),
[🧪🤍基础规格-数字提取]
.REGEXEXTRACTALL("❻(.*?)❻🔞").VALUE()
<1
)
),
"❻🔵试吃❻🔞",
"❻🟣正装❻🔞"
)
))


，CHAR(10)，

IF(
[💰规格&价格数量对应关系]
.REGEXEXTRACTALL("❼")
.ISBLANK()
,"",
IF(

OR(
AND(
[🛍️🤍品类-二级分类（原始值）-优化版]
.REGEXEXTRACTALL("❼[\s\S]*?❼🔞")
.REGEXMATCH("玩具")
,

[🧪商品名称（规格类型提取专用版）]
.REGEXEXTRACTALL("❼[\s\S]*?❼🔞")
.REGEXMATCH([🧠加法符号])
)
，



[💰规格&价格数量对应关系]
.REGEXEXTRACTALL("❼[\s\S]*?❼🔞")
.REGEXMATCH("♠️|🎴")
)
，
"❼🟠礼包❼🔞",


IF(
OR(
[🧪商品名称（规格类型提取专用版）]
.REGEXEXTRACTALL("❼[\s\S]*?❼🔞")
.REGEXMATCH("试吃")
,
AND(
[🛍️🤍品类-二级分类（原始值）-优化版]
.REGEXEXTRACTALL("❼.*?❼🔞")
.REGEXMATCH("主粮")
,
[🧪🤍基础规格-单位提取]
.REGEXEXTRACTALL("❼.*?❼🔞")
.REGEXMATCH("斤"),
[🧪🤍基础规格-数字提取]
.REGEXEXTRACTALL("❼(.*?)❼🔞").VALUE()
<1
)
),
"❼🔵试吃❼🔞",
"❼🟣正装❼🔞"
)
))


，CHAR(10)，

IF(
[💰规格&价格数量对应关系]
.REGEXEXTRACTALL("❽")
.ISBLANK()
,"",


IF(


OR(
AND(
[🛍️🤍品类-二级分类（原始值）-优化版]
.REGEXEXTRACTALL("❽[\s\S]*?❽🔞")
.REGEXMATCH("玩具")
,

[🧪商品名称（规格类型提取专用版）]
.REGEXEXTRACTALL("❽[\s\S]*?❽🔞")
.REGEXMATCH([🧠加法符号])
)
，



[💰规格&价格数量对应关系]
.REGEXEXTRACTALL("❽[\s\S]*?❽🔞")
.REGEXMATCH("♠️|🎴")
)
，
"❽🟠礼包❽🔞",




IF(
OR(
[🧪商品名称（规格类型提取专用版）]
.REGEXEXTRACTALL("❽[\s\S]*?❽🔞")
.REGEXMATCH("试吃")
,
AND(
[🛍️🤍品类-二级分类（原始值）-优化版]
.REGEXEXTRACTALL("❽.*?❽🔞")
.REGEXMATCH("主粮")
,
[🧪🤍基础规格-单位提取]
.REGEXEXTRACTALL("❽.*?❽🔞")
.REGEXMATCH("斤"),
[🧪🤍基础规格-数字提取]
.REGEXEXTRACTALL("❽(.*?)❽🔞").VALUE()
<1
)
),
"❽🔵试吃❽🔞",
"❽🟣正装❽🔞"
)
))


，CHAR(10)，

IF(
[💰规格&价格数量对应关系]
.REGEXEXTRACTALL("❾")
.ISBLANK()
,"",

IF(
OR(
AND(
[🛍️🤍品类-二级分类（原始值）-优化版]
.REGEXEXTRACTALL("❾[\s\S]*?❾🔞")
.REGEXMATCH("玩具")
,

[🧪商品名称（规格类型提取专用版）]
.REGEXEXTRACTALL("❾[\s\S]*?❾🔞")
.REGEXMATCH([🧠加法符号])
)
，


[💰规格&价格数量对应关系]
.REGEXEXTRACTALL("❾[\s\S]*?❾🔞")
.REGEXMATCH("♠️|🎴")
)
，
"❾🟠礼包❾🔞",




IF(
OR(
[🧪商品名称（规格类型提取专用版）]
.REGEXEXTRACTALL("❾[\s\S]*?❾🔞")
.REGEXMATCH("试吃")
,
AND(
[🛍️🤍品类-二级分类（原始值）-优化版]
.REGEXEXTRACTALL("❾.*?❾🔞")
.REGEXMATCH("主粮")
,
[🧪🤍基础规格-单位提取]
.REGEXEXTRACTALL("❾.*?❾🔞")
.REGEXMATCH("斤"),
[🧪🤍基础规格-数字提取]
.REGEXEXTRACTALL("❾(.*?)❾🔞").VALUE()
<1
)
),
"❾🔵试吃❾🔞",
"❾🟣正装❾🔞"
)
))


))

.REGEXREPLACE("[,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🧪规格类型（唯一值）
[🧪🤍规格类型（原始值）].REGEXREPLACE("[❶-❻🔞🟠🔵🟣]","").REGEXEXTRACTALL("(试吃|正装|礼包)").UNIQUE().REGEXREPLACE("(?m)^\s*$\r?\n?","")
=====
🧪商品选项类型
IF(
AND(

[💰 价格解构（基建）]
.FILTER(CurrentValue.[💰编号（最终）]=[🧪编号（最终）]).[💰🩶原始高价]
.REGEXEXTRACTALL("❶.*?❶🔞")
.COUNTA()>1
,

OR(
[💰 价格解构（基建）]
.FILTER(CurrentValue.[💰编号（最终）]=[🧪编号（最终）]).[💰🩶原始高价]
.REGEXEXTRACTALL("❶.*?❶🔞")
.COUNTA()

<=
[🧪🔥🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❶.*?❶🔞")
.COUNTA()
,
[🧪🔥🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❶.*?❶🔞")
.COUNTA()=0
)
,

[💰 价格解构（基建）]
.FILTER(CurrentValue.[💰编号（最终）]=[🧪编号（最终）]).[💰🩶原始高价]
.REGEXEXTRACTALL("❶.*?❶🔞")
.UNIQUE()
.COUNTA()

>=

[🧪🔥🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❶.*?❶🔞")
.UNIQUE()
.COUNTA()

)
,
"❶🥢多选项多规格❶🔞"
，

IF(
[💰 价格解构（基建）]
.FILTER(CurrentValue.[💰编号（最终）]=[🧪编号（最终）]).[💰🩶原始高价]
.REGEXEXTRACTALL("❶.*?❶🔞")
.ISBLANK()
，""
，
IF(
OR(
AND(

[💰 价格解构（基建）]
.FILTER(CurrentValue.[💰编号（最终）]=[🧪编号（最终）]).[💰🩶原始高价]
.REGEXEXTRACTALL("❶.*?❶🔞")
.COUNTA()>1

,
[🧪🔥🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❶.*?❶🔞")
.UNIQUE()
.COUNTA()
<
[💰 价格解构（基建）]
.FILTER(CurrentValue.[💰编号（最终）]=[🧪编号（最终）]).[💰🩶原始高价]
.REGEXEXTRACTALL("❶.*?❶🔞")
.COUNTA()
)

,

AND(
[💰 价格解构（基建）]
.FILTER(CurrentValue.[💰编号（最终）]=[🧪编号（最终）]).[💰🩶原始高价]
.REGEXEXTRACTALL("❶.*?❶🔞")
.COUNTA()>1

,

[🧪🔥🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❶.*?❶🔞")
.COUNTA()=0

))



，"❶🍚多选项❶🔞",

IF(
AND(
[💰 价格解构（基建）]
.FILTER(CurrentValue.[💰编号（最终）]=[🧪编号（最终）]).[💰🩶原始高价]
.REGEXEXTRACTALL("❶.*?❶🔞")
.COUNTA()>1
,


[🧪🔥🤍规格✖️数量✖️起拍数量]
.REGEXEXTRACTALL("❶.*?❶🔞")
.UNIQUE()
.COUNTA()

>=

[💰 价格解构（基建）]
.FILTER(CurrentValue.[💰编号（最终）]=[🧪编号（最终）]).[💰🩶原始高价]
.REGEXEXTRACTALL("❶.*?❶🔞")
.COUNTA()

)

,"❶📱多规格❶🔞"

,"❶👅单选项❶🔞"))))

.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🧪🤍规格数量情况
CONCATENATE(

IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.ISBLANK()
,"",

IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.COUNTA()>1
,"❶多套规格🌼❶🔞"
,"❶单套规格🌸❶🔞"
))

,CHAR(10),
IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.ISBLANK()
,"",
IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.COUNTA()>1
,"❷多套规格🌼❷🔞"
,"❷单套规格🌸❷🔞"
))


,CHAR(10),
IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.ISBLANK()
,"",
IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.COUNTA()>1
,"❸多套规格🌼❸🔞"
,"❸单套规格🌸❸🔞"
))

,CHAR(10),
IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.ISBLANK()
,"",
IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.COUNTA()>1
,"❹多套规格🌼❹🔞"
,"❹单套规格🌸❹🔞"
))


,CHAR(10),
IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.ISBLANK()
,"",
IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.COUNTA()>1
,"❺多套规格🌼❺🔞"
,"❺单套规格🌸❺🔞"
))

,CHAR(10),
IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.ISBLANK()
,"",
IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.COUNTA()>1
,"❻多套规格🌼❻🔞"
,"❻单套规格🌸❻🔞"
))


,CHAR(10),
IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.ISBLANK()
,"",
IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.COUNTA()>1
,"❼多套规格🌼❼🔞"
,"❼单套规格🌸❼🔞"
))


,CHAR(10),
IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.ISBLANK()
,"",
IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.COUNTA()>1
,"❽多套规格🌼❽🔞"
,"❽单套规格🌸❽🔞"
))


,CHAR(10),
IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.ISBLANK()
,"",
IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.COUNTA()>1
,"❾多套规格🌼❾🔞"
,"❾单套规格🌸❾🔞"
))



)

.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🧪🤍规格✖️数量提取（原始版）
IF([🍬商品信息（原始版）].ISBLANK()，""，

CONCATENATE(
[📁🤍商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(1)

.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ARRAYJOIN("；")
.REGEXREPLACE([🧠规格提取参考-逻辑]，"❶$0❶🔞")


，CHAR(10)，



[📁🤍商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(2)


.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ARRAYJOIN("；")
.REGEXREPLACE([🧠规格提取参考-逻辑]，"❷$0❷🔞")


，CHAR(10)，



[📁🤍商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(3)



.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ARRAYJOIN("；")
.REGEXREPLACE([🧠规格提取参考-逻辑]，"❸$0❸🔞")

，CHAR(10)，





[📁🤍商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(4)


.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ARRAYJOIN("；")
.REGEXREPLACE([🧠规格提取参考-逻辑]，"❹$0❹🔞")



，CHAR(10)，



[📁🤍商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(5)


.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ARRAYJOIN("；")
.REGEXREPLACE([🧠规格提取参考-逻辑]，"❺$0❺🔞")


，CHAR(10)，



[📁🤍商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(6)


.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ARRAYJOIN("；")
.REGEXREPLACE([🧠规格提取参考-逻辑]，"❻$0❻🔞")



，CHAR(10)，

[📁🤍商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(7)


.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ARRAYJOIN("；")
.REGEXREPLACE([🧠规格提取参考-逻辑],"❼$0❼🔞")


，CHAR(10)，

[📁🤍商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(8)


.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ARRAYJOIN("；")
.REGEXREPLACE([🧠规格提取参考-逻辑],"❽$0❽🔞")


，CHAR(10)，

[📁🤍商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(9)


.REGEXEXTRACTALL([🧠规格提取参考-逻辑])
.ARRAYJOIN("；")
.REGEXREPLACE([🧠规格提取参考-逻辑],"❾$0❾🔞")


))
.LOWER()
.REGEXREPLACE("^🔞+","")
.REGEXREPLACE("
🔞","")

.REGEXREPLACE("(选项)?(拍|下单)","*")
.REGEXREPLACE("(选项)","")
.REGEXREPLACE("[,，+➕～\s;；]+$", "")
.REGEXREPLACE("[,，+➕～\s;；]"&CHAR(10),CHAR(10))
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🧪🤍规格✖️数量提取（优化版）
IF([🍬商品信息（原始版）].ISBLANK()，""，

IF(
AND(
[💰🤍😀付款类型]
.REGEXMATCH("任选|合并")

，
[🧪🤍规格✖️数量提取（原始版）]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞")
.ARRAYJOIN("")
.REGEXREPLACE("[❶-❾](.*?)[❶-❾]🔞",[💰到手价格（终版）].REGEXEXTRACT("[❶-❾]")&"$1"&[💰到手价格（终版）].REGEXEXTRACT("[❶-❾]")&"🔞")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞")
.UNIQUE()
.COUNTA()=1
)

,

[🧪🤍规格✖️数量提取（原始版）]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞")
.ARRAYJOIN("")
.REGEXREPLACE("[❶-❾](.*?)[❶-❾]🔞",[💰到手价格（终版）].REGEXEXTRACT("[❶-❾]")&"$1"&[💰到手价格（终版）].REGEXEXTRACT("[❶-❾]")&"🔞")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞")
.UNIQUE()

，


IF(
AND(
[💰🤍😀付款类型]
.REGEXMATCH("任选|合并")

，
[🧪🤍规格✖️数量提取（原始版）]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞")
.ARRAYJOIN("")
.REGEXREPLACE("[❶-❾](.*?)[❶-❾]🔞",[💰到手价格（终版）].REGEXEXTRACT("[❶-❾]")&"$1"&[💰到手价格（终版）].REGEXEXTRACT("[❶-❾]")&"🔞")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞")
.UNIQUE()
.COUNTA()>1
)
,""
，



IF(
AND(
[🧪🤍规格✖️数量提取（原始版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.ISBLANK()
.NOT()

，
[💰到手价格（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.ISBLANK()

,
[💰到手价格（终版）]
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.COUNTA()=1

)
,
[🧪🤍规格✖️数量提取（原始版）]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞")
.ARRAYJOIN("")
.REGEXREPLACE("[❶-❾](.*?)[❶-❾]🔞",[💰到手价格（终版）].REGEXEXTRACT("[❶-❾]")&"$1"&[💰到手价格（终版）].REGEXEXTRACT("[❶-❾]")&"🔞")

，

IF(
AND(
[🧪🤍规格✖️数量提取（原始版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.ISBLANK()
.NOT()

，
[💰到手价格（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.ISBLANK()

,
[💰到手价格（终版）]
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.COUNTA()=1

)
,
[🧪🤍规格✖️数量提取（原始版）]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞")
.ARRAYJOIN("")
.REGEXREPLACE("[❶-❾](.*?)[❶-❾]🔞",[💰到手价格（终版）].REGEXEXTRACT("[❶-❾]")&"$1"&[💰到手价格（终版）].REGEXEXTRACT("[❶-❾]")&"🔞")
，

IF(
AND(
[🧪🤍规格✖️数量提取（原始版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.ISBLANK()
.NOT()

，
[💰到手价格（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.ISBLANK()


,
[💰到手价格（终版）]
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.COUNTA()=1
)
,
[🧪🤍规格✖️数量提取（原始版）]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞")
.ARRAYJOIN("")
.REGEXREPLACE("[❶-❾](.*?)[❶-❾]🔞",[💰到手价格（终版）].REGEXEXTRACT("[❶-❾]")&"$1"&[💰到手价格（终版）].REGEXEXTRACT("[❶-❾]")&"🔞")


，

IF(
AND(
[🧪🤍规格✖️数量提取（原始版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.ISBLANK()
.NOT()

，
[💰到手价格（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.ISBLANK()


,
[💰到手价格（终版）]
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.COUNTA()=1
)
,
[🧪🤍规格✖️数量提取（原始版）]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞")
.ARRAYJOIN("")
.REGEXREPLACE("[❶-❾](.*?)[❶-❾]🔞",[💰到手价格（终版）].REGEXEXTRACT("[❶-❾]")&"$1"&[💰到手价格（终版）].REGEXEXTRACT("[❶-❾]")&"🔞")




，

IF(
AND(
[🧪🤍规格✖️数量提取（原始版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.ISBLANK()
.NOT()

，
[💰到手价格（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.ISBLANK()


,
[💰到手价格（终版）]
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.COUNTA()=1
)
,
[🧪🤍规格✖️数量提取（原始版）]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞")
.ARRAYJOIN("")
.REGEXREPLACE("[❶-❾](.*?)[❶-❾]🔞",[💰到手价格（终版）].REGEXEXTRACT("[❶-❾]")&"$1"&[💰到手价格（终版）].REGEXEXTRACT("[❶-❾]")&"🔞")




，

IF(
AND(
[🧪🤍规格✖️数量提取（原始版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.ISBLANK()
.NOT()

，
[💰到手价格（终版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.ISBLANK()


,
[💰到手价格（终版）]
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.COUNTA()=1
)
,
[🧪🤍规格✖️数量提取（原始版）]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞")
.ARRAYJOIN("")
.REGEXREPLACE("[❶-❾](.*?)[❶-❾]🔞",[💰到手价格（终版）].REGEXEXTRACT("[❶-❾]")&"$1"&[💰到手价格（终版）].REGEXEXTRACT("[❶-❾]")&"🔞")

，

IF(
AND(
[🧪🤍规格✖️数量提取（原始版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.ISBLANK()
.NOT()

，
[💰到手价格（终版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.ISBLANK()


,
[💰到手价格（终版）]
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.COUNTA()=1
)
,
[🧪🤍规格✖️数量提取（原始版）]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞")
.ARRAYJOIN("")
.REGEXREPLACE("[❶-❾](.*?)[❶-❾]🔞",[💰到手价格（终版）].REGEXEXTRACT("[❶-❾]")&"$1"&[💰到手价格（终版）].REGEXEXTRACT("[❶-❾]")&"🔞")





，

IF(
AND(
[🧪🤍规格✖️数量提取（原始版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.ISBLANK()
.NOT()

，
[💰到手价格（终版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.ISBLANK()


,
[💰到手价格（终版）]
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.COUNTA()=1
)
,
[🧪🤍规格✖️数量提取（原始版）]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞")
.ARRAYJOIN("")
.REGEXREPLACE("[❶-❾](.*?)[❶-❾]🔞",[💰到手价格（终版）].REGEXEXTRACT("[❶-❾]")&"$1"&[💰到手价格（终版）].REGEXEXTRACT("[❶-❾]")&"🔞")





，

IF(
AND(
[🧪🤍规格✖️数量提取（原始版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.ISBLANK()
.NOT()

，
[💰到手价格（终版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.ISBLANK()


,
[💰到手价格（终版）]
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.COUNTA()=1
)
,
[🧪🤍规格✖️数量提取（原始版）]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞")
.ARRAYJOIN("")
.REGEXREPLACE("[❶-❾](.*?)[❶-❾]🔞",[💰到手价格（终版）].REGEXEXTRACT("[❶-❾]")&"$1"&[💰到手价格（终版）].REGEXEXTRACT("[❶-❾]")&"🔞")




，

[🧪🤍规格✖️数量提取（原始版）]))))))))))))


.REGEXREPLACE("([❶-❾]🔞)\s*([❶-❾])","$1；$2")

.REGEXREPLACE("(❶🔞)；(❷)","$1"&CHAR(10)&"$2")
.REGEXREPLACE("(❷🔞)；(❸)","$1"&CHAR(10)&"$2")
.REGEXREPLACE("(❸🔞)；(❹)","$1"&CHAR(10)&"$2")
.REGEXREPLACE("(❹🔞)；(❺)","$1"&CHAR(10)&"$2")
.REGEXREPLACE("(❺🔞)；(❻)","$1"&CHAR(10)&"$2")
.REGEXREPLACE("(❻🔞)；(❼)","$1"&CHAR(10)&"$2")
.REGEXREPLACE("(❼🔞)；(❽)","$1"&CHAR(10)&"$2")
.REGEXREPLACE("(❽🔞)；(❾)","$1"&CHAR(10)&"$2")
=====
🧪🤍规格✖️数量提取（单位统一版）-参考
IF([🍬商品信息（原始版）].ISBLANK()，""，
[🧪🤍规格✖️数量提取（优化版）])
.REGEXREPLACE("(\d+(?:\.\d+)?)(磅)","®$1×0.90718474斤®")
.REGEXREPLACE("(\d+(?:\.\d+)?)(kg|公斤|千克)","®$1×2斤®")
.REGEXREPLACE("(\d+(?:\.\d+)?)(克|g)","®$1÷500斤®")
.REGEXREPLACE("(\d+(?:\.\d+)?)(ml)","®$1÷1000l®")


.REGEXREPLACE("一定把单位前面的数字带上，继而给到定位符，这里用的是®，否则一旦出现一个nth的基础单位 和其他nth基础单位的小数目是一致的时候就会容易出错",)
=====
🧪🤍规格✖️数量提取（单位统一版）
IF([🍬商品信息（原始版）].ISBLANK()，""，
[🧪🤍规格✖️数量提取（单位统一版）-参考])

.REGEXREPLACE(
[🧪🤍规格✖️数量提取（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(®\d+(?:\.\d+)?\+\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(1)
.REGEXREPLACE([🧠需转义符号参考-逻辑],"\$0")
,

IF(
[🧪🤍规格✖️数量提取（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?\+\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(1)
.ISBLANK()
,""
,

([🧪🤍规格✖️数量提取（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?\+\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(1)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.VALUE()

+

[🧪🤍规格✖️数量提取（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?\+\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(1)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE())
.ROUNDUP(3)

))



.REGEXREPLACE("Ⓜ️Ⓜ️","")

.REGEXREPLACE(
[🧪🤍规格✖️数量提取（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(®\d+(?:\.\d+)?\+\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(2)
.REGEXREPLACE([🧠需转义符号参考-逻辑],"\$0")
,

IF(
[🧪🤍规格✖️数量提取（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?\+\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(2)
.ISBLANK()
,""
,

([🧪🤍规格✖️数量提取（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?\+\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(2)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.VALUE()

+

[🧪🤍规格✖️数量提取（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?\+\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(2)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE())
.ROUNDUP(3)

))



.REGEXREPLACE("Ⓜ️Ⓜ️","")

.REGEXREPLACE(
[🧪🤍规格✖️数量提取（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(®\d+(?:\.\d+)?\+\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(3)
.REGEXREPLACE([🧠需转义符号参考-逻辑],"\$0")
,

IF(
[🧪🤍规格✖️数量提取（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?\+\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(3)
.ISBLANK()
,""
,

([🧪🤍规格✖️数量提取（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?\+\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(3)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.VALUE()

+

[🧪🤍规格✖️数量提取（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?\+\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(3)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE())
.ROUNDUP(3)

))


.REGEXREPLACE("🔥🔥Ⓜ️Ⓜ️🔥🔥","")

.REGEXREPLACE(
[🧪🤍规格✖️数量提取（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(®\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(1)
,

IF(
[🧪🤍规格✖️数量提取（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(1)
.ISBLANK()
,""
,

([🧪🤍规格✖️数量提取（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(1)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.VALUE()

/

[🧪🤍规格✖️数量提取（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(1)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE())
.ROUNDUP(3)

))

.REGEXREPLACE("Ⓜ️Ⓜ️","")


.REGEXREPLACE(
[🧪🤍规格✖️数量提取（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(®\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(2)
,

IF(
[🧪🤍规格✖️数量提取（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(2)
.ISBLANK()
,""
,

([🧪🤍规格✖️数量提取（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(2)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.VALUE()

/

[🧪🤍规格✖️数量提取（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(2)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE())
.ROUNDUP(3)

))



.REGEXREPLACE("Ⓜ️Ⓜ️","")


.REGEXREPLACE(
[🧪🤍规格✖️数量提取（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(®\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(3)
,

IF(
[🧪🤍规格✖️数量提取（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(3)
.ISBLANK()
,""
,

([🧪🤍规格✖️数量提取（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(3)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.VALUE()

/

[🧪🤍规格✖️数量提取（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(3)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE())
.ROUNDUP(3)

))



.REGEXREPLACE("Ⓜ️Ⓜ️","")


.REGEXREPLACE(
[🧪🤍规格✖️数量提取（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(®\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(4)
,

IF(
[🧪🤍规格✖️数量提取（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(4)
.ISBLANK()
,""
,

([🧪🤍规格✖️数量提取（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(4)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.VALUE()

/

[🧪🤍规格✖️数量提取（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(4)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE())
.ROUNDUP(3)

))




.REGEXREPLACE("Ⓜ️Ⓜ️","")


.REGEXREPLACE(
[🧪🤍规格✖️数量提取（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(®\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(5)
,

IF(
[🧪🤍规格✖️数量提取（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(5)
.ISBLANK()
,""
,

([🧪🤍规格✖️数量提取（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(5)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.VALUE()

/

[🧪🤍规格✖️数量提取（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(5)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE())
.ROUNDUP(3)

))




.REGEXREPLACE("Ⓜ️Ⓜ️","")


.REGEXREPLACE(
[🧪🤍规格✖️数量提取（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(®\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(6)
,

IF(
[🧪🤍规格✖️数量提取（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(6)
.ISBLANK()
,""
,

([🧪🤍规格✖️数量提取（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(6)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.VALUE()

/

[🧪🤍规格✖️数量提取（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(6)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE())
.ROUNDUP(3)

))


.REGEXREPLACE("Ⓜ️Ⓜ️","")


.REGEXREPLACE(
[🧪🤍规格✖️数量提取（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(®\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(7)
,

IF(
[🧪🤍规格✖️数量提取（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(7)
.ISBLANK()
,""
,

([🧪🤍规格✖️数量提取（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(7)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.VALUE()

/

[🧪🤍规格✖️数量提取（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?÷\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(7)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE())
.ROUNDUP(3)

))


.REGEXREPLACE("🔥🔥Ⓜ️Ⓜ️🔥🔥","")


.REGEXREPLACE(
[🧪🤍规格✖️数量提取（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(®\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(1)

，
IF(
[🧪🤍规格✖️数量提取（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(1)
.ISBLANK()
,""
,

([🧪🤍规格✖️数量提取（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(1)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.VALUE()

*

[🧪🤍规格✖️数量提取（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(1)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE())
.ROUNDUP(3)

))



.REGEXREPLACE("Ⓜ️Ⓜ️","")


.REGEXREPLACE(
[🧪🤍规格✖️数量提取（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(®\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(2)
,

IF(
[🧪🤍规格✖️数量提取（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(2)
.ISBLANK()
,""
,

([🧪🤍规格✖️数量提取（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(2)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.VALUE()

*

[🧪🤍规格✖️数量提取（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(2)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE())
.ROUNDUP(3)

))




.REGEXREPLACE("Ⓜ️Ⓜ️","")


.REGEXREPLACE(
[🧪🤍规格✖️数量提取（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(®\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(3)
,

IF(
[🧪🤍规格✖️数量提取（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(3)
.ISBLANK()
,""
,

([🧪🤍规格✖️数量提取（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(3)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.VALUE()

*

[🧪🤍规格✖️数量提取（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(3)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE())
.ROUNDUP(3)

))



.REGEXREPLACE("Ⓜ️Ⓜ️","")


.REGEXREPLACE(
[🧪🤍规格✖️数量提取（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(®\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(4)
,

IF(
[🧪🤍规格✖️数量提取（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(4)
.ISBLANK()
,""
,

([🧪🤍规格✖️数量提取（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(4)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.VALUE()

*

[🧪🤍规格✖️数量提取（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(4)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE())
.ROUNDUP(3)

))




.REGEXREPLACE("Ⓜ️Ⓜ️","")


.REGEXREPLACE(
[🧪🤍规格✖️数量提取（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(®\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(5)
,

IF(
[🧪🤍规格✖️数量提取（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(5)
.ISBLANK()
,""
,

([🧪🤍规格✖️数量提取（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(5)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.VALUE()

*

[🧪🤍规格✖️数量提取（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(5)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE())
.ROUNDUP(3)

))




.REGEXREPLACE("Ⓜ️Ⓜ️","")


.REGEXREPLACE(
[🧪🤍规格✖️数量提取（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(®\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(6)
,

IF(
[🧪🤍规格✖️数量提取（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(6)
.ISBLANK()
,""
,

([🧪🤍规格✖️数量提取（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(6)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.VALUE()

*

[🧪🤍规格✖️数量提取（单位统一版）-参考]
.REGEXEXTRACTALL("(?:[❶-❾]).*?(\d+(?:\.\d+)?×\d+(?:\.\d+)?).*?(?:[❶-❾]🔞)")
.NTH(6)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE())
.ROUNDUP(3)

))

.REGEXREPLACE("®","")
.REGEXREPLACE("[❶-❾]🔞\*[❶-❾]","*")
.REGEXREPLACE("([❶-❾]🔞)\s*([❶-❾])","$1"&CHAR(10)&"$2")
.REGEXREPLACE("(^\d+.*?)([❶-❾])(🔞)","$2$1$3")


.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🧪🤍规格✖️数量提取（清洗版）（1-5）
IF([🍬商品信息（原始版）].ISBLANK()，""，

CONCATENATE(

IF(
AND(
  
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(1)
.REGEXMATCH("拍?.*?选项|.*?选项拍|拍\d+"&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位])
,
OR(
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(1)
.REGEXREPLACE("[❶-❾🔞]","")
.REGEXEXTRACTALL("\*(.*)")

.REGEXMATCH(
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(2)
.REGEXREPLACE("("&[🧠单位合集]&"|[❶-❾🔞])","")
)

,

AND(
IF(
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(1)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(1)
.ISBLANK()
,1
,
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(1)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(1)
.VALUE()
)
*
IF(
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(1)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(2)
.ISBLANK()
,1
,
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(1)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(2)
.VALUE()
)


=

IF(
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(2)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(1)
.ISBLANK()
,1
,
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(2)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(1)
.VALUE()
)
*
IF(
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(2)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(2)
.ISBLANK()
,1
,
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(2)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(2)
.VALUE()
)

，
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(1)
.REGEXEXTRACTALL([🧠单位合集]).NTH(1)

=

[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(2)
.REGEXEXTRACTALL([🧠单位合集]).NTH(1)


)))

,


[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❶.*?❶🔞")
.NTH(1)

，
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❶.*?❶🔞").ARRAYJOIN("；")
)


,CHAR(10),

IF(
AND(
  
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(2)
.REGEXMATCH("拍?.*?选项|.*?选项拍|拍\d+"&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位])
,
OR(
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(1)
.REGEXREPLACE("[❶-❾🔞]","")
.REGEXEXTRACTALL("\*(.*)")

.REGEXMATCH(
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(2)
.REGEXREPLACE("("&[🧠单位合集]&"|[❶-❾🔞])","")
)

,

AND(
IF(
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(1)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(1)
.ISBLANK()
,1
,
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(1)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(1)
.VALUE()
)
*
IF(
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(1)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(2)
.ISBLANK()
,1
,
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(1)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(2)
.VALUE()
)


=

IF(
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(2)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(1)
.ISBLANK()
,1
,
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(2)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(1)
.VALUE()
)
*
IF(
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(2)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(2)
.ISBLANK()
,1
,
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(2)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(2)
.VALUE()
)

,
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(1)
.REGEXEXTRACTALL([🧠单位合集]).NTH(1)

=

[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(2)
.REGEXEXTRACTALL([🧠单位合集]).NTH(1)


)))

,


[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❷.*?❷🔞")
.NTH(1)

,
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❷.*?❷🔞").ARRAYJOIN("；")
)





,CHAR(10),

IF(
AND(
  
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(3)
.REGEXMATCH("拍?.*?选项|.*?选项拍|拍\d+"&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位])
,
OR(
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(1)
.REGEXREPLACE("[❶-❾🔞]","")
.REGEXEXTRACTALL("\*(.*)")

.REGEXMATCH(
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(2)
.REGEXREPLACE("("&[🧠单位合集]&"|[❶-❾🔞])","")
)

,

AND(
IF(
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(1)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(1)
.ISBLANK()
,1
,
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(1)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(1)
.VALUE()
)
*
IF(
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(1)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(2)
.ISBLANK()
,1
,
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(1)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(2)
.VALUE()
)


=

IF(
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(2)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(1)
.ISBLANK()
,1
,
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(2)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(1)
.VALUE()
)
*
IF(
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(2)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(2)
.ISBLANK()
,1
,
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(2)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(2)
.VALUE()
)

,
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(1)
.REGEXEXTRACTALL([🧠单位合集]).NTH(1)

=

[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(2)
.REGEXEXTRACTALL([🧠单位合集]).NTH(1)


)))

,


[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❸.*?❸🔞")
.NTH(1)

,
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❸.*?❸🔞").ARRAYJOIN("；")
)




,CHAR(10),

IF(
AND(
  
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(4)
.REGEXMATCH("拍?.*?选项|.*?选项拍|拍\d+"&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位])
,
OR(
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(1)
.REGEXREPLACE("[❶-❾🔞]","")
.REGEXEXTRACTALL("\*(.*)")

.REGEXMATCH(
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(2)
.REGEXREPLACE("("&[🧠单位合集]&"|[❶-❾🔞])","")
)

,

AND(
IF(
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(1)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(1)
.ISBLANK()
,1
,
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(1)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(1)
.VALUE()
)
*
IF(
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(1)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(2)
.ISBLANK()
,1
,
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(1)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(2)
.VALUE()
)


=

IF(
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(2)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(1)
.ISBLANK()
,1
,
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(2)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(1)
.VALUE()
)
*
IF(
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(2)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(2)
.ISBLANK()
,1
,
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(2)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(2)
.VALUE()
)

,
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(1)
.REGEXEXTRACTALL([🧠单位合集]).NTH(1)

=

[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(2)
.REGEXEXTRACTALL([🧠单位合集]).NTH(1)


)))

,


[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❹.*?❹🔞")
.NTH(1)

,
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❹.*?❹🔞").ARRAYJOIN("；")
)



,CHAR(10),

IF(
AND(
  
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(5)
.REGEXMATCH("拍?.*?选项|.*?选项拍|拍\d+"&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位])
,
OR(
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(1)
.REGEXREPLACE("[❶-❾🔞]","")
.REGEXEXTRACTALL("\*(.*)")

.REGEXMATCH(
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(2)
.REGEXREPLACE("("&[🧠单位合集]&"|[❶-❾🔞])","")
)

,

AND(
IF(
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(1)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(1)
.ISBLANK()
,1
,
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(1)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(1)
.VALUE()
)
*
IF(
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(1)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(2)
.ISBLANK()
,1
,
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(1)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(2)
.VALUE()
)


=

IF(
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(2)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(1)
.ISBLANK()
,1
,
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(2)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(1)
.VALUE()
)
*
IF(
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(2)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(2)
.ISBLANK()
,1
,
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(2)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(2)
.VALUE()
)

,
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(1)
.REGEXEXTRACTALL([🧠单位合集]).NTH(1)

=

[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(2)
.REGEXEXTRACTALL([🧠单位合集]).NTH(1)


)))

,


[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❺.*?❺🔞")
.NTH(1)

,
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❺.*?❺🔞").ARRAYJOIN("；")
)
))

.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🧪🤍规格✖️数量提取（清洗版）（6-9）
IF([🍬商品信息（原始版）].ISBLANK()，""，

CONCATENATE(

IF(
AND(
  
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(6)
.REGEXMATCH("拍?.*?选项|.*?选项拍|拍\d+"&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位])
,
OR(
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(1)
.REGEXREPLACE("[❶-❾🔞]","")
.REGEXEXTRACTALL("\*(.*)")

.REGEXMATCH(
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(2)
.REGEXREPLACE("("&[🧠单位合集]&"|[❶-❾🔞])","")
)

,

AND(
IF(
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(1)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(1)
.ISBLANK()
,1
,
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(1)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(1)
.VALUE()
)
*
IF(
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(1)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(2)
.ISBLANK()
,1
,
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(1)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(2)
.VALUE()
)


=

IF(
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(2)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(1)
.ISBLANK()
,1
,
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(2)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(1)
.VALUE()
)
*
IF(
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(2)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(2)
.ISBLANK()
,1
,
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(2)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(2)
.VALUE()
)

,
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(1)
.REGEXEXTRACTALL([🧠单位合集]).NTH(1)

=

[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(2)
.REGEXEXTRACTALL([🧠单位合集]).NTH(1)


)))

,


[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❻.*?❻🔞")
.NTH(1)

,
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❻.*?❻🔞").ARRAYJOIN("；")
)




,CHAR(10),

IF(
AND(
  
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(7)
.REGEXMATCH("拍?.*?选项|.*?选项拍|拍\d+"&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位])
,
OR(
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(1)
.REGEXREPLACE("[❶-❾🔞]","")
.REGEXEXTRACTALL("\*(.*)")

.REGEXMATCH(
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(2)
.REGEXREPLACE("("&[🧠单位合集]&"|[❶-❾🔞])","")
)

,

AND(
IF(
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(1)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(1)
.ISBLANK()
,1
,
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(1)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(1)
.VALUE()
)
*
IF(
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(1)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(2)
.ISBLANK()
,1
,
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(1)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(2)
.VALUE()
)


=

IF(
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(2)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(1)
.ISBLANK()
,1
,
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(2)
.REGEXEXTRACTALL("\d+(?:\.\餐饮+)?")
.NTH(1)
.VALUE()
)
*
IF(
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(2)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(2)
.ISBLANK()
,1
,
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(2)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(2)
.VALUE()
)

,
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(1)
.REGEXEXTRACTALL([🧠单位合集]).NTH(1)

=

[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(2)
.REGEXEXTRACTALL([🧠单位合集]).NTH(1)


)))

,


[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❼.*?❼🔞")
.NTH(1)

,
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❼.*?❼🔞").ARRAYJOIN("；")
)


,CHAR(10),

IF(
AND(
  
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(8)
.REGEXMATCH("拍?.*?选项|.*?选项拍|拍\d+"&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位])
,
OR(
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(1)
.REGEXREPLACE("[❶-❾🔞]","")
.REGEXEXTRACTALL("\*(.*)")

.REGEXMATCH(
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(2)
.REGEXREPLACE("("&[🧠单位合集]&"|[❶-❾🔞])","")
)

,

AND(
IF(
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(1)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(1)
.ISBLANK()
,1
,
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(1)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(1)
.VALUE()
)
*
IF(
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(1)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(2)
.ISBLANK()
,1
,
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(1)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(2)
.VALUE()
)


=

IF(
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(2)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(1)
.ISBLANK()
,1
,
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(2)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(1)
.VALUE()
)
*
IF(
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(2)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(2)
.ISBLANK()
,1
,
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(2)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(2)
.VALUE()
)

,
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(1)
.REGEXEXTRACTALL([🧠单位合集]).NTH(1)

=

[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(2)
.REGEXEXTRACTALL([🧠单位合集]).NTH(1)


)))

,


[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❽.*?❽🔞")
.NTH(1)

,
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❽.*?❽🔞").ARRAYJOIN("；")
)


,CHAR(10),

IF(
AND(
  
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(9)
.REGEXMATCH("拍?.*?选项|.*?选项拍|拍\d+"&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位])
,
OR(
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(1)
.REGEXREPLACE("[❶-❾🔞]","")
.REGEXEXTRACTALL("\*(.*)")

.REGEXMATCH(
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(2)
.REGEXREPLACE("("&[🧠单位合集]&"|[❶-❾🔞])","")
)

,

AND(
IF(
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(1)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(1)
.ISBLANK()
,1
,
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(1)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(1)
.VALUE()
)
*
IF(
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(1)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(2)
.ISBLANK()
,1
,
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(1)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(2)
.VALUE()
)


=

IF(
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(2)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(1)
.ISBLANK()
,1
,
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(2)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(1)
.VALUE()
)
*
IF(
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(2)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(2)
.ISBLANK()
,1
,
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(2)
.REGEXEXTRACTALL("\d+(?:\.\d+)?")
.NTH(2)
.VALUE()
)

,
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(1)
.REGEXEXTRACTALL([🧠单位合集]).NTH(1)

=

[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(2)
.REGEXEXTRACTALL([🧠单位合集]).NTH(1)


)))

,


[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❾.*?❾🔞")
.NTH(1)

,
[🧪🤍规格✖️数量提取（单位统一版）]
.REGEXEXTRACTALL("❾.*?❾🔞").ARRAYJOIN("；")
)

))

.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🧪🤍规格✖️数量提取（清洗版）
CONCATENATE(
[🧪🤍规格✖️数量提取（清洗版）（1-5）]
，CHAR(10)，
[🧪🤍规格✖️数量提取（清洗版）（6-9）])

.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🧪🔥🤍规格✖️数量提取（终版）
IF([🍬商品信息（原始版）].ISBLANK()，""，

[🧪🤍规格✖️数量提取（清洗版）])

.REGEXREPLACE("Ⓜ️将在同一个sku下但是不同纬度的规格，拼合起来👇","")

.REGEXREPLACE("([❶-❾]\d+(?:\.\d+)?"&[🧠基础单位]&")[❶-❾]🔞；[❶-❾](\d+(?:"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&"|"&[🧠终极单位]&"))","$1*$2")

.REGEXREPLACE("([❶-❾]\d+(?:\.\d+)?"&[🧠初级单位]&")[❶-❾]🔞；[❶-❾](\d+(?:"&[🧠进阶单位]&"|"&[🧠高阶单位]&"|"&[🧠终极单位]&"))","$1*$2")

.REGEXREPLACE("([❶-❾]\d+(?:\.\d+)?"&[🧠进阶单位]&")[❶-❾]🔞；[❶-❾](\d+(?:"&[🧠高阶单位]&"|"&[🧠终极单位]&"))","$1*$2")

.REGEXREPLACE("([❶-❾]\d+(?:\.\d+)?"&[🧠高阶单位]&")[❶-❾]🔞；[❶-❾](\d+(?:"&[🧠终极单位]&"))","$1*$2")


.REGEXREPLACE("([❶-❾]\d+(?:\.\d+)?"&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")[❶-❾]🔞；[❶-❾](\d+[❶-❾]🔞)","$1*$2")



.REGEXREPLACE("Ⓜ️一级sku 下一个低纬度规格+多个同样的多维度规格，对每一个多维度规格进行信息补全👇","")

.REGEXREPLACE("基础单位✖️更高阶单位👇","")

.REGEXREPLACE("([❶-❾]\d+(?:\.\d+)?"&[🧠基础单位]&")(\*\d+(?:"&[🧠初级单位]&")[❶-❾]🔞)；[❶-❾](\d+(?:"&[🧠初级单位]&"))","$1$2；$1*$3")

.REGEXREPLACE("([❶-❾]\d+(?:\.\d+)?"&[🧠基础单位]&")(\*\d+(?:"&[🧠进阶单位]&")[❶-❾]🔞)；[❶-❾](\d+(?:"&[🧠进阶单位]&"))","$1$2；$1*$3")

.REGEXREPLACE("([❶-❾]\d+(?:\.\d+)?"&[🧠基础单位]&")(\*\d+(?:"&[🧠高阶单位]&")[❶-❾]🔞)；[❶-❾](\d+(?:"&[🧠高阶单位]&"))","$1$2；$1*$3")

.REGEXREPLACE("([❶-❾]\d+(?:\.\d+)?"&[🧠基础单位]&")(\*\d+(?:"&[🧠终极单位]&")[❶-❾]🔞)；[❶-❾](\d+(?:"&[🧠终极单位]&"))","$1$2；$1*$3")

.REGEXREPLACE("([❶-❾]\d+(?:\.\d+)?"&[🧠基础单位]&")(\*\d+[❶-❾]🔞)；[❶-❾](\d+[❶-❾]🔞)","$1$2；$1*$3")


.REGEXREPLACE("初级单位✖️更高阶单位👇","")

.REGEXREPLACE("([❶-❾]\d+(?:\.\d+)?"&[🧠初级单位]&")(\*\d+(?:"&[🧠进阶单位]&")[❶-❾]🔞)；[❶-❾](\d+(?:"&[🧠进阶单位]&"))","$1$2；$1*$3")

.REGEXREPLACE("([❶-❾]\d+(?:\.\d+)?"&[🧠初级单位]&")(\*\d+(?:"&[🧠高阶单位]&")[❶-❾]🔞)；[❶-❾](\d+(?:"&[🧠高阶单位]&"))","$1$2；$1*$3")

.REGEXREPLACE("([❶-❾]\d+(?:\.\d+)?"&[🧠初级单位]&")(\*\d+(?:"&[🧠终极单位]&")[❶-❾]🔞)；[❶-❾](\d+(?:"&[🧠终极单位]&"))","$1$2；$1*$3")

.REGEXREPLACE("([❶-❾]\d+(?:\.\d+)?"&[🧠初级单位]&")(\*\d+[❶-❾]🔞)；[❶-❾](\d+[❶-❾]🔞)","$1$2；$1*$3")


.REGEXREPLACE("进阶单位✖️更高阶单位👇","")

.REGEXREPLACE("([❶-❾]\d+(?:\.\d+)?"&[🧠进阶单位]&")(\*\d+(?:"&[🧠高阶单位]&")[❶-❾]🔞)；[❶-❾](\d+(?:"&[🧠高阶单位]&"))","$1$2；$1*$3")

.REGEXREPLACE("([❶-❾]\d+(?:\.\d+)?"&[🧠进阶单位]&")(\*\d+(?:"&[🧠终极单位]&")[❶-❾]🔞)；[❶-❾](\d+(?:"&[🧠终极单位]&"))","$1$2；$1*$3")

.REGEXREPLACE("([❶-❾]\d+(?:\.\d+)?"&[🧠进阶单位]&")(\*\d+[❶-❾]🔞)；[❶-❾](\d+[❶-❾]🔞)","$1$2；$1*$3")



.REGEXREPLACE("高阶单位✖️更高阶单位👇","")

.REGEXREPLACE("([❶-❾]\d+(?:\.\d+)?"&[🧠高阶单位]&")(\*\d+(?:"&[🧠终极单位]&")[❶-❾]🔞)；[❶-❾](\d+(?:"&[🧠高阶单位]&"))","$1$2；$1*$3")

.REGEXREPLACE("([❶-❾]\d+(?:\.\d+)?"&[🧠高阶单位]&")(\*\d+[❶-❾]🔞)；[❶-❾](\d+[❶-❾]🔞)","$1$2；$1*$3")




.REGEXREPLACE("([❶-❾]🔞)\s*([❶-❾])","$1"&CHAR(10)&"$2")

.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🧪🤍规格✖️数量提取（多维度规格版）（1-4）
CONCATENATE(


IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(1)
.ISBLANK()

,"",


[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞")
.NTH(1)

.REGEXREPLACE(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(1)
.REGEXREPLACE([🧠需转义符号参考-逻辑],"\$0")

,
CONCATENATE(
(
IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(1)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.ISBLANK()
,1,

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(1)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.VALUE())

*
IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(1)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.ISBLANK()
,1,

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(1)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE()

)

*

IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(1)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(3)
.ISBLANK()
,1,

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(1)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(3)
.VALUE())
)
.ROUNDUP(3)
.CONCATENATE([🧪🔥🤍规格✖️数量提取（终版）]
.REGEXREPLACE([🧠需转义符号参考-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(1)

.REGEXEXTRACTALL([🧠单位合集])
.NTH(1))


," | ",

(IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(1)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.ISBLANK()
,1,

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(1)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE())

*
IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(1)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(3)
.ISBLANK()
,1,

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(1)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(3)
.VALUE())
)

.ROUNDUP(3)
.CONCATENATE([🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(1)
.REGEXREPLACE([🧠需转义符号参考-逻辑],"\$0")

.REGEXEXTRACTALL([🧠单位合集]).NTH(2))




," | ",


IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(1)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(3)
.ISBLANK()
,1,

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(1)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(3)
.VALUE())
.ROUNDUP(3)
.CONCATENATE([🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(1)
.REGEXREPLACE([🧠需转义符号参考-逻辑],"\$0")

.REGEXEXTRACTALL([🧠单位合集]).NTH(3))


)))



,CHAR(10)&"Ⓜ️Ⓜ️Ⓜ️分割符Ⓜ️Ⓜ️Ⓜ️"&CHAR(10),



IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(2)
.ISBLANK()

,"",


[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞")
.NTH(2)

.REGEXREPLACE(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(2)
.REGEXREPLACE([🧠需转义符号参考-逻辑],"\$0")

,
CONCATENATE(
(
IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(2)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.ISBLANK()
,1,

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(2)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.VALUE())

*
IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(2)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.ISBLANK()
,1,

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(2)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE()

)

*

IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(2)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(3)
.ISBLANK()
,1,

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(2)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(3)
.VALUE())
)
.ROUNDUP(3)
.CONCATENATE([🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(2)
.REGEXREPLACE([🧠需转义符号参考-逻辑],"\$0")

.REGEXEXTRACTALL([🧠单位合集])
.NTH(1))


," | ",

(IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(2)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.ISBLANK()
,1,

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(2)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE())

*
IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(2)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(3)
.ISBLANK()
,1,

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(2)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(3)
.VALUE())
)

.ROUNDUP(3)
.CONCATENATE([🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(2)
.REGEXREPLACE([🧠需转义符号参考-逻辑],"\$0")

.REGEXEXTRACTALL([🧠单位合集])
.NTH(2))




," | ",


IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(2)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(3)
.ISBLANK()
,1,

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(2)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(3)
.VALUE())
.ROUNDUP(3)
.CONCATENATE([🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.REGEXREPLACE([🧠需转义符号参考-逻辑],"\$0")
.NTH(2)
.REGEXEXTRACTALL([🧠单位合集])
.NTH(3))


)))




,CHAR(10)&"Ⓜ️Ⓜ️Ⓜ️分割符Ⓜ️Ⓜ️Ⓜ️"&CHAR(10),



IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(3)
.ISBLANK()

,"",


[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞")
.NTH(3)

.REGEXREPLACE(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(3)
.REGEXREPLACE([🧠需转义符号参考-逻辑],"\$0")

,
CONCATENATE(
(
IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(3)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.ISBLANK()
,1,

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(3)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.VALUE())

*
IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(3)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.ISBLANK()
,1,

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(3)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE()

)

*

IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(3)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(3)
.ISBLANK()
,1,

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(3)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(3)
.VALUE())
)
.ROUNDUP(3)
.CONCATENATE([🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(3)
.REGEXREPLACE([🧠需转义符号参考-逻辑],"\$0")

.REGEXEXTRACTALL([🧠单位合集])
.NTH(1))


," | ",

(IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(3)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.ISBLANK()
,1,

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(3)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE())

*
IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(3)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(3)
.ISBLANK()
,1,

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(3)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(3)
.VALUE())
)

.ROUNDUP(3)
.CONCATENATE([🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(3)
.REGEXREPLACE([🧠需转义符号参考-逻辑],"\$0")

.REGEXEXTRACTALL([🧠单位合集])
.NTH(2))




," | ",


IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(3)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(3)
.ISBLANK()
,1,

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(3)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(3)
.VALUE())
.ROUNDUP(3)
.CONCATENATE([🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.REGEXREPLACE([🧠需转义符号参考-逻辑],"\$0")
.NTH(3)
.REGEXEXTRACTALL([🧠单位合集])
.NTH(3))


)))





,CHAR(10)&"Ⓜ️Ⓜ️Ⓜ️分割符Ⓜ️Ⓜ️Ⓜ️"&CHAR(10),



IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(4)
.ISBLANK()

,"",


[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞")
.NTH(4)

.REGEXREPLACE(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(4)
.REGEXREPLACE([🧠需转义符号参考-逻辑],"\$0")

,
CONCATENATE(
(
IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(4)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.ISBLANK()
,1,

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(4)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.VALUE())

*
IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(4)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.ISBLANK()
,1,

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(4)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE()

)

*

IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(4)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(3)
.ISBLANK()
,1,

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(4)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(3)
.VALUE())
)
.ROUNDUP(3)
.CONCATENATE([🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(4)
.REGEXREPLACE([🧠需转义符号参考-逻辑],"\$0")

.REGEXEXTRACTALL([🧠单位合集])
.NTH(1))


," | ",

(IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(4)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.ISBLANK()
,1,

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(4)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE())

*
IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(4)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(3)
.ISBLANK()
,1,

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(4)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(3)
.VALUE())
)

.ROUNDUP(3)
.CONCATENATE([🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(4)
.REGEXREPLACE([🧠需转义符号参考-逻辑],"\$0")

.REGEXEXTRACTALL([🧠单位合集])
.NTH(2))




," | ",


IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(4)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(3)
.ISBLANK()
,1,

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(4)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(3)
.VALUE())
.ROUNDUP(3)
.CONCATENATE([🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.REGEXREPLACE([🧠需转义符号参考-逻辑],"\$0")
.NTH(4)
.REGEXEXTRACTALL([🧠单位合集])
.NTH(3))


)))





)

.REGEXREPLACE("(?:\|\s*1\s*)+([❶-❾]🔞)","$1")
.REGEXREPLACE("Ⓜ️Ⓜ️Ⓜ️分割符Ⓜ️Ⓜ️Ⓜ️","")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🧪🤍规格✖️数量提取（多维度规格版）（5-8）
CONCATENATE(


IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(5)
.ISBLANK()

,"",


[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞")
.NTH(5)

.REGEXREPLACE(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(5)
.REGEXREPLACE([🧠需转义符号参考-逻辑],"\$0")


,
CONCATENATE(
(
IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(5)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.ISBLANK()
,1,

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(5)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.VALUE())

*
IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(5)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.ISBLANK()
,1,

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(5)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE()

)

*

IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(5)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(3)
.ISBLANK()
,1,

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(5)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(3)
.VALUE())
)
.ROUNDUP(3)
.CONCATENATE([🧪🔥🤍规格✖️数量提取（终版）]
.REGEXREPLACE([🧠需转义符号参考-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(5)
.REGEXEXTRACTALL([🧠单位合集])
.NTH(1))


," | ",

(IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(5)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.ISBLANK()
,1,

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(5)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE())

*
IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(5)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(3)
.ISBLANK()
,1,

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(5)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(3)
.VALUE())
)

.ROUNDUP(3)
.CONCATENATE([🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(5)
.REGEXREPLACE([🧠需转义符号参考-逻辑],"\$0")

.REGEXEXTRACTALL([🧠单位合集]).NTH(2))




," | ",


IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(5)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(3)
.ISBLANK()
,1,

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(5)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(3)
.VALUE())
.ROUNDUP(3)
.CONCATENATE([🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(5)
.REGEXREPLACE([🧠需转义符号参考-逻辑],"\$0")

.REGEXEXTRACTALL([🧠单位合集]).NTH(3))


)))



,CHAR(10)&"Ⓜ️Ⓜ️Ⓜ️分割符Ⓜ️Ⓜ️Ⓜ️"&CHAR(10),



IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(6)
.ISBLANK()

,"",


[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞")
.NTH(6)

.REGEXREPLACE(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(6)
.REGEXREPLACE([🧠需转义符号参考-逻辑],"\$0")

,
CONCATENATE(
(
IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(6)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.ISBLANK()
,1,

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(6)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.VALUE())

*
IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(6)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.ISBLANK()
,1,

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(6)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE()

)

*

IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(6)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(3)
.ISBLANK()
,1,

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(6)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(3)
.VALUE())
)
.ROUNDUP(3)
.CONCATENATE([🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(6)
.REGEXREPLACE([🧠需转义符号参考-逻辑],"\$0")

.REGEXEXTRACTALL([🧠单位合集])
.NTH(1))


," | ",

(IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(6)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.ISBLANK()
,1,

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(6)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE())

*
IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(6)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(3)
.ISBLANK()
,1,

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(6)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(3)
.VALUE())
)

.ROUNDUP(3)
.CONCATENATE([🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(6)
.REGEXREPLACE([🧠需转义符号参考-逻辑],"\$0")

.REGEXEXTRACTALL([🧠单位合集])
.NTH(2))




," | ",


IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(6)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(3)
.ISBLANK()
,1,

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(6)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(3)
.VALUE())
.ROUNDUP(3)
.CONCATENATE([🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.REGEXREPLACE([🧠需转义符号参考-逻辑],"\$0")
.NTH(6)
.REGEXEXTRACTALL([🧠单位合集])
.NTH(3))


)))




,CHAR(10)&"Ⓜ️Ⓜ️Ⓜ️分割符Ⓜ️Ⓜ️Ⓜ️"&CHAR(10),



IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(7)
.ISBLANK()

,"",


[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞")
.NTH(7)

.REGEXREPLACE(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(7)
.REGEXREPLACE([🧠需转义符号参考-逻辑],"\$0")

,
CONCATENATE(
(
IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(7)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.ISBLANK()
,1,

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(7)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.VALUE())

*
IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(7)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.ISBLANK()
,1,

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(7)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE()

)

*

IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(7)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(3)
.ISBLANK()
,1,

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(7)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(3)
.VALUE())
)
.ROUNDUP(3)
.CONCATENATE([🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(7)
.REGEXREPLACE([🧠需转义符号参考-逻辑],"\$0")

.REGEXEXTRACTALL([🧠单位合集])
.NTH(1))


," | ",

(IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(7)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.ISBLANK()
,1,

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(7)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE())

*
IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(7)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(3)
.ISBLANK()
,1,

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(7)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(3)
.VALUE())
)

.ROUNDUP(3)
.CONCATENATE([🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(7)
.REGEXREPLACE([🧠需转义符号参考-逻辑],"\$0")

.REGEXEXTRACTALL([🧠单位合集])
.NTH(2))




," | ",


IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(7)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(3)
.ISBLANK()
,1,

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(7)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(3)
.VALUE())
.ROUNDUP(3)
.CONCATENATE([🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.REGEXREPLACE([🧠需转义符号参考-逻辑],"\$0")
.NTH(7)
.REGEXEXTRACTALL([🧠单位合集])
.NTH(3))


)))





,CHAR(10)&"Ⓜ️Ⓜ️Ⓜ️分割符Ⓜ️Ⓜ️Ⓜ️"&CHAR(10),



IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(8)
.ISBLANK()

,"",


[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞")
.NTH(8)

.REGEXREPLACE(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(8)
.REGEXREPLACE([🧠需转义符号参考-逻辑],"\$0")

,
CONCATENATE(
(
IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(8)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.ISBLANK()
,1,

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(8)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.VALUE())

*
IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(8)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.ISBLANK()
,1,

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(8)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE()

)

*

IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(8)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(3)
.ISBLANK()
,1,

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(8)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(3)
.VALUE())
)
.ROUNDUP(3)
.CONCATENATE([🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(8)
.REGEXREPLACE([🧠需转义符号参考-逻辑],"\$0")

.REGEXEXTRACTALL([🧠单位合集])
.NTH(1))


," | ",

(IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(8)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.ISBLANK()
,1,

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(8)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE())

*
IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(8)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(3)
.ISBLANK()
,1,

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(8)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(3)
.VALUE())
)

.ROUNDUP(3)
.CONCATENATE([🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(8)
.REGEXREPLACE([🧠需转义符号参考-逻辑],"\$0")

.REGEXEXTRACTALL([🧠单位合集])
.NTH(2))




," | ",


IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(8)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(3)
.ISBLANK()
,1,

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(8)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(3)
.VALUE())
.ROUNDUP(3)
.CONCATENATE([🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.REGEXREPLACE([🧠需转义符号参考-逻辑],"\$0")
.NTH(8)
.REGEXEXTRACTALL([🧠单位合集])
.NTH(3))


)))





)

.REGEXREPLACE("(?:\|\s*1\s*)+([❶-❾]🔞)","$1")
.REGEXREPLACE("Ⓜ️Ⓜ️Ⓜ️分割符Ⓜ️Ⓜ️Ⓜ️","")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🧪🤍规格✖️数量提取（多维度规格版）（9）
CONCATENATE(


IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(9)
.ISBLANK()

,"",


[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞")
.NTH(9)

.REGEXREPLACE(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(9)
.REGEXREPLACE([🧠需转义符号参考-逻辑],"\$0")


,
CONCATENATE(
(
IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(9)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.ISBLANK()
,1,

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(9)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(1)
.VALUE())

*
IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(9)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.ISBLANK()
,1,

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(9)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE()

)

*

IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(9)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(3)
.ISBLANK()
,1,

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(9)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(3)
.VALUE())
)
.ROUNDUP(3)
.CONCATENATE([🧪🔥🤍规格✖️数量提取（终版）]
.REGEXREPLACE([🧠需转义符号参考-逻辑],"\$0")
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(9)
.REGEXEXTRACTALL([🧠单位合集])
.NTH(1))


," | ",

(IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(9)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.ISBLANK()
,1,

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(9)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(2)
.VALUE())

*
IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(9)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(3)
.ISBLANK()
,1,

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(9)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(3)
.VALUE())
)

.ROUNDUP(3)
.CONCATENATE([🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(9)
.REGEXREPLACE([🧠需转义符号参考-逻辑],"\$0")

.REGEXEXTRACTALL([🧠单位合集]).NTH(2))




," | ",


IF(
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(9)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(3)
.ISBLANK()
,1,

[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(9)
.REGEXEXTRACTALL("(\d+(?:\.\d+)?)")
.NTH(3)
.VALUE())
.ROUNDUP(3)
.CONCATENATE([🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("[❶-❾](.*?)[❶-❾]🔞")
.NTH(9)
.REGEXREPLACE([🧠需转义符号参考-逻辑],"\$0")

.REGEXEXTRACTALL([🧠单位合集]).NTH(3))


)))




)

.REGEXREPLACE("(?:\|\s*1\s*)+([❶-❾]🔞)","$1")
.REGEXREPLACE("Ⓜ️Ⓜ️Ⓜ️分割符Ⓜ️Ⓜ️Ⓜ️","")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🧪🤍规格✖️数量提取（多维度规格版）
CONCATENATE(
[🧪🤍规格✖️数量提取（多维度规格版）（1-4）]
，CHAR(10)，
[🧪🤍规格✖️数量提取（多维度规格版）（5-8）]
，CHAR(10)，
[🧪🤍规格✖️数量提取（多维度规格版）（9）])
=====
🧪🤍规格✖️数量提取（单位回溯版）-参考1
CONCATENATE(
IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(1)
.ISBLANK()
，"",

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(1)
.REGEXMATCH("磅")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(1)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷0.90718474"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(1).REGEXEXTRACTALL([🧠基础单位])&"®")
,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(1)
.REGEXMATCH("kg|公斤|千克")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(1)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷2"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(1).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(1)
.REGEXMATCH("克|g")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(1)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2×500"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(1).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(1)
.REGEXMATCH("ml")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(1)
.REGEXREPLACE("((\d+(?:\.\d+)?)l)","®$2×1000"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(1).REGEXEXTRACTALL([🧠基础单位])&"®")

，
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(1)

)))))

,"；"，


IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(2)
.ISBLANK()
，"",

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(2)
.REGEXMATCH("磅")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(2)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷0.90718474"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(2).REGEXEXTRACTALL([🧠基础单位])&"®")
,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(2)
.REGEXMATCH("kg|公斤|千克")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(2)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷2"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(2).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
 
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(2)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2×500"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(2).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(2)
.REGEXMATCH("ml")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(2)
.REGEXREPLACE("((\d+(?:\.\d+)?)l)","®$2×1000"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(2).REGEXEXTRACTALL([🧠基础单位])&"®")

，
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(2)

)))))


,"；"，

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(3)
.ISBLANK()
，"",


IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(3)
.REGEXMATCH("磅")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(3)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷0.90718474"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(3).REGEXEXTRACTALL([🧠基础单位])&"®")
,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(3)
.REGEXMATCH("kg|公斤|千克")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(3)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷2"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(3).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(3)
.REGEXMATCH("克|g")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(3)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2×500"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(3).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(3)
.REGEXMATCH("ml")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(3)
.REGEXREPLACE("((\d+(?:\.\d+)?)l)","®$2×1000"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(3).REGEXEXTRACTALL([🧠基础单位])&"®")

，
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(3)

)))))

,"；"，

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(4)
.ISBLANK()
，"",

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(4)
.REGEXMATCH("磅")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(4)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷0.90718474"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(4).REGEXEXTRACTALL([🧠基础单位])&"®")
,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(4)
.REGEXMATCH("kg|公斤|千克")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(4)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷2"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(4).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(4)
.REGEXMATCH("克|g")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(4)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2×500"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(4).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(4)
.REGEXMATCH("ml")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(4)
.REGEXREPLACE("((\d+(?:\.\d+)?)l)","®$2×1000"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(4).REGEXEXTRACTALL([🧠基础单位])&"®")

，
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(4)

)))))

,"；"，

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(5)
.ISBLANK()
，"",


IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(5)
.REGEXMATCH("磅")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(5)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷0.90718474"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(5).REGEXEXTRACTALL([🧠基础单位])&"®")
,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(5)
.REGEXMATCH("kg|公斤|千克")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(5)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷2"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(5).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(5)
.REGEXMATCH("克|g")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(5)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2×500"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(5).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(5)
.REGEXMATCH("ml")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(5)
.REGEXREPLACE("((\d+(?:\.\d+)?)l)","®$2×1000"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(5).REGEXEXTRACTALL([🧠基础单位])&"®")

，
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(5)

)))))


,"；"，

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(6)
.ISBLANK()
，"",


IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(6)
.REGEXMATCH("磅")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(6)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷0.90718474"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(6).REGEXEXTRACTALL([🧠基础单位])&"®")
,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(6)
.REGEXMATCH("kg|公斤|千克")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(6)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷2"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(6).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(6)
.REGEXMATCH("克|g")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(6)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2×500"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(6).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(6)
.REGEXMATCH("ml")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(6)
.REGEXREPLACE("((\d+(?:\.\d+)?)l)","®$2×1000"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(6).REGEXEXTRACTALL([🧠基础单位])&"®")

，
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❶.*?❶🔞").NTH(6)

)))))

)

.REGEXREPLACE("；+","；")
.REGEXREPLACE("^；","")
.REGEXREPLACE("；$","")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🧪🤍规格✖️数量提取（单位回溯版）-参考2
CONCATENATE(
IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(1)
.ISBLANK()
，"",

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(1)
.REGEXMATCH("磅")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(1)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷0.90718474"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(1).REGEXEXTRACTALL([🧠基础单位])&"®")
,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(1)
.REGEXMATCH("kg|公斤|千克")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(1)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷2"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(1).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(1)
.REGEXMATCH("克|g")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(1)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2×500"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(1).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(1)
.REGEXMATCH("ml")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(1)
.REGEXREPLACE("((\d+(?:\.\d+)?)l)","®$2×1000"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(1).REGEXEXTRACTALL([🧠基础单位])&"®")

，
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(1)

)))))

,"；"，


IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(2)
.ISBLANK()
，"",

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(2)
.REGEXMATCH("磅")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(2)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷0.90718474"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(2).REGEXEXTRACTALL([🧠基础单位])&"®")
,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(2)
.REGEXMATCH("kg|公斤|千克")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(2)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷2"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(2).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(2)
.REGEXMATCH("克|g")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(2)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2×500"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(2).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(2)
.REGEXMATCH("ml")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(2)
.REGEXREPLACE("((\d+(?:\.\d+)?)l)","®$2×1000"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(2).REGEXEXTRACTALL([🧠基础单位])&"®")

，
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(2)

)))))

,"；"，

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(3)
.ISBLANK()
，"",

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(3)
.REGEXMATCH("磅")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(3)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷0.90718474"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(3).REGEXEXTRACTALL([🧠基础单位])&"®")
,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(3)
.REGEXMATCH("kg|公斤|千克")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(3)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷2"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(3).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(3)
.REGEXMATCH("克|g")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(3)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2×500"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(3).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(3)
.REGEXMATCH("ml")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(3)
.REGEXREPLACE("((\d+(?:\.\d+)?)l)","®$2×1000"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(3).REGEXEXTRACTALL([🧠基础单位])&"®")

，
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(3)

)))))

,"；"，

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(4)
.ISBLANK()
，"",

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(4)
.REGEXMATCH("磅")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(4)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷0.90718474"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(4).REGEXEXTRACTALL([🧠基础单位])&"®")
,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(4)
.REGEXMATCH("kg|公斤|千克")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(4)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷2"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(4).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(4)
.REGEXMATCH("克|g")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(4)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2×500"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(4).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(4)
.REGEXMATCH("ml")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(4)
.REGEXREPLACE("((\d+(?:\.\d+)?)l)","®$2×1000"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(4).REGEXEXTRACTALL([🧠基础单位])&"®")

，
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(4)

)))))

,"；"，

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(5)
.ISBLANK()
，"",

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(5)
.REGEXMATCH("磅")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(5)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷0.90718474"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(5).REGEXEXTRACTALL([🧠基础单位])&"®")
,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(5)
.REGEXMATCH("kg|公斤|千克")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(5)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷2"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(5).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(5)
.REGEXMATCH("克|g")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(5)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2×500"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(5).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(5)
.REGEXMATCH("ml")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(5)
.REGEXREPLACE("((\d+(?:\.\d+)?)l)","®$2×1000"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(5).REGEXEXTRACTALL([🧠基础单位])&"®")

，
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(5)

)))))

,"；"，

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(6)
.ISBLANK()
，"",

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(6)
.REGEXMATCH("磅")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(6)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷0.90718474"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(6).REGEXEXTRACTALL([🧠基础单位])&"®")
,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(6)
.REGEXMATCH("kg|公斤|千克")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(6)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷2"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(6).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(6)
.REGEXMATCH("克|g")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(6)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2×500"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(6).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(6)
.REGEXMATCH("ml")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(6)
.REGEXREPLACE("((\d+(?:\.\d+)?)l)","®$2×1000"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(6).REGEXEXTRACTALL([🧠基础单位])&"®")

，
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❷.*?❷🔞").NTH(6)

)))))

)

.REGEXREPLACE("；+","；")
.REGEXREPLACE("^；","")
.REGEXREPLACE("；$","")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🧪🤍规格✖️数量提取（单位回溯版）-参考3
CONCATENATE(
IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(1)
.ISBLANK()
，"",

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(1)
.REGEXMATCH("磅")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(1)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷0.90718474"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(1).REGEXEXTRACTALL([🧠基础单位])&"®")
,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(1)
.REGEXMATCH("kg|公斤|千克")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(1)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷2"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(1).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(1)
.REGEXMATCH("克|g")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(1)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2×500"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(1).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(1)
.REGEXMATCH("ml")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(1)
.REGEXREPLACE("((\d+(?:\.\d+)?)l)","®$2×1000"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(1).REGEXEXTRACTALL([🧠基础单位])&"®")

，
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(1)

)))))

,"；"，


IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(2)
.ISBLANK()
，"",

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(2)
.REGEXMATCH("磅")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(2)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷0.90718474"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(2).REGEXEXTRACTALL([🧠基础单位])&"®")
,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(2)
.REGEXMATCH("kg|公斤|千克")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(2)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷2"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(2).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(2)
.REGEXMATCH("克|g")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(2)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2×500"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(2).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(2)
.REGEXMATCH("ml")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(2)
.REGEXREPLACE("((\d+(?:\.\d+)?)l)","®$2×1000"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(2).REGEXEXTRACTALL([🧠基础单位])&"®")

，
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(2)

)))))

,"；"，

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(3)
.ISBLANK()
，"",

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(3)
.REGEXMATCH("磅")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(3)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷0.90718474"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(3).REGEXEXTRACTALL([🧠基础单位])&"®")
,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(3)
.REGEXMATCH("kg|公斤|千克")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(3)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷2"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(3).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(3)
.REGEXMATCH("克|g")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(3)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2×500"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(3).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(3)
.REGEXMATCH("ml")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(3)
.REGEXREPLACE("((\d+(?:\.\d+)?)l)","®$2×1000"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(3).REGEXEXTRACTALL([🧠基础单位])&"®")

，
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(3)

)))))

,"；"，

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(4)
.ISBLANK()
，"",

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(4)
.REGEXMATCH("磅")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(4)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷0.90718474"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(4).REGEXEXTRACTALL([🧠基础单位])&"®")
,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(4)
.REGEXMATCH("kg|公斤|千克")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(4)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷2"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(4).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(4)
.REGEXMATCH("克|g")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(4)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2×500"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(4).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(4)
.REGEXMATCH("ml")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(4)
.REGEXREPLACE("((\d+(?:\.\d+)?)l)","®$2×1000"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(4).REGEXEXTRACTALL([🧠基础单位])&"®")

，
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(4)

)))))

,"；"，

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(5)
.ISBLANK()
，"",

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(5)
.REGEXMATCH("磅")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(5)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷0.90718474"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(5).REGEXEXTRACTALL([🧠基础单位])&"®")
,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(5)
.REGEXMATCH("kg|公斤|千克")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(5)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷2"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(5).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(5)
.REGEXMATCH("克|g")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(5)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2×500"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(5).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(5)
.REGEXMATCH("ml")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(5)
.REGEXREPLACE("((\d+(?:\.\d+)?)l)","®$2×1000"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(5).REGEXEXTRACTALL([🧠基础单位])&"®")

，
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(5)

)))))

,"；"，

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(6)
.ISBLANK()
，"",

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(6)
.REGEXMATCH("磅")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(6)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷0.90718474"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(6).REGEXEXTRACTALL([🧠基础单位])&"®")
,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(6)
.REGEXMATCH("kg|公斤|千克")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(6)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷2"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(6).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(6)
.REGEXMATCH("克|g")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(6)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2×500"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(6).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(6)
.REGEXMATCH("ml")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(6)
.REGEXREPLACE("((\d+(?:\.\d+)?)l)","®$2×1000"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(6).REGEXEXTRACTALL([🧠基础单位])&"®")

，
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❸.*?❸🔞").NTH(6)

)))))

)

.REGEXREPLACE("；+","；")
.REGEXREPLACE("^；","")
.REGEXREPLACE("；$","")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🧪🤍规格✖️数量提取（单位回溯版）-参考4
CONCATENATE(
IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(1)
.ISBLANK()
，"",

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(1)
.REGEXMATCH("磅")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(1)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷0.90718474"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(1).REGEXEXTRACTALL([🧠基础单位])&"®")
,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(1)
.REGEXMATCH("kg|公斤|千克")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(1)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷2"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(1).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(1)
.REGEXMATCH("克|g")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(1)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2×500"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(1).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(1)
.REGEXMATCH("ml")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(1)
.REGEXREPLACE("((\d+(?:\.\d+)?)l)","®$2×1000"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(1).REGEXEXTRACTALL([🧠基础单位])&"®")

，
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(1)

)))))

,"；"，


IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(2)
.ISBLANK()
，"",

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(2)
.REGEXMATCH("磅")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(2)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷0.90718474"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(2).REGEXEXTRACTALL([🧠基础单位])&"®")
,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(2)
.REGEXMATCH("kg|公斤|千克")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(2)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷2"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(2).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(2)
.REGEXMATCH("克|g")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(2)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2×500"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(2).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(2)
.REGEXMATCH("ml")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(2)
.REGEXREPLACE("((\d+(?:\.\d+)?)l)","®$2×1000"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(2).REGEXEXTRACTALL([🧠基础单位])&"®")

，
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(2)

)))))

,"；"，

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(3)
.ISBLANK()
，"",

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(3)
.REGEXMATCH("磅")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(3)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷0.90718474"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(3).REGEXEXTRACTALL([🧠基础单位])&"®")
,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(3)
.REGEXMATCH("kg|公斤|千克")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(3)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷2"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(3).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(3)
.REGEXMATCH("克|g")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(3)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2×500"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(3).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(3)
.REGEXMATCH("ml")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(3)
.REGEXREPLACE("((\d+(?:\.\d+)?)l)","®$2×1000"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(3).REGEXEXTRACTALL([🧠基础单位])&"®")

，
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(3)

)))))

,"；"，

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(4)
.ISBLANK()
，"",

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(4)
.REGEXMATCH("磅")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(4)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷0.90718474"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(4).REGEXEXTRACTALL([🧠基础单位])&"®")
,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(4)
.REGEXMATCH("kg|公斤|千克")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(4)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷2"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(4).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(4)
.REGEXMATCH("克|g")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(4)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2×500"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(4).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(4)
.REGEXMATCH("ml")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(4)
.REGEXREPLACE("((\d+(?:\.\d+)?)l)","®$2×1000"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(4).REGEXEXTRACTALL([🧠基础单位])&"®")

，
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(4)

)))))

,"；"，

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(5)
.ISBLANK()
，"",

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(5)
.REGEXMATCH("磅")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(5)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷0.90718474"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(5).REGEXEXTRACTALL([🧠基础单位])&"®")
,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(5)
.REGEXMATCH("kg|公斤|千克")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(5)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷2"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(5).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(5)
.REGEXMATCH("克|g")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(5)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2×500"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(5).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(5)
.REGEXMATCH("ml")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(5)
.REGEXREPLACE("((\d+(?:\.\d+)?)l)","®$2×1000"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(5).REGEXEXTRACTALL([🧠基础单位])&"®")

，
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(5)

)))))

,"；"，

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(6)
.ISBLANK()
，"",

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(6)
.REGEXMATCH("磅")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(6)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷0.90718474"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(6).REGEXEXTRACTALL([🧠基础单位])&"®")
,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(6)
.REGEXMATCH("kg|公斤|千克")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(6)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷2"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(6).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(6)
.REGEXMATCH("克|g")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(6)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2×500"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(6).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(6)
.REGEXMATCH("ml")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(6)
.REGEXREPLACE("((\d+(?:\.\d+)?)l)","®$2×1000"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(6).REGEXEXTRACTALL([🧠基础单位])&"®")

，
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❹.*?❹🔞").NTH(6)

)))))

)

.REGEXREPLACE("；+","；")
.REGEXREPLACE("^；","")
.REGEXREPLACE("；$","")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====

🧪🤍规格✖️数量提取（单位回溯版）-参考5
CONCATENATE(
IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(1)
.ISBLANK()
，"",

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(1)
.REGEXMATCH("磅")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(1)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷0.90718474"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(1).REGEXEXTRACTALL([🧠基础单位])&"®")
,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(1)
.REGEXMATCH("kg|公斤|千克")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(1)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷2"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(1).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(1)
.REGEXMATCH("克|g")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(1)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2×500"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(1).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(1)
.REGEXMATCH("ml")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(1)
.REGEXREPLACE("((\d+(?:\.\d+)?)l)","®$2×1000"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(1).REGEXEXTRACTALL([🧠基础单位])&"®")

，
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(1)

)))))

,"；"，


IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(2)
.ISBLANK()
，"",

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(2)
.REGEXMATCH("磅")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(2)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷0.90718474"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(2).REGEXEXTRACTALL([🧠基础单位])&"®")
,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(2)
.REGEXMATCH("kg|公斤|千克")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(2)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷2"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(2).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(2)
.REGEXMATCH("克|g")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(2)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2×500"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(2).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(2)
.REGEXMATCH("ml")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(2)
.REGEXREPLACE("((\d+(?:\.\d+)?)l)","®$2×1000"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(2).REGEXEXTRACTALL([🧠基础单位])&"®")

，
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(2)

)))))

,"；"，

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(3)
.ISBLANK()
，"",

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(3)
.REGEXMATCH("磅")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(3)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷0.90718474"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(3).REGEXEXTRACTALL([🧠基础单位])&"®")
,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(3)
.REGEXMATCH("kg|公斤|千克")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(3)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷2"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(3).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(3)
.REGEXMATCH("克|g")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(3)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2×500"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(3).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(3)
.REGEXMATCH("ml")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(3)
.REGEXREPLACE("((\d+(?:\.\d+)?)l)","®$2×1000"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(3).REGEXEXTRACTALL([🧠基础单位])&"®")

，
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(3)

)))))

,"；"，

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(4)
.ISBLANK()
，"",

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(4)
.REGEXMATCH("磅")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(4)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷0.90718474"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(4).REGEXEXTRACTALL([🧠基础单位])&"®")
,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(4)
.REGEXMATCH("kg|公斤|千克")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(4)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷2"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(4).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(4)
.REGEXMATCH("克|g")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(4)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2×500"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(4).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(4)
.REGEXMATCH("ml")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(4)
.REGEXREPLACE("((\d+(?:\.\d+)?)l)","®$2×1000"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(4).REGEXEXTRACTALL([🧠基础单位])&"®")

，
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(4)

)))))

,"；"，

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(5)
.ISBLANK()
，"",

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(5)
.REGEXMATCH("磅")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(5)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷0.90718474"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(5).REGEXEXTRACTALL([🧠基础单位])&"®")
,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(5)
.REGEXMATCH("kg|公斤|千克")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(5)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷2"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(5).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(5)
.REGEXMATCH("克|g")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(5)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2×500"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(5).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(5)
.REGEXMATCH("ml")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(5)
.REGEXREPLACE("((\d+(?:\.\d+)?)l)","®$2×1000"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(5).REGEXEXTRACTALL([🧠基础单位])&"®")

，
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(5)

)))))

,"；"，

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(6)
.ISBLANK()
，"",

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(6)
.REGEXMATCH("磅")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(6)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷0.90718474"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(6).REGEXEXTRACTALL([🧠基础单位])&"®")
,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(6)
.REGEXMATCH("kg|公斤|千克")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(6)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2÷2"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(6).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(6)
.REGEXMATCH("克|g")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(6)
.REGEXREPLACE("((\d+(?:\.\d+)?)斤)","®$2×500"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(6).REGEXEXTRACTALL([🧠基础单位])&"®")

,

IF(
[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(6)
.REGEXMATCH("ml")
,
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(6)
.REGEXREPLACE("((\d+(?:\.\d+)?)l)","®$2×1000"&[🧪🤍规格✖️数量提取（优化版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(6).REGEXEXTRACTALL([🧠基础单位])&"®")

，
[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACTALL("❺.*?❺🔞").NTH(6)

)))))

)

.REGEXREPLACE("；+","；")
.REGEXREPLACE("^；","")
.REGEXREPLACE("；$","")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====

Ⓜ️编号（最终）
TEXT([Ⓜ️ ｜ ✍️ 品牌识别（基建）]
.COUNTIF(CurrentValue.[Ⓜ️编号（参考勿删！）]<[Ⓜ️编号（参考勿删！）])+1

，"👀000000000"）
=====
Ⓜ️编号（参考勿删！）
自增数字
=====
🍬商品信息（原始版）
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[Ⓜ️编号（最终）]).[🍬商品信息（原始版）].LISTCOMBINE()
=====
🍬商品信息ID
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[Ⓜ️编号（最终）]).[🍬商品信息ID].LISTCOMBINE()
=====
🧠品牌-逻辑
IF([🍬商品信息（原始版）].ISBLANK(),""，
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠品牌-逻辑]!="").[🧠品牌-逻辑].LISTCOMBINE().UNIQUE())
=====
📁🤍商品信息（纯净分割版）
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[Ⓜ️编号（最终）]).[📁🤍商品信息（纯净分割版）].LISTCOMBINE()
=====
📁💜商品信息（终版分割版）
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[Ⓜ️编号（最终）]).[📁💜商品信息（终版分割版）].LISTCOMBINE()
=====
📁信息留存时间（天）
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[Ⓜ️编号（最终）]).[📁信息留存时间（天）]
=====
📁选品定位符
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[Ⓜ️编号（最终）]).[📁选品定位符].LISTCOMBINE()
=====
📁源信息新鲜度(天)
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[Ⓜ️编号（最终）]).[📁源信息新鲜度(天)]
=====
🐽💚商品信息（重组版）
[🐽 sku重组-社群专用 (基建)-❌（暂时关闭计算）].FILTER(CurrentValue.[🐽编号（最终）]=[Ⓜ️编号（最终）]).[🐽💚商品信息（重组版）]
=====
🌀记录清除/删除判断
IF(
[📁源信息新鲜度(天)]
>=[📁信息留存时间（天）]

,"🆑记录待清理"
，""
)
=====
Ⓜ️✍️复制商品名称用
Ⓜ️✍️品牌识别进度（手动）
Ⓜ️🤍品牌提取（原始值）
CONCATENATE(
"❶"，
IF(
[📁🤍商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(1)
.REGEXEXTRACTALL([🧠品牌-逻辑])
.UNIQUE()
.ISBLANK()

,
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(1)
.REGEXEXTRACTALL([🧠品牌-逻辑])
.ARRAYJOIN("")

,
[📁🤍商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(1)
.REGEXEXTRACTALL([🧠品牌-逻辑])
.UNIQUE()
.ARRAYJOIN("")
)

,"❶🔞"

,CHAR(10),


"❷"，
IF(
[📁🤍商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(2)
.REGEXEXTRACTALL([🧠品牌-逻辑])
.UNIQUE()
.ISBLANK()

,
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(2)
.REGEXEXTRACTALL([🧠品牌-逻辑])
.ARRAYJOIN("")


,
[📁🤍商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(2)
.REGEXEXTRACTALL([🧠品牌-逻辑])
.UNIQUE()
.ARRAYJOIN("")
)

,"❷🔞"


,CHAR(10),


"❸"，
IF(
[📁🤍商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(3)
.REGEXEXTRACTALL([🧠品牌-逻辑])
.UNIQUE()
.ISBLANK()

,
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(3)
.REGEXEXTRACTALL([🧠品牌-逻辑])
.ARRAYJOIN("")


,
[📁🤍商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(3)
.REGEXEXTRACTALL([🧠品牌-逻辑])
.UNIQUE()
.ARRAYJOIN("")
)

,"❸🔞"

,CHAR(10),


"❹"，
IF(
[📁🤍商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(4)
.REGEXEXTRACTALL([🧠品牌-逻辑])
.UNIQUE()
.ISBLANK()

,
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(4)
.REGEXEXTRACTALL([🧠品牌-逻辑])
.ARRAYJOIN("")


,
[📁🤍商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(4)
.REGEXEXTRACTALL([🧠品牌-逻辑])
.UNIQUE()
.ARRAYJOIN("")
)

,"❹🔞"


,CHAR(10),


"❺"，
IF(
[📁🤍商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(5)
.REGEXEXTRACTALL([🧠品牌-逻辑])
.UNIQUE()
.ISBLANK()

,
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(5)
.REGEXEXTRACTALL([🧠品牌-逻辑])
.ARRAYJOIN("")


,
[📁🤍商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(5)
.REGEXEXTRACTALL([🧠品牌-逻辑])
.UNIQUE()
.ARRAYJOIN("")
)

,"❺🔞"


,CHAR(10),


"❻"，
IF(
[📁🤍商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(6)
.REGEXEXTRACTALL([🧠品牌-逻辑])
.UNIQUE()
.ISBLANK()

,
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(6)
.REGEXEXTRACTALL([🧠品牌-逻辑])
.ARRAYJOIN("")


,
[📁🤍商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(6)
.REGEXEXTRACTALL([🧠品牌-逻辑])
.UNIQUE()
.ARRAYJOIN("")
)

,"❻🔞"

,CHAR(10),


"❼",
IF(
[📁🤍商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(7)
.REGEXEXTRACTALL([🧠品牌-逻辑])
.UNIQUE()
.ISBLANK()
,
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(7)
.REGEXEXTRACTALL([🧠品牌-逻辑])
.ARRAYJOIN("")

,
[📁🤍商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(7)
.REGEXEXTRACTALL([🧠品牌-逻辑])
.UNIQUE()
.ARRAYJOIN("")

)
,"❼🔞"

,CHAR(10),


"❽",
IF(
[📁🤍商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(8)
.REGEXEXTRACTALL([🧠品牌-逻辑])
.UNIQUE()
.ISBLANK()
,
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(8)
.REGEXEXTRACTALL([🧠品牌-逻辑])
.ARRAYJOIN("")

,
[📁🤍商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(8)
.REGEXEXTRACTALL([🧠品牌-逻辑])
.UNIQUE()
.ARRAYJOIN("")

)
,"❽🔞"


,CHAR(10),


"❾",
IF(
[📁🤍商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(9)
.REGEXEXTRACTALL([🧠品牌-逻辑])
.UNIQUE()
.ISBLANK()
,
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(9)
.REGEXEXTRACTALL([🧠品牌-逻辑])
.ARRAYJOIN("")

,
[📁🤍商品信息（纯净分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(9)
.REGEXEXTRACTALL([🧠品牌-逻辑])
.UNIQUE()
.ARRAYJOIN("")

)
,"❾🔞"


）

.REGEXREPLACE("[\u200B-\u200D\uFE00-\uFE0F\u202A-\u202E\u2060-\u206F]|CHAR(8203)|CHAR(8288)","")
.REGEXREPLACE("[\u2028]", CHAR(10))
 
.REGEXREPLACE("(❶❶|❷❷|❸❸|❹❹|❺❺|❻❻|❼❼|❽❽|❾❾)🔞","")
.REGEXREPLACE("[,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
Ⓜ️🤍品牌（唯一值）
IF(
[Ⓜ️🤍品牌提取（原始值）]
.ISBLANK()
，""，

IF(
AND([Ⓜ️✍️品牌识别进度（手动）]
.REGEXMATCH("通用")
,

[Ⓜ️🤍品牌提取（原始值）]
.ISBLANK()
)
，"通用品牌"，


[Ⓜ️🤍品牌提取（原始值）]
.LOWER()
.REGEXREPLACE("[❶❷❸❹❺❻🔞]","")
.REGEXEXTRACTALL([🧠品牌-逻辑])
.UNIQUE()
.ARRAYJOIN(CHAR(10))
))

.REGEXREPLACE("高爷家许翠花","许翠花")

.REGEXREPLACE("[,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
Ⓜ️🤍‼️品牌（标准格式）
CONCATENATE(
IF(
[Ⓜ️🤍品牌（唯一值）]
.SPLIT(CHAR(10))
.NTH(1)
.ISBLANK()
,"",

IF(
[♦︎ 品牌库（底库）]
.FILTER(CurrentValue.[🤖品牌名称（标准版）].REGEXREPLACE(" ", "").LOWER()
.CONTAINTEXT([Ⓜ️🤍品牌（唯一值）].SPLIT(CHAR(10)).NTH(1).REGEXREPLACE(" ", "").LOWER()))
.[🤖品牌名称（标准版）].UNIQUE().COUNTA()> 0,

[♦︎ 品牌库（底库）]
.FILTER(
CurrentValue.[🤖品牌名称（标准版）].REGEXREPLACE(" ", "").LOWER()
.CONTAINTEXT([Ⓜ️🤍品牌（唯一值）].SPLIT(CHAR(10)).NTH(1).REGEXREPLACE(" ", "").LOWER()
))
.[🤖品牌名称（标准版）].UNIQUE(),

""))

,CHAR(10),

IF(
[Ⓜ️🤍品牌（唯一值）]
.SPLIT(CHAR(10))
.NTH(2)
.ISBLANK()
,"",
IF(
[♦︎ 品牌库（底库）]
.FILTER(CurrentValue.[🤖品牌名称（标准版）].REGEXREPLACE(" ", "").LOWER()
.CONTAINTEXT([Ⓜ️🤍品牌（唯一值）].SPLIT(CHAR(10)).NTH(2).REGEXREPLACE(" ", "").LOWER()))
.[🤖品牌名称（标准版）].UNIQUE().COUNTA()> 0,

[♦︎ 品牌库（底库）]
.FILTER(
CurrentValue.[🤖品牌名称（标准版）].REGEXREPLACE(" ", "").LOWER()
.CONTAINTEXT([Ⓜ️🤍品牌（唯一值）].SPLIT(CHAR(10)).NTH(2).REGEXREPLACE(" ", "").LOWER()
))
.[🤖品牌名称（标准版）].UNIQUE(),

""))



,CHAR(10),

IF(
[Ⓜ️🤍品牌（唯一值）]
.SPLIT(CHAR(10))
.NTH(3)
.ISBLANK()
,"",
IF(
[♦︎ 品牌库（底库）]
.FILTER(CurrentValue.[🤖品牌名称（标准版）].REGEXREPLACE(" ", "").LOWER()
.CONTAINTEXT([Ⓜ️🤍品牌（唯一值）].SPLIT(CHAR(10)).NTH(3).REGEXREPLACE(" ", "").LOWER()))
.[🤖品牌名称（标准版）].UNIQUE().COUNTA()> 0,

[♦︎ 品牌库（底库）]
.FILTER(
CurrentValue.[🤖品牌名称（标准版）].REGEXREPLACE(" ", "").LOWER()
.CONTAINTEXT([Ⓜ️🤍品牌（唯一值）].SPLIT(CHAR(10)).NTH(3).REGEXREPLACE(" ", "").LOWER()
))
.[🤖品牌名称（标准版）].UNIQUE(),

""))





,CHAR(10),

IF(
[Ⓜ️🤍品牌（唯一值）]
.SPLIT(CHAR(10))
.NTH(4)
.ISBLANK()
,"",
IF(
[♦︎ 品牌库（底库）]
.FILTER(CurrentValue.[🤖品牌名称（标准版）].REGEXREPLACE(" ", "").LOWER()
.CONTAINTEXT([Ⓜ️🤍品牌（唯一值）].SPLIT(CHAR(10)).NTH(4).REGEXREPLACE(" ", "").LOWER()))
.[🤖品牌名称（标准版）].UNIQUE().COUNTA()> 0,

[♦︎ 品牌库（底库）]
.FILTER(
CurrentValue.[🤖品牌名称（标准版）].REGEXREPLACE(" ", "").LOWER()
.CONTAINTEXT([Ⓜ️🤍品牌（唯一值）].SPLIT(CHAR(10)).NTH(4).REGEXREPLACE(" ", "").LOWER()
))
.[🤖品牌名称（标准版）].UNIQUE(),

""))





,CHAR(10),

IF(
[Ⓜ️🤍品牌（唯一值）]
.SPLIT(CHAR(10))
.NTH(5)
.ISBLANK()
,"",
IF(
[♦︎ 品牌库（底库）]
.FILTER(CurrentValue.[🤖品牌名称（标准版）].REGEXREPLACE(" ", "").LOWER()
.CONTAINTEXT([Ⓜ️🤍品牌（唯一值）].SPLIT(CHAR(10)).NTH(5).REGEXREPLACE(" ", "").LOWER()))
.[🤖品牌名称（标准版）].UNIQUE().COUNTA()> 0,

[♦︎ 品牌库（底库）]
.FILTER(
CurrentValue.[🤖品牌名称（标准版）].REGEXREPLACE(" ", "").LOWER()
.CONTAINTEXT([Ⓜ️🤍品牌（唯一值）].SPLIT(CHAR(10)).NTH(5).REGEXREPLACE(" ", "").LOWER()
))
.[🤖品牌名称（标准版）].UNIQUE(),

""))





,CHAR(10),

IF(
[Ⓜ️🤍品牌（唯一值）]
.SPLIT(CHAR(10))
.NTH(6)
.ISBLANK()
,"",
IF(
[♦︎ 品牌库（底库）]
.FILTER(CurrentValue.[🤖品牌名称（标准版）].REGEXREPLACE(" ", "").LOWER()
.CONTAINTEXT([Ⓜ️🤍品牌（唯一值）].SPLIT(CHAR(10)).NTH(6).REGEXREPLACE(" ", "").LOWER()))
.[🤖品牌名称（标准版）].UNIQUE().COUNTA()> 0,

[♦︎ 品牌库（底库）]
.FILTER(
CurrentValue.[🤖品牌名称（标准版）].REGEXREPLACE(" ", "").LOWER()
.CONTAINTEXT([Ⓜ️🤍品牌（唯一值）].SPLIT(CHAR(10)).NTH(6).REGEXREPLACE(" ", "").LOWER()
))
.[🤖品牌名称（标准版）].UNIQUE(),

""))




,CHAR(10),

IF(
[Ⓜ️🤍品牌（唯一值）]
.SPLIT(CHAR(10))
.NTH(7)
.ISBLANK()
,"",
IF(
[♦︎ 品牌库（底库）]
.FILTER(CurrentValue.[🤖品牌名称（标准版）].REGEXREPLACE(" ", "").LOWER()
.CONTAINTEXT([Ⓜ️🤍品牌（唯一值）].SPLIT(CHAR(10)).NTH(7).REGEXREPLACE(" ", "").LOWER()))
.[🤖品牌名称（标准版）].UNIQUE().COUNTA()> 0,

[♦︎ 品牌库（底库）]
.FILTER(
CurrentValue.[🤖品牌名称（标准版）].REGEXREPLACE(" ", "").LOWER()
.CONTAINTEXT([Ⓜ️🤍品牌（唯一值）].SPLIT(CHAR(10)).NTH(7).REGEXREPLACE(" ", "").LOWER()
))
.[🤖品牌名称（标准版）].UNIQUE(),

""))




,CHAR(10),

IF(
[Ⓜ️🤍品牌（唯一值）]
.SPLIT(CHAR(10))
.NTH(8)
.ISBLANK()
,"",
IF(
[♦︎ 品牌库（底库）]
.FILTER(CurrentValue.[🤖品牌名称（标准版）].REGEXREPLACE(" ", "").LOWER()
.CONTAINTEXT([Ⓜ️🤍品牌（唯一值）].SPLIT(CHAR(10)).NTH(8).REGEXREPLACE(" ", "").LOWER()))
.[🤖品牌名称（标准版）].UNIQUE().COUNTA()> 0,

[♦︎ 品牌库（底库）]
.FILTER(
CurrentValue.[🤖品牌名称（标准版）].REGEXREPLACE(" ", "").LOWER()
.CONTAINTEXT([Ⓜ️🤍品牌（唯一值）].SPLIT(CHAR(10)).NTH(8).REGEXREPLACE(" ", "").LOWER()
))
.[🤖品牌名称（标准版）].UNIQUE(),

""))




,CHAR(10),

IF(
[Ⓜ️🤍品牌（唯一值）]
.SPLIT(CHAR(10))
.NTH(9)
.ISBLANK()
,"",
IF(
[♦︎ 品牌库（底库）]
.FILTER(CurrentValue.[🤖品牌名称（标准版）].REGEXREPLACE(" ", "").LOWER()
.CONTAINTEXT([Ⓜ️🤍品牌（唯一值）].SPLIT(CHAR(10)).NTH(9).REGEXREPLACE(" ", "").LOWER()))
.[🤖品牌名称（标准版）].UNIQUE().COUNTA()> 0,

[♦︎ 品牌库（底库）]
.FILTER(
CurrentValue.[🤖品牌名称（标准版）].REGEXREPLACE(" ", "").LOWER()
.CONTAINTEXT([Ⓜ️🤍品牌（唯一值）].SPLIT(CHAR(10)).NTH(9).REGEXREPLACE(" ", "").LOWER()
))
.[🤖品牌名称（标准版）].UNIQUE(),

""))




,CHAR(10),

IF(
[Ⓜ️🤍品牌（唯一值）]
.SPLIT(CHAR(10))
.NTH(10)
.ISBLANK()
,"",
IF(
[♦︎ 品牌库（底库）]
.FILTER(CurrentValue.[🤖品牌名称（标准版）].REGEXREPLACE(" ", "").LOWER()
.CONTAINTEXT([Ⓜ️🤍品牌（唯一值）].SPLIT(CHAR(10)).NTH(10).REGEXREPLACE(" ", "").LOWER()))
.[🤖品牌名称（标准版）].UNIQUE().COUNTA()> 0,

[♦︎ 品牌库（底库）]
.FILTER(
CurrentValue.[🤖品牌名称（标准版）].REGEXREPLACE(" ", "").LOWER()
.CONTAINTEXT([Ⓜ️🤍品牌（唯一值）].SPLIT(CHAR(10)).NTH(10).REGEXREPLACE(" ", "").LOWER()
))
.[🤖品牌名称（标准版）].UNIQUE(),

""))


)

.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
Ⓜ️🖤品牌（标准格式）（字段维护专用）
[Ⓜ️🤍‼️品牌（标准格式）]
.REGEXREPLACE("𝑲9","K9")
.REGEXREPLACE("𝑵1","N1")
.REGEXREPLACE("𝑮𝑶","GO")
.REGEXREPLACE("𝑽𝑬","VE")
.REGEXREPLACE("𝑶𝑫𝑬","ODE")

.REGEXREPLACE("[,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
Ⓜ️🤍品牌梯度
IF(ISBLANK([Ⓜ️🤍品牌（唯一值）]),""，
[♦︎ 品牌库（底库）].FILTER(CurrentValue.[🤖品牌名称（标准版）].LOWER().CONTAINTEXT([Ⓜ️🤍品牌（唯一值）].LOWER())).[🤖品牌梯度].UNIQUE()）
=====
Ⓜ️🤍品牌归属
IF(ISBLANK([Ⓜ️🤍品牌（唯一值）]),""，
[♦︎ 品牌库（底库）].FILTER(CurrentValue.[🤖品牌名称（标准版）].LOWER().CONTAINTEXT([Ⓜ️🤍品牌（唯一值）].LOWER())).[🤖品牌归属].UNIQUE()）
=====
Ⓜ️品牌识别进度（终版）
IF(
[🍬商品信息（原始版）]
.ISBLANK()
，""，

IF(
[Ⓜ️🤍‼️品牌（标准格式）]
.ISBLANK()
.NOT(),
"B已入品牌库✅"，

IF(
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[Ⓜ️编号（最终）]).[📁🩵信息类型（唯一值）]
.REGEXMATCH("🛒")
.NOT(),
"C非商品信息👋"
，

IF(
AND(
[Ⓜ️🤍品牌提取（原始值）]
.ISBLANK()
,
OR(
[📁💜商品信息（终版分割版）]
.REGEXREPLACE("[']","")
.REGEXMATCH([🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠使用对象-逻辑（人）]!="").[🧠使用对象-逻辑（人）].LISTCOMBINE().UNIQUE())


,

[Ⓜ️✍️品牌识别进度（手动）]
.REGEXMATCH("非宠物")
))
，
"C非宠物用品🚼"


,
IF(
AND([Ⓜ️🤍品牌提取（原始值）]
.ISBLANK()
,
[Ⓜ️✍️品牌识别进度（手动）]
.REGEXMATCH("无品牌"))

，"B无品牌🈚️"


，

IF(
AND([Ⓜ️🤍品牌提取（原始值）]
.ISBLANK()
,
[📁💜商品信息（终版分割版）]
.REGEXREPLACE("[']","")
.REGEXMATCH("(宠物|[猫狗犬]|人宠)|垃圾袋|毛.*?梳子|喂食|驱虫|餐包|罐[罐头]|牵引绳"))

，
"A暂无品牌👀"

，

IF(
AND(
[Ⓜ️🤍品牌提取（原始值）]
.ISBLANK()
,
[Ⓜ️✍️品牌识别进度（手动）]
.ISBLANK()
)
,
"A待写入品牌逻辑🙅"


,


IF(
AND(
[Ⓜ️🤍品牌提取（原始值）]
.ISBLANK()
.NOT()
,

[Ⓜ️🤍‼️品牌（标准格式）]
.ISBLANK() 
)

,"A待入品牌库⭕️"



，"B已入品牌库✅"))))))))
=====
Ⓜ️🤍品牌重复度
IF(
ISBLANK([Ⓜ️🤍品牌提取（原始值）]),
"",
IF(
[Ⓜ️ ｜ ✍️ 品牌识别（基建）].COUNTIF(
(CurrentValue.[Ⓜ️编号（最终）].REGEXREPLACE("👀","").VALUE()<[Ⓜ️编号（最终）].REGEXREPLACE("👀","").VALUE())
*
(CurrentValue.[Ⓜ️🤍品牌（唯一值）] = [Ⓜ️🤍品牌（唯一值）]) > 0
),
"🚫重复品牌",
"🆕新品牌"
))
=====
Ⓜ️品牌填写check进度
IF(
OR(
[🍬商品信息（原始版）]
.ISBLANK()
,
[Ⓜ️品牌识别进度（终版）]
.REGEXMATCH("🚼|👋"))
，""，

IF(
AND(
[Ⓜ️品牌识别进度（终版）]
.REGEXMATCH("🙅")
，
[Ⓜ️🤍品牌提取（原始值）]
.ISBLANK())

，"⭕️待check"，

IF(
OR(
[Ⓜ️✍️品牌识别进度（手动）]
.ISBLANK()
.NOT()
,

AND(
[Ⓜ️品牌识别进度（终版）]
.REGEXMATCH("👀")
，
[Ⓜ️✍️品牌识别进度（手动）]
.ISBLANK())

)
，
"✅已check"
，
"✅已check"

)))
=====

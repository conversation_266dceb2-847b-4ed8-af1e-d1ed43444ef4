🛍️编号（最终）
TEXT([🛍️ ｜✍️ 品类｜口味｜制作工艺 ｜ 使用对象 ｜ 成长期（基建）]
.COUNTIF(CurrentValue.[🛍️编号（参考勿删！）]<[🛍️编号（参考勿删！）])+1

，"👀000000000"）
=====
🛍️编号（参考勿删！）
🍬商品信息（原始版）
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🛍️编号（最终）]).[🍬商品信息（原始版）].LISTCOMBINE()
=====
🍬商品信息ID
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🛍️编号（最终）]).[🍬商品信息ID].LISTCOMBINE()
=====
🍬信息来源 / 发车人
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🛍️编号（最终）]).[🍬信息来源 / 发车人].LISTCOMBINE()
=====
🧠制作工艺-逻辑
IF([🍬商品信息（原始版）].ISBLANK(),""，
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠制作工艺-逻辑]!="").[🧠制作工艺-逻辑].LISTCOMBINE().UNIQUE())
=====
🧠成长期-逻辑
IF([🍬商品信息（原始版）].ISBLANK(),"",[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠成长期-逻辑]!="").[🧠成长期-逻辑].LISTCOMBINE().UNIQUE())
=====
🧠使用对象-逻辑（人）
IF([🍬商品信息（原始版）].ISBLANK(),"",[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠使用对象-逻辑（人）]!="").[🧠使用对象-逻辑（人）].LISTCOMBINE().UNIQUE())
=====
🧠品牌-逻辑
IF([🍬商品信息（原始版）].ISBLANK(),"",[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠品牌-逻辑]!="").[🧠品牌-逻辑].LISTCOMBINE().UNIQUE())
=====
🧠品牌参考-逻辑
IF([🍬商品信息（原始版）].ISBLANK(),""，
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠品牌-逻辑]!="").[🧠品牌-逻辑].LISTCOMBINE().UNIQUE())
=====
🧠基础单位
IF([🍬商品信息（原始版）].ISBLANK(),""，
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠基础单位]!="").[🧠基础单位].LISTCOMBINE().UNIQUE())
=====
🧠初级单位
IF([🍬商品信息（原始版）].ISBLANK(),""，
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠初级单位]!="").[🧠初级单位].LISTCOMBINE().UNIQUE())
=====
🧠进阶单位
IF([🍬商品信息（原始版）].ISBLANK(),""，
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠进阶单位]!="").[🧠进阶单位].LISTCOMBINE().UNIQUE())
=====
🧠高阶单位
IF([🍬商品信息（原始版）].ISBLANK(),""，
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠高阶单位]!="").[🧠高阶单位].LISTCOMBINE().UNIQUE())
=====
🧠终极单位
IF([🍬商品信息（原始版）].ISBLANK(),""，
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠终极单位]!="").[🧠终极单位].LISTCOMBINE().UNIQUE())
=====
🧠单位合集
IF([🍬商品信息（原始版）].ISBLANK(),""，
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠单位合集]!="").[🧠单位合集].LISTCOMBINE().UNIQUE())
=====
🧠口味参考-逻辑
IF([🍬商品信息（原始版）].ISBLANK(),""，
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠口味-逻辑]!="").[🧠口味-逻辑].LISTCOMBINE().UNIQUE())
=====
🧠二级分类-逻辑
IF(
[🍬商品信息（原始版）]
.ISBLANK(),""，
[🧠 逻辑表（底库）].FILTER(CurrentValue.[🧠品牌-逻辑]!="").[🧠二级分类-逻辑].LISTCOMBINE().UNIQUE())
=====
📁💜商品信息（终版分割版）
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🛍️编号（最终）]).[📁💜商品信息（终版分割版）]
=====
📁🤍商品信息（纯净分割版）
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🛍️编号（最终）]).[📁🤍商品信息（纯净分割版）].LISTCOMBINE()
=====
📁源信息发布时间(最终)
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🛍️编号（最终）]).[📁源信息发布时间(最终)]
=====
📁源信息新鲜度(天)
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🛍️编号（最终）]).[📁源信息新鲜度(天)]
=====
📁信息留存时间（天）
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🛍️编号（最终）]).[📁信息留存时间（天）]
=====
📁社群定位
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🛍️编号（最终）]).[📁社群定位].LISTCOMBINE()
=====
📁选品定位符
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🛍️编号（最终）]).[📁选品定位符].LISTCOMBINE()
=====
🌀记录清除/删除判断
IF(
[📁源信息新鲜度(天)]
>=[📁信息留存时间（天）]

,"🆑记录待清理"
，""
)
=====
🛍️🤍😀商品名称（品类提取专属）
IF(
[🍬商品信息（原始版）].ISBLANK(),""，

[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[🛍️编号（最终）]).[🔪🤍😀商品名称（原始版）])

.REGEXREPLACE([🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[🛍️编号（最终）]).[🔪🤍😀赠送信息（原始版）],"")

.REGEXREPLACE(
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[🛍️编号（最终）]).[🔪🤍😀商品介绍]
.REGEXREPLACE("\\","\\")
.REGEXREPLACE("\(","\(")
.REGEXREPLACE("\[","\[")
.REGEXREPLACE("[+*]","\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(1)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")


.REGEXREPLACE(
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[🛍️编号（最终）]).[🔪🤍😀商品介绍]
.REGEXREPLACE("\\","\\")
.REGEXREPLACE("\(","\(")
.REGEXREPLACE("\[","\[")
.REGEXREPLACE("[+*]","\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(2)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")


.REGEXREPLACE(
[🔪 ｜ ✍️ sku解构(基建)].FILTER(CurrentValue.[🔪编号（最终）]=[🛍️编号（最终）]).[🔪🤍😀商品介绍]
.REGEXREPLACE("\\","\\")
.REGEXREPLACE("\(","\(")
.REGEXREPLACE("\[","\[")
.REGEXREPLACE("[+*]","\$0")
.REGEXEXTRACTALL("[❶-❾].*?[❶-❾]🔞").NTH(3)
.REGEXREPLACE(CHAR(10)&"|[❶-❾🔞🔥‼️✔️]",""),"")

.REGEXREPLACE("🚾🚾🚾🚾🚾🚾🚾","无实际意义，为了看信息更方便")


.REGEXREPLACE("(?:💰|[赠送]|\+试吃|送试吃|会员送).*?([❶-❾]🔞)","$1")


.REGEXREPLACE("❶🔞[\s\s]*?❶","").REGEXREPLACE(CHAR(10),"")
.REGEXREPLACE("❷🔞[\s\s]*?❷","").REGEXREPLACE(CHAR(10),"")
.REGEXREPLACE("❸🔞[\s\s]*?❸","").REGEXREPLACE(CHAR(10),"")
.REGEXREPLACE("❹🔞[\s\s]*?❹","").REGEXREPLACE(CHAR(10),"")
.REGEXREPLACE("❺🔞[\s\s]*?❺","").REGEXREPLACE(CHAR(10),"")
.REGEXREPLACE("❻🔞[\s\s]*?❻","").REGEXREPLACE(CHAR(10),"")
.REGEXREPLACE("❼🔞[\s\s]*?❼","").REGEXREPLACE(CHAR(10),"")
.REGEXREPLACE("❽🔞[\s\s]*?❽","").REGEXREPLACE(CHAR(10),"")
.REGEXREPLACE("❾🔞[\s\s]*?❾","").REGEXREPLACE(CHAR(10),"")

.REGEXREPLACE("㊙️㊙️㊙️在文本调整之前，必须要保证定位符号中的文本要独立成行的，否则下面的逻辑一旦生效很容易吞掉定位符从而导致定位符号错位","")

.REGEXREPLACE("([❶-❾]🔞)\s?([❶-❾])","$1"&CHAR(10)&"$2")
.REGEXREPLACE("(❶\s?❶|❷\s?❷|❸\s?❸|❹\s?❹|❺\s?❺|❻\s?❻|❼\s?❼|❽\s?❽|❾\s?❾)🔞","")


.REGEXREPLACE([🧠品牌参考-逻辑]&"(\d+)\+(\d+)","$1$2$3加$4")

.SPLIT(CHAR(10))
.ARRAYJOIN("🔥")
.REGEXREPLACE("🔥","")

.REGEXREPLACE("\d+(\*(\d+))?("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")[,，。]?","")
.REGEXREPLACE("(折.*?)?\/("&[🧠基础单位]&"|"&[🧠初级单位]&"|"&[🧠进阶单位]&"|"&[🧠高阶单位]&")[,，。]?","")

.REGEXREPLACE("㊙️㊙️㊙️在文本调整之前（再次强调确保万无一失），必须要保证定位符号中的文本要独立成行的，否则下面的逻辑一旦生效很容易吞掉定位符从而导致定位符号错位","")

.REGEXREPLACE("([❶-❾]🔞)\s?([❶-❾])","$1"&CHAR(10)&"$2")

.REGEXREPLACE("(猫)(砂[铲盆])","$1🥄$2")
.REGEXREPLACE("(猫草)([棒饼])","$1")
.REGEXREPLACE("([猫狗犬]粮)(.*?(罐|猫条|冻干))","$1")
.REGEXREPLACE("(?:(?:主[粮食]|正餐)级?+)([猫狗犬]条|罐头?|冻干|猫饭|[餐汤][包盒]|鲜粮包)","主🍚食$1")
.REGEXREPLACE("(?:(?:主[粮食]|正餐)级?+)[❶-❾]🔞"&CHAR(10)&"[❶-❾]([猫狗犬]条|罐头?|冻干|猫饭|[餐汤][包盒]|鲜粮包)","主🍚食$1")
.REGEXREPLACE("(?:冻干|生骨肉?|肉干|奶糕|猫草|猫薄荷|猫草|鱼油|洁[牙齿]|益生菌|[牛羊]奶|肠胃|鲜肉|肉松)+.*?((?:烘焙|膨化)?[幼]?[猫狗犬]?粮)","$1")
.REGEXREPLACE("(?:生骨肉)+.*?(猫条)","$1")
.REGEXREPLACE("(?:除菌|除臭|抑菌|吸水)+.*?(猫砂)","$1")
.REGEXREPLACE("(冻干)(?:生骨肉?|[鸡鸭鹅鸽牛羊驼兔鹿][大小]胸|[鸡鸭鹅牛羊驼兔鹿]脖|荷包蛋)","$1")
.REGEXREPLACE("(?:生骨肉?|[鸡鸭鹅鸽牛羊驼兔鹿][大小]胸|[鸡鸭鹅牛羊驼兔鹿]脖|荷包蛋)(冻干)","$1")
.REGEXREPLACE("(冻干|生骨肉?|[鸡鸭鹅鸽牛羊驼兔鹿][大小]胸|[鸡鸭鹅牛羊驼兔鹿]脖).*?零食","$1")
.REGEXREPLACE("(?:冻干)(猫草棒?|洁[牙齿]棒?|鱼油棒?|[牛羊]奶棒?|酸奶|荷包蛋)","$1")
.REGEXREPLACE("(?:猫草棒?|洁[牙齿]棒?|鱼油棒?|[牛羊]奶棒?)(冻干)","$1")
.REGEXREPLACE("((?:[一-十双两]|\d+)拼)","$1粮")

.REGEXREPLACE("([❶-❾]🔞)\s?([❶-❾])","$1"&CHAR(10)&"$2")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🛍️商品名称（品类提取专属）正确性判断
IF([🍬商品信息（原始版）].ISBLANK(),""，
IF(
OR(
[🛍️🤍😀商品名称（品类提取专属）]
.REGEXMATCH("❶.*?[❷❸❹❺❻❼❽❾]🔞")
,
[🛍️🤍😀商品名称（品类提取专属）]
.REGEXMATCH("❷.*?[❶❸❹❺❻❼❽❾]🔞")

,
[🛍️🤍😀商品名称（品类提取专属）]
.REGEXMATCH("❸.*?[❶❷❹❺❻❼❽❾]🔞")

,
[🛍️🤍😀商品名称（品类提取专属）]
.REGEXMATCH("❹.*?[❶❷❸❺❻❼❽❾]🔞")

,

[🛍️🤍😀商品名称（品类提取专属）]
.REGEXMATCH("❺.*?[❶❷❸❹❻❼❽❾]🔞")
,
[🛍️🤍😀商品名称（品类提取专属）]
.REGEXMATCH("❻.*?[❶❷❸❹❺❼❽❾]🔞")

,
[🛍️🤍😀商品名称（品类提取专属）]
.REGEXMATCH("❼.*?[❶❷❸❹❺❻❽❾]🔞")

,
[🛍️🤍😀商品名称（品类提取专属）]
.REGEXMATCH("❽.*?[❶❷❸❹❺❻❼❾]🔞")


,
[🛍️🤍😀商品名称（品类提取专属）]
.REGEXMATCH("❾.*?[❶❷❸❹❺❻❼❽]🔞"))
,

"定位符错误(品类提取)🆘"
，""))
=====
🛍️🤍😀品类-二级品类（原始值）
IF(
[🍬商品信息（原始版）].ISBLANK(),""，

[🛍️🤍😀商品名称（品类提取专属）])
.REGEXREPLACE(" ","")
.REGEXREPLACE("([❶-❾]).*?(?:[矿砂]{1,2}).*?([❶-❾]🔞)","$1猫砂$2"&CHAR(10))

.REGEXREPLACE("([❶-❾]).*?(?:罐头|营养罐|零食罐|湿粮|主🍚?食.*?罐|[犬狗猫].*?罐|食罐|小?金罐|小黄罐|[Mm][Ii][Nn][Ii]罐|[Bb]arf罐?|[Ww][Oo][Ww][Oo]罐|噜噜罐|处方罐|鲭鱼罐|大口罐|小满罐|每日罐|恢复罐|刺身罐|补水罐|止渴罐|功能罐|安心罐|乳罐|白金罐|[鸡鸭鹅虾蟹鱼蛋鸽牛羊驼兔鹿肉].*?罐|汤罐|奶罐|随享罐|掰掰罐|吨吨罐|奶昔罐|慕斯罐?|纯罐|益生元罐|[Ii]can罐|牧野奇迹.*?珍稀系列|礼包).*?([❶-❾]🔞)","$1罐头$2"&CHAR(10))
  .REGEXREPLACE("([❶-❾]).*?(?:奶糕|餐盒|猫饭|狗饭|主食.*?盒|猫.*?勺|四季勺|蛋羹杯|小飞盒|小鲜盒|餐杯|肉.*?羹).*?([❶-❾]🔞)","$1餐盒$2"&CHAR(10))
.REGEXREPLACE("([❶-❾]).*?(?:餐包|鲜粮包|汤包|酱包|软包|.*?酱|小仙包|主食包|鲜封包|骨汤袋|肉包|肉块|奶包|妙鲜包|奶昔|胶原汤|阿飞和巴弟.*?厚乳).*?([❶-❾]🔞)","$1餐包$2"&CHAR(10))
.REGEXREPLACE("([❶-❾]).*?(?:猫条|鲜鲜条).*?([❶-❾]🔞)","$1猫条$2"&CHAR(10))
.REGEXREPLACE("([❶-❾]).*?(?:狗条|犬条).*?([❶-❾]🔞)","$1狗条$2"&CHAR(10))


.REGEXREPLACE("([❶-❾]).*?(?:冻干|老板的饼|小鲜砖|超能粒|超级桶).*?([❶-❾]🔞)","$1冻干$2"&CHAR(10))
.REGEXREPLACE("([❶-❾]).*?(?:[牛羊]初?[奶乳]|奶贝|酸奶|奶酪棒?|奶棒|代乳粉|乳化杯|舒化.*?奶|钙奶|奶条).*?([❶-❾]🔞)","$1乳制品$2"&CHAR(10))
.REGEXREPLACE("([❶-❾]).*?(?:蔬菜[干片]).*?([❶-❾]🔞)","$1蔬菜干$2"&CHAR(10))
.REGEXREPLACE("([❶-❾]).*?(?:[鸡鸭鹅虾蟹鱼蛋鸽牛羊驼兔鹿肉].*?[干卷条粒棒]|(?:风干|原切|烘干).*?[鸡鸭鹅虾蟹鱼蛋鸽牛羊驼兔鹿肉]).*?([❶-❾]🔞)","$1肉干$2"&CHAR(10))
.REGEXREPLACE("([❶-❾]).*?(?:[鸡鸭][大小]?胸|(?:蒸煮|水煮).*?[鸡鸭鹅虾蟹鱼蛋鸽牛羊驼兔鹿]|鸡腿|琵琶腿|肉丝).*?([❶-❾]🔞)","$1鲜肉辅食$2"&CHAR(10))
.REGEXREPLACE("([❶-❾]).*?(?:(?:磨牙)?(?:零食)|磨牙棒|饼干|火腿肠|甜甜圈|棒棒糖|小仙棒|小软饼|[缠绕]肉|肉[缠绕]|肉蛋|肉松|肉肠|三明治|气蛋|猫爪饼|泌尿饼|蛋黄).*?([❶-❾]🔞)","$1其他零食$2"&CHAR(10))


.REGEXREPLACE("([❶-❾]).*?(?:驱虫|内外同驱|内驱|外驱|海乐妙|拜宠清|内宠爱|犬心保|福来恩|非泼罗尼|拜宠爽|尼可信|贝卫多|大宠爱|爱沃克|博来恩|超可信|耳肤灵|吡虫|蜱虫|耳漂|除耳螨|药浴|体内|体外).*?([❶-❾]🔞)","$1驱虫药$2"&CHAR(10))
.REGEXREPLACE("([❶-❾]).*?(?:皮肤.*?喷剂|护肤洗剂|皮肤病|防护喷剂|防护喷雾|外出防护).*?([❶-❾]🔞)","$1皮肤用药$2"&CHAR(10))
.REGEXREPLACE("([❶-❾]).*?(?:妙三多|泌尿胶囊|叶黄素|利尿[通片]|尿闭|猫尿通|喂药灵|消炎片|眼药水|滴眼液|菌素软膏|伤口.*?[药剂]|狂犬|应激|情绪安定).*?([❶-❾]🔞)","$1其他药品$2"&CHAR(10))


.REGEXREPLACE("([❶-❾]).*?(?:软骨素|软骨粉|液体乳?钙|钙[片粉]|液体杯|关节生).*?([❶-❾]🔞)","$1关节护理$2"&CHAR(10))
.REGEXREPLACE("([❶-❾]).*?(?:益生菌|乳酸菌|肠舒宁|犬力肠|肠胃|乳铁蛋白|止泻药?|化毛膏?|猫草|排毛).*?([❶-❾]🔞)","$1肠胃护理$2"&CHAR(10))
.REGEXREPLACE("([❶-❻]).*?(?:鱼油|虾油|卵磷脂|海藻粉|爆毛粉|护发素|护毛素).*?([❶-❾]🔞)","$1毛发护理$2"&CHAR(10))
.REGEXREPLACE("([❶-❾]).*?(?:宠静安|护爪膏|[狗猫犬]多维|维生素|微量元素|辅酶|补血[肝甘]精|猫胺|营养膏|多维片|巧克力膏|营养补充剂).*?([❶-❾]🔞)","$1其他补剂$2"&CHAR(10))


.REGEXREPLACE("([❶-❾]).*?(?:清洁剂|洗手液|洗衣液|洗衣凝珠|洗脚|洁足|湿巾|湿纸巾|保湿纸|眼睛清洁|擦眼睛|尿垫|尿片|纸尿裤|洁牙|洁齿|口腔清洁|口腔护理|口腔消毒|漱口水|宠物.*?牙[刷膏]|猫咪.*?牙[刷膏]|沐浴露|香波|浴液|沐浴液|擦浴精华|洗澡|清洁泡沫|滴耳液|洗耳液|洁耳液|耳朵清洁液|拣屎袋|捡屎袋|垃圾袋|猫🥄砂[铲盆]|粘毛|毛粘|手套|指套|毛巾).*?([❶-❾]🔞)","$1清洁用品$2"&CHAR(10))
.REGEXREPLACE("([❶-❾]).*?(?:香氛|清新剂|除臭.*?喷[雾剂]|除臭|除味|环境喷[雾剂]).*?([❶-❾]🔞)","$1除臭剂$2"&CHAR(10))
.REGEXREPLACE("([❶-❾]).*?(?:消毒剂?|次氯酸).*?([❶-❾]🔞)","$1消毒剂$2"&CHAR(10))

.REGEXREPLACE("([❶-❾]).*?(?:吹水机|烘干箱|发泡[机]?|吹梳一体机).*?([❶-❾]🔞)","$1洗护工具$2"&CHAR(10))
.REGEXREPLACE("([❶-❾]).*?(?:剃脚?毛|电推[剪刀子]|剪毛|剪指甲|指甲剪|推毛器|梳|磨甲器|指甲钳).*?([❶-❾]🔞)","$1修剪工具$2"&CHAR(10))
.REGEXREPLACE("([❶-❾]).*?(?:牵引绳|项圈).*?([❶-❾]🔞)","$1牵引工具$2"&CHAR(10))

.REGEXREPLACE("([❶-❾]).*?(?:猫砂盆|厕所).*?([❶-❾]🔞)","$1宠物厕所$2"&CHAR(10))
.REGEXREPLACE("([❶-❾]).*?(?:猫抓板|猫抓柱|猫抓球|猫彩盒).*?([❶-❾]🔞)","$1猫抓板$2")
.REGEXREPLACE("([❶-❾]).*?(?:猫?爬架|隧道|猫柜).*?([❶-❾]🔞)","$1猫爬架$2")
.REGEXREPLACE("([❶-❾]).*?(?:猫衣服|猫咪衣服|狗衣服|宠物衣服|口水巾).*?([❶-❾]🔞)","$1宠物服饰$2"&CHAR(10))
.REGEXREPLACE("([❶-❾]).*?(?:玩具|逗猫棒?|啃咬绳|发声|滚滚球|迷宫|掏掏乐).*?([❶-❾]🔞)","$1宠物玩具$2"&CHAR(10))
.REGEXREPLACE("([❶-❾]).*?(?:宠物窝|[狗猫]窝|[冰凉].*?[席垫窝]|猫抓[席垫窝]|行军床|藤席[席垫窝]|水床|床垫|笼子?|垫子|帐篷|空调[房屋]|猫屋|吊床).*?([❶-❾]🔞)","$1宠物窝$2"&CHAR(10))
.REGEXREPLACE("([❶-❾]).*?(?:宠物背包|猫包|狗包|宠物包|航空箱|手提包).*?([❶-❾]🔞)","$1宠物包$2"&CHAR(10))
.REGEXREPLACE("([❶-❾]).*?(?:碗|狗盆|猫食盆|餐盘|饮水机|饮水器|喂食器|喂食机|水杯|随行杯|一体[机碗]|漏食).*?([❶-❾]🔞)","$1宠物碗$2"&CHAR(10))
.REGEXREPLACE("([❶-❾]).*?(?:餐垫).*?([❶-❾]🔞)","$1宠物餐垫$2"&CHAR(10))


.REGEXREPLACE("([❶-❾]).*?(?:伊丽莎白圈).*?([❶-❾]🔞)","$伊丽莎白圈$2"&CHAR(10))
.REGEXREPLACE("([❶-❾]).*?(?:沙发贴).*?([❶-❾]🔞)","$1沙发贴$2"&CHAR(10))
.REGEXREPLACE("([❶-❾]).*?(?:摄像头|夜视仪|监控).*?([❶-❾]🔞)","$1摄像头$2"&CHAR(10))
.REGEXREPLACE("([❶-❾]).*?(?:干燥[剂条]?).*?([❶-❾]🔞)","$1干燥剂$2"&CHAR(10))
.REGEXREPLACE("([❶-❾]).*?(?:储粮桶?).*?([❶-❾]🔞)","$1储粮桶$2"&CHAR(10))



.REGEXREPLACE("([❶-❾]).*?(?:猫薄荷).*?([❶-❾]🔞)","$1猫薄荷$2"&CHAR(10))


.REGEXREPLACE("([❶-❾]).*?(?:主🍚?[粮食]|(?:[猫狗犬]|宠物).*?粮|(?:冻干|生骨肉|猎物|家禽|风干|专用|处方|配方|烘焙|[一-十双两多]拼|膨化|无谷)[猫狗犬]?.*?粮).*?([❶-❾]🔞)","$1主粮$2"&CHAR(10))
.REGEXREPLACE("([❶-❾]).*?(?:素力高.*?[鸡]|金装素力高|素力高鲜肉美毛|蓝氏猎鸟乳鸽|诚实一口P40|爱肯拿.*?盛宴|牧野奇迹山谷盛宴|伯纳天纯农场派对|诚实一口奇.*?小金袋|百利高(?:蛋白)?).*?([❶-❾]🔞)","$1主粮$2"&CHAR(10))
.REGEXREPLACE("([❶-❾]).*?(?:"&[🧠品牌参考-逻辑]&").*?(?:粮|[大中小]型[老成幼]?[猫犬狗]|[老成幼]年?[猫犬狗]|试吃|禽肉|[鸡鸭鹅虾蟹鱼蛋鸽牛羊驼兔鹿]).*?([❶-❾]🔞)","$1主粮$5"&CHAR(10))
.REGEXREPLACE("([❶-❾]).*?(?:"&[🧠品牌参考-逻辑]&").*?(?:木薯|豆腐).*?([❶-❾]🔞)","$1猫砂$5"&CHAR(10))
.REGEXREPLACE("([❶-❾]).*?(?:"&[🧠品牌参考-逻辑]&").*?(?:勺).*?([❶-❾]🔞)","$1餐包$5"&CHAR(10))

.REGEXEXTRACTALL("[❶-❾]"&[🧠二级分类-逻辑]&"[❶-❾]🔞")
.ARRAYJOIN(CHAR(10))

.REGEXREPLACE("[,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🛍️🤍😀品类-二级分类（原始值）-优化版
IF(
[🍬商品信息（原始版）].ISBLANK(),""，

IF(
AND(
[🧪 规格解构（基建）].FILTER(CurrentValue.[🧪编号（最终）]=[🛍️编号（最终）]).[💰🤍😀付款类型].LISTCOMBINE()
.REGEXMATCH("任选|合并")
,
[🧪 规格解构（基建）].FILTER(CurrentValue.[🧪编号（最终）]=[🛍️编号（最终）]).[🧪🤍规格✖️数量提取（原始版）]
.ISBLANK()
.NOT()

)
,
[🛍️🤍😀品类-二级品类（原始值）]
.CONCATENATE(
CHAR(10),

[🧪 规格解构（基建）].FILTER(CurrentValue.[🧪编号（最终）]=[🛍️编号（最终）]).[🧪🔥🤍规格✖️数量提取（终版）].REGEXEXTRACT("[❶-❾]"),

[🛍️🤍😀品类-二级品类（原始值）].REGEXREPLACE("[❶-❾]其他[❶-❾]🔞","").REGEXEXTRACT("[❶-❾](.*?)[❶-❾]"),

[🧪 规格解构（基建）].FILTER(CurrentValue.[🧪编号（最终）]=[🛍️编号（最终）]).[🧪🔥🤍规格✖️数量提取（终版）].REGEXEXTRACT("[❶-❾]🔞")
)
.REGEXREPLACE([🧪 规格解构（基建）].FILTER(CurrentValue.[🧪编号（最终）]=[🛍️编号（最终）]).[🧪🔥🤍规格✖️数量提取（终版）]
.REGEXEXTRACT("[❶-❾]")&"其他"&[🧪 规格解构（基建）].FILTER(CurrentValue.[🧪编号（最终）]=[🛍️编号（最终）])
.[🧪🔥🤍规格✖️数量提取（终版）].REGEXEXTRACT("[❶-❾]🔞"),"")

,
[🛍️🤍😀品类-二级品类（原始值）]
))


.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🛍️✍️🤍😀二级品类（唯一值）（手写）
作为参数输入
=====
🛍️🤍😀二级品类（唯一值）
IF(
[🍬商品信息（原始版）].ISBLANK(),""，

IF(
[🛍️✍️🤍😀二级品类（唯一值）（手写）]
.ISBLANK()
.NOT(),

[🛍️✍️🤍😀二级品类（唯一值）（手写）]
，

[🛍️🤍😀品类-二级品类（原始值）]
.REGEXREPLACE("[❶-❾🔞]","")
.SPLIT(CHAR(10))
.UNIQUE()
.ARRAYJOIN(CHAR(10))
))

.REGEXREPLACE("🈚️无需check","")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🛍️🤍😀品类-一级品类（原始值）
[🛍️🤍😀品类-二级分类（原始值）-优化版]
.REGEXEXTRACTALL("[❶-❾]"&[🧠二级分类-逻辑]&"[❶-❾]🔞")
.ARRAYJOIN(CHAR(10))

.REGEXREPLACE("(猫砂)","猫砂")
.REGEXREPLACE("(主粮)","主粮")
.REGEXREPLACE("(罐头|餐盒|餐包|鲜肉辅食|猫条|狗条)","湿粮罐头")
.REGEXREPLACE("(冻干|乳制品|肉干|蔬菜干|猫薄荷|其他零食)","零食")
.REGEXREPLACE("(驱虫药|皮肤用药|其他药品)","驱虫保健")
.REGEXREPLACE("(关节护理|肠胃护理|毛发护理|其他补剂)","驱虫保健")
.REGEXREPLACE("(清洁用品|除臭剂|消毒剂)","清洁护理")
.REGEXREPLACE("(洗护工具|修剪工具|牵引工具|宠物厕所|猫抓板|猫爬架|宠物服饰|宠物玩具|宠物窝|宠物包|宠物碗|宠物餐垫|伊丽莎白圈|沙发贴|摄像头|干燥剂|储粮桶|其他)","日用其他")

.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🛍️🤍😀一级品类（唯一值）
IF(
[🍬商品信息（原始版）].ISBLANK(),""，

IF(
[🛍️✍️🤍😀二级品类（唯一值）（手写）]
.ISBLANK()
.NOT(),

[🛍️✍️🤍😀二级品类（唯一值）（手写）]
.REGEXREPLACE("(猫砂)","猫砂")
.REGEXREPLACE("(主粮)","主粮")
.REGEXREPLACE("(罐头|餐盒|餐包|鲜肉辅食|猫条|狗条)","湿粮罐头")
.REGEXREPLACE("(冻干|乳制品|肉干|猫薄荷|蔬菜干|其他零食)","零食")
.REGEXREPLACE("(驱虫药|皮肤用药|其他药品)","驱虫保健")
.REGEXREPLACE("(关节护理|肠胃护理|毛发护理|其他补剂)","驱虫保健")
.REGEXREPLACE("(清洁用品|除臭剂|消毒剂)","清洁护理")
.REGEXREPLACE("(洗护工具|修剪工具|牵引工具|宠物厕所|猫抓板|猫爬架|宠物服饰|宠物玩具|宠物窝|宠物包|宠物碗|宠物餐垫|伊丽莎白圈|沙发贴|摄像头|干燥剂|储粮桶|其他)","日用其他")


，

[🛍️🤍😀品类-一级品类（原始值）]
.REGEXREPLACE("[❶-❾🔞]","")
.SPLIT(CHAR(10))
.UNIQUE()
.ARRAYJOIN(CHAR(10))
))

.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🛍️品类正确性check进度
IF(
OR(
[🍬商品信息（原始版）]
.ISBLANK()

，
[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🛍️编号（最终）]).[📁🩵信息类型（唯一值）]
.REGEXMATCH("🛒").NOT()
)
，""，
 

IF(
[🛍️🤍😀品类-二级品类（原始值）]
.ISBLANK()
.NOT()
，

"✅已check"
，

IF(
OR(
[🛍️ ｜✍️ 品类｜口味｜制作工艺 ｜ 使用对象 ｜ 成长期（基建）].FILTER(CurrentValue.[🛍️编号（最终）]=[🛍️编号（最终）]).[🛍️💜使用对象(唯一值)]
="人"

，
[🛍️✍️🤍😀二级品类（唯一值）（手写）]
.REGEXMATCH("🈚️无需check")

，
AND(
[🛍️ ｜✍️ 品类｜口味｜制作工艺 ｜ 使用对象 ｜ 成长期（基建）].FILTER(CurrentValue.[🛍️编号（最终）]=[🛍️编号（最终）]).[🛍️💜使用对象(唯一值)]
.REGEXMATCH("人")

，

[Ⓜ️ ｜ ✍️ 品牌识别（基建）].FILTER(CurrentValue.[Ⓜ️编号（最终）]=[🛍️编号（最终）]).[Ⓜ️品牌填写check进度]
.REGEXMATCH("A|B|D")
.NOT())

,
[🛍️🤍😀商品名称（品类提取专属）]
.REGEXMATCH("加[购物车]{0,3}.*?(\d+|💰)")


)

，"🈚️无需check"

，
IF(
AND(
[🛍️🤍😀品类-二级品类（原始值）]
.ISBLANK()
，
[🛍️✍️🤍😀二级品类（唯一值）（手写）]
.ISBLANK())

，
"⭕️待check"，
"✅已check"

))))
=====
🛍️💜口味提取（原始值）
CONCATENATE(
"❶"，
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(1)
.REGEXREPLACE([🧠品牌参考-逻辑],"")
.REGEXEXTRACTALL([🧠口味参考-逻辑])
.ARRAYJOIN("，")
.UNIQUE()
,"❶🔞"


,CHAR(10),


"❷"，
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(2)
.REGEXREPLACE([🧠品牌参考-逻辑],"")
.REGEXEXTRACTALL([🧠口味参考-逻辑])
.ARRAYJOIN("，")
.UNIQUE()
,"❷🔞"




,CHAR(10),





"❸"，
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(3)
.REGEXREPLACE([🧠品牌参考-逻辑],"")
.REGEXEXTRACTALL([🧠口味参考-逻辑])
.ARRAYJOIN("，")
.UNIQUE()
,"❸🔞"




,CHAR(10),



"❹"，
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(4)
.REGEXREPLACE([🧠品牌参考-逻辑],"")
.REGEXEXTRACTALL([🧠口味参考-逻辑])
.ARRAYJOIN("，")
.UNIQUE()
,"❹🔞"



,CHAR(10),


"❺"，
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(5)
.REGEXREPLACE([🧠品牌参考-逻辑],"")
.REGEXEXTRACTALL([🧠口味参考-逻辑])
.ARRAYJOIN("，")
.UNIQUE()
,"❺🔞"




,CHAR(10),





"❻"，
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(6)
.REGEXREPLACE([🧠品牌参考-逻辑],"")
.REGEXEXTRACTALL([🧠口味参考-逻辑])
.ARRAYJOIN("，")
.UNIQUE()
,"❻🔞"，



,CHAR(10),

"❼",
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(7)
.REGEXREPLACE([🧠品牌参考-逻辑],"")
.REGEXEXTRACTALL([🧠口味参考-逻辑])
.ARRAYJOIN("，")
.UNIQUE()
,"❼🔞"



,CHAR(10),

"❽",
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(8)
.REGEXREPLACE([🧠品牌参考-逻辑],"")
.REGEXEXTRACTALL([🧠口味参考-逻辑])
.ARRAYJOIN("，")
.UNIQUE()
,"❽🔞"


,CHAR(10),

"❾",
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(9)
.REGEXREPLACE([🧠品牌参考-逻辑],"")
.REGEXEXTRACTALL([🧠口味参考-逻辑])
.ARRAYJOIN("，")
.UNIQUE()
,"❾🔞"

)



.REGEXREPLACE("(❶❶|❷❷|❸❸|❹❹|❺❺|❻❻|❼❼|❽❽|❾❾)🔞","")
.REGEXREPLACE("[,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🛍️💜口味（唯一值）
IF(ISBLANK([🍬商品信息（原始版）])，""，
IF(
[🛍️💜口味提取（原始值）]
.REGEXREPLACE("[❶-❾]","")
.SPLIT("🔞")
.ISBLANK()
,"",

[🛍️💜口味提取（原始值）]
.REGEXREPLACE("[❶-❾🔞]","")
.REGEXEXTRACTALL([🧠口味参考-逻辑])
.UNIQUE()
.ARRAYJOIN("，")
))

.REGEXREPLACE("原?(矿砂|矿)","原矿")
.REGEXREPLACE("生骨","生骨肉")
.REGEXREPLACE("肉+","肉")
.REGEXREPLACE("鲜酪","乳酪")
.REGEXREPLACE("椰壳","椰子")
.REGEXREPLACE("竹纤维|竹子","竹子")

.REGEXEXTRACTALL([🧠口味参考-逻辑])
.UNIQUE()
.ARRAYJOIN()


.REGEXREPLACE("[,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🛍️💜制作工艺提取（原始值）
CONCATENATE(
"❶"，
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(1)
.REGEXREPLACE([🧠品牌参考-逻辑],"")
.REGEXEXTRACTALL([🧠制作工艺-逻辑])
.ARRAYJOIN("，")
.UNIQUE()
,"❶🔞"

,CHAR(10),

"❷"，
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(2)
.REGEXREPLACE([🧠品牌参考-逻辑],"")
.REGEXEXTRACTALL([🧠制作工艺-逻辑])
.ARRAYJOIN("，")
.UNIQUE()
,"❷🔞"

,CHAR(10),

"❸"，
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(3)
.REGEXREPLACE([🧠品牌参考-逻辑],"")
.REGEXEXTRACTALL([🧠制作工艺-逻辑])
.ARRAYJOIN("，")
.UNIQUE()
,"❸🔞"

,CHAR(10),


"❹"，
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(4)
.REGEXREPLACE([🧠品牌参考-逻辑],"")
.REGEXEXTRACTALL([🧠制作工艺-逻辑])
.ARRAYJOIN("，")
.UNIQUE()
,"❹🔞"


,CHAR(10),



"❺"，
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(5)
.REGEXREPLACE([🧠品牌参考-逻辑],"")
.REGEXEXTRACTALL([🧠制作工艺-逻辑])
.ARRAYJOIN("，")
.UNIQUE()
,"❺🔞"


,CHAR(10),


"❻"，
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(6)
.REGEXREPLACE([🧠品牌参考-逻辑],"")
.REGEXEXTRACTALL([🧠制作工艺-逻辑])
.ARRAYJOIN("，")
.UNIQUE()
,"❻🔞"，

,CHAR(10),

"❼",
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(7)
.REGEXREPLACE([🧠品牌参考-逻辑],"")
.REGEXEXTRACTALL([🧠制作工艺-逻辑])
.ARRAYJOIN("，")
.UNIQUE()
,"❼🔞"

,CHAR(10),

"❽",
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(8)
.REGEXREPLACE([🧠品牌参考-逻辑],"")
.REGEXEXTRACTALL([🧠制作工艺-逻辑])
.ARRAYJOIN("，")
.UNIQUE()
,"❽🔞"


,CHAR(10),

"❾",
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(9)
.REGEXREPLACE([🧠品牌参考-逻辑],"")
.REGEXEXTRACTALL([🧠制作工艺-逻辑])
.ARRAYJOIN("，")
.UNIQUE()
,"❾🔞"



）



.REGEXREPLACE("(❶❶|❷❷|❸❸|❹❹|❺❺|❻❻|❼❼|❽❽|❾❾)🔞","")
.REGEXREPLACE("[,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🛍️💜制作工艺（唯一值）
IF(ISBLANK([🍬商品信息（原始版）])，""，
IF(
[🛍️💜制作工艺提取（原始值）]
.REGEXREPLACE("[❶-❾]","")
.SPLIT("🔞")
.ISBLANK()
,"",

[🛍️💜制作工艺提取（原始值）]
.REGEXREPLACE("[❶-❾🔞]","")
.REGEXEXTRACTALL([🧠制作工艺-逻辑])
.UNIQUE()
.ARRAYJOIN("，")
))



.REGEXREPLACE("[,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🛍️💜口味填写情况
IF(
[🛍️💜口味（唯一值）]
.ISBLANK()
.NOT(),

"口味已提取✅"
，"")
=====
🛍️💜适用宠物成长期（原始值）CONCATENATE(
"❶"，
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(1)
.REGEXEXTRACTALL([🧠成长期-逻辑])
.ARRAYJOIN("，")
.UNIQUE()
,"❶🔞"

,CHAR(10),

"❷"，
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(2)
.REGEXEXTRACTALL([🧠成长期-逻辑])
.ARRAYJOIN("，")
.UNIQUE()
,"❷🔞"

,CHAR(10),



"❸"，
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(3)
.REGEXEXTRACTALL([🧠成长期-逻辑])
.ARRAYJOIN("，")
.UNIQUE()
,"❸🔞"

,CHAR(10),



"❹"，
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(4)
.REGEXEXTRACTALL([🧠成长期-逻辑])
.ARRAYJOIN("，")
.UNIQUE()
,"❹🔞"


,CHAR(10),


"❺"，
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(5)
.REGEXEXTRACTALL([🧠成长期-逻辑])
.ARRAYJOIN("，")
.UNIQUE()
,"❺🔞"

,CHAR(10),



"❻"，
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(6)
.REGEXEXTRACTALL([🧠成长期-逻辑])
.ARRAYJOIN("，")
.UNIQUE()
,"❻🔞"，

,CHAR(10),


"❼",
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(7)
.REGEXEXTRACTALL([🧠成长期-逻辑])
.ARRAYJOIN("，")
.UNIQUE()
,"❼🔞"



,CHAR(10),


"❽",
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(8)
.REGEXEXTRACTALL([🧠成长期-逻辑])
.ARRAYJOIN("，")
.UNIQUE()
,"❽🔞"


,CHAR(10),

"❾",
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(9)
.REGEXEXTRACTALL([🧠成长期-逻辑])
.ARRAYJOIN("，")
.UNIQUE()
,"❾🔞"


）

.REGEXREPLACE("(❶❶|❷❷|❸❸|❹❹|❺❺|❻❻|❼❼|❽❽|❾❾)🔞","")
.REGEXREPLACE("[,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🛍️💜适用宠物成长期（唯一值）
IF(ISBLANK([🍬商品信息（原始版）])，""，
IF(ISBLANK(
[🛍️💜适用宠物成长期（原始值）]
.REGEXREPLACE("[❶-❾]","")
.SPLIT("🔞"))
,"全年龄段通用",

[🛍️💜适用宠物成长期（原始值）]
.REGEXREPLACE("[❶-❾🔞]","")
.REGEXEXTRACTALL([🧠成长期-逻辑])
.UNIQUE()
.ARRAYJOIN("，")
))



.TRIM()

.REGEXREPLACE("幼年?(猫|犬)","幼年期")
.REGEXREPLACE("成年?(猫|犬)","成年期")
.REGEXREPLACE("老年?(猫|犬)","老年期")

.REGEXREPLACE("[,，+➕～\s]+$", "")
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🛍️💜使用对象(原始值)(1-6)
CONCATENATE(
IF(
OR(
[🍬商品信息（原始版）]
.ISBLANK()
,

[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🛍️编号（最终）]).[📁💜信息类型]
.REGEXEXTRACTALL("❶.*?❶🔞")
.REGEXMATCH("🛒")
.NOT()

，

[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(1)
.TRIM()
.ISBLANK()
)
, "",

IF(
AND(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(1)
.REGEXREPLACE([🧠品牌-逻辑],"")
.REGEXREPLACE("猫超|天猫","")
.REGEXMATCH("猫")
,

[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(1)
.REGEXREPLACE([🧠品牌-逻辑],"")
.REGEXREPLACE("猫超|天猫","")
.REGEXMATCH("狗|犬"))

, "❶猫，狗❶🔞",

IF(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(1)
.REGEXREPLACE([🧠品牌-逻辑],"")
.REGEXREPLACE("猫超|天猫","")
.REGEXMATCH("狗|犬")
, "❶狗❶🔞",

IF(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(1)
.REGEXREPLACE([🧠品牌-逻辑],"")
.REGEXREPLACE("猫超|天猫","")
.REGEXMATCH("猫|砂")
,"❶猫❶🔞"
,

IF(
AND(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(1)
.REGEXREPLACE([🧠品牌-逻辑],"")
.REGEXREPLACE("猫超|天猫","")
.REGEXMATCH("猫|狗|犬")
.NOT()
,
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(1)
.REGEXREPLACE([🧠品牌-逻辑],"")
.REGEXREPLACE("猫超|天猫","")
.REGEXMATCH("宠物")
)
, "❶猫，狗❶🔞",


IF(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(1)
.REGEXREPLACE([🧠品牌-逻辑],"")
.REGEXREPLACE("猫超|天猫","")
.REGEXMATCH([🧠使用对象-逻辑（人）])
, "❶人❶🔞",


[📁社群定位]
.REGEXEXTRACTALL("猫|狗")
.REGEXREPLACE("猫|狗","❶$0❶🔞")

))))))

，CHAR(10)，

IF(
OR(
[🍬商品信息（原始版）]
.ISBLANK()
,

[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🛍️编号（最终）]).[📁💜信息类型]
.REGEXEXTRACTALL("❷.*?❷🔞")
.REGEXMATCH("🛒")
.NOT()

,

[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(2)
.TRIM()
.ISBLANK()
)
, "",

IF(
AND(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(2)
.REGEXREPLACE([🧠品牌-逻辑],"")
.REGEXREPLACE("猫超|天猫","")
.REGEXMATCH("猫")
,

[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(2)
.REGEXREPLACE([🧠品牌-逻辑],"")
.REGEXREPLACE("猫超|天猫","")
.REGEXMATCH("狗|犬"))

, "❷猫，狗❷🔞",

IF(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(2)
.REGEXREPLACE([🧠品牌-逻辑],"")
.REGEXREPLACE("猫超|天猫","")
.REGEXMATCH("狗|犬")
, "❷狗❷🔞",

IF(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(2)
.REGEXREPLACE([🧠品牌-逻辑],"")
.REGEXREPLACE("猫超|天猫","")
.REGEXMATCH("猫|砂")
,"❷猫❷🔞"
,

IF(
AND(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(2)
.REGEXREPLACE([🧠品牌-逻辑],"")
.REGEXREPLACE("猫超|天猫","")
.REGEXMATCH("猫|狗|犬")
.NOT()
,
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(2)
.REGEXREPLACE([🧠品牌-逻辑],"")
.REGEXREPLACE("猫超|天猫","")
.REGEXMATCH("宠物")
)
, "❷猫，狗❷🔞",


IF(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(2)
.REGEXREPLACE([🧠品牌-逻辑],"")
.REGEXREPLACE("猫超|天猫","")
.REGEXMATCH([🧠使用对象-逻辑（人）])
, "❷人❷🔞",


[📁社群定位]
.REGEXEXTRACTALL("猫|狗")
.REGEXREPLACE("猫|狗","❷$0❷🔞")
))))))

，CHAR(10)，

IF(
OR(
[🍬商品信息（原始版）]
.ISBLANK()
,

[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🛍️编号（最终）]).[📁💜信息类型]
.REGEXEXTRACTALL("❸.*?❸🔞")
.REGEXMATCH("🛒")
.NOT()

,

[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(3)
.TRIM()
.ISBLANK()
)
, "",

IF(
AND(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(3)
.REGEXREPLACE([🧠品牌-逻辑],"")
.REGEXREPLACE("猫超|天猫","")
.REGEXMATCH("猫")
,

[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(3)
.REGEXREPLACE([🧠品牌-逻辑],"")
.REGEXREPLACE("猫超|天猫","")
.REGEXMATCH("狗|犬"))

, "❸猫，狗❸🔞",

IF(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(3)
.REGEXREPLACE([🧠品牌-逻辑],"")
.REGEXREPLACE("猫超|天猫","")
.REGEXMATCH("狗|犬")
, "❸狗❸🔞",

IF(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(3)
.REGEXREPLACE([🧠品牌-逻辑],"")
.REGEXREPLACE("猫超|天猫","")
.REGEXMATCH("猫|砂")
,"❸猫❸🔞"
,

IF(
AND(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(3)
.REGEXREPLACE([🧠品牌-逻辑],"")
.REGEXREPLACE("猫超|天猫","")
.REGEXMATCH("猫|狗|犬")
.NOT()
,
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(3)
.REGEXREPLACE([🧠品牌-逻辑],"")
.REGEXREPLACE("猫超|天猫","")
.REGEXMATCH("宠物")
)
, "❸猫，狗❸🔞",


IF(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(3)
.REGEXREPLACE([🧠品牌-逻辑],"")
.REGEXREPLACE("猫超|天猫","")
.REGEXMATCH([🧠使用对象-逻辑（人）])
, "❸人❸🔞",


[📁社群定位]
.REGEXEXTRACTALL("猫|狗")
.REGEXREPLACE("猫|狗","❸$0❸🔞")

))))))

，CHAR(10)，

IF(
OR(
[🍬商品信息（原始版）]
.ISBLANK()
,

[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🛍️编号（最终）]).[📁💜信息类型]
.REGEXEXTRACTALL("❹.*?❹🔞")
.REGEXMATCH("🛒")
.NOT()

,

[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(4)
.TRIM()
.ISBLANK()
)
, "",

IF(
AND(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(4)
.REGEXREPLACE([🧠品牌-逻辑],"")
.REGEXREPLACE("猫超|天猫","")
.REGEXMATCH("猫")
,

[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(4)
.REGEXREPLACE([🧠品牌-逻辑],"")
.REGEXREPLACE("猫超|天猫","")
.REGEXMATCH("狗|犬"))

, "❹猫，狗❹🔞",

IF(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(4)
.REGEXREPLACE([🧠品牌-逻辑],"")
.REGEXREPLACE("猫超|天猫","")
.REGEXMATCH("狗|犬")
, "❹狗❹🔞",

IF(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(4)
.REGEXREPLACE([🧠品牌-逻辑],"")
.REGEXREPLACE("猫超|天猫","")
.REGEXMATCH("猫|砂")
,"❹猫❹🔞"
,

IF(
AND(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(4)
.REGEXREPLACE([🧠品牌-逻辑],"")
.REGEXREPLACE("猫超|天猫","")
.REGEXMATCH("猫|狗|犬")
.NOT()
,
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(4)
.REGEXREPLACE([🧠品牌-逻辑],"")
.REGEXREPLACE("猫超|天猫","")
.REGEXMATCH("宠物")
)
, "❹猫，狗❹🔞",


IF(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(4)
.REGEXREPLACE([🧠品牌-逻辑],"")
.REGEXREPLACE("猫超|天猫","")
.REGEXMATCH([🧠使用对象-逻辑（人）])
, "❹人❹🔞",


[📁社群定位]
.REGEXEXTRACTALL("猫|狗")
.REGEXREPLACE("猫|狗","❹$0❹🔞")

))))))

，CHAR(10)，

IF(
OR(
[🍬商品信息（原始版）]
.ISBLANK()
,

[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🛍️编号（最终）]).[📁💜信息类型]
.REGEXEXTRACTALL("❺.*?❺🔞")
.REGEXMATCH("🛒")
.NOT()


,

[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(5)
.TRIM()
.ISBLANK()
)
, "",

IF(
AND(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(5)
.REGEXREPLACE([🧠品牌-逻辑],"")
.REGEXREPLACE("猫超|天猫","")
.REGEXMATCH("猫")
,

[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(5)
.REGEXREPLACE([🧠品牌-逻辑],"")
.REGEXREPLACE("猫超|天猫","")
.REGEXMATCH("狗|犬"))

, "❺猫，狗❺🔞",

IF(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(5)
.REGEXREPLACE([🧠品牌-逻辑],"")
.REGEXREPLACE("猫超|天猫","")
.REGEXMATCH("狗|犬")
, "❺狗❺🔞",

IF(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(5)
.REGEXREPLACE([🧠品牌-逻辑],"")
.REGEXREPLACE("猫超|天猫","")
.REGEXMATCH("猫|砂")
,"❺猫❺🔞"
,

IF(
AND(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(5)
.REGEXREPLACE([🧠品牌-逻辑],"")
.REGEXREPLACE("猫超|天猫","")
.REGEXMATCH("猫|狗|犬")
.NOT()
,
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(5)
.REGEXREPLACE([🧠品牌-逻辑],"")
.REGEXREPLACE("猫超|天猫","")
.REGEXMATCH("宠物")
)
, "❺猫，狗❺🔞",


IF(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(5)
.REGEXREPLACE([🧠品牌-逻辑],"")
.REGEXREPLACE("猫超|天猫","")
.REGEXMATCH([🧠使用对象-逻辑（人）])
, "❺人❺🔞",


[📁社群定位]
.REGEXEXTRACTALL("猫|狗")
.REGEXREPLACE("猫|狗","❺$0❺🔞")

))))))


，CHAR(10)，

IF(
OR(
[🍬商品信息（原始版）]
.ISBLANK()
,

[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🛍️编号（最终）]).[📁💜信息类型]
.REGEXEXTRACTALL("❻.*?❻🔞")
.REGEXMATCH("🛒")
.NOT()

,

[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(6)
.TRIM()
.ISBLANK()
)
, "",

IF(
AND(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(6)
.REGEXREPLACE([🧠品牌-逻辑],"")
.REGEXREPLACE("猫超|天猫","")
.REGEXMATCH("猫")
,

[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(6)
.REGEXREPLACE([🧠品牌-逻辑],"")
.REGEXREPLACE("猫超|天猫","")
.REGEXMATCH("狗|犬"))

, "❻猫，狗❻🔞",

IF(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(6)
.REGEXREPLACE([🧠品牌-逻辑],"")
.REGEXREPLACE("猫超|天猫","")
.REGEXMATCH("狗|犬")
, "❻狗❻🔞",

IF(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(6)
.REGEXREPLACE([🧠品牌-逻辑],"")
.REGEXREPLACE("猫超|天猫","")
.REGEXMATCH("猫|砂")
,"❻猫❻🔞"
,

IF(
AND(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(6)
.REGEXREPLACE([🧠品牌-逻辑],"")
.REGEXREPLACE("猫超|天猫","")
.REGEXMATCH("猫|狗|犬")
.NOT()
,
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(6)
.REGEXREPLACE([🧠品牌-逻辑],"")
.REGEXREPLACE("猫超|天猫","")
.REGEXMATCH("宠物")
)
, "❻猫，狗❻🔞",


IF(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(6)
.REGEXREPLACE([🧠品牌-逻辑],"")
.REGEXREPLACE("猫超|天猫","")
.REGEXMATCH([🧠使用对象-逻辑（人）])
, "❻人❻🔞",


[📁社群定位]
.REGEXEXTRACTALL("猫|狗")
.REGEXREPLACE("猫|狗","❻$0❻🔞")

))))))

)
.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🛍️💜使用对象(原始值)(7-9)
CONCATENATE(
IF(
OR(
[🍬商品信息（原始版）]
.ISBLANK()
,

[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🛍️编号（最终）]).[📁💜信息类型]
.REGEXEXTRACTALL("❼.*?❼🔞")
.REGEXMATCH("🛒")
.NOT()

,

[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(7)
.TRIM()
.ISBLANK()
)
, "",

IF(
AND(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(7)
.REGEXREPLACE([🧠品牌-逻辑],"")
.REGEXREPLACE("猫超|天猫","")
.REGEXMATCH("猫")
,

[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(7)
.REGEXREPLACE([🧠品牌-逻辑],"")
.REGEXREPLACE("猫超|天猫","")
.REGEXMATCH("狗|犬"))

, "❼猫，狗❼🔞",

IF(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(7)
.REGEXREPLACE([🧠品牌-逻辑],"")
.REGEXREPLACE("猫超|天猫","")
.REGEXMATCH("狗|犬")
, "❼狗❼🔞",

IF(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(7)
.REGEXREPLACE([🧠品牌-逻辑],"")
.REGEXREPLACE("猫超|天猫","")
.REGEXMATCH("猫|砂")
,"❼猫❼🔞"
,

IF(
AND(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(7)
.REGEXREPLACE([🧠品牌-逻辑],"")
.REGEXREPLACE("猫超|天猫","")
.REGEXMATCH("猫|狗|犬")
.NOT()
,
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(7)
.REGEXREPLACE([🧠品牌-逻辑],"")
.REGEXREPLACE("猫超|天猫","")
.REGEXMATCH("宠物")
)
, "❼猫，狗❼🔞",


IF(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(7)
.REGEXREPLACE([🧠品牌-逻辑],"")
.REGEXREPLACE("猫超|天猫","")
.REGEXMATCH([🧠使用对象-逻辑（人）])
, "❼人❼🔞",


[📁社群定位]
.REGEXEXTRACTALL("猫|狗")
.REGEXREPLACE("猫|狗","❼$0❼🔞")

))))))


，CHAR(10)，

IF(
OR(
[🍬商品信息（原始版）]
.ISBLANK()
,

[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🛍️编号（最终）]).[📁💜信息类型]
.REGEXEXTRACTALL("❽.*?❽🔞")
.REGEXMATCH("🛒")
.NOT()

,

[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(8)
.TRIM()
.ISBLANK()
)
, "",

IF(
AND(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(8)
.REGEXREPLACE([🧠品牌-逻辑],"")
.REGEXREPLACE("猫超|天猫","")
.REGEXMATCH("猫")
,

[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(8)
.REGEXREPLACE([🧠品牌-逻辑],"")
.REGEXREPLACE("猫超|天猫","")
.REGEXMATCH("狗|犬"))

, "❽猫，狗❽🔞",

IF(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(8)
.REGEXREPLACE([🧠品牌-逻辑],"")
.REGEXREPLACE("猫超|天猫","")
.REGEXMATCH("狗|犬")
, "❽狗❽🔞",

IF(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(8)
.REGEXREPLACE([🧠品牌-逻辑],"")
.REGEXREPLACE("猫超|天猫","")
.REGEXMATCH("猫|砂")
,"❽猫❽🔞"
,

IF(
AND(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(8)
.REGEXREPLACE([🧠品牌-逻辑],"")
.REGEXREPLACE("猫超|天猫","")
.REGEXMATCH("猫|狗|犬")
.NOT()
,
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(8)
.REGEXREPLACE([🧠品牌-逻辑],"")
.REGEXREPLACE("猫超|天猫","")
.REGEXMATCH("宠物")
)
, "❽猫，狗❽🔞",


IF(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(8)
.REGEXREPLACE([🧠品牌-逻辑],"")
.REGEXREPLACE("猫超|天猫","")
.REGEXMATCH([🧠使用对象-逻辑（人）])
, "❽人❽🔞",


[📁社群定位]
.REGEXEXTRACTALL("猫|狗")
.REGEXREPLACE("猫|狗","❽$0❽🔞")

))))))


，CHAR(10)，

IF(
OR(
[🍬商品信息（原始版）]
.ISBLANK()
,

[📁 ｜✍️信息获取(基建）].FILTER(CurrentValue.[📁编号（最终）]=[🛍️编号（最终）]).[📁💜信息类型]
.REGEXEXTRACTALL("❾.*?❾🔞")
.REGEXMATCH("🛒")
.NOT()

,

[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(9)
.TRIM()
.ISBLANK()
)
, "",

IF(
AND(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(9)
.REGEXREPLACE([🧠品牌-逻辑],"")
.REGEXREPLACE("猫超|天猫","")
.REGEXMATCH("猫")
,

[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(9)
.REGEXREPLACE([🧠品牌-逻辑],"")
.REGEXREPLACE("猫超|天猫","")
.REGEXMATCH("狗|犬"))

, "❾猫，狗❾🔞",

IF(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(9)
.REGEXREPLACE([🧠品牌-逻辑],"")
.REGEXREPLACE("猫超|天猫","")
.REGEXMATCH("狗|犬")
, "❾狗❾🔞",

IF(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(9)
.REGEXREPLACE([🧠品牌-逻辑],"")
.REGEXREPLACE("猫超|天猫","")
.REGEXMATCH("猫|砂")
,"❾猫❾🔞"
,

IF(
AND(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(9)
.REGEXREPLACE([🧠品牌-逻辑],"")
.REGEXREPLACE("猫超|天猫","")
.REGEXMATCH("猫|狗|犬")
.NOT()
,
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(9)
.REGEXREPLACE([🧠品牌-逻辑],"")
.REGEXREPLACE("猫超|天猫","")
.REGEXMATCH("宠物")
)
, "❾猫，狗❾🔞",


IF(
[📁💜商品信息（终版分割版）]
.SPLIT("🙂‍↕️🅾️")
.NTH(9)
.REGEXREPLACE([🧠品牌-逻辑],"")
.REGEXREPLACE("猫超|天猫","")
.REGEXMATCH([🧠使用对象-逻辑（人）])
, "❾人❾🔞",


[📁社群定位]
.REGEXEXTRACTALL("猫|狗")
.REGEXREPLACE("猫|狗","❾$0❾🔞")

))))))

)

.REGEXREPLACE("(?m)^\s*$\r?\n?", "")
=====
🛍️💜使用对象(原始值)
CONCATENATE(
[🛍️💜使用对象(原始值)(1-6)]
,CHAR(10),
[🛍️💜使用对象(原始值)(7-9)])
=====
🛍️💜使用对象(唯一值)
IF(ISBLANK([🛍️💜使用对象(原始值)])，""，
[🛍️💜使用对象(原始值)]
.REGEXEXTRACTALL("(猫，狗|猫|狗|人)")
.UNIQUE()
.ARRAYJOIN("，")
)

.REGEXEXTRACTALL("(猫|狗|人)")
.UNIQUE()
.ARRAYJOIN("，")
=====

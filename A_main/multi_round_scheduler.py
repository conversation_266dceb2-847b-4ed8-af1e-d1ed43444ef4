"""
多轮处理调度器
统一管理所有表格的多轮迭代处理流程
实现您的策略：所有表一起实现，通过多轮迭代完成依赖关系处理
"""

import sys
import json
from pathlib import Path
from typing import Dict, List, Any, Optional
import logging
from datetime import datetime

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from A_main.round1_processor import Round1Processor
from A_main.round2_processor import Round2Processor
from A_main.dependency_manager import DependencyManager, FieldStatus

class MultiRoundScheduler:
    """多轮处理调度器"""
    
    def __init__(self):
        self.logger = self._setup_logger()
        
        # 初始化各轮处理器
        self.round1_processor = Round1Processor()
        self.round2_processor = Round2Processor()
        self.dependency_manager = DependencyManager()
        
        # 处理状态跟踪
        self.processing_stats = {
            "start_time": None,
            "end_time": None,
            "total_fields": 0,
            "completed_fields": 0,
            "failed_fields": 0,
            "rounds_completed": 0
        }
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger('MultiRoundScheduler')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def process_complete_workflow(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行完整的多轮处理工作流
        这是您策略的核心实现：所有表一起实现，多轮迭代处理
        """
        self.logger.info("🚀 开始多轮迭代处理工作流")
        self.processing_stats["start_time"] = datetime.now()
        
        try:
            # 第一轮：源信息和第一原生字段
            self.logger.info("=" * 60)
            self.logger.info("🔵 第一轮：所有表的源信息和第一原生字段")
            self.logger.info("=" * 60)
            
            round1_results = self.round1_processor.process_all_tables_round1(raw_data)
            self.processing_stats["rounds_completed"] = 1
            self._log_round_summary("第一轮", round1_results)
            
            # 第二轮：关联字段
            self.logger.info("\n" + "=" * 60)
            self.logger.info("🟡 第二轮：所有表的关联字段")
            self.logger.info("=" * 60)
            
            round2_results = self.round2_processor.process_all_tables_round2(round1_results)
            self.processing_stats["rounds_completed"] = 2
            self._log_round_summary("第二轮", round2_results)
            
            # 第三轮：第二原生字段（待实现）
            self.logger.info("\n" + "=" * 60)
            self.logger.info("🟢 第三轮：所有表的第二原生字段")
            self.logger.info("=" * 60)
            
            round3_results = self._process_round3(round2_results)
            self.processing_stats["rounds_completed"] = 3
            self._log_round_summary("第三轮", round3_results)
            
            # 第四轮：复杂交叉依赖（待实现）
            self.logger.info("\n" + "=" * 60)
            self.logger.info("🟣 第四轮：复杂交叉依赖字段")
            self.logger.info("=" * 60)
            
            final_results = self._process_round4(round3_results)
            self.processing_stats["rounds_completed"] = 4
            self._log_round_summary("第四轮", final_results)
            
            # 处理完成
            self.processing_stats["end_time"] = datetime.now()
            self._log_final_summary(final_results)
            
            return final_results
            
        except Exception as e:
            self.logger.error(f"❌ 多轮处理工作流失败: {e}")
            self.processing_stats["end_time"] = datetime.now()
            raise
    
    def _process_round3(self, round2_results: Dict[str, Any]) -> Dict[str, Any]:
        """第三轮：第二原生字段处理"""
        self.logger.info("🟢 开始第三轮处理：第二原生字段")
        
        # 在第二轮结果基础上添加第二原生字段
        round3_results = {}
        
        for table_name, round2_data in round2_results.items():
            self.logger.info(f"📊 处理表格第二原生字段: {table_name}")
            
            try:
                updated_data = round2_data.copy()
                updated_data["status"] = "第三轮完成"
                
                # 根据表格类型处理第二原生字段
                if table_name == "📁 信息获取（基建）":
                    updated_data = self._process_info_table_round3(updated_data, round2_results)
                elif table_name == "Ⓜ️ 品牌识别（基建）":
                    updated_data = self._process_brand_table_round3(updated_data, round2_results)
                elif table_name == "🛍️ 品类管理（基建）":
                    updated_data = self._process_category_table_round3(updated_data, round2_results)
                elif table_name == "🔪 SKU解构（基建）":
                    updated_data = self._process_sku_table_round3(updated_data, round2_results)
                elif table_name == "🧪 规格解构（基建）":
                    updated_data = self._process_spec_table_round3(updated_data, round2_results)
                elif table_name == "💰 价格解构（基建）":
                    updated_data = self._process_price_table_round3(updated_data, round2_results)
                elif table_name == "💻 单价计算（基建）":
                    updated_data = self._process_unit_price_table_round3(updated_data, round2_results)
                elif table_name == "🤖 中转表（工具）":
                    updated_data = self._process_transfer_table_round3(updated_data, round2_results)
                
                round3_results[table_name] = updated_data
                self.logger.info(f"✅ 完成表格第二原生字段: {table_name}")
                
            except Exception as e:
                self.logger.error(f"❌ 表格第二原生字段处理失败 {table_name}: {e}")
                updated_data = round2_data.copy()
                updated_data["status"] = "第三轮失败"
                updated_data["error"] = str(e)
                round3_results[table_name] = updated_data
        
        return round3_results
    
    def _process_round4(self, round3_results: Dict[str, Any]) -> Dict[str, Any]:
        """第四轮：复杂交叉依赖处理"""
        self.logger.info("🟣 开始第四轮处理：复杂交叉依赖")
        
        # 在第三轮结果基础上处理复杂依赖
        final_results = {}
        
        for table_name, round3_data in round3_results.items():
            self.logger.info(f"📊 处理表格复杂依赖: {table_name}")
            
            try:
                updated_data = round3_data.copy()
                updated_data["status"] = "全部完成✅"
                
                # 最终验证和优化
                if table_name == "🤖 中转表（工具）":
                    updated_data = self._finalize_transfer_table(updated_data, round3_results)
                
                # 添加处理时间戳
                updated_data["processing_completed_at"] = datetime.now().isoformat()
                
                final_results[table_name] = updated_data
                self.logger.info(f"✅ 完成表格复杂依赖: {table_name}")
                
            except Exception as e:
                self.logger.error(f"❌ 表格复杂依赖处理失败 {table_name}: {e}")
                updated_data = round3_data.copy()
                updated_data["status"] = "第四轮失败"
                updated_data["error"] = str(e)
                final_results[table_name] = updated_data
        
        return final_results
    
    # 第三轮处理方法
    def _process_info_table_round3(self, data: Dict, all_results: Dict) -> Dict:
        """信息获取表第二原生字段"""
        # 基于关联字段计算更复杂的字段
        
        # 商品评分计算
        brand_score = self._calculate_brand_score(data.get("Ⓜ️🤍‼️品牌（标准格式）", ""))
        price_score = self._calculate_price_score(data.get("💰到手价格（终版）", 0))
        
        data["📁💚商品综合评分"] = round((brand_score + price_score) / 2, 1)
        
        # 推荐等级
        if data["📁💚商品综合评分"] >= 8.0:
            data["📁💚推荐等级"] = "⭐⭐⭐强烈推荐"
        elif data["📁💚商品综合评分"] >= 6.0:
            data["📁💚推荐等级"] = "⭐⭐推荐"
        else:
            data["📁💚推荐等级"] = "⭐一般"
        
        return data
    
    def _process_brand_table_round3(self, data: Dict, all_results: Dict) -> Dict:
        """品牌识别表第二原生字段"""
        # 品牌可信度评估
        brand = data.get("Ⓜ️🤍‼️品牌（标准格式）", "")
        if brand:
            data["Ⓜ️💚品牌可信度"] = self._assess_brand_credibility(brand)
        
        return data
    
    def _process_category_table_round3(self, data: Dict, all_results: Dict) -> Dict:
        """品类管理表第二原生字段"""
        # 品类热度分析
        category = data.get("🛍️🤍😀品类-一级品类（原始值）", "")
        if category:
            data["🛍️💚品类热度"] = self._analyze_category_popularity(category)
        
        return data
    
    def _process_sku_table_round3(self, data: Dict, all_results: Dict) -> Dict:
        """SKU解构表第二原生字段"""
        # SKU复杂度评估
        sku_count = data.get("🔪🖤SKU数量", 0)
        data["🔪💚SKU复杂度"] = "高" if sku_count > 5 else "中" if sku_count > 2 else "低"
        
        return data
    
    def _process_spec_table_round3(self, data: Dict, all_results: Dict) -> Dict:
        """规格解构表第二原生字段"""
        # 规格标准化程度
        specs = data.get("🧪🤍规格✖️数量✖️起拍数量（终版）", "")
        data["🧪💚规格标准化程度"] = "高" if specs else "低"
        
        return data
    
    def _process_price_table_round3(self, data: Dict, all_results: Dict) -> Dict:
        """价格解构表第二原生字段"""
        # 价格优势分析
        price = data.get("💰🤎到手价格（终版）", 0)
        data["💰💚价格优势等级"] = self._analyze_price_advantage(price)
        
        return data
    
    def _process_unit_price_table_round3(self, data: Dict, all_results: Dict) -> Dict:
        """单价计算表第二原生字段"""
        # 性价比评估
        unit_price = data.get("💻💚基础单价", 0)
        data["💻💚性价比等级"] = self._assess_cost_performance(unit_price)
        
        return data
    
    def _process_transfer_table_round3(self, data: Dict, all_results: Dict) -> Dict:
        """中转表第二原生字段"""
        # 综合质量评估
        info_table = all_results.get("📁 信息获取（基建）", {})
        
        data["🤖💚综合质量评分"] = info_table.get("📁💚商品综合评分", 0)
        data["🤖💚推荐等级"] = info_table.get("📁💚推荐等级", "")
        
        # 是否推荐上架
        quality_score = data.get("🤖💚综合质量评分", 0)
        info_type = data.get("🤖🏷️信息类型", "")
        
        if quality_score >= 7.0 and "🛒商品信息" in info_type:
            data["🤖💚推荐上架"] = "✅推荐上架"
        else:
            data["🤖💚推荐上架"] = "❌不推荐上架"
        
        return data
    
    def _finalize_transfer_table(self, data: Dict, all_results: Dict) -> Dict:
        """最终化中转表"""
        # 设置最终状态
        data["🤖🔥最终处理状态"] = "✅处理完成"
        data["🤖🔥数据完整性"] = self._check_data_completeness(data)
        
        return data
    
    # 辅助计算方法
    def _calculate_brand_score(self, brand: str) -> float:
        """计算品牌评分"""
        if not brand:
            return 5.0
        
        # 简化的品牌评分逻辑
        premium_brands = ["爱肯拿", "渴望", "巅峰", "皇家", "希尔思"]
        if any(b in brand for b in premium_brands):
            return 9.0
        
        return 7.0
    
    def _calculate_price_score(self, price: float) -> float:
        """计算价格评分"""
        if price <= 0:
            return 5.0
        
        # 简化的价格评分逻辑
        if price < 50:
            return 9.0
        elif price < 100:
            return 8.0
        elif price < 200:
            return 7.0
        else:
            return 6.0
    
    def _assess_brand_credibility(self, brand: str) -> str:
        """评估品牌可信度"""
        if not brand:
            return "未知"
        
        return "高" if len(brand) > 2 else "中"
    
    def _analyze_category_popularity(self, category: str) -> str:
        """分析品类热度"""
        hot_categories = ["主粮", "零食", "罐头"]
        return "热门" if category in hot_categories else "一般"
    
    def _analyze_price_advantage(self, price: float) -> str:
        """分析价格优势"""
        if price <= 0:
            return "无价格"
        elif price < 50:
            return "高性价比"
        elif price < 100:
            return "中等价位"
        else:
            return "高端价位"
    
    def _assess_cost_performance(self, unit_price: float) -> str:
        """评估性价比"""
        if unit_price <= 0:
            return "无法评估"
        elif unit_price < 1.0:
            return "超高性价比"
        elif unit_price < 2.0:
            return "高性价比"
        else:
            return "一般性价比"
    
    def _check_data_completeness(self, data: Dict) -> str:
        """检查数据完整性"""
        required_fields = ["🤖🏷️品牌（标准格式）", "🤖🏷️一级品类", "🤖🏷️信息类型"]
        
        missing_count = sum(1 for field in required_fields if not data.get(field))
        
        if missing_count == 0:
            return "✅完整"
        elif missing_count <= 1:
            return "⚠️基本完整"
        else:
            return "❌不完整"
    
    def _log_round_summary(self, round_name: str, results: Dict):
        """记录轮次摘要"""
        total_tables = len(results)
        completed_tables = sum(1 for r in results.values() if "完成" in r.get("status", ""))
        failed_tables = sum(1 for r in results.values() if "失败" in r.get("status", ""))
        
        self.logger.info(f"📊 {round_name}处理摘要:")
        self.logger.info(f"   总表格数: {total_tables}")
        self.logger.info(f"   完成表格: {completed_tables}")
        self.logger.info(f"   失败表格: {failed_tables}")
        self.logger.info(f"   成功率: {completed_tables/total_tables*100:.1f}%")
    
    def _log_final_summary(self, final_results: Dict):
        """记录最终摘要"""
        duration = self.processing_stats["end_time"] - self.processing_stats["start_time"]
        
        self.logger.info("\n" + "=" * 60)
        self.logger.info("🎉 多轮处理工作流完成")
        self.logger.info("=" * 60)
        self.logger.info(f"⏱️  处理时长: {duration.total_seconds():.2f}秒")
        self.logger.info(f"🔄 完成轮次: {self.processing_stats['rounds_completed']}/4")
        self.logger.info(f"📊 处理表格: {len(final_results)}")
        
        # 统计各表格最终状态
        status_count = {}
        for result in final_results.values():
            status = result.get("status", "未知")
            status_count[status] = status_count.get(status, 0) + 1
        
        self.logger.info("📈 最终状态统计:")
        for status, count in status_count.items():
            self.logger.info(f"   {status}: {count}个表格")
    
    def export_results(self, results: Dict, output_path: str = None) -> str:
        """导出处理结果"""
        if not output_path:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"processing_results_{timestamp}.json"
        
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2, default=str)
            
            self.logger.info(f"📁 结果已导出到: {output_path}")
            return output_path
            
        except Exception as e:
            self.logger.error(f"❌ 结果导出失败: {e}")
            return ""

# 测试代码
if __name__ == "__main__":
    scheduler = MultiRoundScheduler()
    
    # 测试数据
    test_data = {
        "🍬商品信息（原始版）": "爱肯拿 农场盛宴 全猫粮 1.8kg 💰128.5元 券后💰98元 u.jd.com/abc123",
        "🍬商品信息ID": "test_001",
        "🍬信息来源 / 发车人": "测试用户",
        "🍬源信息发布时间（自动）": "2024-01-01 12:00:00",
        "🍬源信息采集时间（自动）": "2024-01-01 12:05:00"
    }
    
    print("🚀 开始完整的多轮处理工作流测试")
    
    try:
        final_results = scheduler.process_complete_workflow(test_data)
        
        # 导出结果
        output_file = scheduler.export_results(final_results)
        
        print(f"\n🎉 测试完成！结果已保存到: {output_file}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
